# Git y control de versiones
.git
.gitignore
.gitattributes

# Archivos de configuración local
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencias de Node.js
web/node_modules
web/.next/cache
web/.pnpm-store

# Archivos de build temporales
web/.next/build-manifest.json
web/.next/export-detail.json
web/.next/images-manifest.json
web/.next/package.json
web/.next/prerender-manifest.json
web/.next/react-loadable-manifest.json
web/.next/routes-manifest.json

# Python
**/__pycache__
**/*.py[cod]
**/*.egg-info/
*.db
*.sqlite
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# Archivos de datos locales
data/
conversations/
codegen_sessions/
projects/projects/
monitoring_data/
semantic_memories/
*.log
*.pid

# Archivos de IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Archivos de OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Archivos de testing
coverage/
.nyc_output/
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/
.dmypy.json
dmypy.json

# Documentación
docs/
*.md
!README.Docker.md

# Archivos de configuración de desarrollo
docker-compose.override.yml
docker-compose.dev.yml

# Archivos temporales
tmp/
temp/
*.tmp
*.temp

# Archivos de configuración específicos del proyecto
custom/
config/custom/

# Archivos de respaldo
*.bak
*.backup
*.old

# Archivos de logs
logs/
*.log

# Test, docs, screenshots
tests/
screenshots/

# GitHub metadata
.github/ 