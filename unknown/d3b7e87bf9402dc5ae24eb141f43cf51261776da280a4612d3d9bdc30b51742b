#!/bin/bash

# Simple Background Jobs Setup for QAK
# Uses local Redis for simplicity, can be upgraded to Upstash later

set -e

echo "🚀 Setting up Simple Background Jobs (Local Redis)..."

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "❌ Virtual environment not found. Please run 'python -m venv .venv' first."
    exit 1
fi

echo "📦 Activating virtual environment..."
source .venv/bin/activate

echo "📥 Installing background job dependencies..."
pip install 'celery[redis]' redis flower kombu

# Check if Redis is installed
if ! command -v redis-server &> /dev/null; then
    echo "📥 Installing Redis..."
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install redis
        else
            echo "❌ Please install Homebrew first, then run: brew install redis"
            exit 1
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y redis-server
        elif command -v yum &> /dev/null; then
            sudo yum install -y redis
        else
            echo "❌ Please install Redis manually for your Linux distribution"
            exit 1
        fi
    else
        echo "❌ Unsupported OS. Please install Redis manually."
        exit 1
    fi
fi

echo "🔧 Testing background jobs system..."
python3 -c "
from src.core.background_jobs import BACKGROUND_JOBS_AVAILABLE
if BACKGROUND_JOBS_AVAILABLE:
    print('✅ Background jobs system is ready!')
    
    # Test local Redis
    from redis import Redis
    try:
        redis_client = Redis.from_url('redis://localhost:6379/0', decode_responses=True)
        redis_client.ping()
        print('✅ Local Redis connection test successful')
    except:
        print('⚠️ Local Redis not running, will start it automatically')
    
    from src.core.background_jobs.job_manager import get_job_manager
    print('✅ Job manager initialized successfully')
else:
    print('❌ Background jobs system not available')
    exit(1)
"

# Create a simple local Redis configuration
cat > redis-local.conf << EOF
# Simple Redis configuration for QAK Background Jobs
port 6379
bind 127.0.0.1
save 900 1
save 300 10
save 60 10000
dir ./redis-data
EOF

# Create redis data directory
mkdir -p redis-data

echo ""
echo "🎉 Simple Background Jobs Setup Complete!"
echo ""
echo "📋 Quick Start Commands:"
echo ""
echo "1. Start Redis (Terminal 1):"
echo "   redis-server redis-local.conf"
echo ""
echo "2. Start Celery Worker (Terminal 2):"
echo "   source .venv/bin/activate && python celery_worker.py"
echo ""
echo "3. Start QAK API (Terminal 3):"
echo "   source .venv/bin/activate && python app.py"
echo ""
echo "4. Test system:"
echo "   curl http://localhost:8000/api/v2/background-jobs/health"
echo ""
echo "💡 Or use the all-in-one script:"
echo "   ./scripts/start_qak_simple.sh"
echo ""
echo "🔧 Configuration:"
echo "   - Redis: localhost:6379 (local)"
echo "   - Data: ./redis-data/"
echo "   - Config: ./redis-local.conf"
echo ""
echo "🚀 Ready for background job processing!"