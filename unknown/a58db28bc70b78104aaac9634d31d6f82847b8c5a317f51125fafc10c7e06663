# QAK Background Jobs System

## Overview

QAK now includes a robust background jobs system for processing CPU-intensive tasks like AI-powered test analysis. This prevents the main API from blocking during long-running analysis operations and provides real-time progress updates to users.

## Features

- **Asynchronous Processing**: AI analysis runs in background workers
- **Real-time Progress**: Live updates via polling endpoints
- **Job Management**: Start, monitor, cancel, and retrieve results
- **Graceful Degradation**: System works without background jobs (falls back to synchronous processing)
- **Monitoring**: Health checks and optional Flower monitoring interface
- **Scalable**: Multiple worker processes can be spawned as needed

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   FastAPI       │    │   Celery        │
│   (React)       │────│   API Server    │────│   Worker        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Progress UI     │    │ Job Endpoints   │    │ Analysis Tasks  │
│ Component       │    │ /background-jobs│    │ AI Processing   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                 │
                                 ▼
                       ┌─────────────────┐
                       │     Redis       │
                       │ (Message Broker │
                       │  & Job Storage) │
                       └─────────────────┘
```

## Quick Setup

### 1. Install Dependencies

```bash
# Option 1: Use setup script
./scripts/setup_background_jobs.sh

# Option 2: Manual installation
source .venv/bin/activate
pip install 'celery[redis]' redis flower kombu
```

### 2. Start Infrastructure

```bash
# Start Redis (required)
docker run -d -p 6379:6379 --name qak-redis redis:7-alpine

# Start Celery worker
python celery_worker.py

# Start QAK API
python app.py
```

### 3. Verify Setup

```bash
# Check health
curl http://localhost:8000/api/v2/background-jobs/health

# Expected response:
{
  "status": "available",
  "components": {
    "redis": "connected",
    "celery_workers": "1 workers active"
  },
  "message": "Background jobs system is ready"
}
```

## API Usage

### Start Background Analysis

```bash
curl -X POST http://localhost:8000/api/v2/background-jobs/analysis/start \
  -H "Content-Type: application/json" \
  -d '{
    "execution_id": "exec_123",
    "result_data": {...},
    "screenshot_data": [...]
  }'
```

Response:
```json
{
  "job_id": "analysis_a1b2c3d4",
  "execution_id": "exec_123",
  "status": "PENDING",
  "message": "Análisis iniciado en background",
  "endpoints": {
    "status": "/api/v2/background-jobs/analysis_a1b2c3d4/status",
    "result": "/api/v2/background-jobs/analysis_a1b2c3d4/result"
  }
}
```

### Monitor Progress

```bash
curl http://localhost:8000/api/v2/background-jobs/analysis_a1b2c3d4/status
```

Response:
```json
{
  "job_id": "analysis_a1b2c3d4",
  "execution_id": "exec_123",
  "status": "RUNNING",
  "progress": 65,
  "message": "Analizando paso 5 de 7...",
  "created_at": "2025-01-08T12:00:00Z",
  "started_at": "2025-01-08T12:00:01Z",
  "has_result": false,
  "has_error": false
}
```

### Get Results

```bash
curl http://localhost:8000/api/v2/background-jobs/analysis_a1b2c3d4/result
```

Response:
```json
{
  "job_id": "analysis_a1b2c3d4",
  "status": "COMPLETED",
  "result": {
    "ai_analysis": {...},
    "summary": {
      "final_verdict": "PASS",
      "confidence_level": 0.95
    }
  },
  "execution_time_ms": 15420
}
```

## Frontend Integration

### React Component

```tsx
import { AnalysisProgressIndicator } from '@/components/ui/AnalysisProgressIndicator'

function TestResultPage() {
  const [jobId, setJobId] = useState<string | null>(null)

  const startAnalysis = async () => {
    const response = await fetch('/api/v2/background-jobs/analysis/start', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        execution_id: "exec_123",
        result_data: testResult,
        screenshot_data: screenshots
      })
    })
    
    const data = await response.json()
    setJobId(data.job_id)
  }

  return (
    <div>
      {jobId ? (
        <AnalysisProgressIndicator
          jobId={jobId}
          onAnalysisComplete={(result) => {
            console.log('Analysis completed:', result)
          }}
          onAnalysisError={(error) => {
            console.error('Analysis failed:', error)
          }}
        />
      ) : (
        <button onClick={startAnalysis}>
          Start AI Analysis
        </button>
      )}
    </div>
  )
}
```

## Production Deployment

### Docker Compose

```yaml
# docker-compose.celery.yml
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  celery-worker:
    build: .
    command: python celery_worker.py
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379/0
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    restart: unless-stopped

  flower:
    build: .
    command: celery -A src.core.background_jobs.celery_app flower --port=5555
    ports:
      - "5555:5555"
    depends_on:
      - redis
    profiles:
      - monitoring

volumes:
  redis_data:
```

Start with:
```bash
# Basic setup (Redis + Worker)
docker-compose -f docker-compose.celery.yml up -d

# With monitoring
docker-compose -f docker-compose.celery.yml --profile monitoring up -d
```

### Scaling Workers

```bash
# Scale to 3 workers
docker-compose -f docker-compose.celery.yml up -d --scale celery-worker=3

# Or manually start multiple workers
python celery_worker.py --concurrency=4
```

## Configuration

### Environment Variables

```bash
# Redis connection
REDIS_URL=redis://localhost:6379/0

# Celery logging
CELERY_LOG_LEVEL=INFO

# AI Analysis settings (existing)
GOOGLE_API_KEY=your_key
OPENROUTER_API_KEY=your_key
LLM_MODEL=gemini-2.5-flash-exp
AI_ANALYSIS_COST_OPTIMIZATION=medium
```

### Job TTL and Cleanup

Jobs are automatically cleaned up after 1 hour by default. Configure via:

```python
# In JobManager initialization
job_manager = JobManager()
job_manager.job_ttl = 3600  # 1 hour

# Manual cleanup
curl -X POST http://localhost:8000/api/v2/background-jobs/cleanup?max_age_hours=24
```

## Monitoring

### Health Checks

```bash
# System health
curl http://localhost:8000/api/v2/background-jobs/health

# API health
curl http://localhost:8000/health
```

### Flower Monitoring (Optional)

Access web interface at http://localhost:5555 to monitor:
- Active/completed tasks
- Worker status
- Task statistics
- Real-time graphs

### Logs

Background jobs integrate with existing Logfire setup:

```python
# Logs appear under "qak-api" service in Logfire
# https://logfire-us.pydantic.dev/nahuelcio/qak
```

## Troubleshooting

### Common Issues

1. **"Background jobs not available"**
   ```bash
   pip install 'celery[redis]' redis
   ```

2. **Redis connection failed**
   ```bash
   # Start Redis
   docker run -d -p 6379:6379 redis:7-alpine
   
   # Or install locally
   brew install redis && redis-server
   ```

3. **No workers active**
   ```bash
   # Start worker
   python celery_worker.py
   
   # Check worker status
   celery -A src.core.background_jobs.celery_app inspect active
   ```

4. **Jobs stuck in PENDING**
   - Ensure Redis is running
   - Ensure Celery worker is running
   - Check worker logs for errors

### Debug Commands

```bash
# Check Celery status
celery -A src.core.background_jobs.celery_app inspect active

# List queues
celery -A src.core.background_jobs.celery_app inspect active_queues

# Purge all jobs (use with caution)
celery -A src.core.background_jobs.celery_app purge

# Monitor in real-time
celery -A src.core.background_jobs.celery_app events
```

## Migration from Synchronous Analysis

The system is designed for gradual migration:

1. **Current**: Synchronous analysis in `result_transformer.py:754`
2. **Future**: Background analysis using job system
3. **Transition**: Both methods can coexist

To migrate existing analysis calls:

```python
# Before (synchronous)
ai_analysis = await analysis_service.analyze_full_test(result, screenshot_data)

# After (background)
job_id = job_manager.create_job(execution_id, "analysis")
analyze_test_background.delay(job_id, result.to_dict(), screenshot_data)
```

## Performance Considerations

- **Memory**: Each worker process uses ~100-200MB
- **CPU**: Analysis tasks are CPU-intensive, limit concurrency appropriately
- **Redis**: Minimal memory usage for job metadata
- **Network**: Screenshots are compressed before sending to workers

Recommended scaling:
- **Development**: 1-2 workers
- **Production**: 2-4 workers per CPU core
- **High-load**: Dedicated worker machines

## Support

For issues or questions:
1. Check logs in Logfire dashboard
2. Verify health endpoints
3. Review Celery worker logs
4. Check Redis connectivity