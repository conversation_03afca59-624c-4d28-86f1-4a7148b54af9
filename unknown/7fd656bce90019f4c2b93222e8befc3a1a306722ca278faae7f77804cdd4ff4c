"""
Job Manager for Background Tasks

Manages job lifecycle, status tracking, and result storage.
"""

import os
import json
import uuid
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from redis import Redis
from src.core.background_jobs.models import JobStatus, AnalysisJob, JobProgress, JobResult
from src.core.background_jobs.celery_app import celery_app

logger = logging.getLogger(__name__)


class JobManager:
    """Manages background job lifecycle and status."""
    
    def __init__(self, redis_client: Optional[Redis] = None):
        """Initialize job manager."""
        if redis_client:
            self.redis = redis_client
        else:
            # Use Upstash if available
            upstash_url = os.getenv("UPSTASH_REDIS_REST_URL")
            upstash_token = os.getenv("UPSTASH_REDIS_REST_TOKEN")
            
            if upstash_url and upstash_token:
                import urllib.parse
                host = urllib.parse.urlparse(upstash_url).netloc
                redis_url = f"rediss://default:{upstash_token}@{host}:6380"
                self.redis = Redis.from_url(redis_url, decode_responses=True)
                logger.info(f"✅ JobManager using Upstash Redis: {host}")
            else:
                # Fallback to local Redis
                redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
                self.redis = Redis.from_url(redis_url, decode_responses=True)
                logger.info(f"✅ JobManager using local Redis: {redis_url}")
        self.job_prefix = "qak:job:"
        self.job_ttl = 3600  # 1 hour TTL for job data
        
    def create_job(self, execution_id: str, job_type: str = "analysis") -> str:
        """
        Create a new background job.
        
        Args:
            execution_id: Test execution ID
            job_type: Type of job (default: analysis)
            
        Returns:
            str: Job ID
        """
        job_id = f"{job_type}_{uuid.uuid4().hex[:8]}"
        
        job = AnalysisJob(
            job_id=job_id,
            execution_id=execution_id,
            status=JobStatus.PENDING,
            message="Job created, waiting to start..."
        )
        
        # Store job in Redis
        self._store_job(job)
        
        logger.info(f"Created job {job_id} for execution {execution_id}")
        return job_id
    
    def get_job(self, job_id: str) -> Optional[AnalysisJob]:
        """
        Get job by ID.
        
        Args:
            job_id: Job identifier
            
        Returns:
            AnalysisJob or None if not found
        """
        key = f"{self.job_prefix}{job_id}"
        data = self.redis.get(key)
        
        if data:
            try:
                job_data = json.loads(data)
                return AnalysisJob(**job_data)
            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"Failed to parse job data for {job_id}: {e}")
                return None
        
        return None
    
    def update_job_status(self, job_id: str, status: JobStatus, message: str = ""):
        """
        Update job status.
        
        Args:
            job_id: Job identifier
            status: New status
            message: Status message
        """
        job = self.get_job(job_id)
        if not job:
            logger.warning(f"Job {job_id} not found for status update")
            return
        
        job.status = status
        job.message = message
        
        if status == JobStatus.RUNNING and not job.started_at:
            job.started_at = datetime.utcnow()
        elif status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
            job.completed_at = datetime.utcnow()
        
        self._store_job(job)
        logger.info(f"Updated job {job_id} status to {status}")
    
    def update_job_progress(self, job_id: str, progress: int, message: str = ""):
        """
        Update job progress.
        
        Args:
            job_id: Job identifier
            progress: Progress percentage (0-100)
            message: Progress message
        """
        job = self.get_job(job_id)
        if not job:
            logger.warning(f"Job {job_id} not found for progress update")
            return
        
        job.progress = max(0, min(100, progress))
        if message:
            job.message = message
        
        self._store_job(job)
        logger.debug(f"Updated job {job_id} progress to {progress}%")
    
    def store_job_result(self, job_id: str, result: Dict[str, Any]):
        """
        Store job result.
        
        Args:
            job_id: Job identifier
            result: Analysis result
        """
        job = self.get_job(job_id)
        if not job:
            logger.warning(f"Job {job_id} not found for result storage")
            return
        
        job.result = result
        job.status = JobStatus.COMPLETED
        job.completed_at = datetime.utcnow()
        job.progress = 100
        job.message = "Analysis completed successfully"
        
        self._store_job(job)
        logger.info(f"Stored result for job {job_id}")
    
    def store_job_error(self, job_id: str, error: str):
        """
        Store job error.
        
        Args:
            job_id: Job identifier
            error: Error message
        """
        job = self.get_job(job_id)
        if not job:
            logger.warning(f"Job {job_id} not found for error storage")
            return
        
        job.error = error
        job.status = JobStatus.FAILED
        job.completed_at = datetime.utcnow()
        job.message = f"Analysis failed: {error}"
        
        self._store_job(job)
        logger.error(f"Stored error for job {job_id}: {error}")
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get job status summary.
        
        Args:
            job_id: Job identifier
            
        Returns:
            Dict with status information
        """
        job = self.get_job(job_id)
        if not job:
            return None
        
        # Handle status as either enum or string (due to Redis serialization)
        status_value = job.status.value if hasattr(job.status, 'value') else str(job.status)

        return {
            "job_id": job.job_id,
            "execution_id": job.execution_id,
            "status": status_value,
            "progress": job.progress,
            "message": job.message,
            "created_at": job.created_at.isoformat(),
            "started_at": job.started_at.isoformat() if job.started_at else None,
            "completed_at": job.completed_at.isoformat() if job.completed_at else None,
            "has_result": job.result is not None,
            "has_error": job.error is not None
        }
    
    def get_job_result(self, job_id: str) -> Optional[JobResult]:
        """
        Get job result.
        
        Args:
            job_id: Job identifier
            
        Returns:
            JobResult or None
        """
        job = self.get_job(job_id)
        if not job:
            return None
        
        execution_time_ms = None
        if job.started_at and job.completed_at:
            execution_time_ms = int((job.completed_at - job.started_at).total_seconds() * 1000)
        
        return JobResult(
            job_id=job.job_id,
            status=job.status,
            result=job.result,
            error=job.error,
            execution_time_ms=execution_time_ms
        )
    
    def cancel_job(self, job_id: str):
        """
        Cancel a job.
        
        Args:
            job_id: Job identifier
        """
        # Try to revoke the Celery task
        try:
            celery_app.control.revoke(job_id, terminate=True)
            logger.info(f"Revoked Celery task for job {job_id}")
        except Exception as e:
            logger.warning(f"Failed to revoke Celery task for job {job_id}: {e}")
        
        # Update job status
        self.update_job_status(job_id, JobStatus.CANCELLED, "Job cancelled by user")
    
    def cleanup_old_jobs(self, max_age_hours: int = 24):
        """
        Clean up old jobs.
        
        Args:
            max_age_hours: Maximum age in hours before cleanup
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
        
        # Get all job keys
        keys = self.redis.keys(f"{self.job_prefix}*")
        deleted_count = 0
        
        for key in keys:
            try:
                data = self.redis.get(key)
                if data:
                    job_data = json.loads(data)
                    created_at = datetime.fromisoformat(job_data["created_at"])
                    
                    if created_at < cutoff_time:
                        self.redis.delete(key)
                        deleted_count += 1
            except Exception as e:
                logger.warning(f"Failed to process job key {key} during cleanup: {e}")
        
        logger.info(f"Cleaned up {deleted_count} old jobs")
    
    def _store_job(self, job: AnalysisJob):
        """Store job in Redis."""
        key = f"{self.job_prefix}{job.job_id}"
        data = job.model_dump_json()
        self.redis.setex(key, self.job_ttl, data)


# Global job manager instance
_job_manager: Optional[JobManager] = None

def get_job_manager() -> JobManager:
    """Get global job manager instance."""
    global _job_manager
    if _job_manager is None:
        _job_manager = JobManager()
    return _job_manager