"use client"

import React, { useState, useEffect } from 'react'
import { CheckCircle, Loader2, XCircle, Clock } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

export interface JobStatus {
  job_id: string
  execution_id: string
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  progress: number
  message: string
  created_at: string
  started_at?: string
  completed_at?: string
  has_result: boolean
  has_error: boolean
}

export interface JobResult {
  job_id: string
  status: string
  result?: any
  error?: string
  execution_time_ms?: number
}

interface AnalysisProgressIndicatorProps {
  jobId: string
  onAnalysisComplete?: (result: JobResult) => void
  onAnalysisError?: (error: string) => void
  className?: string
}

export const AnalysisProgressIndicator: React.FC<AnalysisProgressIndicatorProps> = ({
  jobId,
  onAnalysisComplete,
  onAnalysisError,
  className = ""
}) => {
  const [status, setStatus] = useState<JobStatus | null>(null)
  const [result, setResult] = useState<JobResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isPolling, setIsPolling] = useState(true)

  // Poll job status
  useEffect(() => {
    if (!jobId || !isPolling) return

    const pollStatus = async () => {
      try {
        const response = await fetch(`/api/v2/background-jobs/${jobId}/status`)
        
        if (response.ok) {
          const statusData: JobStatus = await response.json()
          setStatus(statusData)

          // Check if job is complete
          if (statusData.status === 'COMPLETED') {
            setIsPolling(false)

            // Fetch result
            try {
              const resultResponse = await fetch(`/api/v2/background-jobs/${jobId}/result`)
              if (resultResponse.ok) {
                const resultData: JobResult = await resultResponse.json()
                setResult(resultData)
                console.log('🎉 AI Analysis completed successfully:', resultData)

                // Give a small delay to ensure the database has been updated
                setTimeout(() => {
                  onAnalysisComplete?.(resultData)
                }, 1000)
              }
            } catch (err) {
              console.error('Failed to fetch job result:', err)
            }
          } else if (statusData.status === 'FAILED') {
            setIsPolling(false)
            setError(statusData.message || 'Analysis failed')
            onAnalysisError?.(statusData.message || 'Analysis failed')
          } else if (statusData.status === 'CANCELLED') {
            setIsPolling(false)
            setError('Analysis was cancelled')
            onAnalysisError?.('Analysis was cancelled')
          }
        } else {
          throw new Error(`HTTP ${response.status}`)
        }
      } catch (err) {
        console.error('Failed to poll job status:', err)
        setError('Failed to check analysis status')
        setIsPolling(false)
        onAnalysisError?.('Failed to check analysis status')
      }
    }

    // Initial poll
    pollStatus()

    // Set up polling interval
    const interval = setInterval(pollStatus, 2000) // Poll every 2 seconds

    return () => clearInterval(interval)
  }, [jobId, isPolling, onAnalysisComplete, onAnalysisError])

  const handleCancel = async () => {
    try {
      const response = await fetch(`/api/v2/background-jobs/${jobId}/cancel`, {
        method: 'POST'
      })
      
      if (response.ok) {
        setIsPolling(false)
        setError('Analysis cancelled')
        onAnalysisError?.('Analysis cancelled')
      }
    } catch (err) {
      console.error('Failed to cancel job:', err)
    }
  }

  const getStatusIcon = () => {
    if (!status) return <Clock className="h-4 w-4" />
    
    switch (status.status) {
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'RUNNING':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'FAILED':
      case 'CANCELLED':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getStatusColor = () => {
    if (!status) return 'default'
    
    switch (status.status) {
      case 'PENDING':
        return 'secondary'
      case 'RUNNING':
        return 'default'
      case 'COMPLETED':
        return 'success'
      case 'FAILED':
      case 'CANCELLED':
        return 'destructive'
      default:
        return 'default'
    }
  }

  const formatExecutionTime = (ms?: number) => {
    if (!ms) return null
    
    if (ms < 1000) return `${ms}ms`
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
    return `${(ms / 60000).toFixed(1)}m`
  }

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          {getStatusIcon()}
          Análisis QAK en Progreso
          <Badge variant={getStatusColor() as any} className="ml-auto">
            {status?.status || 'INITIALIZING'}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Progress Bar */}
        {status && status.status === 'RUNNING' && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progreso</span>
              <span>{status.progress}%</span>
            </div>
            <Progress value={status.progress} className="h-2" />
          </div>
        )}
        
        {/* Status Message */}
        <div className="text-sm text-muted-foreground">
          {status?.message || 'Iniciando análisis...'}
        </div>
        
        {/* Job Details */}
        {status && (
          <div className="text-xs text-muted-foreground space-y-1">
            <div>Job ID: {status.job_id}</div>
            <div>Execution ID: {status.execution_id}</div>
            {status.started_at && (
              <div>Iniciado: {new Date(status.started_at).toLocaleTimeString()}</div>
            )}
            {status.completed_at && (
              <div>Completado: {new Date(status.completed_at).toLocaleTimeString()}</div>
            )}
          </div>
        )}
        
        {/* Execution Time */}
        {result?.execution_time_ms && (
          <div className="text-sm text-muted-foreground">
            Tiempo de ejecución: {formatExecutionTime(result.execution_time_ms)}
          </div>
        )}
        
        {/* Error Display */}
        {error && (
          <div className="text-sm text-red-600 bg-red-50 p-2 rounded border">
            {error}
          </div>
        )}
        
        {/* Result Summary */}
        {result?.result && (
          <div className="text-sm bg-green-50 p-3 rounded border">
            <div className="font-medium text-green-800">
              ✅ Análisis completado exitosamente
            </div>
            {result.result.summary && (
              <div className="mt-1 text-green-700">
                Veredicto: {result.result.summary.final_verdict} 
                {result.result.summary.confidence_level && 
                  ` (${(result.result.summary.confidence_level * 100).toFixed(0)}% confianza)`
                }
              </div>
            )}
          </div>
        )}
        
        {/* Cancel Button */}
        {status && ['PENDING', 'RUNNING'].includes(status.status) && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleCancel}
            className="w-full"
          >
            Cancelar Análisis
          </Button>
        )}
      </CardContent>
    </Card>
  )
}

export default AnalysisProgressIndicator