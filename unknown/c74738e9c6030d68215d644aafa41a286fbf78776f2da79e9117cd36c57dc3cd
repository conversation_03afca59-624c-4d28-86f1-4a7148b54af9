"""
Migration Script for Multi-Environment System

Migrates existing projects to support the new multi-environment system by:
1. Creating default environments for projects without environments
2. Converting absolute URLs in test cases to relative paths
3. Ensuring backward compatibility
"""

import asyncio
import logging
from typing import List, Dict, Any
from urllib.parse import urlparse
from collections import Counter

from src.database.models.project import Project
from src.database.models.environment import Environment
from src.database.connection import get_database_manager

logger = logging.getLogger(__name__)


class ProjectEnvironmentMigrator:
    """Handles migration of projects to multi-environment system."""
    
    def __init__(self):
        self.migrated_projects = 0
        self.failed_migrations = 0
        self.total_projects = 0
    
    async def migrate_all_projects(self) -> Dict[str, Any]:
        """
        Migrate all existing projects to support environments.
        
        Returns:
            Dict: Migration summary with statistics
        """
        logger.info("🚀 Starting project migration to multi-environment system...")
        
        try:
            # Connect to database
            db_manager = get_database_manager()
            await db_manager.connect()
            
            # Get all projects
            projects = await Project.find_all().to_list()
            self.total_projects = len(projects)
            
            logger.info(f"📊 Found {self.total_projects} projects to migrate")
            
            # Migrate each project
            for project in projects:
                try:
                    await self._migrate_project(project)
                    self.migrated_projects += 1
                    logger.info(f"✅ Migrated project: {project.name} ({project.project_id})")
                except Exception as e:
                    self.failed_migrations += 1
                    logger.error(f"❌ Failed to migrate project {project.project_id}: {e}")
            
            # Generate summary
            summary = {
                "total_projects": self.total_projects,
                "migrated_successfully": self.migrated_projects,
                "failed_migrations": self.failed_migrations,
                "migration_rate": f"{(self.migrated_projects/self.total_projects)*100:.1f}%" if self.total_projects > 0 else "0%"
            }
            
            logger.info(f"🎉 Migration completed! {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"💥 Migration failed: {e}")
            raise
    
    async def _migrate_project(self, project: Project):
        """
        Migrate a single project to multi-environment system.
        
        Args:
            project: Project to migrate
        """
        # Skip if project already has environments
        if project.environments:
            logger.debug(f"⏭️  Project {project.project_id} already has environments, skipping")
            return
        
        # Extract base URLs from test cases
        base_urls = self._extract_base_urls_from_project(project)
        
        # Create default environment
        default_env = self._create_default_environment(base_urls, project)
        
        # Convert test case URLs to relative paths
        self._convert_test_case_urls_to_relative(project, default_env.base_url)
        
        # Add environment to project
        project.add_environment(default_env)
        
        # Save project
        await project.save()
            
    def _extract_base_urls_from_project(self, project: Project) -> List[str]:
        """
        Extract all base URLs from test cases in the project.
        
        Args:
            project: Project to analyze
            
        Returns:
            List[str]: List of base URLs found
        """
        urls = []
        
        for suite in project.test_suites.values():
            for test_case in suite.test_cases.values():
                url = test_case.url
                if url and url.startswith(('http://', 'https://')):
                    try:
                        parsed = urlparse(url)
                        base_url = f"{parsed.scheme}://{parsed.netloc}"
                        urls.append(base_url)
                    except Exception as e:
                        logger.warning(f"⚠️  Failed to parse URL {url}: {e}")
        
        return urls
    
    def _create_default_environment(self, base_urls: List[str], project: Project) -> Environment:
        """
        Create a default environment for the project.
        
        Args:
            base_urls: List of base URLs found in test cases
            project: Project being migrated
            
        Returns:
            Environment: Default environment for the project
        """
        # Determine the most common base URL
        if base_urls:
            url_counts = Counter(base_urls)
            most_common_url = url_counts.most_common(1)[0][0]
            
            # If there are multiple URLs, log them for review
            if len(url_counts) > 1:
                logger.info(f"📋 Project {project.project_id} has multiple base URLs: {dict(url_counts)}")
                logger.info(f"🎯 Selected most common: {most_common_url}")
        else:
            # No URLs found, use empty base URL (test cases will use absolute URLs)
            most_common_url = ""
            logger.debug(f"🔍 No base URLs found in project {project.project_id}, using empty base URL")
        
        # Create default environment
        return Environment(
            name="Default",
            description=f"Auto-generated default environment for {project.name}",
            base_url=most_common_url,
            is_default=True,
            config_overrides={},
            tags=["default", "migrated", "auto-generated"]
        )
    
    def _convert_test_case_urls_to_relative(self, project: Project, base_url: str):
        """
        Convert absolute URLs in test cases to relative paths.
        
        Args:
            project: Project to update
            base_url: Base URL to remove from test case URLs
        """
        if not base_url:
            # No base URL, keep URLs as absolute
            return
        
        converted_count = 0
        
        for suite in project.test_suites.values():
            for test_case in suite.test_cases.values():
                if test_case.url and test_case.url.startswith(base_url):
                    # Convert to relative path
                    relative_path = test_case.url[len(base_url):]
                    if not relative_path.startswith('/'):
                        relative_path = '/' + relative_path
                    
                    test_case.url = relative_path
                    converted_count += 1
        
        if converted_count > 0:
            logger.info(f"🔗 Converted {converted_count} URLs to relative paths in project {project.project_id}")
    
    async def rollback_migration(self, project_id: str) -> bool:
        """
        Rollback migration for a specific project.
        
        Args:
            project_id: ID of project to rollback
            
        Returns:
            bool: True if rollback successful
        """
        try:
            project = await Project.find_one({"project_id": project_id})
            if not project:
                logger.error(f"❌ Project {project_id} not found")
                return False
            
            # Remove auto-generated environments
            original_count = len(project.environments)
            project.environments = [
                env for env in project.environments 
                if "auto-generated" not in env.tags
            ]
            
            # Reset default environment ID if we removed the default
            if not project.environments:
                project.default_environment_id = None
            elif not any(env.is_default for env in project.environments):
                if project.environments:
                    project.environments[0].is_default = True
                    project.default_environment_id = project.environments[0].env_id
            
            await project.save()
            
            removed_count = original_count - len(project.environments)
            logger.info(f"🔙 Rollback completed for project {project_id}: removed {removed_count} auto-generated environments")
            return True
            
        except Exception as e:
            logger.error(f"💥 Failed to rollback project {project_id}: {e}")
            return False


async def run_migration():
    """Run the migration script."""
    migrator = ProjectEnvironmentMigrator()
    summary = await migrator.migrate_all_projects()
    
    print("\n" + "="*60)
    print("📊 MIGRATION SUMMARY")
    print("="*60)
    print(f"Total Projects: {summary['total_projects']}")
    print(f"Successfully Migrated: {summary['migrated_successfully']}")
    print(f"Failed Migrations: {summary['failed_migrations']}")
    print(f"Success Rate: {summary['migration_rate']}")
    print("="*60)
    
    if summary['failed_migrations'] > 0:
        print("⚠️  Some migrations failed. Check logs for details.")
    else:
        print("🎉 All projects migrated successfully!")


async def rollback_project(project_id: str):
    """Rollback migration for a specific project."""
    migrator = ProjectEnvironmentMigrator()
    success = await migrator.rollback_migration(project_id)
    
    if success:
        print(f"✅ Successfully rolled back project {project_id}")
    else:
        print(f"❌ Failed to rollback project {project_id}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        if len(sys.argv) < 3:
            print("Usage: python migrate_projects_to_environments.py rollback <project_id>")
            sys.exit(1)
        
        project_id = sys.argv[2]
        asyncio.run(rollback_project(project_id))
    else:
        asyncio.run(run_migration())