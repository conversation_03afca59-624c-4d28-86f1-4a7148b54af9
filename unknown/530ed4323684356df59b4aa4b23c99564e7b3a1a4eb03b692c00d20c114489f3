"""
Environment Management API Routes

API endpoints for managing environments within projects, including
creation, update, deletion, and setting default environments.
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from src.core.test_service import TestService
from src.database.models.environment import Environment
from src.api.models import SuccessResponse, ErrorResponse, ListResponse
import os

# Router for environment management
router = APIRouter(tags=["Environments"])


def get_test_service():
    """Create and return an instance of the test service."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))


# === REQUEST/RESPONSE MODELS ===

class EnvironmentCreateRequest(BaseModel):
    """Request model for creating a new environment."""
    name: str = Field(..., description="Environment name (e.g., Development, QA, Staging, Production)")
    description: str = Field(default="", description="Environment description")
    base_url: str = Field(..., description="Base URL for this environment")
    is_default: bool = Field(default=False, description="Whether this is the default environment")
    config_overrides: Dict[str, Any] = Field(default_factory=dict, description="Browser config overrides")
    tags: List[str] = Field(default_factory=list, description="Environment tags")


class EnvironmentUpdateRequest(BaseModel):
    """Request model for updating an environment."""
    name: Optional[str] = Field(None, description="Environment name")
    description: Optional[str] = Field(None, description="Environment description")
    base_url: Optional[str] = Field(None, description="Base URL for this environment")
    is_default: Optional[bool] = Field(None, description="Whether this is the default environment")
    config_overrides: Optional[Dict[str, Any]] = Field(None, description="Browser config overrides")
    tags: Optional[List[str]] = Field(None, description="Environment tags")


class EnvironmentResponse(BaseModel):
    """Response model for environment data."""
    env_id: str
    name: str
    description: str
    base_url: str
    is_default: bool
    config_overrides: Dict[str, Any]
    tags: List[str]
    created_at: str
    updated_at: str


# === ENVIRONMENT CRUD OPERATIONS ===

@router.post("/{project_id}/environments", response_model=EnvironmentResponse, summary="Create environment")
async def create_environment(
    project_id: str,
    request: EnvironmentCreateRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Create a new environment for the project."""
    try:
        # Get the project to ensure it exists
        project_data = await test_service.get_project(project_id)
        if not project_data:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Create the environment
        environment = Environment(
            name=request.name,
            description=request.description,
            base_url=request.base_url,
            is_default=request.is_default,
            config_overrides=request.config_overrides,
            tags=request.tags
        )
        
        # Add environment to project using the test service
        success = await test_service.add_environment_to_project(project_id, environment)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to create environment")
        
        return EnvironmentResponse(
            env_id=environment.env_id,
            name=environment.name,
            description=environment.description,
            base_url=environment.base_url,
            is_default=environment.is_default,
            config_overrides=environment.config_overrides,
            tags=environment.tags,
            created_at=environment.created_at.isoformat(),
            updated_at=environment.updated_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}/environments", response_model=ListResponse, summary="List environments")
async def list_environments(
    project_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """List all environments for the project."""
    try:
        environments = await test_service.get_project_environments(project_id)
        
        # Create response items with explicit field mapping
        items = []
        for env in environments:
            try:
                env_response = EnvironmentResponse(
                    env_id=env.env_id,
                    name=env.name,
                    description=env.description,
                    base_url=env.base_url,
                    is_default=env.is_default,
                    config_overrides=env.config_overrides,
                    tags=env.tags,
                    created_at=env.created_at.isoformat(),
                    updated_at=env.updated_at.isoformat()
                )
                items.append(env_response.model_dump())
            except Exception as env_error:
                print(f"Error creating EnvironmentResponse for env {env.env_id}: {env_error}")
                # Skip this environment or raise with more specific error
                raise HTTPException(status_code=500, detail=f"Error serializing environment {env.env_id}: {str(env_error)}")
        
        return ListResponse(
            count=len(items),
            items=items
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in list_environments: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}/environments/{env_id}", response_model=EnvironmentResponse, summary="Get environment")
async def get_environment(
    project_id: str,
    env_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Get details of a specific environment."""
    try:
        environment = await test_service.get_project_environment(project_id, env_id)
        if not environment:
            raise HTTPException(status_code=404, detail="Environment not found")
        
        return EnvironmentResponse(
            env_id=environment.env_id,
            name=environment.name,
            description=environment.description,
            base_url=environment.base_url,
            is_default=environment.is_default,
            config_overrides=environment.config_overrides,
            tags=environment.tags,
            created_at=environment.created_at.isoformat(),
            updated_at=environment.updated_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{project_id}/environments/{env_id}", response_model=EnvironmentResponse, summary="Update environment")
async def update_environment(
    project_id: str,
    env_id: str,
    request: EnvironmentUpdateRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Update an existing environment."""
    try:
        # Get the current environment
        environment = await test_service.get_project_environment(project_id, env_id)
        if not environment:
            raise HTTPException(status_code=404, detail="Environment not found")
        
        # Update fields that were provided
        update_data = request.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(environment, field, value)
        
        # Update timestamp
        environment.update_timestamp()
        
        # Update environment in project
        success = await test_service.update_project_environment(project_id, environment)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to update environment")
        
        return EnvironmentResponse(
            env_id=environment.env_id,
            name=environment.name,
            description=environment.description,
            base_url=environment.base_url,
            is_default=environment.is_default,
            config_overrides=environment.config_overrides,
            tags=environment.tags,
            created_at=environment.created_at.isoformat(),
            updated_at=environment.updated_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{project_id}/environments/{env_id}", response_model=SuccessResponse, summary="Delete environment")
async def delete_environment(
    project_id: str,
    env_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Delete an environment."""
    try:
        success = await test_service.remove_environment_from_project(project_id, env_id)
        if not success:
            raise HTTPException(status_code=404, detail="Environment not found")
        
        return SuccessResponse(message="Environment deleted successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# === ENVIRONMENT MANAGEMENT OPERATIONS ===

@router.post("/{project_id}/environments/{env_id}/set-default", response_model=SuccessResponse, summary="Set default environment")
async def set_default_environment(
    project_id: str,
    env_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Set an environment as the default for the project."""
    try:
        success = await test_service.set_default_environment(project_id, env_id)
        if not success:
            raise HTTPException(status_code=404, detail="Environment not found")
        
        return SuccessResponse(message="Default environment set successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}/environments/default", response_model=EnvironmentResponse, summary="Get default environment")
async def get_default_environment(
    project_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Get the default environment for the project."""
    try:
        environment = await test_service.get_default_environment(project_id)
        if not environment:
            raise HTTPException(status_code=404, detail="No default environment found")
        
        return EnvironmentResponse(
            env_id=environment.env_id,
            name=environment.name,
            description=environment.description,
            base_url=environment.base_url,
            is_default=environment.is_default,
            config_overrides=environment.config_overrides,
            tags=environment.tags,
            created_at=environment.created_at.isoformat(),
            updated_at=environment.updated_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# === ENVIRONMENT FILTERING AND SEARCH ===

@router.get("/{project_id}/environments/by-tag/{tag}", response_model=ListResponse, summary="Get environments by tag")
async def get_environments_by_tag(
    project_id: str,
    tag: str,
    test_service: TestService = Depends(get_test_service)
):
    """Get all environments with a specific tag."""
    try:
        environments = await test_service.get_environments_by_tag(project_id, tag)
        
        # Create response items with explicit field mapping
        items = []
        for env in environments:
            env_response = EnvironmentResponse(
                env_id=env.env_id,
                name=env.name,
                description=env.description,
                base_url=env.base_url,
                is_default=env.is_default,
                config_overrides=env.config_overrides,
                tags=env.tags,
                created_at=env.created_at.isoformat(),
                updated_at=env.updated_at.isoformat()
            )
            items.append(env_response.model_dump())
        
        return ListResponse(
            count=len(items),
            items=items
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# === ENVIRONMENT UTILITIES ===

@router.post("/{project_id}/environments/{env_id}/test-url", summary="Test environment URL")
async def test_environment_url(
    project_id: str,
    env_id: str,
    relative_path: str = Query("/", description="Relative path to test"),
    test_service: TestService = Depends(get_test_service)
):
    """Test if an environment URL is accessible."""
    try:
        environment = await test_service.get_project_environment(project_id, env_id)
        if not environment:
            raise HTTPException(status_code=404, detail="Environment not found")
        
        full_url = environment.construct_full_url(relative_path)
        
        # Basic URL accessibility test
        import aiohttp
        import asyncio
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(full_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    accessible = response.status < 400
                    status_code = response.status
            except Exception as e:
                accessible = False
                status_code = None
        
        return {
            "environment_id": env_id,
            "environment_name": environment.name,
            "full_url": full_url,
            "accessible": accessible,
            "status_code": status_code
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}/environments/{env_id}/executions", response_model=ListResponse, summary="Get environment executions")
async def get_environment_executions(
    project_id: str,
    env_id: str,
    limit: int = Query(50, ge=1, le=200, description="Maximum number of executions to return"),
    offset: int = Query(0, ge=0, description="Number of executions to skip"),
    test_service: TestService = Depends(get_test_service)
):
    """Get execution history for a specific environment."""
    try:
        executions = await test_service.get_executions_by_environment(
            project_id, env_id, limit=limit, offset=offset
        )
        
        return ListResponse(
            count=len(executions),
            items=executions
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))