// src/ai/schemas/test-execution-history-schema.ts
'use server';

import {z} from 'zod';

export const TestExecutionStepResultSchema = z.object({
  step: z.number().describe("The step number."),
  content: z.string().describe("A description of what happened at this step or the result."),
  success: z.boolean().optional().describe("Whether this specific step was successful. If not provided, it implies an informational step or success is not applicable."),
});
export type TestExecutionStepResult = z.infer<typeof TestExecutionStepResultSchema>;

export const TestExecutionActionSchema = z.object({
  step: z.number().describe("The step number corresponding to this action."),
  type: z.string().describe("The type of action performed (e.g., 'navigate', 'click', 'type', 'assert', 'verifyText')."),
  details: z.union([z.record(z.string(), z.any()), z.string()]).describe("Details of the action, can be an object with parameters (e.g., {'selector': '#id', 'text': 'value'}) or a descriptive string."),
});
export type TestExecutionAction = z.infer<typeof TestExecutionActionSchema>;

export const TestExecutionElementSchema = z.object({
  step: z.number().describe("The step number where this element was interacted with."),
  tag_name: z.string().describe("The HTML tag name of the element (e.g., 'button', 'input', 'a')."),
  xpath: z.string().describe("The XPath selector for the element, if identifiable."),
  attributes: z.record(z.string()).describe("A key-value map of the element's relevant attributes (e.g., {'id': 'submitBtn', 'text': 'Login'})."),
});
export type TestExecutionElement = z.infer<typeof TestExecutionElementSchema>;

export const TestExecutionUrlSchema = z.object({
  step: z.number().describe("The step number when this URL was visited or interacted with."),
  url: z.string().url().describe("The URL that was visited or is relevant to the step."),
  title: z.string().describe("The title of the page at the visited URL, if applicable."),
});
export type TestExecutionUrl = z.infer<typeof TestExecutionUrlSchema>;

export const TestExecutionMetadataSchema = z.object({
  start_time: z.string().datetime({ message: "Invalid datetime string. Must be in ISO 8601 format." }).nullable().describe("The ISO 8601 timestamp when the test execution started. Null if not applicable."),
  end_time: z.string().datetime({ message: "Invalid datetime string. Must be in ISO 8601 format." }).nullable().describe("The ISO 8601 timestamp when the test execution ended. Null if not applicable."),
  total_steps: z.number().int().positive().describe("The total number of steps defined or executed in the test."),
  success: z.boolean().describe("Overall success status of the test execution (true if all critical steps passed, false otherwise)."),
});
export type TestExecutionMetadata = z.infer<typeof TestExecutionMetadataSchema>;

export const TestExecutionHistoryDataSchema = z.object({
  actions: z.array(TestExecutionActionSchema).describe("A log of actions performed during the test execution."),
  results: z.array(TestExecutionStepResultSchema).describe("The results observed at each step of the test."),
  elements: z.array(TestExecutionElementSchema).describe("Details of UI elements simulated to be interacted with during the test."),
  urls: z.array(TestExecutionUrlSchema).describe("A list of URLs relevant to the test execution."),
  errors: z.array(z.string()).describe("A list of error messages encountered during the test execution. Each error should be a descriptive string."),
  screenshots: z.array(z.string()).describe("For simulated smoke tests, this will be an empty array as no actual screenshots are taken."),
  metadata: TestExecutionMetadataSchema.describe("Metadata about the test execution, including simulated timings and overall status."),
  test_id: z.string().optional().describe("The ID of the test case, if this execution were to be saved."),
  execution_id: z.string().optional().describe("A unique identifier for this specific execution run (e.g., a timestamp)."),
  history_path: z.string().optional().describe("The path where this execution history might be stored, if applicable (not used for playground)."),
  generatedGherkin: z.string().optional().describe("The Gherkin scenario generated and used for this smoke test execution. This should be a complete Gherkin feature string."),
  userStory: z.string().optional().describe("The user story associated with this test execution, used for generating manual test cases.")
});
export type TestExecutionHistoryData = z.infer<typeof TestExecutionHistoryDataSchema>;
