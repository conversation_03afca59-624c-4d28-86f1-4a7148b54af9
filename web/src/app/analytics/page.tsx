"use client";

import { useEffect, useState } from "react";
import {
  getProjects,
  getSuitesByProjectId,
  getAnalytics,
  type AnalyticsResponse,
} from "@/lib/api";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { format, subDays } from "date-fns";

// Helper types
interface DailyExec {
  date: string; // yyyy-MM-dd
  passed: number;
  failed: number;
  total: number;
}

interface FolderStat {
  suiteId: string;
  suiteName: string;
  tests: number;
  runs: number;
  avgDuration: number; // seconds, placeholder for now
  lifetimeSuccessRate: number; // 0-1
  lifetimeTests: number;
  lifetimeRuns: number;
  lifetimePassed: number;
  lifetimeFailed: number;
  runsInDateRange: number;
  lastExecutionAt: string | null;
}

export default function AnalyticsPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [totalTests, setTotalTests] = useState(0);
  const [testsRun, setTestsRun] = useState(0);
  const [passRate, setPassRate] = useState(0);
  const [avgDuration, setAvgDuration] = useState(0);
  const [dailyExecutions, setDailyExecutions] = useState<DailyExec[]>([]);
  const [folderStats, setFolderStats] = useState<FolderStat[]>([]);
  const [testDetails, setTestDetails] = useState<any[]>([]);

  const [projects, setProjects] = useState<{ project_id: string; name: string }[]>([]);
  const [selectedProject, setSelectedProject] = useState<string | undefined>();
  const [suites, setSuites] = useState<{ suite_id: string; name: string }[]>([]);
  const [selectedSuite, setSelectedSuite] = useState<string | undefined>();

  const [dateRange, setDateRange] = useState<{ start: string; end: string }>(() => {
    const end = new Date();
    const start = subDays(end, 29);
    const fmt = (d: Date) => format(d, "yyyy-MM-dd");
    return { start: fmt(start), end: fmt(end) };
  });

  useEffect(() => {
    async function fetchAnalytics() {
      try {
        setLoading(true);

        // fetch projects for dropdown once
        if (projects.length === 0) {
          const projectsRes = await getProjects();
          const list = projectsRes.items?.map((p) => ({ project_id: p.project_id, name: p.name })) || [];
          setProjects(list);
        }

        // fetch suites when project selected
        if (selectedProject && suites.length === 0) {
          const suitesRes = await getSuitesByProjectId(selectedProject);
          const list = suitesRes.items?.map((s) => ({ suite_id: s.suite_id, name: s.name })) || [];
          setSuites(list);
        }

        const analytics: AnalyticsResponse = await getAnalytics({
          startDate: dateRange.start,
          endDate: dateRange.end,
          projectId: selectedProject,
          suiteId: selectedSuite,
          detailed: true,
        });

        setTotalTests(analytics.totalTests);
        setTestsRun(analytics.testsRun);
        setPassRate(analytics.passRate);
        setAvgDuration(analytics.avgDuration);
        setDailyExecutions(
          analytics.dailyExecutions.map((d) => ({
            date: d.date,
            passed: d.passed,
            failed: d.failed,
            total: d.total,
          }))
        );
        setFolderStats(analytics.suiteStats.map((s) => ({
          suiteId: s.suiteId,
          suiteName: s.suiteName,
          tests: s.lifetimeTests, // Use lifetimeTests for tests count
          runs: s.lifetimeRuns, // Use lifetimeRuns for runs count
          avgDuration: 0, // Placeholder
          lifetimeSuccessRate: s.lifetimeSuccessRate,
          lifetimeTests: s.lifetimeTests,
          lifetimeRuns: s.lifetimeRuns,
          lifetimePassed: s.lifetimePassed,
          lifetimeFailed: s.lifetimeFailed,
          runsInDateRange: s.runsInDateRange,
          lastExecutionAt: s.lastExecutionAt,
        })));
        setTestDetails(analytics.testDetails || []);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Error fetching analytics");
      } finally {
        setLoading(false);
      }
    }

    fetchAnalytics();
  }, [selectedProject, selectedSuite, dateRange]);

  // Handler change
  const handleProjectChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedProject(e.target.value || undefined);
    setSuites([]);
    setSelectedSuite(undefined);
  };

  const handleSuiteChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedSuite(e.target.value || undefined);
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="page-header">Analytics</h1>

      {/* Filters */}
      <div className="flex gap-4 mb-4 items-end flex-wrap">
        <div>
          <label className="block text-sm mb-1">Project</label>
          <select
            value={selectedProject ?? ""}
            onChange={handleProjectChange}
            className="border rounded px-2 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          >
            <option value="">All</option>
            {projects.map((p) => (
              <option key={p.project_id} value={p.project_id}>
                {p.name}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label className="block text-sm mb-1">Suite</label>
          <select
            value={selectedSuite ?? ""}
            onChange={handleSuiteChange}
            className="border rounded px-2 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 disabled:opacity-50"
            disabled={!selectedProject}
          >
            <option value="">All</option>
            {suites.map((s) => (
              <option key={s.suite_id} value={s.suite_id}>
                {s.name}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label className="block text-sm mb-1">Start Date</label>
          <input
            type="date"
            value={dateRange.start}
            onChange={(e) => setDateRange((prev) => ({ ...prev, start: e.target.value }))}
            className="border rounded px-2 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          />
        </div>
        <div>
          <label className="block text-sm mb-1">End Date</label>
          <input
            type="date"
            value={dateRange.end}
            max={format(new Date(), "yyyy-MM-dd")}
            onChange={(e) => setDateRange((prev) => ({ ...prev, end: e.target.value }))}
            className="border rounded px-2 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          />
        </div>
      </div>

      {/* Top Stats */}
      <div className="grid gap-6 md:grid-cols-4">
        <StatCard
          title="Total Tests"
          value={totalTests}
          loading={loading}
        />
        <StatCard title="Tests Run" value={testsRun} loading={loading} />
        <StatCard
          title="Pass Rate"
          value={`${(passRate * 100).toFixed(0)}%`}
          loading={loading}
          valueClassName={passRate >= 0.8 ? "text-green-600" : "text-yellow-600"}
        />
        <StatCard
          title="Avg. Test Duration"
          value={`${avgDuration.toFixed(1)}s`}
          loading={loading}
        />
      </div>

      {/* Daily Executions Chart and Folder table */}
      <div className="grid gap-6 lg:grid-cols-2 mt-6">
        <Card className="h-full">
          <CardHeader>
            <CardTitle>Daily Test Executions (last 30 days)</CardTitle>
            <CardDescription>
              Stacked bar chart showing total executions per day, split between
              passed and failed.
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[350px]">
            {loading ? (
              <Skeleton className="h-full w-full" />
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={dailyExecutions} stackOffset="sign">
                  <XAxis dataKey="date" fontSize={10} tickLine={false} />
                  <YAxis allowDecimals={false} fontSize={10} tickLine={false} />
                  <Tooltip />
                  <Bar dataKey="passed" stackId="a" fill="#4ade80" />
                  <Bar dataKey="failed" stackId="a" fill="#f87171" />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        {/* Folder success table */}
        <Card>
          <CardHeader>
            <CardTitle>Test Success by Suite</CardTitle>
            <CardDescription>
              Click on a suite to filter tests below. Shows lifetime stats for each suite.
            </CardDescription>
          </CardHeader>
          <CardContent className="overflow-auto">
            {loading ? (
              <Skeleton className="h-64 w-full" />
            ) : (
              <table className="min-w-full text-sm">
                <thead className="border-b">
                  <tr>
                    <th className="text-left py-2 px-3">Suite</th>
                    <th className="text-center py-2 px-3">Total Tests</th>
                    <th className="text-center py-2 px-3">Runs (in range)</th>
                    <th className="text-center py-2 px-3">Passed</th>
                    <th className="text-center py-2 px-3">Failed</th>
                    <th className="text-center py-2 px-3">Success Rate</th>
                    <th className="text-left py-2 px-3">Last Executed</th>
                  </tr>
                </thead>
                <tbody>
                  {folderStats.map((s) => (
                    <tr
                      key={s.suiteId}
                      className={`border-b last:border-0 hover:bg-muted/50 cursor-pointer ${selectedSuite === s.suiteId ? 'bg-muted/50' : ''}`}
                      onClick={() => {
                        setSelectedSuite(selectedSuite === s.suiteId ? undefined : s.suiteId);
                      }}
                    >
                      <td className="py-2 px-3">{s.suiteName}</td>
                      <td className="text-center py-2 px-3">{s.lifetimeTests}</td>
                      <td className="text-center py-2 px-3">{s.runsInDateRange}</td>
                      <td className="text-center py-2 px-3 text-green-400">{s.lifetimePassed}</td>
                      <td className="text-center py-2 px-3 text-red-400">{s.lifetimeFailed}</td>
                      <td className="text-center py-2 px-3">
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            s.lifetimeSuccessRate >= 0.8
                              ? "bg-green-900 text-green-300"
                              : s.lifetimeSuccessRate >= 0.5
                              ? "bg-yellow-900 text-yellow-300"
                              : "bg-red-900 text-red-300"
                          }`}
                        >
                          {(s.lifetimeSuccessRate * 100).toFixed(0)}%
                        </span>
                      </td>
                      <td className="py-2 px-3 whitespace-nowrap">
                        {s.lastExecutionAt ? new Date(s.lastExecutionAt).toLocaleString() : 'N/A'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Test details */}
      {testDetails.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Test Details</CardTitle>
          </CardHeader>
          <CardContent className="overflow-auto">
            <table className="min-w-full text-sm">
              <thead className="border-b">
                <tr>
                  <th className="text-left py-2 px-3">Test</th>
                  <th className="text-left py-2 px-3">Suite</th>
                  <th className="text-left py-2 px-3">Status</th>
                  <th className="text-left py-2 px-3">Last Exec</th>
                </tr>
              </thead>
              <tbody>
                {testDetails.map((t, idx) => (
                  <tr key={`${t.testId}-${t.suiteId}-${idx}`} className="border-b last:border-0">
                    <td className="py-2 px-3">{t.testName}</td>
                    <td className="py-2 px-3">{t.suiteName}</td>
                    <td className="py-2 px-3">
                      <span className={
                        t.status === 'Passed' ? 'text-green-500' : t.status === 'Failed' ? 'text-red-500' : 'text-yellow-500'
                      }>
                        {t.status}
                      </span>
                    </td>
                    <td className="py-2 px-3">{t.lastExecution ? new Date(t.lastExecution).toLocaleString() : '-'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function StatCard({
  title,
  value,
  loading,
  valueClassName,
}: {
  title: string;
  value: string | number;
  loading: boolean;
  valueClassName?: string;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <Skeleton className="h-8 w-full" />
        ) : (
          <p className={`text-2xl font-bold ${valueClassName ?? ""}`}>{value}</p>
        )}
      </CardContent>
    </Card>
  );
} 