"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Edit3, 
  Search, 
  Plus, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  Download,
  Upload,
  Info,
  Clock,
  Tag,
  Eye,
  HelpCircle,
  Languages
} from 'lucide-react';
import Link from 'next/link';
import { fetchPrompts, validateAllPrompts } from '@/lib/api';
import { SplitScreenPromptEditor } from '@/components/prompts/SplitScreenPromptEditor';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface PromptMetadata {
  title: string;
  description: string;
  version: string;
  lastModified: string;
  tags?: string[];
  author?: string;
  // Additional fields from API
  id?: string;
  name?: string;
  displayName?: string;
  file?: string;
  languages?: string[];
  variables?: string[];
  outputs?: string[];
  examples?: any[];
}

interface PromptItem {
  id: string;
  metadata: PromptMetadata;
  filePath: string;
  isValid?: boolean;
  validationErrors?: string[];
}

interface PromptCategories {
  [category: string]: PromptItem[];
}

interface PromptListResponse {
  categories: PromptCategories;
  total_prompts: number;
}

export default function PromptsPage() {
  const [prompts, setPrompts] = useState<PromptCategories>({});
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [validationStatus, setValidationStatus] = useState<Record<string, boolean>>({});
  const [showSplitScreenEditor, setShowSplitScreenEditor] = useState(false);

  useEffect(() => {
    loadPrompts();
  }, []);

  const loadPrompts = async () => {
    try {
      setLoading(true);
      const data: PromptListResponse = await fetchPrompts();
      setPrompts(data.categories);
      
      // Load validation status for all prompts
      await loadValidationStatus();
    } catch (error) {
      console.error('Error loading prompts:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadValidationStatus = async () => {
    try {
      const validation = await validateAllPrompts();
      const status: Record<string, boolean> = {};
      
      // The validation response is a direct object with categories
      if (validation && typeof validation === 'object') {
        Object.entries(validation).forEach(([category, categoryResult]: [string, any]) => {
          if (categoryResult && categoryResult.prompts) {
            Object.entries(categoryResult.prompts).forEach(([promptId, promptResult]: [string, any]) => {
              const key = `${category}/${promptId}`;
              status[key] = promptResult?.valid === true;
            });
          }
        });
      }
      
      setValidationStatus(status);
    } catch (error) {
      console.error('Error loading validation status:', error);
      setValidationStatus({});
    }
  };

  const getValidationIcon = (isValid: boolean | undefined) => {
    if (isValid === true) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <CheckCircle className="h-5 w-5 text-green-500" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Prompt is valid and ready to use</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    if (isValid === false) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <XCircle className="h-5 w-5 text-red-500" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Prompt has validation errors</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger>
            <AlertCircle className="h-5 w-5 text-amber-500" />
          </TooltipTrigger>
          <TooltipContent>
            <p>Validation status unknown</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  const getFilteredPrompts = () => {
    let allPrompts: Array<PromptItem & { category: string }> = [];
    
    Object.entries(prompts).forEach(([category, items]) => {
      if (selectedCategory === 'all' || selectedCategory === category) {
        allPrompts.push(...items.map(item => ({ ...item, category })));
      }
    });

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      allPrompts = allPrompts.filter(prompt => 
        (prompt.metadata?.title && prompt.metadata.title.toLowerCase().includes(term)) ||
        (prompt.metadata?.description && prompt.metadata.description.toLowerCase().includes(term)) ||
        (prompt.metadata?.tags && prompt.metadata.tags.some(tag => tag.toLowerCase().includes(term)))
      );
    }

    return allPrompts;
  };

  const getCategoryStats = (category: string) => {
    const items = prompts[category] || [];
    const validCount = items.filter(item => validationStatus[`${category}/${item.id}`]).length;
    return { total: items.length, valid: validCount };
  };

  const getCategoryDescription = (category: string): string => {
    const descriptions: Record<string, string> = {
      'test-cases': 'Prompts for generating and converting test cases, including Gherkin scenarios',
      'code-generation': 'Prompts for generating automated test code in different frameworks',
      'user-story': 'Prompts for enhancing and processing user stories',
      'test-analysis': 'Prompts for analyzing and summarizing test results',
      'browser-automation': 'Prompts for browser automation and task generation'
    };
    return descriptions[category] || 'Custom prompt category';
  };

  const categories = Object.keys(prompts);
  const filteredPrompts = getFilteredPrompts();

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h1 className="text-3xl font-bold text-foreground">Prompt Management</h1>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-5 w-5 text-muted-foreground hover:text-foreground" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-sm">
                    <p>Manage AI prompts used for test generation, code creation, and automation. 
                    Each prompt can be edited, validated, and customized for different languages.</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <p className="text-muted-foreground font-medium">
                Manage and edit your AI prompts for test generation and automation
              </p>
              <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                <span className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  {Object.values(prompts).flat().length} prompts total
                </span>
                <span className="flex items-center gap-1">
                  <CheckCircle className="h-4 w-4 text-green-400" />
                  {Object.entries(validationStatus).filter(([, valid]) => valid).length} validated
                </span>
              </div>
            </div>
            <div className="flex gap-2 mt-4 md:mt-0">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export All
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Export all prompts as a backup file</p>
                </TooltipContent>
              </Tooltip>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Import
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Import prompts from a backup file</p>
                </TooltipContent>
              </Tooltip>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => setShowSplitScreenEditor(true)}
                    className="border-green-500/50 text-green-700 hover:bg-green-50 hover:border-green-500"
                  >
                    <Languages className="h-4 w-4 mr-2" />
                    Spanish Editor
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Write prompts in Spanish with automatic English translation</p>
                </TooltipContent>
              </Tooltip>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button size="sm" className="bg-primary hover:bg-primary/90">
                    <Plus className="h-4 w-4 mr-2" />
                    New Prompt
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Create a new custom prompt</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search prompts by title, description, or tags..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Category Tabs */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="mb-6">
        <TabsList className="grid w-full grid-cols-auto overflow-x-auto bg-muted p-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <TabsTrigger value="all" className="text-sm font-medium">
                  All ({filteredPrompts.length})
                </TabsTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>Show all prompts from every category</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          {categories.map(category => {
            const stats = getCategoryStats(category);
            const categoryDisplayName = category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            const categoryDescription = getCategoryDescription(category);
            
            return (
              <TooltipProvider key={category}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value={category} className="text-sm font-medium">
                      {categoryDisplayName}
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {stats.valid}/{stats.total}
                      </Badge>
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="font-semibold">{categoryDisplayName}</p>
                    <p className="text-sm">{categoryDescription}</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {stats.valid} of {stats.total} prompts validated
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            );
          })}
        </TabsList>
      </Tabs>

      {/* Prompts Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredPrompts.map((prompt) => {
          const validationKey = `${prompt.category}/${prompt.id}`;
          const isValid = validationStatus[validationKey];
          
          return (
            <Card key={`${prompt.category}/${prompt.id}`} className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-primary">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <CardTitle className="text-lg">{prompt.metadata?.title || prompt.id}</CardTitle>
                      {getValidationIcon(isValid)}
                    </div>
                    <CardDescription className="text-sm font-medium">
                      {prompt.metadata?.description || 'No description available'}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  {/* Category & Version */}
                  <div className="flex items-center justify-between text-sm">
                    <Badge variant="outline" className="text-xs font-semibold">
                      {prompt.category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                    <div className="flex items-center gap-1 text-muted-foreground">
                      <Info className="h-3 w-3" />
                      <span className="font-medium">v{prompt.metadata?.version || '1.0'}</span>
                    </div>
                  </div>

                  {/* Tags */}
                  {prompt.metadata?.tags && prompt.metadata.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Tag className="h-3 w-3" />
                              <span>Tags:</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Keywords that help categorize this prompt</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      {prompt.metadata.tags.slice(0, 3).map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {prompt.metadata.tags.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{prompt.metadata.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}

                  {/* Last Modified */}
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span className="font-medium">Modified:</span>
                    <span>{prompt.metadata?.lastModified 
                      ? new Date(prompt.metadata.lastModified).toLocaleDateString() 
                      : 'Unknown'}</span>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Link href={`/prompts/${prompt.category}/${prompt.id}`} className="flex-1">
                            <Button variant="outline" size="sm" className="w-full">
                              <Edit3 className="h-4 w-4 mr-2" />
                              Edit
                            </Button>
                          </Link>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Edit this prompt's content and settings</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Link href={`/prompts/${prompt.category}/${prompt.id}/preview`}>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Preview this prompt</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredPrompts.length === 0 && (
        <div className="text-center py-12 bg-muted/50 rounded-lg border-2 border-dashed border-muted">
          <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">
            {searchTerm ? 'No matching prompts found' : 'No prompts available'}
          </h3>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            {searchTerm 
              ? `No prompts match your search "${searchTerm}". Try adjusting your search terms or browse all prompts.` 
              : "Get started by creating your first prompt or importing existing ones to begin managing your AI automation templates."
            }
          </p>
          
          {!searchTerm && (
            <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button className="bg-primary hover:bg-primary/90">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Your First Prompt
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Start by creating a custom prompt for your use case</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline">
                      <Upload className="h-4 w-4 mr-2" />
                      Import Prompts
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Import prompts from a backup or template file</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
          
          {searchTerm && (
            <Button variant="outline" onClick={() => setSearchTerm('')}>
              <Search className="h-4 w-4 mr-2" />
              Clear Search
            </Button>
          )}
        </div>
      )}
      </div>

      {/* Split-Screen Prompt Editor Modal */}
      {showSplitScreenEditor && (
        <SplitScreenPromptEditor
          onClose={() => setShowSplitScreenEditor(false)}
          onSave={(promptData) => {
            console.log('Prompt saved:', promptData);
            setShowSplitScreenEditor(false);
            // Refresh prompts list after saving
            loadPrompts();
          }}
        />
      )}
    </div>
    </TooltipProvider>
  );
}
