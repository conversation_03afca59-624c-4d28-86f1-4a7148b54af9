"use client";

import React from 'react';
import { Play, Square, CheckCircle, XCircle, Clock, Monitor } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';

import type { CodegenSessionInfo } from '@/lib/types';

interface SessionsListProps {
  sessions: Array<{
    session_id: string;
    status: string;
    target_language: string;
    url?: string;
    created_at: string;
    updated_at: string;
  }>;
  isLoading: boolean;
  selectedSessionId: string | null;
  onSelectSession: (sessionId: string) => void;
  onStopSession: (sessionId: string) => void;
  isStoppingSession: boolean;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'starting':
      return <Clock className="h-4 w-4" />;
    case 'running':
      return <Play className="h-4 w-4" />;
    case 'completed':
      return <CheckCircle className="h-4 w-4" />;
    case 'failed':
      return <XCircle className="h-4 w-4" />;
    case 'stopped':
      return <Square className="h-4 w-4" />;
    default:
      return <Monitor className="h-4 w-4" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'starting':
      return 'secondary';
    case 'running':
      return 'default';
    case 'completed':
      return 'success';
    case 'failed':
      return 'destructive';
    case 'stopped':
      return 'outline';
    default:
      return 'secondary';
  }
};

const getLanguageColor = (language: string) => {
  switch (language) {
    case 'javascript':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    case 'typescript':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'python':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'java':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
    case 'csharp':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
};

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

export function SessionsList({
  sessions,
  isLoading,
  selectedSessionId,
  onSelectSession,
  onStopSession,
  isStoppingSession
}: SessionsListProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Active Sessions</CardTitle>
          <CardDescription>Loading sessions...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded-lg" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[100px]" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Monitor className="h-5 w-5" />
          Recording Sessions
        </CardTitle>
        <CardDescription>
          {sessions.length} session{sessions.length !== 1 ? 's' : ''} available
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[600px] pr-4">
          {sessions.length === 0 ? (
            <div className="flex items-center justify-center h-32 text-muted-foreground">
              <div className="text-center">
                <Monitor className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No recording sessions</p>
                <p className="text-sm">Start a new session to begin interactive recording</p>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {sessions.map((session) => (
                <Card 
                  key={session.session_id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedSessionId === session.session_id 
                      ? 'ring-2 ring-primary bg-muted/50' 
                      : ''
                  }`}
                  onClick={() => onSelectSession(session.session_id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 flex-1">
                        <div className="flex-shrink-0">
                          {getStatusIcon(session.status)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge 
                              variant={getStatusColor(session.status) as any}
                              className="text-xs"
                            >
                              {session.status}
                            </Badge>
                            <Badge 
                              className={`text-xs ${getLanguageColor(session.target_language)}`}
                              variant="outline"
                            >
                              {session.target_language}
                            </Badge>
                          </div>
                          <p className="text-sm font-medium truncate">
                            Session {session.session_id.slice(0, 8)}...
                          </p>
                          {session.url && (
                            <p className="text-xs text-muted-foreground truncate">
                              {session.url}
                            </p>
                          )}
                          <p className="text-xs text-muted-foreground">
                            Started: {formatTime(session.created_at)}
                          </p>
                        </div>
                      </div>
                      
                      {session.status === 'running' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onStopSession(session.session_id);
                          }}
                          disabled={isStoppingSession}
                          className="ml-2"
                        >
                          <Square className="h-3 w-3 mr-1" />
                          Stop
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
