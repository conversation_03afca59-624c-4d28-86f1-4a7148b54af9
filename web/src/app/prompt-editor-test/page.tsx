"use client";

import { PromptEditor } from "@/components/prompts/PromptEditor";

export default function PromptEditorTestPage() {
  const handleSave = (spanishText: string, englishText: string) => {
    console.log('Saving prompt:');
    console.log('Spanish:', spanishText);
    console.log('English:', englishText);
    
    // Here you would typically save to your backend
    alert('Prompt saved successfully! Check console for details.');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Prompt Editor Test</h1>
        <p className="text-muted-foreground">
          Test the new Spanish→English prompt editor with AI translation.
        </p>
      </div>
      
      <PromptEditor
        promptType="test-automation"
        onSave={handleSave}
        initialSpanishText="Eres un asistente de automatización de pruebas..."
        initialEnglishText=""
      />
    </div>
  );
}
