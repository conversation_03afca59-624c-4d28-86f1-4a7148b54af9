"use client";

import { useEffect, useState, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  executeTest,
  getTestExecutionHistoryDetails,
  getTestCaseById,
  getExecutionById,
  pauseExecution,
  resumeExecution,
  stopExecution
} from "@/lib/api";
import {
  TestExecutionHistoryData,
  ExecutionResponse as ExecutionResponseV2,
  ExecutionRequest,
  StandardResult,
  ExecutionStatus
} from "@/lib/types";
import { ExecutionTypeEnum } from "@/lib/types";
import { ExecutionDetailsView } from "@/components/execution/ExecutionDetailsView";
import { UnifiedResultsViewer } from "@/components/execution/UnifiedResultsViewer";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Al<PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ArrowLeft, AlertCircle, RefreshCw, Play } from "lucide-react"; // Added Play icon
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"; // Added Card components

function ExecutionLoadingSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-10 w-1/3" /> {/* Header */}
      <Skeleton className="h-12 w-full rounded-md" /> {/* Tabs List */}
      <div className="mt-4 p-4 rounded-lg border bg-card">
        <Skeleton className="h-6 w-1/4 mb-4" /> {/* Tab Title */}
        <div className="space-y-4">
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    </div>
  );
}

export default function ExecuteTestPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const queryClient = useQueryClient();

  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;
  const testId = params.testId as string;
  
  const historyPath = searchParams.get("historyPath");
  const executionIdFromQuery = searchParams.get("executionId");

  const [currentExecutionId, setCurrentExecutionId] = useState<string | null>(executionIdFromQuery);
  const [currentExecutionStatus, setCurrentExecutionStatus] = useState<ExecutionStatus | null>(null);
  const [executionResult, setExecutionResult] = useState<StandardResult | null>(null);
  
  const isExecutionActive = currentExecutionStatus === ExecutionStatus.RUNNING || currentExecutionStatus === ExecutionStatus.PENDING;

  // Check if AI analysis is in progress or if we have a job ID but no completed analysis yet
  const isAIAnalysisInProgress = (executionResult?.metadata?.ai_analysis_status === "processing" ||
                                 (executionResult?.metadata?.ai_analysis_job_id && !executionResult?.metadata?.ai_analysis?.completion_analysis && !executionResult?.metadata?.ai_analysis?.summary));

  // Polling for live execution status
  const {
    data: liveExecutionData,
    isLoading: isLoadingExecution,
    error: executionError,
    isError: isExecutionError,
    refetch: refetchExecution
  } = useQuery({
    queryKey: ['executionStatus', currentExecutionId],
    queryFn: () => getExecutionById(currentExecutionId!),
    enabled: !!currentExecutionId && !historyPath,
    refetchInterval: (isExecutionActive || isAIAnalysisInProgress) ? 2000 : false, // Poll every 2 seconds if active or AI analysis in progress
  });

  useEffect(() => {
    if (liveExecutionData) {
      setCurrentExecutionStatus(liveExecutionData.status as ExecutionStatus);
      if (liveExecutionData.result) {
        setExecutionResult(liveExecutionData.result);
      }
      if (liveExecutionData.status !== ExecutionStatus.RUNNING && liveExecutionData.status !== ExecutionStatus.PENDING) {
        // Stop polling once execution is finished
        queryClient.cancelQueries({ queryKey: ['executionStatus', currentExecutionId] });
      }
    }
  }, [liveExecutionData, queryClient, currentExecutionId]);

  // Fetching specific history file if historyPath is provided
  const { 
    data: fetchedHistoryData, 
    isLoading: isLoadingHistoryFile, 
    error: historyFileError,
    isError: isHistoryFileError,
    refetch: refetchHistoryFile
  } = useQuery({
    queryKey: ['testExecutionHistory', historyPath],
    queryFn: () => getTestExecutionHistoryDetails(historyPath!),
    enabled: !!historyPath,
    retry: false,
  });

  // Fetch test case details for context (e.g. name)
  const {data: testCaseDetails} = useQuery({
    queryKey: ['testCase', projectId, suiteId, testId],
    queryFn: () => getTestCaseById(projectId, suiteId, testId),
    enabled: !!projectId && !!suiteId && !!testId,
  });
  
  const handlePause = async () => {
    if (!currentExecutionId) return;
    try {
      await pauseExecution(currentExecutionId);
      setCurrentExecutionStatus(ExecutionStatus.PAUSED);
      refetchExecution(); // Refetch to confirm status change
    } catch (error) {
      console.error("Failed to pause execution", error);
    }
  };

  const handleResume = async () => {
    if (!currentExecutionId) return;
    try {
      await resumeExecution(currentExecutionId);
      setCurrentExecutionStatus(ExecutionStatus.RUNNING);
      refetchExecution(); // Refetch to confirm status change
    } catch (error) {
      console.error("Failed to resume execution", error);
    }
  };

  const handleStop = async () => {
    if (!currentExecutionId) return;
    try {
      await stopExecution(currentExecutionId);
      setCurrentExecutionStatus(ExecutionStatus.CANCELLED);
      refetchExecution(); // Refetch to confirm status change
    } catch (error) {
      console.error("Failed to cancel execution", error);
    }
  };

  const displayData = historyPath ? fetchedHistoryData : executionResult;
  const isLoading = (!!currentExecutionId && isLoadingExecution) || (!!historyPath && isLoadingHistoryFile);
  const displayError = isExecutionError ? executionError?.message : (isHistoryFileError ? historyFileError?.message : null);

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Test Case
          </Link>
        </Button>
        {currentExecutionId && !historyPath && (
          <div className="flex items-center gap-2">
            {currentExecutionStatus === ExecutionStatus.RUNNING && (
              <Button onClick={handlePause} variant="outline">Pause</Button>
            )}
            {currentExecutionStatus === ExecutionStatus.PAUSED && (
              <Button onClick={handleResume} variant="outline">Resume</Button>
            )}
            {(currentExecutionStatus === ExecutionStatus.RUNNING || currentExecutionStatus === ExecutionStatus.PAUSED) && (
              <Button onClick={handleStop} variant="destructive">Cancel</Button>
            )}
            <Button onClick={() => refetchExecution()} disabled={isLoadingExecution || !isExecutionActive}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoadingExecution ? 'animate-spin' : ''}`} />
              Refresh Status
            </Button>
          </div>
        )}
      </div>

      {testCaseDetails && <h2 className="text-2xl font-semibold mb-1">Test: {testCaseDetails.name}</h2>}
      
      {isLoading && <ExecutionLoadingSkeleton />}
      
      {displayError && (
        <Alert variant="destructive" className="my-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error Displaying Execution Details</AlertTitle>
          <AlertDescription>{displayError}</AlertDescription>
           {historyPath && isHistoryFileError && (
            <Button onClick={() => refetchHistoryFile()} variant="outline" size="sm" className="mt-2">
              <RefreshCw className="mr-2 h-4 w-4"/> Retry Fetching History
            </Button>
          )}
        </Alert>
      )}

      {!isLoading && !displayError && !displayData && (
        <Card className="my-4">
          <CardHeader>
            <CardTitle>
              {currentExecutionId ? "Waiting for execution data..." : "No Execution Data"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              {currentExecutionId 
                ? `The execution (${currentExecutionId}) is pending or in progress. Status updates will appear here automatically.`
                : "No historical execution was selected, and no live execution is in progress."
              }
            </p>
          </CardContent>
        </Card>
      )}

      {!isLoading && !displayError && displayData && (
        <UnifiedResultsViewer
          data={displayData as StandardResult}
          onAnalysisComplete={() => {
            console.log('AI Analysis completed, refreshing execution data...');
            refetchExecution();
          }}
        />
      )}
    </div>
  );
}
