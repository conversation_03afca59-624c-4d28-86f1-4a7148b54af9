"use client";

import { usePara<PERSON>, useRouter } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getTestCaseById, updateTestCase } from "@/lib/api";
import type { TestCaseUpdateInput } from "@/lib/types";
import { TestCaseForm } from "@/components/forms/TestCaseForm";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

export default function EditTestCasePage() {
  const router = useRouter();
  const params = useParams();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;
  const testId = params.testId as string;

  const { data: testCase, isLoading, error, isError } = useQuery({
    queryKey: ['testCase', projectId, suiteId, testId],
    queryFn: () => getTestCaseById(projectId, suiteId, testId),
    enabled: !!projectId && !!suiteId && !!testId,
  });

  const mutation = useMutation({
    mutationFn: (updatedTestCase: TestCaseUpdateInput) => updateTestCase(projectId, suiteId, testId, updatedTestCase),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['testCase', projectId, suiteId, testId] });
      queryClient.invalidateQueries({ queryKey: ['testCases', projectId, suiteId] });
      toast({
        title: "Test Case Updated",
        description: `Test Case "${data.name}" has been successfully updated.`,
      });
      router.push(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`);
    },
    onError: (error) => {
      toast({
        title: "Error Updating Test Case",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (data: TestCaseUpdateInput) => {
    await mutation.mutateAsync(data);
  };

  if (isLoading) {
    return (
      <div>
        <Skeleton className="h-9 w-40 mb-4" /> {/* Back button */}
        <Skeleton className="h-10 w-1/2 mb-6" /> {/* Page header */}
        <div className="space-y-6">
          <Skeleton className="h-32 w-full" /> {/* Form skeleton */}
          <Skeleton className="h-20 w-full" />
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div>
        <Button variant="outline" size="sm" asChild className="mb-4">
          <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Test Case
          </Link>
        </Button>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error loading test case</AlertTitle>
          <AlertDescription>{error?.message || 'An unknown error occurred.'}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!testCase) {
    return (
      <div>
        <Button variant="outline" size="sm" asChild className="mb-4">
          <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Test Case
          </Link>
        </Button>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Test Case not found</AlertTitle>
          <AlertDescription>The requested test case could not be found.</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div>
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}`}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Test Case
        </Link>
      </Button>
      <h1 className="page-header">Edit Test Case</h1>
      <TestCaseForm 
        testCase={testCase}
        onSubmit={handleSubmit} 
        isSubmitting={mutation.isPending}
        submitButtonText="Update Test Case"
      />
    </div>
  );
} 