"use client";

import { usePara<PERSON>, useRouter } from "next/navigation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createTestCase } from "@/lib/api";
import type { TestCaseCreateInput, TestCaseUpdateInput } from "@/lib/types";
import { TestCaseForm } from "@/components/forms/TestCaseForm";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function CreateTestCasePage() {
  const router = useRouter();
  const params = useParams();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;

  const mutation = useMutation({
    mutationFn: (newTestCase: TestCaseCreateInput) => createTestCase(projectId, suiteId, newTestCase),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['testCases', projectId, suiteId] });
      queryClient.invalidateQueries({ queryKey: ['suite', projectId, suiteId] }); // Also invalidate suite details 
      toast({
        title: "Test Case Created",
        description: `Test Case "${data.name}" has been successfully created.`,
      });
      router.push(`/projects/${projectId}/suites/${suiteId}/tests/${data.test_id}`);
    },
    onError: (error) => {
      toast({
        title: "Error Creating Test Case",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (data: TestCaseCreateInput) => {
    await mutation.mutateAsync(data);
  };

  return (
    <div>
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href={`/projects/${projectId}/suites/${suiteId}`}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Suite
        </Link>
      </Button>
      <h1 className="page-header">Create New Test Case</h1>
      <TestCaseForm 
        onSubmit={handleSubmit as (data: TestCaseCreateInput | TestCaseUpdateInput) => Promise<void>} 
        isSubmitting={mutation.isPending}
        submitButtonText="Create Test Case"
      />
    </div>
  );
}
