"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

export default function TestCaseDetailLoading() {
 return (
    <div>
      <Skeleton className="h-9 w-40 mb-4" /> {/* Back button */}
      <div className="flex flex-col md:flex-row justify-between md:items-start mb-4">
        <div className="flex-1">
          <Skeleton className="h-10 w-3/4 mb-1" /> {/* TC Name */}
          <Skeleton className="h-5 w-full mb-3" /> {/* TC Description */}
          <div className="flex flex-wrap gap-2 mb-2">
            <Skeleton className="h-6 w-20 rounded-full" />
            <Skeleton className="h-6 w-16 rounded-full" />
          </div>
          <Skeleton className="h-4 w-72 mb-1" /> {/* Dates */}
          <div className="mt-3 flex items-center gap-2">
            <Skeleton className="h-4 w-16" /> {/* Status Label */}
            <Skeleton className="h-8 w-36 rounded-md" /> {/* Status Select */}
          </div>
        </div>
         <div className="flex flex-col items-start md:items-end gap-2 mt-4 md:mt-0">
          <div className="flex gap-2">
            <Skeleton className="h-9 w-24" /> {/* Edit button */}
            <Skeleton className="h-9 w-28" /> {/* Delete button */}
          </div>
          <Skeleton className="h-9 w-40" /> {/* Execute Test Case button */}
        </div>
      </div>
      
      <div className="grid md:grid-cols-2 gap-6 mt-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center gap-2 pb-2">
              <Skeleton className="h-5 w-5 rounded-full" />
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-20 w-full bg-muted p-3 rounded-md" />
            </CardContent>
          </Card>
        ))}
      </div>

      <Separator className="my-8" />

      <div>
        <Skeleton className="h-8 w-48 mb-4" /> {/* History Header */}
        <Card>
            <CardContent className="p-6">
                <Skeleton className="h-6 w-full mb-2" />
                <Skeleton className="h-6 w-5/6" />
            </CardContent>
        </Card>
      </div>
    </div>
  );
}
