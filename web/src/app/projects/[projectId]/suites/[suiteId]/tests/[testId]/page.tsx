"use client";

import Link from 'next/link';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getTestCaseById, deleteTestCase, executeTest, updateTestCaseStatus, pauseExecution, resumeExecution, stopExecution, getExecutionById, getTestMongoExecutions, getProjectEnvironments, deleteExecution } from '@/lib/api';
import { TestCaseStatus, ExecutionResponse as ExecutionResponseV2, ExecutionRequest, ExecutionTypeEnum, ExecutionStatus, TestCaseRequest, TestCase, StandardResult, Environment } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { TestTube2, ArrowLeft, Edit, Trash2, Play, AlertCircle, CheckCircle, XCircle, Clock, FileText, History, Calendar, Timer, ChevronRight, Loader2, Sparkles, Brain, Lightbulb, Tag, Filter, SortDesc, Code2, Download, Edit3, Globe } from 'lucide-react';
import { format } from 'date-fns';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import React, { useState, useEffect } from 'react';
import { EnvironmentSelector } from '@/components/environments/EnvironmentSelector';

// Define a type for the test case step for clarity
interface TestCaseStep {
  success: boolean;
  // Add other properties of a step if available
}

function TestCaseDetailSkeleton() {
  return (
    <div>
      <Skeleton className="h-9 w-40 mb-4" /> {/* Back button */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <Skeleton className="h-10 w-3/4 mb-1" /> {/* TC Name */}
          <Skeleton className="h-5 w-full mb-3" /> {/* TC Description */}
          <div className="flex flex-wrap gap-2 mb-2">
            <Skeleton className="h-6 w-20 rounded-full" />
          </div>
          <Skeleton className="h-4 w-72 mb-1" /> {/* Dates */}
          <Skeleton className="h-6 w-24 rounded-full mt-2" /> {/* Status Badge */}
        </div>
        <div className="flex flex-col items-end gap-2">
            <div className="flex gap-2">
                <Skeleton className="h-9 w-24" /> {/* Edit button */}
                <Skeleton className="h-9 w-28" /> {/* Delete button */}
            </div>
            <Skeleton className="h-9 w-40" /> {/* Execute Test Case button */}
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6 mt-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader><Skeleton className="h-6 w-1/2" /></CardHeader>
            <CardContent><Skeleton className="h-20 w-full" /></CardContent>
          </Card>
        ))}
      </div>
      <Separator className="my-8" />
      <Skeleton className="h-8 w-48 mb-4" /> {/* History Header */}
      <Card><CardContent className="p-6"><Skeleton className="h-10 w-full" /></CardContent></Card>
    </div>
  );
}

// Helper function to extract execution details from filename
function parseExecutionFileName(filename: string) {
  // Extract the filename without path
  const name = filename.split('/').pop() || filename;
  
  // Try to extract timestamp from filename patterns like:
  // smoke_test_20240115143022 or execution_20240115_143022
  const timestampMatch = name.match(/(\d{4})(\d{2})(\d{2})[\-_]?(\d{2})(\d{2})(\d{2})/);
  
  if (timestampMatch) {
    const [, year, month, day, hour, minute, second] = timestampMatch;
    try {
      const date = new Date(
        parseInt(year),
        parseInt(month) - 1, // Month is 0-indexed
        parseInt(day),
        parseInt(hour),
        parseInt(minute),
        parseInt(second)
      );
      return {
        date,
        displayName: `Execution ${format(date, 'MMM d, yyyy HH:mm:ss')}`,
        isValid: !isNaN(date.getTime())
      };
    } catch (e) {
      // If date parsing fails, fall back to original name
    }
  }
  
  // Fallback: try to make the filename more readable
  const cleanName = name
    .replace(/\.json$/, '')
    .replace(/[_\-]/g, ' ')
    .replace(/smoke test/i, 'Smoke Test')
    .replace(/execution/i, 'Execution');
  
  return {
    date: null,
    displayName: cleanName,
    isValid: false
  };
}

function ExecutionHistoryCard({ file, index, projectId, suiteId, testId }: {
  file: string;
  index: number;
  projectId: string;
  suiteId: string;
  testId: string;
}) {
  const execution = parseExecutionFileName(file);
  
  return (
    <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <Timer className="h-4 w-4 text-primary" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-sm">
                {execution.displayName}
              </h4>
              <div className="flex items-center gap-2 mt-1">
                {execution.isValid && execution.date ? (
                  <>
                    <Calendar className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">
                      {format(execution.date, 'MMM d, yyyy')}
                    </span>
                    <Clock className="h-3 w-3 text-muted-foreground ml-2" />
                    <span className="text-xs text-muted-foreground">
                      {format(execution.date, 'HH:mm:ss')}
                    </span>
                  </>
                ) : (
                  <>
                    <FileText className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">
                      Record #{index + 1}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="ghost" 
              size="sm" 
              asChild
              className="hover:bg-primary/10"
            >
              <Link 
                href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}/execute?historyPath=${encodeURIComponent(file)}`}
                className="flex items-center gap-2"
              >
                <span className="text-sm">View Details</span>
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="icon">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Execution History</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will permanently remove this execution record and its JSON file. This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={async () => {
                    await fetch(`/api/executions/history?history_path=${encodeURIComponent(file)}&project_id=${projectId}&suite_id=${suiteId}&test_id=${testId}`, { method: 'DELETE' });
                    window.location.reload();
                  }}>Delete</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function MongoExecutionHistoryCard({ execution, projectId, suiteId, testId, availableEnvironments }: {
  execution: any;
  projectId: string;
  suiteId: string;
  testId: string;
  availableEnvironments: Environment[];
}) {
  // Formato para la fecha desde MongoDB - verificar múltiples fuentes
  const getExecutionDate = () => {
    // Prioridad 1: completed_at
    if (execution.completed_at) {
      return new Date(execution.completed_at);
    }
    
    // Prioridad 2: started_at 
    if (execution.started_at) {
      return new Date(execution.started_at);
    }
    
    // Prioridad 3: verificar en result
    if (execution.result?.completed_at) {
      return new Date(execution.result.completed_at);
    }
    
    if (execution.result?.started_at) {
      return new Date(execution.result.started_at);
    }
    
    // Prioridad 4: campos de compatibilidad
    if (execution.result?.end_time) {
      return new Date(execution.result.end_time);
    }
    
    if (execution.result?.start_time) {
      return new Date(execution.result.start_time);
    }
    
    return null;
  };
  
  const executionDate = getExecutionDate();
  
  // Helper para obtener environment name - fallback si es null pero tenemos environment_id
  const getEnvironmentInfo = () => {
    const envName = execution.environment_name || execution.result?.metadata?.environment_name;
    const envId = execution.environment_id || execution.result?.metadata?.environment_id;
    const fullUrl = execution.full_url || execution.result?.metadata?.full_url;
    
    // Si tenemos environment_id pero no environment_name, buscar en availableEnvironments
    if (!envName && envId && availableEnvironments.length > 0) {
      const matchedEnv = availableEnvironments.find(env => env.env_id === envId);
      return {
        name: matchedEnv?.name || `Environment ${envId.substring(0, 8)}...`,
        id: envId,
        url: fullUrl
      };
    }
    
    return {
      name: envName,
      id: envId,
      url: fullUrl
    };
  };
  
  const environmentInfo = getEnvironmentInfo();
  
  // Debug: log execution data to understand structu
  
  // Determinar el status icon y color
  let statusColor = "text-gray-500";
  let statusIcon = <Clock className="h-4 w-4" />;
  let statusBg = "bg-gray-100";
  
  if (execution.status === "success") {
    statusColor = "text-green-500";
    statusIcon = <CheckCircle className="h-4 w-4" />;
    statusBg = "bg-green-50";
  } else if (execution.status === "failure" || execution.status === "error") {
    statusColor = "text-red-500";
    statusIcon = <XCircle className="h-4 w-4" />;
    statusBg = "bg-red-50";
  } else if (execution.status === "running") {
    statusColor = "text-blue-500";
    statusIcon = <Loader2 className="h-4 w-4 animate-spin" />;
    statusBg = "bg-blue-50";
  }
  
  // Generar título descriptivo basado en test_type
  const getExecutionTitle = () => {
    const testType = execution.test_type as keyof typeof testTypeLabel | 'unknown';
    const testTypeLabel = {
      'smoke': 'Smoke Test',
      'full': 'Full Test',
      'case': 'Test Case',
      'suite': 'Test Suite',
      'codegen': 'Code Generation',
      'unknown': 'Test'
    };
    
    return testTypeLabel[testType] || 'Test';
  };
  
  // Formatear duración
  const formatDuration = (durationMs: number) => {
    if (durationMs < 1000) return `${durationMs}ms`;
    if (durationMs < 60000) return `${(durationMs / 1000).toFixed(1)}s`;
    return `${Math.floor(durationMs / 60000)}m ${Math.floor((durationMs % 60000) / 1000)}s`;
  };
  
  // Obtener resumen de pasos - verificar múltiples fuentes
  const getStepsSummary = () => {
    // Prioridad 1: summary con total_steps
    if (execution.summary?.total_steps) {
      return `${execution.summary.successful_steps || 0}/${execution.summary.total_steps}`;
    }
    
    // Prioridad 2: steps en result
    if (execution.result?.steps && execution.result.steps.length > 0) {
      const successfulSteps = execution.result.steps.filter((step: TestCaseStep) => step.success).length;
      return `${successfulSteps}/${execution.result.steps.length}`;
    }
    
    // Prioridad 3: verificar si hay steps en el objeto principal
    if (execution.steps && execution.steps.length > 0) {
      const successfulSteps = execution.steps.filter((step: TestCaseStep) => step.success).length;
      return `${successfulSteps}/${execution.steps.length}`;
    }
    
    return 'No steps';
  };
  
  const stepsSummary = getStepsSummary();
  
  // Helper functions para información de ejecución
  const getTotalSteps = () => {
    if (execution.summary?.total_steps) {
      return execution.summary.total_steps;
    }
    if (execution.result?.steps) {
      return execution.result.steps.length;
    }
    if (execution.steps) {
      return execution.steps.length;
    }
    return 0;
  };
  
  const getSuccessRate = () => {
    if (execution.summary?.success_rate !== undefined) {
      return execution.summary.success_rate;
    }
    
    // Calcular success rate desde steps
    const steps: TestCaseStep[] = execution.result?.steps || execution.steps || [];
    if (steps.length === 0) return 0;
    
    const successfulSteps = steps.filter((step: TestCaseStep) => step.success).length;
    return successfulSteps / steps.length;
  };
  
  // Obtener primer error si existe
  const firstError = execution.errors && execution.errors.length > 0 ? 
    execution.errors[0] : execution.error;
    
  return (
    <Card className={`hover:shadow-md transition-all duration-200 hover:border-primary/20 border-l-4 ${
      execution.status === "success" ? "border-l-green-500" : 
      execution.status === "failure" || execution.status === "error" ? "border-l-red-500" :
      execution.status === "running" ? "border-l-blue-500" : "border-l-gray-300"
    }`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <div className={`p-2 rounded-lg ${statusBg}`}>
              <span className={statusColor}>
                {statusIcon}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium text-sm truncate">
                  {getExecutionTitle()}
                </h4>
                <Badge variant={execution.status === "success" ? "default" : 
                              execution.status === "failure" || execution.status === "error" ? "destructive" : 
                              "secondary"} 
                       className="text-xs">
                  {execution.status?.toUpperCase() || 'UNKNOWN'}
                </Badge>
              </div>
              
              {/* Información principal */}
              <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-xs text-muted-foreground mb-2">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>
                    {executionDate ? format(executionDate, 'MMM d, HH:mm') : 'No date available'}
                  </span>
                </div>
                {execution.duration_ms && (
                  <div className="flex items-center gap-1">
                    <Timer className="h-3 w-3" />
                    <span>{formatDuration(execution.duration_ms)}</span>
                  </div>
                )}
                <div className="flex items-center gap-1">
                  <TestTube2 className="h-3 w-3" />
                  <span>{stepsSummary === 'No steps' ? stepsSummary : `${stepsSummary} steps`}</span>
                </div>
                <div className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  <span>{execution.execution_id.substring(0, 8)}...</span>
                </div>
                {environmentInfo.name && (
                  <div className="flex items-center gap-1 col-span-2">
                    <Globe className="h-3 w-3" />
                    <Badge variant="secondary" className="text-xs">
                      {environmentInfo.name}
                    </Badge>
                    {environmentInfo.url && (
                      <span className="text-xs text-muted-foreground ml-1 truncate">
                        {environmentInfo.url}
                      </span>
                    )}
                  </div>
                )}
                {(execution.application_version || execution.result?.metadata?.application_version) && (
                  <div className="flex items-center gap-1">
                    <Badge variant="outline" className="text-xs">
                      v{execution.application_version || execution.result?.metadata?.application_version}
                    </Badge>
                    <span className="text-xs text-muted-foreground">App Version</span>
                  </div>
                )}
              </div>
              
              {/* Mostrar primer error si existe */}
              {firstError && (
                <div className="text-xs text-red-600 bg-red-50 p-2 rounded border-l-2 border-red-200 mb-2">
                  <div className="flex items-start gap-1">
                    <AlertCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <span className="truncate">{firstError}</span>
                  </div>
                </div>
              )}
              
              {/* Success rate para ejecuciones completadas */}
              {execution.summary?.success_rate !== undefined && execution.status !== "running" && (
                <div className="flex items-center gap-2 text-xs">
                  <span className="text-muted-foreground">Success Rate:</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-1.5">
                    <div 
                      className={`h-1.5 rounded-full ${
                        (execution.summary?.success_rate || 0) >= 0.8 ? 'bg-green-500' :
                        (execution.summary?.success_rate || 0) >= 0.5 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${(execution.summary?.success_rate || 0) * 100}%` }}
                    />
                  </div>
                  <span className="text-muted-foreground">
                    {Math.round((execution.summary?.success_rate || 0) * 100)}%
                  </span>
                </div>
              )}
              
              {/* Análisis y recomendaciones disponibles */}
              {(execution.metadata?.test_analysis || execution.metadata?.ai_insights || execution.metadata?.recommendations) && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {execution.metadata?.test_analysis && (
                    <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                      <Sparkles className="h-3 w-3 mr-1" />
                      Analysis
                    </Badge>
                  )}
                  {execution.metadata?.ai_insights && (
                    <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700 border-purple-200">
                      <Brain className="h-3 w-3 mr-1" />
                      Insights
                    </Badge>
                  )}
                  {execution.metadata?.recommendations && execution.metadata.recommendations.length > 0 && (
                    <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                      <Lightbulb className="h-3 w-3 mr-1" />
                      {execution.metadata.recommendations.length} Tips
                    </Badge>
                  )}
                  {execution.metadata?.execution_tags && execution.metadata.execution_tags.length > 0 && (
                    <Badge variant="outline" className="text-xs bg-gray-50 text-gray-700 border-gray-200">
                      <Tag className="h-3 w-3 mr-1" />
                      {execution.metadata.execution_tags[0]}
                      {execution.metadata.execution_tags.length > 1 && ` +${execution.metadata.execution_tags.length - 1}`}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex gap-2 flex-shrink-0">
            <Button 
              variant="ghost" 
              size="sm" 
              asChild
              className="hover:bg-primary/10"
            >
              <Link 
                href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}/execute?executionId=${execution.execution_id}`}
                className="flex items-center gap-2"
              >
                <span className="text-sm">View Details</span>
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
            
            {/* Generate Script Button */}
            {execution.status === "success" && (
              // Usar la misma lógica mejorada para detectar steps
              (execution.summary?.total_steps && execution.summary.total_steps > 0) || 
              (execution.result?.steps && execution.result.steps.length > 0) ||
              (execution.steps && execution.steps.length > 0)
            ) && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" size="sm" className="hover:bg-blue-50">
                    <Code2 className="h-4 w-4 mr-1" />
                    <span className="text-sm">Generate Script</span>
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent className="max-w-md">
                  <AlertDialogHeader>
                    <AlertDialogTitle>Generate Automation Script</AlertDialogTitle>
                    <AlertDialogDescription>
                      Generate and edit an automation script from this execution. Choose your preferred framework:
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 gap-2">
                      {/* Visual Editor Option */}
                      <div className="border rounded-lg p-3 bg-gradient-to-r from-blue-50 to-purple-50">
                        <div className="flex items-center gap-2 mb-2">
                          <Edit3 className="h-4 w-4 text-blue-600" />
                          <span className="font-medium text-sm">Visual Editor (Recommended)</span>
                        </div>
                        <p className="text-xs text-muted-foreground mb-3">
                          Edit steps visually, add validations, and customize the script without code.
                        </p>
                        <div className="flex gap-2">
                          <Button 
                            size="sm"
                            className="flex-1"
                            asChild
                          >
                            <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}/scripts/playwright_python?executionId=${execution.execution_id}`}>
                              <Edit3 className="h-3 w-3 mr-1" />
                              Edit Playwright
                            </Link>
                          </Button>
                          <Button 
                            size="sm"
                            variant="outline"
                            className="flex-1"
                            asChild
                          >
                            <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}/scripts/selenium_python?executionId=${execution.execution_id}`}>
                              <Edit3 className="h-3 w-3 mr-1" />
                              Edit Selenium
                            </Link>
                          </Button>
                        </div>
                      </div>
                      
                      <Separator />
                      
                      {/* Quick Download Options */}
                      <div>
                        <p className="text-sm font-medium mb-2">Quick Download</p>
                        <div className="grid grid-cols-1 gap-2">
                          <Button 
                            variant="outline" 
                            className="justify-start"
                            onClick={async () => {
                              try {
                                const response = await fetch('/api/v2/script-generation/generate', {
                                  method: 'POST',
                                  headers: { 'Content-Type': 'application/json' },
                                  body: JSON.stringify({
                                    execution_id: execution.execution_id,
                                    framework: 'playwright_python',
                                    include_assertions: true,
                                    include_waits: true,
                                    add_comments: true
                                  })
                                });
                                
                                if (response.ok) {
                                  const result = await response.json();
                                  const blob = new Blob([result.script_content], { type: 'text/plain' });
                                  const url = URL.createObjectURL(blob);
                                  const a = document.createElement('a');
                                  a.href = url;
                                  a.download = `test_${execution.execution_id.substring(0, 8)}_playwright.py`;
                                  a.click();
                                  URL.revokeObjectURL(url);
                                  
                                  toast({
                                    title: "Script Downloaded",
                                    description: "Playwright Python script downloaded successfully"
                                  });
                                } else {
                                  throw new Error('Failed to generate script');
                                }
                              } catch (error) {
                                toast({
                                  title: "Generation Failed", 
                                  description: "Could not generate script. Please try again.",
                                  variant: "destructive"
                                });
                              }
                            }}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Download Playwright (Python)
                          </Button>
                          
                          <Button 
                            variant="outline" 
                            className="justify-start"
                            onClick={async () => {
                              try {
                                const response = await fetch('/api/v2/script-generation/generate', {
                                  method: 'POST',
                                  headers: { 'Content-Type': 'application/json' },
                                  body: JSON.stringify({
                                    execution_id: execution.execution_id,
                                    framework: 'selenium_python',
                                    include_assertions: true,
                                    include_waits: true,
                                    add_comments: true
                                  })
                                });
                                
                                if (response.ok) {
                                  const result = await response.json();
                                  const blob = new Blob([result.script_content], { type: 'text/plain' });
                                  const url = URL.createObjectURL(blob);
                                  const a = document.createElement('a');
                                  a.href = url;
                                  a.download = `test_${execution.execution_id.substring(0, 8)}_selenium.py`;
                                  a.click();
                                  URL.revokeObjectURL(url);
                                  
                                  toast({
                                    title: "Script Downloaded",
                                    description: "Selenium Python script downloaded successfully"
                                  });
                                } else {
                                  throw new Error('Failed to generate script');
                                }
                              } catch (error) {
                                toast({
                                  title: "Generation Failed",
                                  description: "Could not generate script. Please try again.", 
                                  variant: "destructive"
                                });
                              }
                            }}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Download Selenium (Python)
                          </Button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-xs text-muted-foreground bg-blue-50 p-2 rounded">
                      <div className="flex items-center gap-1 mb-1">
                        <FileText className="h-3 w-3" />
                        <span className="font-medium">Execution Info:</span>
                      </div>
                      <div>• {getTotalSteps()} steps captured</div>
                      <div>• {Math.round(getSuccessRate() * 100)}% success rate</div>
                      {execution.metadata?.execution_tags && (
                        <div>• Tags: {execution.metadata.execution_tags.join(', ')}</div>
                      )}
                    </div>
                  </div>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="icon">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Execution History</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will permanently delete this MongoDB execution record. This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={async () => {
                    try {
                      await deleteExecution(execution.execution_id);
                      window.location.reload();
                    } catch (error) {
                      console.error('Failed to delete execution:', error);
                      alert('Failed to delete execution. Please try again.');
                    }
                  }}>Delete</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function TestCaseDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;
  const testId = params.testId as string;
  const [executionTimes, setExecutionTimes] = useState(1);
  const [selectedEnvironmentId, setSelectedEnvironmentId] = useState<string | undefined>();
  const [applicationVersion, setApplicationVersion] = useState<string>('');
  const [currentExecutionId, setCurrentExecutionId] = useState<string | null>(null);
  const [currentExecutionStatus, setCurrentExecutionStatus] = useState<ExecutionStatus | null>(null);
  const [testCase, setTestCase] = useState<TestCase | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionId, setExecutionId] = useState<string | null>(null);
  const [mongoExecutions, setMongoExecutions] = useState<any[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortOrder, setSortOrder] = useState<'newest' | 'oldest'>('newest');
  const [envFilter, setEnvFilter] = useState<string>('all');
  const [versionFilter, setVersionFilter] = useState<string>('all');
  
  // New state for available environments
  const [availableEnvironments, setAvailableEnvironments] = useState<Environment[]>([]);

  const { data: testCaseData, isLoading, error: queryError, refetch } = useQuery({
    queryKey: ['testCase', projectId, suiteId, testId],
    queryFn: () => getTestCaseById(projectId, suiteId, testId),
    enabled: !!projectId && !!suiteId && !!testId,
  });

  const deleteMutation = useMutation({
    mutationFn: () => deleteTestCase(projectId, suiteId, testId),
    onSuccess: () => {
      toast({ title: "Test Case Deleted", description: `Test Case "${testCaseData?.name}" has been deleted.` });
      queryClient.invalidateQueries({ queryKey: ['testCases', projectId, suiteId] });
      router.push(`/projects/${projectId}/suites/${suiteId}`);
    },
    onError: (err) => {
      toast({ title: "Error Deleting Test Case", description: err.message, variant: "destructive" });
    },
  });

  const executeMutation = useMutation({
    mutationFn: (times: number) => {
      const request: TestCaseRequest = {
        type: ExecutionTypeEnum.CASE,
        test_id: testId,
        project_id: projectId,
        suite_id: suiteId,
        execution_times: times,
      };
      return executeTest(request);
    },
    onMutate: () => {
      setCurrentExecutionStatus(ExecutionStatus.RUNNING);
    },
    onSuccess: (data: ExecutionResponseV2) => {
      setCurrentExecutionId(data.execution_id);
      setCurrentExecutionStatus(data.status as ExecutionStatus);
      toast({
        title: "Test Execution Initiated",
        description: `Execution ID: ${data.execution_id}`,
      });
      refetch(); // Refetch test case to update status and last execution
    },
    onError: (err) => {
      toast({ title: "Error Executing Test", description: err.message, variant: "destructive" });
      setCurrentExecutionStatus(ExecutionStatus.ERROR);
    },
  });

  const handlePause = async () => {
    if (!currentExecutionId) return;
    try {
      await pauseExecution(currentExecutionId);
      setCurrentExecutionStatus(ExecutionStatus.PAUSED);
      toast({ title: "Execution Paused" });
    } catch (error) {
      toast({ title: "Error Pausing Execution", description: (error as Error).message, variant: "destructive" });
    }
  };

  const handleResume = async () => {
    if (!currentExecutionId) return;
    try {
      await resumeExecution(currentExecutionId);
      setCurrentExecutionStatus(ExecutionStatus.RUNNING);
      toast({ title: "Execution Resumed" });
    } catch (error) {
      toast({ title: "Error Resuming Execution", description: (error as Error).message, variant: "destructive" });
    }
  };

  const handleStop = async () => {
    if (!currentExecutionId) return;
    try {
      await stopExecution(currentExecutionId);
      setCurrentExecutionStatus(ExecutionStatus.CANCELLED);
      toast({ title: "Execution Cancelled" });
    } catch (error) {
      toast({ title: "Error Cancelling Execution", description: (error as Error).message, variant: "destructive" });
    }
  };

  const updateStatusMutation = useMutation({
    mutationFn: (newStatus: TestCaseStatus) => updateTestCaseStatus(projectId, suiteId, testId, { status: newStatus }),
    onSuccess: (updatedTc) => {
      queryClient.setQueryData(['testCase', projectId, suiteId, testId], updatedTc);
      queryClient.invalidateQueries({queryKey: ['testCases', projectId, suiteId]});
      toast({ title: "Status Updated", description: `Test Case status changed to ${updatedTc.status}.` });
    },
    onError: (err) => {
      toast({ title: "Error Updating Status", description: err.message, variant: "destructive" });
    }
  });

  useEffect(() => {
    const fetchTestCase = async () => {
      if (projectId && suiteId && testId) {
        try {
          const tc = await getTestCaseById(projectId, suiteId, testId);
          setTestCase(tc);
          
          // También obtenemos las ejecuciones de MongoDB
          try {
            const mongoExecs = await getTestMongoExecutions(
              projectId,
              suiteId,
              testId,
              envFilter !== 'all' ? envFilter : undefined,
              versionFilter !== 'all' ? versionFilter : undefined
            );
            setMongoExecutions(mongoExecs);
          } catch (mongoErr) {
            console.error("Error fetching MongoDB executions:", mongoErr);
            // No bloqueamos el flujo principal si falla la obtención de MongoDB
          }
          
          setLoading(false);
        } catch (err) {
          console.error("Error fetching test case:", err);
          setError("Failed to load test case.");
          setLoading(false);
        }
      }
    };

    if (projectId && suiteId && testId) {
      fetchTestCase();
    }
  }, [projectId, suiteId, testId, envFilter, versionFilter]);

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;

    const pollExecutionStatus = async (id: string) => {
      try {
        const executionResult = await getExecutionById(id);
        if (executionResult.status === "success" || executionResult.status === "failure" || executionResult.status === "error") {
          if (intervalId) clearInterval(intervalId);
          setIsExecuting(false);
          router.push(`/projects/${projectId}/suites/${suiteId}/tests/${testId}/execute?executionId=${id}`);
        }
      } catch (err) {
        console.error("Failed to poll execution status:", err);
        setError("Failed to get execution status.");
        if (intervalId) clearInterval(intervalId);
        setIsExecuting(false);
      }
    };

    if (isExecuting && executionId) {
      intervalId = setInterval(() => pollExecutionStatus(executionId), 3000); // Poll every 3 seconds
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [isExecuting, executionId, projectId, suiteId, testId, router]);

  const handleExecute = async () => {
    if (!testCase) return;

    setIsExecuting(true);
    setExecutionId(null);
    setError(null);

    try {
      const response = await executeTest({
        type: ExecutionTypeEnum.CASE,
        test_id: testCase.test_id,
        project_id: projectId,
        suite_id: suiteId,
        environment_id: selectedEnvironmentId,
        application_version: applicationVersion || undefined,
      });

      if (response && response.execution_id) {
        setExecutionId(response.execution_id);
      } else {
        setError("Failed to start execution: No execution ID received.");
        setIsExecuting(false);
      }
    } catch (err) {
      console.error("Execution failed to start:", err);
      setError("Failed to start test execution.");
      setIsExecuting(false);
    }
  };

  useEffect(() => {
    const fetchEnvironments = async () => {
      if (projectId) {
        try {
          const envs = await getProjectEnvironments(projectId);
          setAvailableEnvironments(envs);
          
          // Auto-select default environment if none is selected
          if (!selectedEnvironmentId && envs.length > 0) {
            const defaultEnv = envs.find(env => env.is_default) || envs[0];
            setSelectedEnvironmentId(defaultEnv.env_id);
            console.log("🔧 Auto-selected environment:", defaultEnv.name, defaultEnv.env_id);
          }
        } catch (err) {
          console.error("Error fetching project environments:", err);
        }
      }
    };
    
    fetchEnvironments();
  }, [projectId, selectedEnvironmentId]);

  if (isLoading) return <TestCaseDetailSkeleton />;
  if (queryError) return <Alert variant="destructive"><AlertCircle className="h-4 w-4" /><AlertTitle>Error</AlertTitle><AlertDescription>{queryError.message}</AlertDescription></Alert>;
  if (!testCase) return <p>Test Case not found.</p>;

  let StatusIcon = Clock;
  if (testCase.status === 'Passed') StatusIcon = CheckCircle;
  else if (testCase.status === 'Failed') StatusIcon = XCircle;

  const detailSections = [
    { title: "Instructions", content: testCase.instrucciones, icon: <FileText className="h-5 w-5 text-primary" /> },
    { title: "User Story", content: testCase.historia_de_usuario, icon: <FileText className="h-5 w-5 text-primary" /> },
    { title: "Target URL", content: testCase.url, icon: <FileText className="h-5 w-5 text-primary" /> },
    { title: "Gherkin Scenario", content: testCase.gherkin || "Not specified", icon: <FileText className="h-5 w-5 text-primary" /> },
  ];

  // Filter and sort MongoDB executions
  const filteredAndSortedMongoExecutions = mongoExecutions
    .filter(execution => {
      if (statusFilter !== 'all' && execution.status !== statusFilter) return false;
      return true;
    })
    .sort((a, b) => {
      const dateA = a.completed_at ? new Date(a.completed_at) : a.started_at ? new Date(a.started_at) : new Date(0);
      const dateB = b.completed_at ? new Date(b.completed_at) : b.started_at ? new Date(b.started_at) : new Date(0);
      
      if (sortOrder === 'newest') {
        return dateB.getTime() - dateA.getTime();
      } else {
        return dateA.getTime() - dateB.getTime();
      }
    });

  // Sort execution files by date (newest first) if possible
  const sortedHistoryFiles = testCase.history_files 
    ? [...testCase.history_files].sort((a, b) => {
        const dateA = parseExecutionFileName(a);
        const dateB = parseExecutionFileName(b);
        
        if (dateA.isValid && dateB.isValid && dateA.date && dateB.date) {
          if (sortOrder === 'newest') {
            return dateB.date.getTime() - dateA.date.getTime(); // Newest first
          } else {
            return dateA.date.getTime() - dateB.date.getTime(); // Oldest first
          }
        }
        
        // Fallback to original order for non-parseable filenames
        return 0;
      })
    : [];
    
  // Get unique statuses and app versions for filters
  const availableStatuses: string[] = ['all', ...Array.from(new Set(mongoExecutions.map((e: any) => e.status).filter(Boolean)))];
  const availableVersions: string[] = ['all', ...Array.from(new Set(mongoExecutions.map((e: any) => e.result?.metadata?.application_version).filter(Boolean)))];
  const totalFilteredExecutions = filteredAndSortedMongoExecutions.length + sortedHistoryFiles.length;
  
  // Group executions by application version for comparison
  const executionsByVersion = mongoExecutions.reduce((acc: Record<string, any[]>, execution: any) => {
    const version = execution.application_version || execution.result?.metadata?.application_version || 'unknown';
    if (!acc[version]) {
      acc[version] = [];
    }
    acc[version].push(execution);
    return acc;
  }, {} as Record<string, any[]>);

  return (
    <div>
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href={`/projects/${projectId}/suites/${suiteId}`}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Suite
        </Link>
      </Button>

      <div className="flex flex-col md:flex-row justify-between md:items-start mb-4">
        <div className="flex-1">
          <h1 className="page-header mb-1 flex items-center gap-2">
            <TestTube2 className="h-8 w-8 text-primary" />
            {testCase.name}
          </h1>
          <p className="text-muted-foreground text-sm mb-3">{testCase.description}</p>
          <div className="flex flex-wrap gap-2 mb-2">
            {testCase.tags.map((tag) => (
              <Badge key={tag} variant="outline">{tag}</Badge>
            ))}
          </div>
          <p className="text-xs text-muted-foreground">
            Created: {format(new Date(testCase.created_at), 'PPP p')} | Updated: {format(new Date(testCase.updated_at), 'PPP p')}
          </p>
           {testCase.last_execution && (
            <p className="text-xs text-muted-foreground mt-1">
              Last Execution: {format(new Date(testCase.last_execution), 'PPP p')}
            </p>
          )}
          <div className="mt-3 flex items-center gap-2">
            <span className="text-sm font-medium">Status:</span>
             <Select
                value={testCase.status}
                onValueChange={(value: TestCaseStatus) => updateStatusMutation.mutate(value)}
                disabled={updateStatusMutation.isPending}
              >
                <SelectTrigger className="w-[180px] h-8">
                  <div className="flex items-center">

                    <SelectValue placeholder="Set status" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Not Executed"><Clock className="mr-2 h-4 w-4 text-gray-500 inline-block"/>Not Executed</SelectItem>
                  <SelectItem value="Passed"><CheckCircle className="mr-2 h-4 w-4 text-green-500 inline-block"/>Passed</SelectItem>
                  <SelectItem value="Failed"><XCircle className="mr-2 h-4 w-4 text-red-500 inline-block"/>Failed</SelectItem>
                </SelectContent>
              </Select>
          </div>
        </div>
         <div className="flex flex-col items-end gap-2 shrink-0">
            <div className="flex gap-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="mr-2 h-4 w-4" /> Delete
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete the test case
                      and all of its execution history.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => deleteMutation.mutate()}
                      disabled={deleteMutation.isPending}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      {deleteMutation.isPending ? 'Deleting...' : 'Continue'}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            <div className="space-y-3">
              <EnvironmentSelector
                projectId={projectId}
                selectedEnvId={selectedEnvironmentId}
                onSelect={setSelectedEnvironmentId}
                className="w-full"
                disabled={isExecuting}
              />
              <div className="flex items-center gap-2">
                <Label htmlFor="application-version" className="text-sm">App Version:</Label>
                <Input
                  id="application-version"
                  type="text"
                  value={applicationVersion}
                  onChange={(e) => setApplicationVersion(e.target.value)}
                  placeholder="e.g., 1.0.1, 2.3.0"
                  className="w-32"
                  disabled={isExecuting}
                />
              </div>
              <div className="flex items-center gap-2">
                <Label htmlFor="execution-times" className="text-sm">Executions:</Label>
                <Input
                  id="execution-times"
                  type="number"
                  min="1"
                  value={executionTimes}
                  onChange={(e) => setExecutionTimes(Math.max(1, parseInt(e.target.value, 10) || 1))}
                  className="w-20"
                  disabled={executeMutation.isPending || currentExecutionStatus === ExecutionStatus.RUNNING || currentExecutionStatus === ExecutionStatus.PAUSED}
                />
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                onClick={handleExecute}
                disabled={isExecuting}
              >
                {isExecuting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Executing...
                  </>
                ) : (
                  "Execute Test Case"
                )}
              </Button>
              <Button variant="outline" disabled={!isExecuting}>
                Pause
              </Button>
              <Button variant="destructive" disabled={!isExecuting}>
                Cancel
              </Button>
            </div>
            {currentExecutionId && (
              <Button asChild variant="secondary" className="w-full">
                <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}/execute?executionId=${currentExecutionId}`}>
                  View Execution
                </Link>
              </Button>
            )}
        </div>
      </div>
      
      <div className="grid md:grid-cols-2 gap-6 mt-6">
        {detailSections.map(section => (
          <Card key={section.title}>
            <CardHeader className="flex flex-row items-center gap-2 pb-2">
              {section.icon}
              <CardTitle className="text-lg">{section.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-sm whitespace-pre-wrap font-sans bg-muted p-3 rounded-md max-h-60 overflow-y-auto">{section.content}</pre>
            </CardContent>
          </Card>
        ))}
      </div>

      <Separator className="my-8" />

      <div>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <History className="h-6 w-6 text-primary"/>
            </div>
            <div>
              <h2 className="section-header mb-0">Execution History</h2>
              <p className="text-sm text-muted-foreground">
                {totalFilteredExecutions > 0
                  ? `${totalFilteredExecutions} execution record${totalFilteredExecutions > 1 ? 's' : ''} found`
                  : 'No execution records available'
                }
              </p>
            </div>
          </div>
          
          {(mongoExecutions.length > 0 || sortedHistoryFiles.length > 0) && (
            <div className="flex items-center gap-2 flex-wrap">
              {/* Environment Filter */}
              <Select value={envFilter} onValueChange={setEnvFilter}>
                <SelectTrigger className="w-40 h-8">
                  <div className="flex items-center gap-1">
                    <Globe className="h-3 w-3" />
                    <SelectValue />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Environments</SelectItem>
                  {availableEnvironments.map(env => (
                    <SelectItem key={env.env_id} value={env.env_id}>
                      {env.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {/* Application Version Filter */}
              <Select value={versionFilter} onValueChange={setVersionFilter}>
                <SelectTrigger className="w-40 h-8">
                  <div className="flex items-center gap-1">
                    <Tag className="h-3 w-3" />
                    <SelectValue />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Versions</SelectItem>
                  {availableVersions.map((version: string) => (
                    <SelectItem key={version} value={version}>
                      {version === 'all' ? 'All Versions' : `v${version}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Status Filter */}
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32 h-8">
                  <div className="flex items-center gap-1">
                    <Filter className="h-3 w-3" />
                    <SelectValue />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  {availableStatuses.map((status: string) => (
                    <SelectItem key={status} value={status}>
                      {status === 'all' ? 'All Status' : status.charAt(0).toUpperCase() + status.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {/* Sort Order */}
              <Select value={sortOrder} onValueChange={(value: 'newest' | 'oldest') => setSortOrder(value)}>
                <SelectTrigger className="w-32 h-8">
                  <div className="flex items-center gap-1">
                    <SortDesc className="h-3 w-3" />
                    <SelectValue />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
        
        {/* Version Comparison Summary */}
        {availableVersions.length > 2 && ( // More than 'all' and one version
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Environment Version Comparison
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(executionsByVersion)
                  .filter(([version]) => version !== 'unknown')
                  .map(([version, versionExecutions]: [string, any[]]) => {
                    const successCount = versionExecutions.filter((e: any) => e.status === 'success').length;
                    const failureCount = versionExecutions.filter((e: any) => e.status === 'failure' || e.status === 'error').length;
                    const successRate = versionExecutions.length > 0 ? (successCount / versionExecutions.length) * 100 : 0;
                    
                    return (
                      <div key={version} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="outline" className="text-sm">
                            v{version}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {versionExecutions.length} run{versionExecutions.length !== 1 ? 's' : ''}
                          </span>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-green-600">Success: {successCount}</span>
                            <span className="text-red-600">Failed: {failureCount}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${
                                successRate >= 80 ? 'bg-green-500' :
                                successRate >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${successRate}%` }}
                            />
                          </div>
                          <p className="text-xs text-center text-muted-foreground">
                            {Math.round(successRate)}% success rate
                          </p>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>
        )}
        
        {totalFilteredExecutions > 0 ? (
          <div className="space-y-3">
            {/* MongoDB execuciones (nuevas) */}
            {filteredAndSortedMongoExecutions.map((execution) => (
              <MongoExecutionHistoryCard
                key={execution.execution_id}
                execution={execution}
                projectId={projectId}
                suiteId={suiteId}
                testId={testId}
                availableEnvironments={availableEnvironments}
              />
            ))}
            
            {/* Legacy execuciones (archivos) */}
            {sortedHistoryFiles.map((file, index) => (
              <ExecutionHistoryCard
                key={file}
                file={file}
                index={index}
                projectId={projectId}
                suiteId={suiteId}
                testId={testId}
              />
            ))}
          </div>
        ) : (
          <Card className="border-dashed">
            <CardContent className="p-8 text-center">
              <div className="flex flex-col items-center gap-4">
                <div className="p-4 rounded-full bg-muted/50">
                  <History className="h-8 w-8 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="font-medium text-lg mb-1">No Execution History</h3>
                  <p className="text-muted-foreground">
                    This test case hasn't been executed yet. Click "Execute Test Case" to run your first test.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
