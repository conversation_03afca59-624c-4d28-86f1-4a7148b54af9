"use client";

import Link from 'next/link';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { getProjectById, getSuitesByProjectId, deleteProject } from '@/lib/api';
import type { Project, TestSuite } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { FolderKanban, ClipboardList, PlusCircle, ArrowLeft, Edit, Trash2, AlertCircle, Globe } from 'lucide-react';
import { format } from 'date-fns';

function SuiteCard({ suite, projectId }: { suite: TestSuite; projectId: string }) {
  return (
    <Card className="flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ClipboardList className="h-6 w-6 text-primary" />
          {suite.name}
        </CardTitle>
        <CardDescription className="line-clamp-2">{suite.description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="mb-2">
          <span className="text-sm font-semibold">Tags:</span>
          <div className="flex flex-wrap gap-1 mt-1">
            {suite.tags.length > 0 ? (
              suite.tags.map((tag) => <Badge key={tag} variant="secondary">{tag}</Badge>)
            ) : (
              <span className="text-xs text-muted-foreground">No tags</span>
            )}
          </div>
        </div>
         <p className="text-xs text-muted-foreground">
          Last updated: {format(new Date(suite.updated_at), 'PPP')}
        </p>
      </CardContent>
      <CardFooter>
        <Button asChild size="sm" className="w-full">
          <Link href={`/projects/${projectId}/suites/${suite.suite_id}`}>View Suite</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

function SuiteListSkeleton() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-6">
      {[...Array(2)].map((_, i) => (
        <Card key={i}>
           <CardHeader>
            <Skeleton className="h-6 w-3/4 mb-2" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3 mt-1" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-1/4 mb-2" />
            <div className="flex gap-1 mt-1">
              <Skeleton className="h-5 w-12 rounded-full" />
            </div>
            <Skeleton className="h-3 w-1/2 mt-3" />
          </CardContent>
          <CardFooter>
            <Skeleton className="h-9 w-full" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}

export default function ProjectDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const projectId = params.projectId as string;

  const { data: project, isLoading: isLoadingProject, error: projectError, isError: isProjectError } = useQuery({
    queryKey: ['project', projectId],
    queryFn: () => getProjectById(projectId),
    enabled: !!projectId,
  });

  const { data: suitesResponse, isLoading: isLoadingSuites, error: suitesError, isError: isSuitesError } = useQuery({
    queryKey: ['suites', projectId],
    queryFn: () => getSuitesByProjectId(projectId),
    enabled: !!projectId,
  });
  
  const suites = suitesResponse?.items || [];

  const deleteMutation = useMutation({
    mutationFn: () => deleteProject(projectId),
    onSuccess: () => {
      toast({ title: "Project Deleted", description: `Project "${project?.name}" has been deleted.` });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      router.push('/projects');
    },
    onError: (error) => {
      toast({ title: "Error Deleting Project", description: error.message, variant: "destructive" });
    },
  });

  if (isLoadingProject) {
    return (
      <div>
        <Skeleton className="h-9 w-36 mb-4" /> {/* Back button */}
        <Skeleton className="h-10 w-1/2 mb-2" /> {/* Project Name */}
        <Skeleton className="h-5 w-3/4 mb-4" /> {/* Project Description */}
        <Skeleton className="h-8 w-1/4 mb-6" /> {/* Tags label */}
        <div className="flex flex-wrap gap-2 mb-6">
          <Skeleton className="h-6 w-20 rounded-full" />
          <Skeleton className="h-6 w-24 rounded-full" />
        </div>
        <Skeleton className="h-5 w-1/3 mb-6" /> {/* Dates */}
        <div className="flex justify-between items-center mb-6">
           <Skeleton className="h-8 w-1/3" /> {/* Suites Header */}
           <Skeleton className="h-9 w-32" /> {/* Create Suite Button */}
        </div>
        <SuiteListSkeleton />
      </div>
    );
  }

  if (isProjectError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error fetching project</AlertTitle>
        <AlertDescription>{projectError?.message || 'An unknown error occurred.'}</AlertDescription>
      </Alert>
    );
  }

  if (!project) {
    return <p>Project not found.</p>;
  }

  return (
    <div>
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href="/projects">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Projects
        </Link>
      </Button>

      <div className="flex justify-between items-start mb-4">
        <div>
          <h1 className="page-header mb-1 flex items-center gap-2">
            <FolderKanban className="h-8 w-8 text-primary" />
            {project.name}
          </h1>
          <p className="text-muted-foreground text-sm mb-3">{project.description}</p>
          <div className="flex flex-wrap gap-2 mb-2">
            {project.tags.map((tag) => (
              <Badge key={tag} variant="outline">{tag}</Badge>
            ))}
          </div>
          <p className="text-xs text-muted-foreground">
            Created: {format(new Date(project.created_at), 'PPP p')} | Updated: {format(new Date(project.updated_at), 'PPP p')}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/projects/${projectId}/environments`}>
              <Globe className="mr-2 h-4 w-4" /> Environments
            </Link>
          </Button>
          <Button variant="outline" size="sm" disabled> {/* TODO: Link to edit page */}
            <Edit className="mr-2 h-4 w-4" /> Edit
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" size="sm">
                <Trash2 className="mr-2 h-4 w-4" /> Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete the project "{project.name}" and all its associated suites and test cases.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={() => deleteMutation.mutate()} disabled={deleteMutation.isPending}>
                  {deleteMutation.isPending ? "Deleting..." : "Delete Project"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>
      
      <hr className="my-6"/>

      <div className="flex justify-between items-center mb-6">
        <h2 className="section-header mb-0">Test Suites</h2>
        <Button asChild>
          <Link href={`/projects/${projectId}/suites/create`}>
            <PlusCircle className="mr-2 h-4 w-4" /> Create Suite
          </Link>
        </Button>
      </div>

      {isLoadingSuites && <SuiteListSkeleton />}
      {isSuitesError && (
         <Alert variant="destructive" className="mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error fetching suites</AlertTitle>
          <AlertDescription>{suitesError?.message || 'An unknown error occurred.'}</AlertDescription>
        </Alert>
      )}
      {!isLoadingSuites && !isSuitesError && suites.length === 0 && (
         <Card className="text-center py-12 mt-6">
          <CardHeader>
            <ClipboardList className="mx-auto h-12 w-12 text-muted-foreground" />
            <CardTitle className="mt-4">No Test Suites Yet</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>This project doesn't have any test suites. Create one to get started.</CardDescription>
          </CardContent>
          <CardFooter className="justify-center">
             <Button asChild>
                <Link href={`/projects/${projectId}/suites/create`}>
                  <PlusCircle className="mr-2 h-4 w-4" /> Create Suite
                </Link>
              </Button>
          </CardFooter>
        </Card>
      )}
      {!isLoadingSuites && !isSuitesError && suites.length > 0 && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {suites.map((suite) => (
            <SuiteCard key={suite.suite_id} suite={suite} projectId={projectId} />
          ))}
        </div>
      )}
    </div>
  );
}
