"use client";

import type { TestExecutionHistoryData } from "@/lib/types";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import Link from "next/link";

interface UrlsTabProps {
  historyData: TestExecutionHistoryData;
}

export function UrlsTab({ historyData }: UrlsTabProps) {
  const { urls } = historyData;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="glow-text">Visited URLs</CardTitle>
      </CardHeader>
      <CardContent>
        {urls && urls.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[80px]">Step</TableHead>
                <TableHead>URL</TableHead>
                <TableHead>Page Title</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {urls.map((urlItem, index) => (
                <TableRow key={index}>
                  <TableCell>{urlItem.step}</TableCell>
                  <TableCell>
                    <Link href={urlItem.url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline break-all">
                      {urlItem.url}
                    </Link>
                  </TableCell>
                  <TableCell className="text-muted-foreground">{urlItem.title}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <p className="text-muted-foreground">No URLs were visited during this execution.</p>
        )}
      </CardContent>
    </Card>
  );
}
