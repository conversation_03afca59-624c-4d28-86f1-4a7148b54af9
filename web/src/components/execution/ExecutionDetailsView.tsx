"use client";

import type { TestExecutionHistoryData } from "@/lib/types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ResultsTab } from "./ResultsTab";
import { DetailsTab } from "./DetailsTab";
import { ElementsTab } from "./ElementsTab";
import { UrlsTab } from "./UrlsTab";
import { CapturesTab } from "./CapturesTab";
import { MetadataTab } from "./MetadataTab";
import { TestCasesTab } from "./TestCasesTab";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";

interface ExecutionDetailsViewProps {
  historyData: TestExecutionHistoryData;
  testCaseId: string; // Original TestCase ID from URL or parent
}

export function ExecutionDetailsView({ historyData, testCaseId }: ExecutionDetailsViewProps) {
  return (
    <div className="space-y-4">
      <h3 className="page-header glow-text">Execution Details</h3>
      <Tabs defaultValue="results" className="w-full">
        <ScrollArea className="w-full whitespace-nowrap rounded-md border">
          <TabsList className="bg-card p-2 h-auto">
            <TabsTrigger value="results" className="text-sm px-4 py-2">Results</TabsTrigger>
            <TabsTrigger value="details" className="text-sm px-4 py-2">Details</TabsTrigger>
            <TabsTrigger value="elements" className="text-sm px-4 py-2">Elements</TabsTrigger>
            <TabsTrigger value="urls" className="text-sm px-4 py-2">URLs</TabsTrigger>
            <TabsTrigger value="captures" className="text-sm px-4 py-2">Captures</TabsTrigger>
            <TabsTrigger value="testcases" className="text-sm px-4 py-2">Test Cases</TabsTrigger>
            <TabsTrigger value="metadata" className="text-sm px-4 py-2">Metadata</TabsTrigger>
          </TabsList>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>

        <div className="mt-4 p-1 rounded-lg bg-card border">
            <TabsContent value="results" className="mt-0 p-4 fade-in">
              <ResultsTab historyData={historyData} />
            </TabsContent>
            <TabsContent value="details" className="mt-0 p-4 fade-in">
              <DetailsTab historyData={historyData} />
            </TabsContent>
            <TabsContent value="elements" className="mt-0 p-4 fade-in">
              <ElementsTab historyData={historyData} />
            </TabsContent>
            <TabsContent value="urls" className="mt-0 p-4 fade-in">
              <UrlsTab historyData={historyData} />
            </TabsContent>
            <TabsContent value="captures" className="mt-0 p-4 fade-in">
              <CapturesTab historyData={historyData} />
            </TabsContent>
            <TabsContent value="testcases" className="mt-0 p-4 fade-in">
              <TestCasesTab historyData={historyData} />
            </TabsContent>
            <TabsContent value="metadata" className="mt-0 p-4 fade-in">
              <MetadataTab historyData={historyData} testIdFromUrl={testCaseId} />
            </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
