"use client";

import type { TestExecutionHistoryData, TestExecutionAction } from "@/lib/types";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ExpandableText } from "@/components/ExpandableContent";

interface DetailsTabProps {
  historyData: TestExecutionHistoryData;
}

function ActionDetailItem({ action, index }: { action: TestExecutionAction; index: number }) {
  const detailsString = typeof action.details === 'object' ? JSON.stringify(action.details, null, 2) : String(action.details);
  return (
    <div className="mb-3 p-3 border rounded-md bg-card">
      <div className="font-medium mb-1">Step {action.step}: <span className="text-primary">{action.type}</span></div>
      <ExpandableText text={detailsString || "No details for this action."} maxLength={150} className="text-sm" contentClassName="text-muted-foreground font-mono text-xs whitespace-pre-wrap"/>
    </div>
  );
}

export function DetailsTab({ historyData }: DetailsTabProps) {
  const { actions } = historyData;

  const simplifiedActions = actions.map(a => {
    const detailsStr = typeof a.details === 'object' ? JSON.stringify(a.details) : String(a.details);
    return {
      step: a.step,
      type: a.type,
      detailsPreview: detailsStr.substring(0, 100) + (detailsStr.length > 100 ? "..." : ""),
    };
  });

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="glow-text">Simplified Actions Log</CardTitle>
        </CardHeader>
        <CardContent>
          {actions && actions.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px]">Step</TableHead>
                  <TableHead>Action Type</TableHead>
                  <TableHead>Details Preview</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {simplifiedActions.map((action, index) => (
                  <TableRow key={index}>
                    <TableCell>{action.step}</TableCell>
                    <TableCell className="font-medium text-primary">{action.type}</TableCell>
                    <TableCell className="text-muted-foreground text-xs font-mono">{action.detailsPreview}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <p className="text-muted-foreground">No actions recorded for this execution.</p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="glow-text">Detailed Action View</CardTitle>
        </CardHeader>
        <CardContent>
          {actions && actions.length > 0 ? (
            actions.map((action, index) => (
              <ActionDetailItem key={index} action={action} index={index} />
            ))
          ) : (
            <p className="text-muted-foreground">No detailed actions available.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
