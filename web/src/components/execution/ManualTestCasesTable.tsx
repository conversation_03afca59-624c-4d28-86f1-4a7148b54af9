"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { Plus, Trash2 } from "lucide-react";
import { TestCaseObject } from "@/lib/types";

interface ManualTestCasesTableProps {
  testCases: string[] | TestCaseObject[] | (string | TestCaseObject)[];
  onTestCasesChange?: (testCases: TestCaseRow[]) => void;
}

interface TestCaseRow {
  caseId: string;
  step: string;
  expected: string;
  testData?: string;
  comments?: string;
  priority?: string;
  userStory?: string;
}

function convertTestCasesToRows(testCases: string[] | TestCaseObject[] | (string | TestCaseObject)[]): TestCaseRow[] {
  if (!testCases || testCases.length === 0) return [];
  
  return testCases.map((testCase, index) => {
    if (typeof testCase === 'object' && testCase !== null) {
      // Object format from API - use the structured data directly
      const tcObj = testCase as TestCaseObject;
      // Handle instrucciones as either string or array
      let stepText = '';
      if (Array.isArray(tcObj.instrucciones)) {
        stepText = tcObj.instrucciones.join(' ');
      } else if (typeof tcObj.instrucciones === 'string') {
        stepText = tcObj.instrucciones;
      } else {
        stepText = tcObj.title || '';
      }
      
      return {
        caseId: tcObj.id || `TC-${String(index + 1).padStart(3, '0')}`,
        step: stepText,
        expected: tcObj.expected_results || '',
        testData: '',
        comments: tcObj.preconditions ? `Preconditions: ${tcObj.preconditions}` : '',
        priority: tcObj.priority || 'Medium',
        userStory: tcObj.historia_de_usuario || ''
      };
    } else {
      // String format - parse it
      const testCaseStr = String(testCase);
      let step = testCaseStr;
      let expected = "";
      
      // Try to parse "Expected:" pattern
      if (testCaseStr.includes("Expected:")) {
        const parts = testCaseStr.split("Expected:");
        step = parts[0].trim();
        expected = parts[1].trim();
      }
      
      return {
        caseId: `TC-${String(index + 1).padStart(3, '0')}`,
        step: step.replace(/^\d+\.\s*/, ''), // Remove leading numbers
        expected: expected,
        testData: '',
        comments: '',
        priority: 'Medium',
        userStory: ''
      };
    }
  });
}

export function ManualTestCasesTable({ testCases, onTestCasesChange }: ManualTestCasesTableProps) {
  const [rows, setRows] = useState<TestCaseRow[]>(() => convertTestCasesToRows(testCases));

  // Update rows when testCases prop changes
  useEffect(() => {
    const newRows = convertTestCasesToRows(testCases);
    setRows(newRows);
  }, [testCases]);

  const handleCellChange = (index: number, field: keyof TestCaseRow, value: string) => {
    const newRows = rows.map((row, i) => 
      i === index ? { ...row, [field]: value } : row
    );
    setRows(newRows);
    onTestCasesChange?.(newRows);
  };

  const addNewRow = () => {
    const newId = `TC-${String(rows.length + 1).padStart(3, '0')}`;
    const newRow: TestCaseRow = {
      caseId: newId,
      step: '',
      expected: '',
      testData: '',
      comments: '',
      priority: 'Medium',
      userStory: ''
    };
    setRows(prev => [...prev, newRow]);
  };

  const deleteRow = (index: number) => {
    if (rows.length > 1) {
      setRows(prev => prev.filter((_, i) => i !== index));
    }
  };

  return (
    <div className="w-full space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Manual Test Cases</h3>
        <Button onClick={addNewRow} size="sm" variant="outline" className="gap-2">
          <Plus className="h-4 w-4" />
          Add Row
        </Button>
      </div>
      
      <ScrollArea className="h-[500px] rounded-md border border-border">
        <Table className="excel-style-table">
          <TableHeader className="bg-muted/50 sticky top-0 z-10">
            <TableRow>
              <TableHead className="w-[80px]">Case ID</TableHead>
              <TableHead className="w-[25%]">Test Step</TableHead>
              <TableHead className="w-[20%]">Expected Result</TableHead>
              <TableHead className="w-[10%]">Test Data</TableHead>
              <TableHead className="w-[15%]">Comments</TableHead>
              <TableHead className="w-[8%]">Priority</TableHead>
              <TableHead className="w-[15%]">User Story</TableHead>
              <TableHead className="w-[50px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.map((row, index) => (
              <TableRow key={index} className="excel-row hover:bg-muted/50">
                <TableCell className="font-medium">
                  <div 
                    contentEditable 
                    suppressContentEditableWarning 
                    className="p-1 border-none focus:ring-1 focus:ring-primary rounded-md min-h-[30px] hover:bg-card"
                    onBlur={(e) => handleCellChange(index, "caseId", e.currentTarget.textContent || "")}
                  >
                    {row.caseId}
                  </div>
                </TableCell>
                <TableCell>
                  <div 
                    contentEditable 
                    suppressContentEditableWarning 
                    className="p-1 border-none focus:ring-1 focus:ring-primary rounded-md min-h-[30px] hover:bg-card whitespace-pre-wrap"
                    onBlur={(e) => handleCellChange(index, "step", e.currentTarget.textContent || "")}
                  >
                    {row.step}
                  </div>
                </TableCell>
                <TableCell>
                  <div 
                    contentEditable 
                    suppressContentEditableWarning 
                    className="p-1 border-none focus:ring-1 focus:ring-primary rounded-md min-h-[30px] hover:bg-card whitespace-pre-wrap"
                    onBlur={(e) => handleCellChange(index, "expected", e.currentTarget.textContent || "")}
                  >
                    {row.expected}
                  </div>
                </TableCell>
                <TableCell>
                  <div 
                    contentEditable 
                    suppressContentEditableWarning 
                    className="p-1 border-none focus:ring-1 focus:ring-primary rounded-md min-h-[30px] hover:bg-card"
                    onBlur={(e) => handleCellChange(index, "testData", e.currentTarget.textContent || "")}
                  >
                    {row.testData}
                  </div>
                </TableCell>
                <TableCell>
                  <div 
                    contentEditable 
                    suppressContentEditableWarning 
                    className="p-1 border-none focus:ring-1 focus:ring-primary rounded-md min-h-[30px] hover:bg-card"
                    onBlur={(e) => handleCellChange(index, "comments", e.currentTarget.textContent || "")}
                  >
                    {row.comments}
                  </div>
                </TableCell>
                <TableCell>
                  <div 
                    contentEditable 
                    suppressContentEditableWarning 
                    className="p-1 border-none focus:ring-1 focus:ring-primary rounded-md min-h-[30px] hover:bg-card"
                    onBlur={(e) => handleCellChange(index, "priority", e.currentTarget.textContent || "")}
                  >
                    {row.priority}
                  </div>
                </TableCell>
                <TableCell>
                  <div 
                    contentEditable 
                    suppressContentEditableWarning 
                    className="p-1 border-none focus:ring-1 focus:ring-primary rounded-md min-h-[30px] hover:bg-card whitespace-pre-wrap"
                    onBlur={(e) => handleCellChange(index, "userStory", e.currentTarget.textContent || "")}
                  >
                    {row.userStory}
                  </div>
                </TableCell>
                <TableCell>
                  <Button 
                    onClick={() => deleteRow(index)} 
                    size="sm" 
                    variant="ghost"
                    className="h-8 w-8 p-0 hover:bg-destructive/10"
                    disabled={rows.length <= 1}
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </ScrollArea>
      
      <div className="text-sm text-muted-foreground">
        {rows.length} test case{rows.length !== 1 ? 's' : ''} • Click on any cell to edit • Use the Add Row button to create new test cases
      </div>
    </div>
  );
}
