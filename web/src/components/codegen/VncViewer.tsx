"use client";

import React, { useRef, useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Monitor, 
  Maximize2, 
  Minimize2, 
  RotateCcw, 
  Settings,
  ExternalLink
} from 'lucide-react';
import type { VncSessionInfo } from '@/lib/types';

interface VncViewerProps {
  vncInfo: VncSessionInfo;
  className?: string;
}

export function VncViewer({ vncInfo, className = "" }: VncViewerProps) {
  const vncRef = useRef<HTMLDivElement>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [rfb, setRfb] = useState<any>(null);

  useEffect(() => {
    let rfbConnection: any = null;

    const connectVnc = async () => {
      if (!vncRef.current) return;

      try {
        setConnectionStatus('connecting');
        
        // Importación dinámica de noVNC usando el entry point correcto
        // @ts-ignore - noVNC no tiene tipos TypeScript completos
        const RFB = (await import('@novnc/novnc/lib/rfb')).default;
        
        // Configuración de conexión VNC
        const url = `ws://localhost:${vncInfo.novnc_port}/websockify`;
        
        rfbConnection = new RFB(vncRef.current, url, {
          credentials: {},
          repeaterID: "",
          shared: true
        });

        // Event listeners
        rfbConnection.addEventListener('connect', () => {
          setIsConnected(true);
          setConnectionStatus('connected');
          console.log('VNC conectado exitosamente');
        });

        rfbConnection.addEventListener('disconnect', (e: any) => {
          setIsConnected(false);
          setConnectionStatus('disconnected');
          console.log('VNC desconectado:', e.detail);
        });

        rfbConnection.addEventListener('credentialsrequired', () => {
          console.log('VNC requiere credenciales');
          // En nuestro caso, usamos -nopw, así que esto no debería ocurrir
        });

        rfbConnection.addEventListener('securityfailure', (e: any) => {
          setConnectionStatus('error');
          console.error('Fallo de seguridad VNC:', e.detail);
        });

        setRfb(rfbConnection);

      } catch (error) {
        console.error('Error conectando VNC:', error);
        setConnectionStatus('error');
      }
    };

    connectVnc();

    return () => {
      if (rfbConnection) {
        rfbConnection.disconnect();
      }
    };
  }, [vncInfo.novnc_port]);

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      vncRef.current?.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  };

  const reconnect = () => {
    if (rfb) {
      rfb.disconnect();
    }
    // El useEffect se encargará de reconectar
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  const openExternalVnc = () => {
    window.open(vncInfo.web_vnc_url, '_blank');
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'default';
      case 'connecting': return 'secondary';
      case 'error': return 'destructive';
      default: return 'outline';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Conectado';
      case 'connecting': return 'Conectando...';
      case 'error': return 'Error de conexión';
      default: return 'Desconectado';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Monitor className="mr-2 h-4 w-4" />
              Acceso Remoto VNC
            </CardTitle>
            <CardDescription>
              Display :{vncInfo.display_number} - Puerto {vncInfo.vnc_port}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={getStatusColor()}>
              {getStatusText()}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Controles */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={reconnect}
              disabled={connectionStatus === 'connecting'}
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Reconectar
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={toggleFullscreen}
              disabled={!isConnected}
            >
              {isFullscreen ? (
                <Minimize2 className="mr-2 h-4 w-4" />
              ) : (
                <Maximize2 className="mr-2 h-4 w-4" />
              )}
              {isFullscreen ? 'Salir' : 'Pantalla completa'}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={openExternalVnc}
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              Ventana externa
            </Button>
          </div>

          {/* Área de visualización VNC */}
          <div className="border rounded-lg overflow-hidden bg-black">
            <div 
              ref={vncRef}
              className="w-full min-h-[400px] relative"
              style={{ 
                minHeight: isFullscreen ? '100vh' : '400px',
                background: connectionStatus === 'connected' ? 'transparent' : '#000'
              }}
            >
              {connectionStatus === 'connecting' && (
                <div className="absolute inset-0 flex items-center justify-center text-white">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                    <p>Conectando a VNC...</p>
                  </div>
                </div>
              )}
              
              {connectionStatus === 'error' && (
                <div className="absolute inset-0 flex items-center justify-center text-white">
                  <div className="text-center">
                    <p className="text-red-400 mb-2">Error de conexión VNC</p>
                    <Button onClick={reconnect} variant="outline" size="sm">
                      Reintentar
                    </Button>
                  </div>
                </div>
              )}
              
              {connectionStatus === 'disconnected' && (
                <div className="absolute inset-0 flex items-center justify-center text-white">
                  <div className="text-center">
                    <p className="text-gray-400 mb-2">VNC desconectado</p>
                    <Button onClick={reconnect} variant="outline" size="sm">
                      Conectar
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Información adicional */}
          <div className="text-xs text-muted-foreground space-y-1">
            <p><strong>URL VNC:</strong> {vncInfo.vnc_url}</p>
            <p><strong>Web VNC:</strong> {vncInfo.web_vnc_url}</p>
            <p><strong>Estado:</strong> {vncInfo.status}</p>
            <p><strong>Creado:</strong> {new Date(vncInfo.created_at).toLocaleString()}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
