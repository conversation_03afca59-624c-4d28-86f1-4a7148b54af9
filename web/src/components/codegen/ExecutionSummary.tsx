import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  Zap, 
  DollarSign, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Activity,
  Target
} from 'lucide-react';
import { CodegenExecutionSummary } from '@/lib/types';

interface ExecutionSummaryProps {
  summary: CodegenExecutionSummary;
}

export function ExecutionSummary({ summary }: ExecutionSummaryProps) {
  const getStatusIcon = () => {
    if (summary.has_errors) {
      return <XCircle className="h-4 w-4 text-red-500" />;
    }
    if (summary.is_successful) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    return <AlertCircle className="h-4 w-4 text-yellow-500" />;
  };

  const getStatusBadge = () => {
    if (summary.has_errors) {
      return <Badge variant="destructive">Failed</Badge>;
    }
    if (summary.is_successful) {
      return <Badge variant="default" className="bg-green-600">Success</Badge>;
    }
    return <Badge variant="secondary">Partial</Badge>;
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  const formatCost = (cost: number) => {
    if (cost < 0.01) {
      return `$${(cost * 1000).toFixed(2)}k`;
    }
    return `$${cost.toFixed(4)}`;
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-lg">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            Execution Summary
          </div>
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex items-center gap-2">
            <Target className="h-4 w-4 text-blue-500" />
            <div className="text-sm">
              <div className="font-medium">{summary.total_steps}</div>
              <div className="text-muted-foreground">Steps</div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-purple-500" />
            <div className="text-sm">
              <div className="font-medium">{formatDuration(summary.total_duration_seconds)}</div>
              <div className="text-muted-foreground">Duration</div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4 text-green-500" />
            <div className="text-sm">
              <div className="font-medium">
                {(summary.total_input_tokens + summary.total_output_tokens).toLocaleString()}
              </div>
              <div className="text-muted-foreground">Total Tokens</div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-yellow-500" />
            <div className="text-sm">
              <div className="font-medium">{formatCost(summary.total_cost)}</div>
              <div className="text-muted-foreground">Est. Cost</div>
            </div>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-muted-foreground">Input Tokens</div>
              <div className="font-medium">{summary.total_input_tokens.toLocaleString()}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Output Tokens</div>
              <div className="font-medium">{summary.total_output_tokens.toLocaleString()}</div>
            </div>
          </div>
        </div>

        {summary.final_url && (
          <div className="mt-4 pt-4 border-t">
            <div className="text-sm">
              <div className="text-muted-foreground">Final URL</div>
              <div className="font-mono text-xs truncate" title={summary.final_url}>
                {summary.final_url}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 