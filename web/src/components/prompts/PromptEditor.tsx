"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { 
  Languages, 
  Copy, 
  Eye, 
  Edit3, 
  ArrowRight, 
  Sparkles,
  FileText,
  Save,
  RefreshCw,
  Info
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface PromptEditorProps {
  initialSpanishText?: string;
  initialEnglishText?: string;
  onSave?: (spanishText: string, englishText: string) => void;
  promptType?: string;
}

export function PromptEditor({ 
  initialSpanishText = "", 
  initialEnglishText = "",
  onSave,
  promptType = "generic"
}: PromptEditorProps) {
  const [spanishText, setSpanishText] = useState(initialSpanishText);
  const [englishText, setEnglishText] = useState(initialEnglishText);
  const [isTranslating, setIsTranslating] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const { toast } = useToast();

  // Auto-translate from Spanish to English
  const translateToEnglish = async () => {
    if (!spanishText.trim()) {
      toast({
        title: "Nothing to translate",
        description: "Please enter some Spanish text first",
        variant: "destructive"
      });
      return;
    }

    setIsTranslating(true);
    try {
      // TODO: Integrate with your AI translation service
      const response = await fetch('/api/translate', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'ngrok-skip-browser-warning': 'true'
        },
        body: JSON.stringify({
          text: spanishText,
          from: 'es',
          to: 'en',
          context: 'prompt_instruction'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setEnglishText(data.translatedText);
        toast({
          title: "Translation completed",
          description: "Spanish text has been translated to English"
        });
      } else {
        throw new Error('Translation failed');
      }
    } catch (error) {
      // Fallback: simple notification
      toast({
        title: "Translation service unavailable",
        description: "Please translate manually or check your connection",
        variant: "destructive"
      });
    } finally {
      setIsTranslating(false);
    }
  };

  const handleSave = () => {
    if (onSave) {
      onSave(spanishText, englishText);
    }
    toast({
      title: "Prompt saved",
      description: "Your prompt changes have been saved successfully"
    });
  };

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: `${type} prompt copied successfully`
    });
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FileText className="h-6 w-6" />
            <div>
              <h2 className="text-2xl font-bold">Prompt Editor</h2>
              <p className="text-sm text-muted-foreground">
                Write prompts in Spanish and translate to English for consistency
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <Info className="h-3 w-3" />
              {promptType}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPreviewMode(!previewMode)}
            >
              {previewMode ? (
                <>
                  <Edit3 className="h-4 w-4 mr-2" />
                  Edit
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </>
              )}
            </Button>
          </div>
        </div>

        {previewMode ? (
          /* Preview Mode */
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  🇪🇸 Spanish (Draft)
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(spanishText, "Spanish")}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none bg-muted/30 p-4 rounded-md min-h-[300px]">
                  {spanishText ? (
                    <pre className="whitespace-pre-wrap font-sans text-sm">
                      {spanishText}
                    </pre>
                  ) : (
                    <p className="text-muted-foreground italic">No Spanish content yet...</p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  🇺🇸 English (Production)
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(englishText, "English")}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none bg-muted/30 p-4 rounded-md min-h-[300px]">
                  {englishText ? (
                    <pre className="whitespace-pre-wrap font-sans text-sm">
                      {englishText}
                    </pre>
                  ) : (
                    <p className="text-muted-foreground italic">No English content yet...</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          /* Edit Mode */
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Spanish Input */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    🇪🇸 Spanish (Draft)
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Write your prompt in Spanish first. This helps with initial conceptualization.</p>
                      </TooltipContent>
                    </Tooltip>
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(spanishText, "Spanish")}
                    disabled={!spanishText}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Label htmlFor="spanish-prompt">Spanish Prompt Content</Label>
                  <Textarea
                    id="spanish-prompt"
                    placeholder="Escribe tu prompt en español aquí... Por ejemplo:

Eres un asistente de pruebas automatizadas. Tu tarea es...

Instrucciones:
1. Analiza el escenario proporcionado
2. Genera los pasos necesarios
3. Verifica los resultados esperados

Escenario: {scenario_text}"
                    value={spanishText}
                    onChange={(e) => setSpanishText(e.target.value)}
                    className="min-h-[300px] font-mono text-sm"
                  />
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>{spanishText.length} characters</span>
                    <span>Draft content</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* English Output */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    🇺🇸 English (Production)
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>This is the final English version that will be used by the AI agents.</p>
                      </TooltipContent>
                    </Tooltip>
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={translateToEnglish}
                      disabled={isTranslating || !spanishText.trim()}
                    >
                      {isTranslating ? (
                        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <ArrowRight className="h-4 w-4 mr-2" />
                      )}
                      Translate
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(englishText, "English")}
                      disabled={!englishText}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Label htmlFor="english-prompt">English Prompt Content</Label>
                  <Textarea
                    id="english-prompt"
                    placeholder="English version will appear here after translation, or you can write directly...

You are an automated testing assistant. Your task is to...

Instructions:
1. Analyze the provided scenario
2. Generate necessary steps  
3. Verify expected results

Scenario: {scenario_text}"
                    value={englishText}
                    onChange={(e) => setEnglishText(e.target.value)}
                    className="min-h-[300px] font-mono text-sm"
                  />
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>{englishText.length} characters</span>
                    <span>Production content</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Translation Flow Indicator */}
        {!previewMode && (
          <div className="flex items-center justify-center py-4">
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <span>🇪🇸 Spanish Draft</span>
              </div>
              <ArrowRight className="h-4 w-4" />
              <div className="flex items-center gap-2">
                <Languages className="h-4 w-4" />
                <span>AI Translation</span>
              </div>
              <ArrowRight className="h-4 w-4" />
              <div className="flex items-center gap-2">
                <span>🇺🇸 English Production</span>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Sparkles className="h-4 w-4" />
            <span>
              Instructions will always be in English, responses will be in the user's selected language
            </span>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" onClick={() => {
              setSpanishText("");
              setEnglishText("");
            }}>
              Clear All
            </Button>
            <Button onClick={handleSave} disabled={!englishText.trim()}>
              <Save className="h-4 w-4 mr-2" />
              Save Prompt
            </Button>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}
