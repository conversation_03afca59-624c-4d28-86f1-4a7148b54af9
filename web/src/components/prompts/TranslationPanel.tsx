"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Languages, 
  ArrowRight, 
  Copy, 
  CheckCircle, 
  Loader2,
  RefreshCw,
  AlertCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface TranslationPanelProps {
  originalText: string;
  onTranslatedTextChange: (translatedText: string) => void;
  sectionName: string;
  sourceLanguage?: "es" | "en";
  targetLanguage?: "es" | "en";
}

export function TranslationPanel({
  originalText,
  onTranslatedTextChange,
  sectionName,
  sourceLanguage = "es",
  targetLanguage = "en"
}: TranslationPanelProps) {
  const [translatedText, setTranslatedText] = useState("");
  const [isTranslating, setIsTranslating] = useState(false);
  const [activeTab, setActiveTab] = useState("translate");
  const { toast } = useToast();

  const handleTranslate = async () => {
    if (!originalText.trim()) {
      toast({
        title: "No content to translate",
        description: `Please enter some ${sourceLanguage === "es" ? "Spanish" : "English"} text first`,
        variant: "destructive"
      });
      return;
    }

    setIsTranslating(true);
    try {
      const response = await fetch('/api/translate', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'ngrok-skip-browser-warning': 'true'
        },
        body: JSON.stringify({
          text: originalText,
          from: sourceLanguage,
          to: targetLanguage,
          context: `prompt_section_${sectionName}`
        })
      });

      if (!response.ok) {
        throw new Error(`Translation failed: ${response.status}`);
      }

      const data = await response.json();
      setTranslatedText(data.translatedText || "");
      
      toast({
        title: "Translation completed",
        description: `${sectionName} has been translated successfully`,
        duration: 3000
      });
    } catch (error) {
      console.error('Translation error:', error);
      toast({
        title: "Translation failed",
        description: "Please check your connection and try again",
        variant: "destructive"
      });
    } finally {
      setIsTranslating(false);
    }
  };

  const handleApplyTranslation = () => {
    if (translatedText.trim()) {
      onTranslatedTextChange(translatedText);
      toast({
        title: "Translation applied",
        description: `${sectionName} content updated with translation`,
        duration: 2000
      });
    }
  };

  const handleCopyTranslation = async () => {
    if (translatedText.trim()) {
      try {
        await navigator.clipboard.writeText(translatedText);
        toast({
          title: "Copied to clipboard",
          description: "Translation copied successfully",
          duration: 2000
        });
      } catch (error) {
        toast({
          title: "Copy failed",
          description: "Unable to copy to clipboard",
          variant: "destructive"
        });
      }
    }
  };

  const handleTextChange = (value: string) => {
    setTranslatedText(value);
  };

  const getLanguageLabel = (lang: string) => {
    return lang === "es" ? "Spanish" : "English";
  };

  return (
    <Card className="mt-4 border-2 border-blue-200">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2 text-blue-700">
          <Languages className="h-5 w-5" />
          Translation Panel - {sectionName}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="translate">Translate</TabsTrigger>
            <TabsTrigger value="compare">Compare</TabsTrigger>
          </TabsList>

          <TabsContent value="translate" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Badge variant="outline">{getLanguageLabel(sourceLanguage)}</Badge>
                <ArrowRight className="h-4 w-4 text-muted-foreground" />
                <Badge variant="outline">{getLanguageLabel(targetLanguage)}</Badge>
              </div>
              <Button
                onClick={handleTranslate}
                disabled={isTranslating || !originalText.trim()}
                className="gap-2"
                size="sm"
              >
                {isTranslating ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Languages className="h-4 w-4" />
                )}
                {isTranslating ? "Translating..." : "Translate"}
              </Button>
            </div>

            {translatedText && (
              <div className="space-y-3">
                <Textarea
                  value={translatedText}
                  onChange={(e) => handleTextChange(e.target.value)}
                  className="min-h-[200px] font-mono text-sm"
                  placeholder="Translation will appear here..."
                />
                <div className="flex items-center gap-2">
                  <Button
                    onClick={handleApplyTranslation}
                    size="sm"
                    className="gap-2"
                  >
                    <CheckCircle className="h-4 w-4" />
                    Apply Translation
                  </Button>
                  <Button
                    onClick={handleCopyTranslation}
                    variant="outline"
                    size="sm"
                    className="gap-2"
                  >
                    <Copy className="h-4 w-4" />
                    Copy
                  </Button>
                  <Button
                    onClick={() => setTranslatedText("")}
                    variant="ghost"
                    size="sm"
                    className="gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Clear
                  </Button>
                </div>
              </div>
            )}

            {!translatedText && !isTranslating && (
              <div className="text-center py-8 text-muted-foreground">
                <Languages className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>Click "Translate" to generate {getLanguageLabel(targetLanguage)} translation</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="compare" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{getLanguageLabel(sourceLanguage)} (Original)</Badge>
                </div>
                <Textarea
                  value={originalText}
                  readOnly
                  className="min-h-[300px] font-mono text-sm bg-muted/50"
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{getLanguageLabel(targetLanguage)} (Translation)</Badge>
                  {translatedText && (
                    <Badge variant="outline" className="text-green-600">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Ready
                    </Badge>
                  )}
                </div>
                <Textarea
                  value={translatedText}
                  onChange={(e) => handleTextChange(e.target.value)}
                  className="min-h-[300px] font-mono text-sm"
                  placeholder={`${getLanguageLabel(targetLanguage)} translation will appear here...`}
                />
              </div>
            </div>
            
            {translatedText && (
              <div className="flex items-center gap-2 justify-center">
                <Button
                  onClick={handleApplyTranslation}
                  size="sm"
                  className="gap-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  Apply Translation to Section
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
