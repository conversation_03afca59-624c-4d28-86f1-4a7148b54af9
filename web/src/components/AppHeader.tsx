
"use client";

import { SidebarTrigger } from "@/components/ui/sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Sun, Moon, Github } from "lucide-react";
import { useTheme } from "next-themes"; // Assuming next-themes is or will be installed
import Link from "next/link";

// Placeholder for ThemeProvider setup - typically in layout.tsx or a similar top-level component
// import { ThemeProvider as NextThemesProvider } from "next-themes"
// ...
// <NextThemesProvider attribute="class" defaultTheme="dark" enableSystem>
// ...
// </NextThemesProvider>

export function AppHeader() {
  // const { setTheme, theme } = useTheme(); // Uncomment when next-themes is integrated

  return (
    <header className="sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-background/80 backdrop-blur-sm px-4 md:px-6">
      <div className="md:hidden">
        <SidebarTrigger />
      </div>
      <div className="flex-1">
        {/* Can add breadcrumbs or page title here */}
      </div>
      <div className="flex items-center gap-2">
        {/* 
        // Theme Toggle - requires next-themes setup
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setTheme(theme === "light" ? "dark" : "light")}
          aria-label="Toggle theme"
        >
          <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
        </Button> 
        */}
        <Button variant="ghost" size="icon" asChild>
          <Link href="https://github.com/nahuelcio" target="_blank" aria-label="Nahuel Cio GitHub Profile">
            <Github className="h-5 w-5" />
          </Link>
        </Button>
      </div>
    </header>
  );
}
