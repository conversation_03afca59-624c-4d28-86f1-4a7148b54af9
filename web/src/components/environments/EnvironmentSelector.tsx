"use client";

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { getProjectEnvironments } from '@/lib/api';
import type { Environment } from '@/lib/types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Globe, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EnvironmentSelectorProps {
  projectId: string;
  selectedEnvId?: string;
  onSelect: (envId: string) => void;
  className?: string;
  label?: string;
  showLabel?: boolean;
  disabled?: boolean;
}

export function EnvironmentSelector({
  projectId,
  selectedEnvId,
  onSelect,
  className,
  label = "Environment",
  showLabel = true,
  disabled = false
}: EnvironmentSelectorProps) {
  const { data: environments, isLoading, error } = useQuery({
    queryKey: ['environments', projectId],
    queryFn: () => getProjectEnvironments(projectId),
    enabled: !!projectId,
  });

  // Don't show selector if no environments or only one environment
  if (!environments || environments.length <= 1) {
    return null;
  }

  // Find selected environment for display
  const selectedEnvironment = environments.find(env => env.env_id === selectedEnvId);
  const defaultEnvironment = environments.find(env => env.is_default);

  return (
    <div className={cn("space-y-2", className)}>
      {showLabel && (
        <Label htmlFor="environment-selector" className="text-sm font-medium flex items-center gap-2">
          <Globe className="h-4 w-4" />
          {label}
        </Label>
      )}
      
      <Select
        value={selectedEnvId || ''}
        onValueChange={onSelect}
        disabled={disabled || isLoading}
      >
        <SelectTrigger className="w-full" id="environment-selector">
          <SelectValue placeholder={
            isLoading 
              ? "Loading environments..." 
              : defaultEnvironment 
                ? `Select environment (default: ${defaultEnvironment.name})` 
                : "Select environment"
          }>
            {selectedEnvironment && (
              <div className="flex items-center gap-2">
                <Badge variant={selectedEnvironment.is_default ? "default" : "secondary"}>
                  {selectedEnvironment.name}
                </Badge>
                {selectedEnvironment.is_default && (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                )}
                <span className="text-xs text-muted-foreground max-w-48 truncate">
                  {selectedEnvironment.base_url}
                </span>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent>
          {environments.map((env) => (
            <SelectItem key={env.env_id} value={env.env_id}>
              <div className="flex items-center gap-2 w-full">
                <div className="flex items-center gap-2">
                  <Badge variant={env.is_default ? "default" : "secondary"}>
                    {env.name}
                  </Badge>
                  {env.is_default && (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  )}
                </div>
                <span className="text-xs text-muted-foreground max-w-32 truncate ml-auto">
                  {env.base_url}
                </span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {error && (
        <p className="text-sm text-destructive">
          Failed to load environments
        </p>
      )}
      
      {selectedEnvironment && (
        <div className="text-xs text-muted-foreground space-y-1">
          <div>
            <strong>URL:</strong> {selectedEnvironment.base_url || "No base URL"}
          </div>
          {selectedEnvironment.description && (
            <div>
              <strong>Description:</strong> {selectedEnvironment.description}
            </div>
          )}
          {selectedEnvironment.tags.length > 0 && (
            <div className="flex items-center gap-1 flex-wrap">
              <strong>Tags:</strong>
              {selectedEnvironment.tags.map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default EnvironmentSelector;