"use client";

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  getProjectEnvironments, 
  createProjectEnvironment, 
  updateProjectEnvironment, 
  deleteProjectEnvironment,
  setDefaultEnvironment,
  testEnvironmentUrl
} from '@/lib/api';
import type { Environment, EnvironmentCreateInput, EnvironmentUpdateInput } from '@/lib/types';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Plus, 
  Settings, 
  Trash2, 
  CheckCircle, 
  Globe,
  ExternalLink,
  Star,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface EnvironmentManagementProps {
  projectId: string;
}

interface EnvironmentFormData {
  name: string;
  description: string;
  base_url: string;
  is_default: boolean;
  tags: string;
  // User-friendly configuration fields
  headless: boolean;
  wait_time: string;
  timeout: string;
  viewport_width: string;
  viewport_height: string;
  user_agent: string;
  // Advanced JSON config for additional settings
  config_overrides: string;
}

const initialFormData: EnvironmentFormData = {
  name: '',
  description: '',
  base_url: '',
  is_default: false,
  tags: '',
  headless: false,
  wait_time: '',
  timeout: '',
  viewport_width: '',
  viewport_height: '',
  user_agent: '',
  config_overrides: '',
};

export function EnvironmentManagement({ projectId }: EnvironmentManagementProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingEnv, setEditingEnv] = useState<Environment | null>(null);
  const [formData, setFormData] = useState<EnvironmentFormData>(initialFormData);
  const [testingUrl, setTestingUrl] = useState<string | null>(null);
  
  const queryClient = useQueryClient();

  // Query environments
  const { 
    data: environments, 
    isLoading, 
    error 
  } = useQuery({
    queryKey: ['environments', projectId],
    queryFn: () => getProjectEnvironments(projectId),
    enabled: !!projectId,
  });

  // Create environment mutation
  const createMutation = useMutation({
    mutationFn: (data: EnvironmentCreateInput) => createProjectEnvironment(projectId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['environments', projectId] });
      setIsDialogOpen(false);
      setFormData(initialFormData);
      toast({
        title: "Environment created",
        description: "The environment has been created successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error creating environment",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update environment mutation
  const updateMutation = useMutation({
    mutationFn: ({ envId, data }: { envId: string; data: EnvironmentUpdateInput }) => 
      updateProjectEnvironment(projectId, envId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['environments', projectId] });
      setIsDialogOpen(false);
      setEditingEnv(null);
      setFormData(initialFormData);
      toast({
        title: "Environment updated",
        description: "The environment has been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error updating environment",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete environment mutation
  const deleteMutation = useMutation({
    mutationFn: (envId: string) => deleteProjectEnvironment(projectId, envId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['environments', projectId] });
      toast({
        title: "Environment deleted",
        description: "The environment has been deleted successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error deleting environment",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Set default environment mutation
  const setDefaultMutation = useMutation({
    mutationFn: (envId: string) => setDefaultEnvironment(projectId, envId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['environments', projectId] });
      toast({
        title: "Default environment set",
        description: "The default environment has been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error setting default environment",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Test URL mutation
  const testUrlMutation = useMutation({
    mutationFn: ({ envId, path }: { envId: string; path: string }) => 
      testEnvironmentUrl(projectId, envId, path),
    onSuccess: (result) => {
      toast({
        title: `URL Test ${result.accessible ? 'Successful' : 'Failed'}`,
        description: `${result.full_url} is ${result.accessible ? 'accessible' : 'not accessible'}${result.status_code ? ` (${result.status_code})` : ''}`,
        variant: result.accessible ? "default" : "destructive",
      });
    },
    onError: (error) => {
      toast({
        title: "Error testing URL",
        description: error.message,
        variant: "destructive",
      });
    },
    onSettled: () => {
      setTestingUrl(null);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const configOverrides = formData.config_overrides 
        ? JSON.parse(formData.config_overrides) 
        : {};
      
      const tags = formData.tags 
        ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean)
        : [];

      const envData = {
        name: formData.name,
        description: formData.description,
        base_url: formData.base_url,
        is_default: formData.is_default,
        // User-friendly configuration fields
        headless: formData.headless,
        wait_time: formData.wait_time ? parseFloat(formData.wait_time) : undefined,
        timeout: formData.timeout ? parseInt(formData.timeout) : undefined,
        viewport_width: formData.viewport_width ? parseInt(formData.viewport_width) : undefined,
        viewport_height: formData.viewport_height ? parseInt(formData.viewport_height) : undefined,
        user_agent: formData.user_agent || undefined,
        config_overrides: configOverrides,
        tags,
      };

      if (editingEnv) {
        updateMutation.mutate({ envId: editingEnv.env_id, data: envData });
      } else {
        createMutation.mutate(envData);
      }
    } catch (error) {
      toast({
        title: "Invalid configuration",
        description: "Please check the configuration JSON format.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (env: Environment) => {
    setEditingEnv(env);
    setFormData({
      name: env.name,
      description: env.description,
      base_url: env.base_url,
      is_default: env.is_default,
      tags: env.tags.join(', '),
      // User-friendly configuration fields
      headless: env.headless ?? false,
      wait_time: env.wait_time?.toString() || '',
      timeout: env.timeout?.toString() || '',
      viewport_width: env.viewport_width?.toString() || '',
      viewport_height: env.viewport_height?.toString() || '',
      user_agent: env.user_agent || '',
      config_overrides: JSON.stringify(env.config_overrides, null, 2),
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (envId: string) => {
    if (confirm('Are you sure you want to delete this environment?')) {
      deleteMutation.mutate(envId);
    }
  };

  const handleTestUrl = (envId: string) => {
    setTestingUrl(envId);
    testUrlMutation.mutate({ envId, path: '/' });
  };

  const resetForm = () => {
    setFormData(initialFormData);
    setEditingEnv(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
        <p className="text-destructive">Failed to load environments</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Environments</h2>
          <p className="text-muted-foreground">
            Manage test execution environments for this project
          </p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={(open) => {
          setIsDialogOpen(open);
          if (!open) resetForm();
        }}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Environment
            </Button>
          </DialogTrigger>
          
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingEnv ? 'Edit Environment' : 'Create Environment'}
              </DialogTitle>
              <DialogDescription>
                {editingEnv 
                  ? 'Update the environment configuration'
                  : 'Add a new environment for test execution'
                }
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Development, QA, Production"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="base_url">Base URL</Label>
                  <Input
                    id="base_url"
                    value={formData.base_url}
                    onChange={(e) => setFormData(prev => ({ ...prev, base_url: e.target.value }))}
                    placeholder="https://example.com"
                    type="url"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Environment description"
                  rows={2}
                />
              </div>
              
              {/* Browser Configuration */}
              <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <Label className="text-sm font-medium">Browser Configuration</Label>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="headless" className="text-xs">Headless Mode</Label>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="headless"
                        checked={formData.headless}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, headless: checked }))}
                      />
                      <Label htmlFor="headless" className="text-xs text-muted-foreground">
                        Run browser without UI
                      </Label>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="wait_time" className="text-xs">Wait Time (seconds)</Label>
                    <Input
                      id="wait_time"
                      type="number"
                      step="0.1"
                      value={formData.wait_time}
                      onChange={(e) => setFormData(prev => ({ ...prev, wait_time: e.target.value }))}
                      placeholder="2.0"
                      className="h-8"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="timeout" className="text-xs">Timeout (ms)</Label>
                    <Input
                      id="timeout"
                      type="number"
                      value={formData.timeout}
                      onChange={(e) => setFormData(prev => ({ ...prev, timeout: e.target.value }))}
                      placeholder="30000"
                      className="h-8"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="viewport_width" className="text-xs">Viewport Width</Label>
                    <Input
                      id="viewport_width"
                      type="number"
                      value={formData.viewport_width}
                      onChange={(e) => setFormData(prev => ({ ...prev, viewport_width: e.target.value }))}
                      placeholder="1920"
                      className="h-8"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="viewport_height" className="text-xs">Viewport Height</Label>
                    <Input
                      id="viewport_height"
                      type="number"
                      value={formData.viewport_height}
                      onChange={(e) => setFormData(prev => ({ ...prev, viewport_height: e.target.value }))}
                      placeholder="1080"
                      className="h-8"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="user_agent" className="text-xs">User Agent</Label>
                    <Input
                      id="user_agent"
                      value={formData.user_agent}
                      onChange={(e) => setFormData(prev => ({ ...prev, user_agent: e.target.value }))}
                      placeholder="Mozilla/5.0 (compatible; QAK-Bot)"
                      className="h-8"
                    />
                  </div>
                </div>
              </div>
              
              {/* Tags and Advanced Configuration */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tags">Tags (comma-separated)</Label>
                  <Input
                    id="tags"
                    value={formData.tags}
                    onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                    placeholder="dev, internal, testing"
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_default"
                    checked={formData.is_default}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_default: checked }))}
                  />
                  <Label htmlFor="is_default">Set as default environment</Label>
                </div>
              </div>
              
              {/* Advanced Configuration */}
              <div className="space-y-3 p-4 border rounded-lg bg-muted/10">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-secondary rounded-full"></div>
                  <Label htmlFor="config_overrides" className="text-sm font-medium">
                    Advanced Configuration (JSON)
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground">
                  Additional browser settings that override the fields above
                </p>
                <Textarea
                  id="config_overrides"
                  value={formData.config_overrides}
                  onChange={(e) => setFormData(prev => ({ ...prev, config_overrides: e.target.value }))}
                  placeholder='{"cookies": {...}, "proxy": {...}}'
                  rows={4}
                  className="font-mono text-xs"
                />
              </div>
              
              <DialogFooter className="flex gap-2 pt-6 border-t">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={createMutation.isPending || updateMutation.isPending}
                  className="min-w-[100px]"
                >
                  {(createMutation.isPending || updateMutation.isPending) && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  {editingEnv ? 'Update Environment' : 'Create Environment'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {!environments || environments.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No environments configured</h3>
            <p className="text-muted-foreground mb-4">
              Create your first environment to manage test execution across different environments.
            </p>
            <Button onClick={() => setIsDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Environment
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {environments.map((env) => (
            <Card key={env.env_id} className={cn(
              "relative",
              env.is_default && "ring-2 ring-primary"
            )}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {env.name}
                    {env.is_default && (
                      <Badge variant="default" className="text-xs">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Default
                      </Badge>
                    )}
                  </CardTitle>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleTestUrl(env.env_id)}
                      disabled={testingUrl === env.env_id}
                      title="Test URL accessibility"
                    >
                      {testingUrl === env.env_id ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <ExternalLink className="h-3 w-3" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(env)}
                    >
                      <Settings className="h-3 w-3" />
                    </Button>
                    {!env.is_default && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(env.env_id)}
                        disabled={deleteMutation.isPending}
                      >
                        <Trash2 className="h-3 w-3 text-destructive" />
                      </Button>
                    )}
                  </div>
                </div>
                {env.description && (
                  <CardDescription>{env.description}</CardDescription>
                )}
              </CardHeader>
              
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-xs font-medium text-muted-foreground">Base URL</Label>
                  <p className="text-sm break-all">{env.base_url || "No base URL"}</p>
                </div>
                
                {env.tags.length > 0 && (
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">Tags</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {env.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                {Object.keys(env.config_overrides).length > 0 && (
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">Configuration</Label>
                    <p className="text-xs text-muted-foreground">
                      {Object.keys(env.config_overrides).length} override(s)
                    </p>
                  </div>
                )}
                
                {!env.is_default && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setDefaultMutation.mutate(env.env_id)}
                    disabled={setDefaultMutation.isPending}
                    className="w-full"
                  >
                    <Star className="h-3 w-3 mr-2" />
                    Set as Default
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

export default EnvironmentManagement;