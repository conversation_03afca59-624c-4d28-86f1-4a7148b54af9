{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "regions": ["iad1"], "headers": [{"source": "/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}], "rewrites": [{"source": "/api/:path*", "destination": "https://a485-152-171-115-184.ngrok-free.app/api/:path*"}]}