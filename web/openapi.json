{"openapi": "3.1.0", "info": {"title": "QA Agent API", "description": "API para automatizaciu00f3n de pruebas, generaciu00f3n de casos de prueba y gestiou00f3n de proyectos.", "version": "1.0.0"}, "paths": {"/api/projects/": {"get": {"tags": ["projects"], "summary": "Listar proyectos", "description": "Obtiene todos los proyectos.", "operationId": "get_all_projects_api_projects__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse"}}}}}}, "post": {"tags": ["projects"], "summary": "<PERSON><PERSON>r proyecto", "description": "Crea un nuevo proyecto.", "operationId": "create_project_api_projects__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/projects/{project_id}": {"get": {"tags": ["projects"], "summary": "Obtener proyecto", "description": "Obtiene un proyecto por su ID.", "operationId": "get_project_api_projects__project_id__get", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["projects"], "summary": "Actualizar proyecto", "description": "Actualiza un proyecto existente.", "operationId": "update_project_api_projects__project_id__put", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectUpdateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["projects"], "summary": "Eliminar proyecto", "description": "Elimina un proyecto.", "operationId": "delete_project_api_projects__project_id__delete", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/projects/{project_id}/suites": {"get": {"tags": ["projects"], "summary": "Listar suites del proyecto", "description": "Obtiene todas las suites de pruebas de un proyecto.", "operationId": "get_project_suites_api_projects__project_id__suites_get", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/projects/{project_id}/suites/{suite_id}/tests": {"get": {"tags": ["projects"], "summary": "Listar casos de prueba de la suite", "description": "Obtiene todos los casos de prueba de una suite.", "operationId": "get_suite_test_cases_api_projects__project_id__suites__suite_id__tests_get", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "suite_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Suite Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/projects/{project_id}/suites/{suite_id}/execute": {"post": {"tags": ["test-suites"], "summary": "Ejecutar suite completa", "description": "Ejecuta todos los casos de prueba de una suite.", "operationId": "execute_test_suite_api_projects__project_id__suites__suite_id__execute_post", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "suite_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Suite Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuiteExecutionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/projects/{project_id}/suites/{suite_id}/tests/{test_id}/execute": {"post": {"tags": ["test-cases"], "summary": "Ejecutar caso de <PERSON>", "description": "Ejecuta un caso de prueba específico.", "operationId": "execute_test_case_api_projects__project_id__suites__suite_id__tests__test_id__execute_post", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "suite_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Suite Id"}}, {"name": "test_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Test Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestExecutionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/projects/{project_id}/suites/": {"post": {"tags": ["test-suites"], "summary": "Crear suite de pruebas", "description": "Crea una nueva suite de pruebas en un proyecto.", "operationId": "create_test_suite_api_projects__project_id__suites__post", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestSuiteCreateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestSuiteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/projects/{project_id}/suites/{suite_id}": {"get": {"tags": ["test-suites"], "summary": "Obtener suite de pruebas", "description": "Obtiene una suite de pruebas por su ID.", "operationId": "get_test_suite_api_projects__project_id__suites__suite_id__get", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "suite_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Suite Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestSuiteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["test-suites"], "summary": "Actualizar suite de pruebas", "description": "Actualiza una suite de pruebas existente.", "operationId": "update_test_suite_api_projects__project_id__suites__suite_id__put", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "suite_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Suite Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestSuiteUpdateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestSuiteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["test-suites"], "summary": "Eliminar suite de pruebas", "description": "Elimina una suite de pruebas.", "operationId": "delete_test_suite_api_projects__project_id__suites__suite_id__delete", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "suite_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Suite Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/projects/{project_id}/suites/{suite_id}/tests/": {"post": {"tags": ["test-cases"], "summary": "<PERSON><PERSON><PERSON> caso de <PERSON>", "description": "Crea un nuevo caso de prueba en una suite.", "operationId": "create_test_case_api_projects__project_id__suites__suite_id__tests__post", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "suite_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Suite Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseCreateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/projects/{project_id}/suites/{suite_id}/tests/{test_id}": {"get": {"tags": ["test-cases"], "summary": "Obtener caso de prueba", "description": "Obtiene un caso de prueba por su ID.", "operationId": "get_test_case_api_projects__project_id__suites__suite_id__tests__test_id__get", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "suite_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Suite Id"}}, {"name": "test_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Test Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["test-cases"], "summary": "Actualizar caso de <PERSON>", "description": "Actualiza un caso de prueba existente.", "operationId": "update_test_case_api_projects__project_id__suites__suite_id__tests__test_id__put", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "suite_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Suite Id"}}, {"name": "test_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Test Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseUpdateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["test-cases"], "summary": "Eliminar caso de p<PERSON>ba", "description": "Elimina un caso de prueba.", "operationId": "delete_test_case_api_projects__project_id__suites__suite_id__tests__test_id__delete", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "suite_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Suite Id"}}, {"name": "test_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Test Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/projects/{project_id}/suites/{suite_id}/tests/{test_id}/status": {"patch": {"tags": ["test-cases"], "summary": "Actualizar estado del caso de prueba", "description": "Actualiza el estado de un caso de prueba.", "operationId": "update_test_case_status_api_projects__project_id__suites__suite_id__tests__test_id__status_patch", "parameters": [{"name": "project_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Project Id"}}, {"name": "suite_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Suite Id"}}, {"name": "test_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Test Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseStatusUpdateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/tests/smoke": {"post": {"summary": "Ejecutar smoke test", "description": "Ejecuta un smoke test con las instrucciones proporcionadas.", "operationId": "run_smoke_test_api_tests_smoke_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmokeTestRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/tests/full": {"post": {"summary": "Ejecutar test completo", "description": "Ejecuta un test completo con el escenario Gherkin proporcionado.", "operationId": "run_full_test_api_tests_full_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FullTestRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/generate/gherkin": {"post": {"summary": "Generar escenar<PERSON>", "description": "Genera un escenario Gherkin a partir de instrucciones.", "operationId": "create_gherkin_scenario_api_generate_gherkin_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GherkinRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/generate/code": {"post": {"summary": "Generar cu00f3digo de automatizaciu00f3n", "description": "Genera cu00f3digo de automatizaciu00f3n para un framework especu00edfico.", "operationId": "generate_code_api_generate_code_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeGenerationRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/stories/enhance": {"post": {"summary": "Mejorar historia de usuario", "description": "Mejora una historia de usuario.", "operationId": "enhance_story_api_stories_enhance_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnhanceStoryRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/stories/generate-manual-tests": {"post": {"summary": "Generar casos de prueba manuales", "description": "Genera casos de prueba manuales a partir de una historia mejorada.", "operationId": "generate_manual_tests_api_stories_generate_manual_tests_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateManualTestsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/stories/generate-gherkin": {"post": {"summary": "Generar escenarios <PERSON>kin desde casos manuales", "description": "Genera escenarios Gherkin a partir de casos de prueba manuales.", "operationId": "generate_gherkin_from_manual_api_stories_generate_gherkin_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateGherkinRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/projects/save-history": {"post": {"summary": "Guardar historial en proyecto", "description": "Guarda un historial de prueba en un proyecto.", "operationId": "save_history_to_project_api_projects_save_history_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveHistoryRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/health": {"get": {"summary": "Verificar estado de la API", "description": "Verifica el estado de la API y devuelve informacion basica.", "operationId": "get_status_api_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"CodeGenerationRequest": {"properties": {"framework": {"type": "string", "title": "Framework", "description": "Framework para generar el código"}, "gherkin_scenario": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "test_history": {"type": "object", "title": "Test History", "description": "Historial de la prueba"}}, "type": "object", "required": ["framework", "gherkin_scenario", "test_history"], "title": "CodeGenerationRequest", "description": "Modelo para solicitud de generación de código."}, "EnhanceStoryRequest": {"properties": {"user_story": {"type": "string", "title": "User Story", "description": "Historia de usuario a mejorar"}, "language": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Language", "description": "Idioma de respuesta ('es' o 'en')", "default": "es"}}, "type": "object", "required": ["user_story"], "title": "EnhanceStoryRequest", "description": "Modelo para solicitud de mejora de historia de usuario."}, "FullTestRequest": {"properties": {"gherkin_scenario": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> a ejecutar"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Url", "description": "URL para la prueba (opcional)"}}, "type": "object", "required": ["gherkin_scenario"], "title": "FullTestRequest", "description": "Modelo para solicitud de test completo."}, "GenerateGherkinRequest": {"properties": {"manual_tests": {"type": "string", "title": "Manual Tests", "description": "Casos de prueba manuales"}, "language": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Language", "description": "Idioma de respuesta ('es' o 'en')", "default": "es"}}, "type": "object", "required": ["manual_tests"], "title": "GenerateGherkinRequest", "description": "Modelo para solicitud de generación de escenarios Gherkin desde casos manuales."}, "GenerateManualTestsRequest": {"properties": {"enhanced_story": {"type": "string", "title": "Enhanced Story", "description": "Historia de usuario mejorada"}, "language": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Language", "description": "Idioma de respuesta ('es' o 'en')", "default": "es"}}, "type": "object", "required": ["enhanced_story"], "title": "GenerateManualTestsRequest", "description": "Modelo para solicitud de generación de casos de prueba manuales."}, "GherkinRequest": {"properties": {"instructions": {"type": "string", "title": "Instructions", "description": "Instrucciones para la prueba"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Url", "description": "URL para la prueba (opcional)"}, "user_story": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Story", "description": "Historia de usuario (opcional)"}, "language": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Language", "description": "Idioma de respuesta ('es' o 'en')", "default": "es"}}, "type": "object", "required": ["instructions"], "title": "GherkinRequest", "description": "Modelo para solicitud de generación de escenario G<PERSON>kin."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "count": {"type": "integer", "title": "Count"}, "items": {"items": {"type": "object"}, "type": "array", "title": "Items"}}, "type": "object", "required": ["count", "items"], "title": "ListResponse", "description": "Modelo de respuesta genérica para listas."}, "ProjectCreateRequest": {"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Nombre del proyecto"}, "description": {"type": "string", "maxLength": 1000, "title": "Description", "description": "Descripción del proyecto", "default": ""}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "Lista de etiquetas"}}, "type": "object", "required": ["name"], "title": "ProjectCreateRequest", "description": "Modelo para crear un proyecto."}, "ProjectResponse": {"properties": {"project_id": {"type": "string", "title": "Project Id"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "created_at": {"type": "string", "title": "Created At"}, "updated_at": {"type": "string", "title": "Updated At"}, "test_suites": {"type": "object", "title": "Test Suites"}}, "type": "object", "required": ["project_id", "name", "description", "tags", "created_at", "updated_at"], "title": "ProjectResponse", "description": "Modelo de respuesta para un proyecto."}, "ProjectUpdateRequest": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Nuevo nombre del proyecto"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Nueva descripción del proyecto"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "Nueva lista de etiquetas"}}, "type": "object", "title": "ProjectUpdateRequest", "description": "Modelo para actualizar un proyecto."}, "SaveHistoryRequest": {"properties": {"project_id": {"type": "string", "title": "Project Id", "description": "ID del proyecto"}, "suite_id": {"type": "string", "title": "Suite Id", "description": "ID de la suite de pruebas"}, "test_history": {"type": "object", "title": "Test History", "description": "Historial de la prueba"}, "name": {"type": "string", "title": "Name", "description": "Nombre del caso de prueba"}, "description": {"type": "string", "title": "Description", "description": "Descripción del caso de prueba"}, "gherkin": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["project_id", "suite_id", "test_history", "name", "description", "g<PERSON>kin"], "title": "SaveHistoryRequest", "description": "Modelo para solicitud de guardado de historial en proyecto."}, "SmokeTestRequest": {"properties": {"instructions": {"type": "string", "title": "Instructions", "description": "Instrucciones para la prueba"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Url", "description": "URL para la prueba (opcional)"}, "user_story": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Story", "description": "Historia de usuario (opcional)"}}, "type": "object", "required": ["instructions"], "title": "SmokeTestRequest", "description": "Modelo para solicitud de smoke test."}, "SuccessResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operación completada exitosamente"}}, "type": "object", "title": "SuccessResponse", "description": "Modelo de respuesta genérica de éxito."}, "SuiteExecutionResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "suite_id": {"type": "string", "title": "Suite Id"}, "suite_name": {"type": "string", "title": "Suite Name"}, "total_tests": {"type": "integer", "title": "Total Tests"}, "passed": {"type": "integer", "title": "Passed"}, "failed": {"type": "integer", "title": "Failed"}, "results": {"items": {"$ref": "#/components/schemas/TestExecutionResponse"}, "type": "array", "title": "Results"}, "execution_time": {"type": "string", "title": "Execution Time"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}}, "type": "object", "required": ["success", "suite_id", "suite_name", "total_tests", "passed", "failed", "results", "execution_time"], "title": "SuiteExecutionResponse", "description": "Modelo de respuesta para la ejecución de una suite."}, "TestCaseCreateRequest": {"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Nombre del caso de prueba"}, "description": {"type": "string", "maxLength": 1000, "title": "Description", "description": "Descripción del caso de prueba", "default": ""}, "instrucciones": {"type": "string", "maxLength": 2000, "title": "Instrucciones", "description": "Instrucciones para ejecutar la prueba", "default": ""}, "historia_de_usuario": {"type": "string", "maxLength": 2000, "title": "Historia De Usuario", "description": "Historia de usuario", "default": ""}, "gherkin": {"type": "string", "maxLength": 5000, "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": ""}, "url": {"type": "string", "maxLength": 500, "title": "Url", "description": "URL para la prueba", "default": ""}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "Lista de etiquetas"}}, "type": "object", "required": ["name"], "title": "TestCaseCreateRequest", "description": "Modelo para crear un caso de prueba."}, "TestCaseResponse": {"properties": {"test_id": {"type": "string", "title": "Test Id"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "instrucciones": {"type": "string", "title": "Instrucciones"}, "historia_de_usuario": {"type": "string", "title": "Historia De Usuario"}, "gherkin": {"type": "string", "title": "<PERSON><PERSON><PERSON>"}, "url": {"type": "string", "title": "Url"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "created_at": {"type": "string", "title": "Created At"}, "updated_at": {"type": "string", "title": "Updated At"}, "history_files": {"items": {"type": "string"}, "type": "array", "title": "History Files"}, "status": {"type": "string", "title": "Status", "default": "Not Executed"}, "last_execution": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Execution"}, "code": {"type": "string", "title": "Code", "default": ""}, "framework": {"type": "string", "title": "Framework", "default": ""}}, "type": "object", "required": ["test_id", "name", "description", "instrucciones", "historia_de_usuario", "g<PERSON>kin", "url", "tags", "created_at", "updated_at"], "title": "TestCaseResponse", "description": "Modelo de respuesta para un caso de prueba."}, "TestCaseStatusUpdateRequest": {"properties": {"status": {"type": "string", "title": "Status", "description": "Nuevo estado del caso de prueba"}}, "type": "object", "required": ["status"], "title": "TestCaseStatusUpdateRequest", "description": "Modelo para actualizar el estado de un caso de prueba."}, "TestCaseUpdateRequest": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Nuevo nombre del caso de prueba"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Nueva descripción del caso de prueba"}, "instrucciones": {"anyOf": [{"type": "string", "maxLength": 2000}, {"type": "null"}], "title": "Instrucciones", "description": "Nuevas instrucciones para ejecutar la prueba"}, "historia_de_usuario": {"anyOf": [{"type": "string", "maxLength": 2000}, {"type": "null"}], "title": "Historia De Usuario", "description": "Nueva historia de usuario"}, "gherkin": {"anyOf": [{"type": "string", "maxLength": 5000}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Nuevo escenario Gherkin"}, "url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Url", "description": "Nueva URL para la prueba"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "Nueva lista de etiquetas"}}, "type": "object", "title": "TestCaseUpdateRequest", "description": "Modelo para actualizar un caso de prueba."}, "TestExecutionResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "test_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Test Id"}, "test_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Test Name"}, "result": {"type": "object", "title": "Result"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}, "execution_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Execution Time"}}, "type": "object", "required": ["success"], "title": "TestExecutionResponse", "description": "Modelo de respuesta para la ejecución de un test."}, "TestSuiteCreateRequest": {"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Nombre de la suite"}, "description": {"type": "string", "maxLength": 1000, "title": "Description", "description": "Descripción de la suite", "default": ""}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "Lista de etiquetas"}}, "type": "object", "required": ["name"], "title": "TestSuiteCreateRequest", "description": "Modelo para crear una suite de pruebas."}, "TestSuiteResponse": {"properties": {"suite_id": {"type": "string", "title": "Suite Id"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "created_at": {"type": "string", "title": "Created At"}, "updated_at": {"type": "string", "title": "Updated At"}, "test_cases": {"type": "object", "title": "Test Cases"}}, "type": "object", "required": ["suite_id", "name", "description", "tags", "created_at", "updated_at"], "title": "TestSuiteResponse", "description": "Modelo de respuesta para una suite de pruebas."}, "TestSuiteUpdateRequest": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Nuevo nombre de la suite"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Nueva descripción de la suite"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "Nueva lista de etiquetas"}}, "type": "object", "title": "TestSuiteUpdateRequest", "description": "Modelo para actualizar una suite de pruebas."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}