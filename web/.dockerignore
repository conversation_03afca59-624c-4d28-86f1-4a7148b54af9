# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/
.env*.local

# Production build
build/
dist/

# Cache
.cache/
.turbo/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Coverage
coverage/
.nyc_output/

# Docker
Dockerfile*
.dockerignore

# Git
.git/
.gitignore

# TypeScript
*.tsbuildinfo

# Testing
.coverage
.pytest_cache/

# Temporary files
*.tmp
*.temp
tmp/
temp/
