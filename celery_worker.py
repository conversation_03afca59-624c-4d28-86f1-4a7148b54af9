#!/usr/bin/env python3
"""
Celery Worker Entry Point for QAK Background Jobs

Run this file to start a Celery worker for processing background analysis tasks.

Usage:
    python celery_worker.py

Environment Variables:
    REDIS_URL: Redis connection URL (default: redis://localhost:6379/0)
    CELERY_LOG_LEVEL: Log level for Celery worker (default: INFO)
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Ensure the project root is in Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import Celery app
from src.core.background_jobs.celery_app import celery_app

if __name__ == "__main__":
    # Set default log level
    log_level = os.getenv("CELERY_LOG_LEVEL", "INFO")
    
    # Start worker with appropriate configuration
    celery_app.worker_main([
        "worker",
        f"--loglevel={log_level}",
        "--queues=analysis,default",
        "--concurrency=2",  # Limit concurrency for analysis tasks
        "--hostname=qak-worker@%h",
        "--without-gossip",
        "--without-mingle",
    ])