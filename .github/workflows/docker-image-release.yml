name: Build and Push Docker image (on tag)

on:
  push:
    tags:
      - "*"  # Trigger on every tag push

jobs:
  build-and-push:
    name: Build & push image to Docker Hub
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up QEMU (multi-arch)
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract Docker metadata (tags & labels)
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ secrets.DOCKERHUB_USERNAME }}/qak
          flavor: |
            latest=true
          tags: |
            type=ref,event=tag

      - name: Build and push image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          # Building both amd64 and arm64 doubles disk usage and can exhaust the
          # 14 GB available on GitHub runners.  Build only amd64 by default.
          platforms: linux/amd64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          # Enable GitHub Actions cache for BuildKit layers
          cache-from: type=gha
          cache-to: type=gha,mode=max 