{"github.copilot.chat.codeGeneration.instructions": [{"text": "# Copilot Memory Bank Instructions"}, {"text": "This repository uses a structured 'Memory Bank' in the `memory-bank/` directory to provide persistent project context. Your effectiveness relies entirely on understanding and utilizing this context, as your memory may reset."}, {"text": "FIRST ACTION: At the beginning of each new chat session, read ALL Memory Bank files to initialize your understanding. Never proceed without complete context from these files."}, {"text": "## Memory Bank Files & Purpose:"}, {"text": "Refer to the following files in the `memory-bank/` directory for specific context. If any are missing, attempt to create them based on available information or by asking the user before proceeding."}, {"text": "- `project-brief.md`: Overall project goals, scope, and success criteria. (High-level understanding)."}, {"text": "- `productContext.md`: The 'why' behind the project, problems it solves, how it should work. (User needs, product features)."}, {"text": "- `techContext.md`: Technologies used, development setup, technical constraints. (Implementation details, dependencies, limitations)."}, {"text": "- `systemPatterns.md`: System architecture, key technical decisions, design patterns, component relationships. (System structure and organization)."}, {"text": "- `activeContext.md`: Current work focus, recent changes, next steps, active decisions. (Your primary source of truth for current state)."}, {"text": "- `progress.md`: What works, what's left to build, current status, known issues. (Project status assessment)."}, {"text": "## Core Workflows & Behavior:"}, {"text": "1. Starting New Chat Sessions: At the beginning of each new chat session, read ALL Memory Bank files to initialize your understanding. Check for the existence of all required `memory-bank/` files. If ANY file is missing, STOP. Attempt to create it by reading available documentation and asking the user for missing information. Do not proceed without complete context. Verify you have complete context before starting development."}, {"text": "2. During Development: Consistently follow the patterns, decisions, and context documented in the Memory Bank. When using tools (like writing files, executing commands), preface the action description with `[MEMORY BANK: ACTIVE]` to signal you are operating based on the established context. Update Memory Bank files (especially `activeContext.md` and `progress.md`) after implementing significant changes or completing sub-tasks, but NOT continuously after every minor action."}, {"text": "3. Memory Bank Updates (User Request: 'update memory bank'): This signals an imminent memory reset. Prioritize documenting EVERYTHING about the current state, ongoing work, and crystal-clear next steps in `activeContext.md` and `progress.md`. Complete the immediate task if possible before the reset."}, {"text": "## External Documentation Access:"}, {"text": "Context7 MCP Integration: This repository has Context7 MCP configured for accessing external technical documentation."}, {"text": "Use Context7 when you need to obtain technical documentation for: Framework-specific implementation details (FastAPI, Next.js, LangChain, browser-use, etc.), API documentation for external libraries, Best practices for technologies used in the project, Troubleshooting specific technical issues."}, {"text": "Context7 provides real-time access to official documentation and should be used when the Memory Bank context is insufficient for technical implementation details."}, {"text": "General Guidance: When context from `memory-bank/` files conflicts with your general knowledge, always prioritize the `memory-bank/` information for this specific repository. Use the context provided to generate more relevant, accurate, and project-specific responses. For technical implementation details not covered in the Memory Bank, leverage Context7 to access current official documentation. Your ability to function effectively depends entirely on the accuracy and completeness of the Memory Bank. Maintain it diligently."}], "github.copilot.chat.testGeneration.instructions": [{"text": "When generating tests, use the Memory Bank files to understand the project context and follow established testing patterns and frameworks."}], "github.copilot.chat.reviewSelection.instructions": [{"text": "When reviewing code, use the Memory Bank files to understand the project context, architecture, and established patterns for better feedback."}], "github.copilot.chat.commitMessageGeneration.instructions": [{"text": "When generating commit messages, include context from the Memory Bank files to create more precise and relevant messages."}]}