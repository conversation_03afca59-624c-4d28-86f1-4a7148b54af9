#!/bin/bash

echo "🚀 Instalando dependencias del proyecto QAK..."
echo ""

# Instalar dependencias principales
echo "📦 Instalando dependencias principales..."
pip install -r requirements.txt

# Manejar el conflicto de google-generativeai
echo ""
echo "🔧 Resolviendo conflicto de dependencias de Google..."
echo "   (Esto es necesario para que funcione la memoria con Google Gemini)"

# Instalar google-generativeai sin verificar dependencias
pip install --force-reinstall --no-deps google-generativeai

echo ""
echo "✅ ¡Instalación completada!"
echo ""
echo "⚠️  Nota: Puede aparecer un warning sobre conflicto de versiones de google-ai-generativelanguage."
echo "    Esto es esperado y no afecta el funcionamiento de la aplicación."
echo ""
echo "🎯 Siguiente paso: Configura tu .env con GOOGLE_API_KEY y ejecuta 'python app.py'" 