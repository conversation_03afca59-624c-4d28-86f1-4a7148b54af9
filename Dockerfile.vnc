# Dockerfile para QAK con soporte VNC completo
FROM ubuntu:22.04

# Instalar dependencias básicas
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    nodejs \
    npm \
    xvfb \
    x11vnc \
    fluxbox \
    wget \
    curl \
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# Instalar websockify
RUN npm install -g websockify

# Descargar e instalar noVNC (versión más reciente)
RUN wget -O /tmp/novnc.tar.gz https://github.com/novnc/noVNC/archive/refs/tags/v1.4.0.tar.gz \
    && tar -xzf /tmp/novnc.tar.gz -C /usr/share/ \
    && mv /usr/share/noVNC-1.4.0 /usr/share/novnc \
    && rm /tmp/novnc.tar.gz

# Instalar Playwright y navegadores
RUN npm install -g playwright \
    && playwright install --with-deps chromium

# Crear directorio de trabajo y copiar aplicación
WORKDIR /app
COPY . /app

# Instalar dependencias Python
RUN pip3 install -r requirements.txt

# Configurar variables de entorno para VNC
ENV DISPLAY=:0
ENV QAK_VNC_ENABLED=true
ENV VNC_RESOLUTION=1920x1080x24

# Crear directorio para archivos temporales de QAK
RUN mkdir -p /tmp/qak_codegen

# Exponer puertos necesarios
EXPOSE 8000 5900 6080

# Hacer ejecutable el script de inicio
RUN chmod +x /app/docker/start-with-vnc.sh

# Healthcheck para verificar que todos los servicios estén funcionando
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/api/health && \
        netstat -tuln | grep :5900 && \
        netstat -tuln | grep :6080

# Comando por defecto
CMD ["/app/docker/start-with-vnc.sh"]
