---
title: 'Browser Settings'
---

The browser environment in which the agent operates is controlled by a `BrowserProfile` object. This powerful configuration class allows you to customize everything from the browser's appearance and network settings to its security policies and recording capabilities.

## Usage

You can create a `BrowserProfile` instance and pass it to a `BrowserSession`. The agent is then initialized with this session.

```python
from aery_browser import Agent, BrowserSession, BrowserProfile

# 1. Create a custom browser profile
my_profile = BrowserProfile(
    headless=False,
    stealth=True,
    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36",
    allowed_domains=["mintlify.com", "*.google.com"]
)

# 2. Create a session with the profile
# The session must be started within an async context
async with BrowserSession(browser_profile=my_profile) as session:
    # 3. Initialize the agent with the session
    agent = Agent(
        task="Document the BrowserProfile class on Mintlify",
        llm=my_llm,
        browser_session=session
    )
    await agent.run()
```

## Key Settings

Below are some of the most important settings available in `BrowserProfile`, grouped by category.

### Launch & Connection

| Parameter         | Type          | Default | Description                                                                                                                              |
| ----------------- | ------------- | ------- | ---------------------------------------------------------------------------------------------------------------------------------------- |
| `headless`        | `bool`        | `None`  | Whether to run the browser in headless mode. If `None`, it's determined automatically.                                                   |
| `channel`         | `str`         | `None`  | The browser channel to use, e.g., `'chrome'`, `'msedge'`. Allows using installed browsers instead of Playwright's bundled ones.           |
| `slow_mo`         | `float`       | `0.0`   | Slows down Playwright operations by the specified number of milliseconds. Useful for debugging and watching the agent in action.         |
| `executable_path` | `str` / `Path`| `None`  | Path to a specific browser executable to use.                                                                                            |

### Context & Viewport

| Parameter             | Type   | Default                                 | Description                                                                                             |
| --------------------- | ------ | --------------------------------------- | ------------------------------------------------------------------------------------------------------- |
| `user_agent`          | `str`  | `None`                                  | A custom user-agent string to use for the browser.                                                      |
| `viewport`            | `dict` | `{'width': 1280, 'height': 720}`        | The size of the browser viewport in pixels.                                                             |
| `device_scale_factor` | `float`| `None`                                  | The device scale factor (e.g., `2` for Retina displays).                                                  |
| `locale`              | `str`  | `None`                                  | The browser locale, e.g., `'en-US'`.                                                                    |

### Security & Privacy

| Parameter         | Type        | Default     | Description                                                                                                                                                           |
| ----------------- | ----------- | ----------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `stealth`         | `bool`      | `False`     | Enables stealth mode to make automated browsing less detectable by anti-bot systems.                                                                                    |
| `allowed_domains` | `list[str]` | `None`      | A list of domain patterns the browser is allowed to navigate to (e.g., `['*.google.com']`). **Crucial for security when using `sensitive_data`**. |
| `proxy`           | `dict`      | `None`      | Proxy server settings. Example: `{'server': 'http://myproxy.com:3128'}`.                                                                                                |
| `user_data_dir`   | `str` / `Path`| (see docs)  | Path to a Chrome user data directory to enable persistent sessions, cookies, and extensions.                                                                          |

### Performance & Timing

| Parameter                    | Type    | Default | Description                                                                                                   |
| ---------------------------- | ------- | ------- | ------------------------------------------------------------------------------------------------------------- |
| `default_navigation_timeout` | `float` | `None`  | The default timeout in milliseconds for page navigations.                                                     |
| `wait_between_actions`       | `float` | `0.5`   | Time in seconds to wait between agent actions, which can help with page stability.                            |

### Recording

| Parameter          | Type          | Default | Description                                                                                                                                    |
| ------------------ | ------------- | ------- | ---------------------------------------------------------------------------------------------------------------------------------------------- |
| `record_har_path`  | `str` / `Path`| `None`  | If a path is provided, a HAR file with network requests will be saved.                                                                         |
| `record_video_dir` | `str` / `Path`| `None`  | Path to a directory where a video of the browser session will be saved.                                                                        |
``` 