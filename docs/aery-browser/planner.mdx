---
title: 'Planner'
---

import { Card, Cards } from 'mintlify/components';

# Planner

The Planner is a specialized agent component responsible for high-level reasoning and task decomposition. When enabled, it periodically analyzes the agent's progress, identifies potential challenges, and suggests the next steps to achieve the main goal. This helps the agent stay on track and handle complex, multi-step tasks more effectively.

## Enabling the Planner

You can enable the Planner by providing a separate Language Model (LLM) for it during the `Agent`'s initialization.

```python
from aery_browser.agent import Agent
from langchain_openai import ChatOpenAI

# Main LLM for the agent
agent_llm = ChatOpenAI(model="gpt-4-turbo")

# A separate, often more powerful, LLM for planning
planner_llm = ChatOpenAI(model="gpt-4-vision-preview")

agent = Agent(
    task="Research the top 3 competitors for our product and create a summary.",
    llm=agent_llm,
    planner_llm=planner_llm,
    planner_interval=3, # Run the planner every 3 steps
    is_planner_reasoning=True # Enables chain-of-thought reasoning for the planner
)
```

### Planner Configuration

You configure the planner using these parameters in the `Agent` constructor:

| Parameter                       | Type             | Default | Description                                                                                                                                                             |
| ------------------------------- | ---------------- | ------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `planner_llm`                   | `BaseChatModel`  | `None`  | An instance of a chat model to be used for planning. If `None`, the planner is disabled.                                                                                 |
| `planner_interval`              | `int`            | `1`     | The planner will run every `n` steps.                                                                                                                                   |
| `is_planner_reasoning`          | `bool`           | `False` | If `True`, the planner's system prompt is sent as a `HumanMessage`, which can encourage more detailed, chain-of-thought style reasoning from some models.                 |
| `extend_planner_system_message` | `str`            | `None`  | Optional text to append to the planner's default system prompt, allowing for custom instructions or context.                                                            |

## Planner Output

The planner's primary job is to analyze the current state and produce a structured JSON object that guides the agent. The expected output format is as follows:

```json
{
    "state_analysis": "Brief analysis of the current state and what has been done so far.",
    "progress_evaluation": "Evaluation of progress towards the ultimate goal (as percentage and description).",
    "challenges": "List any potential challenges or roadblocks.",
    "next_steps": "List 2-3 concrete next steps to take.",
    "reasoning": "Explain your reasoning for the suggested next steps."
}
```

The `Agent` will then inject this plan into its message history, providing valuable context for the main LLM to decide on the next concrete actions. 