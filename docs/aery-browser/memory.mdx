---
title: 'Memory'
---

import { Card, Cards } from 'mintlify/components';

# Memory

Aery Browser includes a sophisticated memory system that allows the agent to learn from its interactions and improve its performance over time. This system is composed of two main types of memory: **procedural memory** and **semantic memory**.

- **Procedural Memory:** This is a short-term memory that helps the agent recall the sequence of actions taken in the current session. It's useful for understanding the immediate context of a task.
- **Semantic Memory:** This is a long-term memory that stores learned patterns and successful workflows. The agent can query this memory to get suggestions on how to perform a new task based on past experiences.

## Enabling Memory

To enable the memory system, you need to set `enable_memory=True` when creating an `Agent` instance and provide a `MemoryConfig` object.

```python
from aery_browser.agent import Agent, MemoryConfig
from langchain_openai import ChatOpenAI

llm = ChatOpenAI(model="gpt-4-turbo")

# Configure the memory system
memory_config = MemoryConfig(
    vector_store_provider="chroma",
    embedder_model="all-MiniLM-L6-v2"
)

# Create an agent with memory enabled
agent = Agent(
    task="Log in to the website and navigate to the dashboard.",
    llm=llm,
    enable_memory=True,
    memory_config=memory_config
)
```

## Memory Configuration (`MemoryConfig`)

The `MemoryConfig` class allows you to customize every aspect of the memory system.

### General Memory Settings

| Parameter         | Type   | Default             | Description                                                                                             |
| ----------------- | ------ | ------------------- | ------------------------------------------------------------------------------------------------------- |
| `agent_id`        | `str`  | `'browser_use_agent'` | A unique identifier for the agent, used to namespace memories.                                          |
| `memory_interval` | `int`  | `10`                | The number of steps after which the agent will try to create a new procedural memory. Must be > 1 and < 100. |

### Embedder Settings

The embedder is responsible for converting text into vector representations for storage and retrieval.

| Parameter           | Type                                            | Default              | Description                                        |
| ------------------- | ----------------------------------------------- | -------------------- | -------------------------------------------------- |
| `embedder_provider` | `Literal['openai', 'gemini', 'ollama', 'huggingface']` | `'huggingface'`      | The provider for the embedding model.              |
| `embedder_model`    | `str`                                           | `'all-MiniLM-L6-v2'` | The specific embedding model to use.               |
| `embedder_dims`     | `int`                                           | `384`                | The dimensions of the embeddings. Must be > 10 and < 10000. |

### Vector Store Settings

The vector store is the database where the memory embeddings are stored. Aery Browser supports a wide range of vector stores through `Mem0`.

| Parameter                        | Type      | Default      | Description                                                                                                                              |
| -------------------------------- | --------- | ------------ | ---------------------------------------------------------------------------------------------------------------------------------------- |
| `vector_store_provider`          | `Literal` | `'chroma'`   | The vector store provider to use. Supported options include `faiss`, `qdrant`, `pinecone`, `chroma`, `redis`, and many more.                |
| `vector_store_collection_name`   | `str`     | `None`       | The name for the collection or index in the vector store. If not provided, a default name will be generated.                               |
| `vector_store_base_path`         | `str`     | `'/tmp/mem0'`  | The base path for local vector stores like Chroma or FAISS.                                                                                |
| `vector_store_config_override`   | `dict`    | `None`       | A dictionary to override or provide additional configuration for the chosen vector store provider (e.g., API keys, host, port).              |

## Semantic Memory

For more advanced use cases, you can directly interact with the `SemanticMemory` class located in `aery_browser.agent.semantic_memory`. This class allows you to:
- Manually record interactions.
- Trigger the pattern learning process.
- Get suggestions for actions based on the learned memory.
- Save and load the memory state to a file.

This provides fine-grained control over the agent's long-term learning capabilities. 