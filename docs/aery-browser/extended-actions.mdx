---
title: 'Extended Actions'
---

import { Card, Cards } from 'mintlify/components';

# Extended Actions

Aery Browser provides a suite of advanced extensions to handle complex web automation scenarios. These extensions are automatically registered by the `Controller` and provide powerful capabilities for interacting with modern web applications.

## Smart Forms

The Smart Forms extension provides actions for intelligently interacting with HTML forms.

<CardGroup>
    <Card title="smart_form_fill">
        **Description:** Auto-fill a form with smart field detection and mapping. It can handle various input types and validation.
    </Card>
    <Card title="detect_form_structure">
        **Description:** Detect and analyze the structure of a form on the current page, including its fields, labels, and validation rules.
    </Card>
</CardGroup>

## Smart Dropdowns

This extension offers robust tools for handling both native and custom-built dropdown menus.

<CardGroup>
    <Card title="detect_dropdown">
        **Description:** Detect and analyze dropdown elements on the page, identifying their type, options, and current state.
    </Card>
</CardGroup>

## Alerts and Modals

Handle JavaScript alerts and dynamic modal dialogs gracefully.

<CardGroup>
    <Card title="handle_js_alert">
        **Description:** Handle JavaScript `alert`, `confirm`, and `prompt` dialogs by accepting or dismissing them.
    </Card>
    <Card title="handle_modal">
        **Description:** Handle dynamic modal dialogs and overlays using various closing strategies.
    </Card>
</CardGroup>


## File Uploads

This extension provides actions for managing file uploads in the browser.

<CardGroup>
    <Card title="upload_files">
        **Description:** Upload multiple files with validation and progress monitoring.
    </Card>
    <Card title="monitor_upload_progress">
        **Description:** Monitor the progress of file uploads.
    </Card>
</CardGroup>

## HTTP Requests

Make HTTP requests using the browser's context, which is useful for tasks like interacting with APIs that require authentication from the current session.

<CardGroup>
    <Card title="http_request_with_context">
        **Description:** Make an HTTP request (GET, POST, etc.) using the browser's cookies and headers.
    </Card>
    <Card title="upload_files_http">
        **Description:** Upload files via an HTTP request, inheriting the browser's context.
    </Card>
</CardGroup> 