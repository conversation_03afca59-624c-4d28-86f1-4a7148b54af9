---
title: 'Output Format & History'
---

import { Card, Cards } from 'mintlify/components';

# Output Format & History

When an Aery Browser agent runs, it generates a rich history of its actions, thoughts, and the state of the browser at each step. Understanding the structure of this output is key to debugging, analysis, and leveraging the full power of the agent.

The entire execution history is captured in a single `AgentHistoryList` object, which is returned by the `agent.run()` method.

## The Structure of a Step

The history list is composed of `AgentHistory` items, where each item represents one full step in the agent's execution. A single step consists of:

<CardGroup>
    <Card title="1. Browser State">
        The agent captures the state of the browser, including the DOM, URL, and a screenshot. This is what the agent "sees".
    </Card>
    <Card title="2. LLM Decides Action">
        Based on the browser state and its goal, the LLM decides on the next action to take (`AgentOutput`).
    </Card>
    <Card title="3. Action is Executed">
        The agent executes the chosen action in the browser.
    </Card>
    <Card title="4. Result is Recorded">
        The outcome of the action (`ActionResult`) is recorded.
    </Card>
</CardGroup>

These pieces are bundled into an `AgentHistory` object for each step.

## Core Output Objects

### `AgentHistory`
This is the main container for a single step's data.

- **`model_output: AgentOutput | None`**: The LLM's "thought process". It contains:
    - `thinking`: The agent's reasoning.
    - `evaluation_previous_goal`: Assessment of the previous step.
    - `memory`: What the agent wants to remember.
    - `next_goal`: The goal for the current step.
    - `action`: A list of one or more `ActionModel` objects the agent decided to execute.

- **`result: list[ActionResult]`**: The outcome(s) of executing the action(s). Each `ActionResult` contains:
    - `is_done`: `True` if this was the final `done` action.
    - `success`: `True` or `False` for the `done` action to indicate overall task success.
    - `error`: An error message if the action failed.
    - `long_term_memory`: A summary of the result for the agent's memory.
    - `extracted_content`: Any data extracted by the action.

- **`state: BrowserStateHistory`**: A snapshot of the browser state *before* the action was executed.
    - `url`: The URL of the page.
    - `title`: The title of the page.
    - `tabs`: A list of all open tabs.
    - `interacted_element`: The DOM element the agent chose to interact with.
    - `screenshot`: A Base64-encoded screenshot of the viewport.

- **`metadata: StepMetadata | None`**: Performance metrics for the step.
    - `step_number`: The step number (starting from 1).
    - `duration_seconds`: How long the step took to execute.
    - `input_tokens`: The number of tokens sent to the LLM for this step.

## Analyzing the `AgentHistoryList`

The `AgentHistoryList` object that `agent.run()` returns is more than just a list. It has many useful helper methods for quickly analyzing the results of a run.

```python
# Assuming 'agent' is an initialized Agent instance
history = await agent.run(max_steps=10)

# Example: Get a summary of the run
print(f"Run finished in {history.total_duration_seconds():.2f} seconds.")
print(f"Total steps: {history.number_of_steps()}")
print(f"Total input tokens: {history.total_input_tokens()}")

# Check final status
if history.is_done() and history.is_successful():
    print(f"✅ Task successful! Final result: {history.final_result()}")
elif history.has_errors():
    print(f"❌ Run failed with errors: {history.errors()}")
```

### `AgentHistoryList` Method Reference

| Method                       | Returns         | Description                                                            |
| ---------------------------- | --------------- | ---------------------------------------------------------------------- |
| `is_done()`                  | `bool`          | Returns `True` if the `done` action was called.                        |
| `is_successful()`            | `bool`          | Returns `True` if the task was marked as successful in the `done` action. |
| `has_errors()`               | `bool`          | Returns `True` if any step in the history has an error.                |
| `final_result()`             | `str | None`    | Returns the final message from the `done` action.                      |
| `number_of_steps()`          | `int`           | The total number of steps taken.                                       |
| `total_duration_seconds()`   | `float`         | The total time elapsed for all steps.                                  |
| `total_input_tokens()`       | `int`           | The sum of all input tokens sent to the LLM across all steps.          |
| `action_names()`             | `list[str]`     | A list of the names of every action executed (e.g., `['click_element', 'input_text']`). |
| `model_thoughts()`           | `list[AgentBrain]`| A list of the agent's "brain" state (thinking, goal, etc.) for each step. |
| `extracted_content()`        | `list[str]`     | A list of all content extracted by actions during the run.             |
| `screenshots()`              | `list[str|None]`| A list of all Base64-encoded screenshots, one for each step.           |
| `errors()`                   | `list[str|None]`| A list of error messages, with `None` for steps that had no error.     |
| `urls()`                     | `list[str|None]`| A list of the URLs visited at each step.                               |
| `save_to_file(path)`         | `None`          | Saves the entire history object to a JSON file.                        |

## Screenshots

A screenshot is automatically taken at each step of the agent's execution, right before the agent decides on its next action. This screenshot is encoded in Base64 and stored within the `BrowserStateHistory` object for that step.

### Accessing Screenshots
As shown in the example above, the easiest way to get all screenshots from a run is to use the `history.screenshots()` method. This returns a list of Base64-encoded strings, where each string corresponds to a step in the history. You can then decode these strings and save them as image files.

This feature is invaluable for debugging, as it gives you a visual log of what the agent "saw" at each point in time. 