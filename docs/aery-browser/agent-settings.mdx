---
title: 'Agent Settings'
---

The `Agent` class is highly configurable through its initialization parameters. These settings allow you to fine-tune the agent's behavior, performance, and interaction with the web environment.

All these settings are passed directly to the `Agent` constructor.

```python
from aery_browser.agent import Agent
from langchain_openai import ChatOpenAI

llm = ChatOpenAI(model="gpt-4-turbo")

agent = Agent(
    task="Your task here",
    llm=llm,
    max_failures=5,
    use_vision=True,
    max_output_tokens=2048,
    llm_timeout=90,  # 90 second timeout
    planner_llm=ChatOpenAI(model="gpt-4-vision-preview")
)
```

## Core Behavior

These settings control the agent's main execution loop and error handling.

| Parameter        | Type     | Default | Description                                                                                              |
| ---------------- | -------- | ------- | -------------------------------------------------------------------------------------------------------- |
| `max_failures`   | `int`    | `3`     | The number of consecutive failures the agent will tolerate before stopping.                                |
| `retry_delay`    | `int`    | `10`    | The delay in seconds to wait before retrying after a rate limit error from the LLM.                        |
| `max_llm_retries`| `int`    | `2`     | The maximum number of times to retry a call to the LLM if it fails.                                      |

## Vision and Media

Control how the agent uses vision capabilities.

| Parameter      | Type          | Default   | Description                                                                                                                              |
| -------------- | ------------- | --------- | ---------------------------------------------------------------------------------------------------------------------------------------- |
| `use_vision`   | `bool`        | `True`    | Whether to provide screenshots of the webpage to the LLM. Requires a vision-capable model.                                               |
| `generate_gif` | `bool` / `str`| `False`   | If `True` or a path is provided, a GIF of the agent's entire run will be saved. Example: `'path/to/history.gif'`. |

## LLM & Prompting

Customize how the agent interacts with the Language Model.

| Parameter                   | Type                | Default      | Description                                                                                                                       |
| --------------------------- | ------------------- | ------------ | --------------------------------------------------------------------------------------------------------------------------------- |
| `max_input_tokens`          | `int`               | `128000`     | The maximum number of tokens to include in the prompt sent to the LLM.                                                            |
| `max_output_tokens`         | `int`               | `4096`       | The maximum number of tokens the LLM can generate in its response.                                                               |
| `llm_timeout`               | `int`               | `120`        | Timeout in seconds for LLM API calls. If exceeded, the call will be cancelled and retried.                                       |
| `override_system_message`   | `str`               | `None`       | A string to completely replace the default system prompt.                                                                         |
| `extend_system_message`     | `str`               | `None`       | A string to append to the end of the default system prompt, allowing for customization without losing the base instructions.      |
| `message_context`           | `str`               | `None`       | A string to provide extra context in the prompt for the LLM.                                                                      |
| `tool_calling_method`       | `ToolCallingMethod` | `'auto'`     | The method to use for tool calling (`'function_calling'`, `'json_mode'`, `'raw'`, `'tools'`, `'auto'`). `'auto'` detects the best method. |
| `validate_output`           | `bool`              | `False`      | Whether to send the model's output to a separate LLM call for validation.                                                         |

## Planner Settings

Configure the high-level planner agent.

| Parameter                       | Type            | Default   | Description                                                                                                  |
| ------------------------------- | --------------- | --------- | ------------------------------------------------------------------------------------------------------------ |
| `planner_llm`                   | `BaseChatModel` | `None`    | If provided, a separate LLM is used for planning. This enables the planner.                                  |
| `use_vision_for_planner`        | `bool`          | `False`   | Whether to provide screenshots to the planner LLM.                                                           |
| `planner_interval`              | `int`           | `1`       | The agent will run the planner every `n` steps.                                                              |
| `is_planner_reasoning`          | `bool`          | `False`   | If `True`, the planner will provide more detailed reasoning in its output.                                   |
| `extend_planner_system_message` | `str`           | `None`    | A string to append to the planner's system prompt for customization.                                         |

## DOM & Actions

Settings related to how the agent perceives and interacts with the webpage.

| Parameter            | Type          | Default     | Description                                                                                                   |
| -------------------- | ------------- | ----------- | ------------------------------------------------------------------------------------------------------------- |
| `include_attributes` | `list[str]`   | (see list)  | The HTML attributes to include in the DOM representation sent to the LLM.                                       |
| `max_actions_per_step` | `int`         | `10`        | The maximum number of actions the agent can propose in a single step.                                         |

## File System & Debugging

Manage file access and save conversation history for debugging.

| Parameter                         | Type         | Default   | Description                                                                                                   |
| --------------------------------- | ------------ | --------- | ------------------------------------------------------------------------------------------------------------- |
| `available_file_paths`            | `list[str]`  | `None`    | A list of file paths the agent is allowed to access using file system actions (`read_file`, `write_file`).    |
| `save_conversation_path`          | `str` / `Path` | `None`    | If a path is provided, the full conversation history for each step will be saved to a `.txt` file in that directory. |
| `save_conversation_path_encoding` | `str`        | `'utf-8'` | The encoding to use when saving conversation files.                                                           |

## Page Content Extraction

Configure the model used for extracting structured data from a page.

| Parameter               | Type            | Default | Description                                                                                                                      |
| ----------------------- | --------------- | ------- | -------------------------------------------------------------------------------------------------------------------------------- |
| `page_extraction_llm`   | `BaseChatModel` | `None`  | A separate LLM for the `extract_structured_data` action. If not provided, the main `llm` is used. |

</rewritten_file> 