---
title: 'Lifecycle Hooks'
---

Lifecycle hooks allow you to tap into the agent's execution process, giving you the ability to monitor, log, or even alter its behavior at key moments. These are advanced features for when you need tight integration with your own systems.

Hooks are asynchronous functions that you can pass to the `Agent` during initialization or when calling the `run` method.

## Available Hooks

There are three main hooks you can use:

- **`on_step_start`**: An `async` function that is called at the very beginning of each step, before the agent processes the browser state or calls the LLM.
- **`on_step_end`**: An `async` function that is called at the end of each step, after the action has been executed and the result is recorded.
- **`register_done_callback`**: A synchronous or asynchronous function passed during agent initialization that is called only once, when the agent's run is complete (i.e., when the `done` action is called).

## Usage Example

Both `on_step_start` and `on_step_end` receive the `Agent` instance as their only argument, allowing you to inspect its state. The `register_done_callback` receives the final `AgentHistoryList`.

```python
import asyncio
from aery_browser import Agent
from aery_browser.agent.views import AgentHistoryList

# --- Define Hook Functions ---

async def my_step_start_hook(agent: Agent):
    """Called at the start of every step."""
    print(f"\n--- Step {agent.state.n_steps} Starting ---")
    print(f"Current URL: {agent.state.history.history[-1].state.url if agent.state.history.history else 'N/A'}")

async def my_step_end_hook(agent: Agent):
    """Called at the end of every step."""
    last_step = agent.state.history.history[-1]
    action_name = "N/A"
    if last_step.model_output and last_step.model_output.action:
        action_name = list(last_step.model_output.action[0].model_dump().keys())[0]

    print(f"Action Taken: {action_name}")
    print(f"Result: {last_step.result[0].long_term_memory or 'No result'}")
    print(f"--- Step {agent.state.n_steps - 1} Finished ---")


def my_done_callback(final_history: AgentHistoryList):
    """Called once when the agent is finished."""
    print("\n🎉🎉🎉 AGENT RUN COMPLETE 🎉🎉🎉")
    if final_history.is_successful():
        print(f"Final Result: {final_history.final_result()}")
    else:
        print("Task finished without complete success.")

# --- Run the Agent with Hooks ---

agent = Agent(
    task="Search Google for 'Aery Browser' and then go to the first result.",
    llm=my_llm,
    register_done_callback=my_done_callback # Pass at init
)

# You can run this in a script
async def main():
    await agent.run(
        max_steps=5,
        on_step_start=my_step_start_hook, # Pass at run
        on_step_end=my_step_end_hook      # Pass at run
    )

if __name__ == "__main__":
    asyncio.run(main())
```

### Accessing Agent State

As shown in the example, the hooks provide a powerful way to inspect the agent's state in real-time. You can access:
- `agent.state`: The current `AgentState` object, including `n_steps`, `last_result`, etc.
- `agent.state.history`: The full `AgentHistoryList` up to the current point.
- `agent.task`: The overall task definition.
- `agent.browser_session`: The current browser session. 