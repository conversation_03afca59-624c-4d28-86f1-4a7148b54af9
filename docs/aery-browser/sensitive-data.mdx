---
title: 'Handling Sensitive Data'
---

import { Card, Cards } from 'mintlify/components';

When automating tasks like logging into websites, the agent needs access to credentials. It is critical to handle this sensitive information securely to prevent it from being exposed.

<PERSON><PERSON> Browser provides a `sensitive_data` parameter in the `Agent` constructor for this purpose. **Never hardcode secrets directly into your prompts or tasks.**

## How it Works

The `sensitive_data` mechanism works by replacing placeholders in your action parameters with the actual secret values at runtime. The agent tells the LLM to use a placeholder (e.g., `{{my_password}}`), and the `Agent` class substitutes it with the real value *after* the LLM has decided on an action, but *before* the action is executed.

This prevents the raw credential from ever being sent to the LLM or appearing in logs or history files.

## Basic Usage

You can provide a simple dictionary of placeholders to secrets.

```python
import os
from aery_browser import Agent

# Load secrets from environment variables or a secure vault
my_secrets = {
    "login_username": os.environ.get("MY_APP_USERNAME"),
    "login_password": os.environ.get("MY_APP_PASSWORD")
}

agent = Agent(
    task="Log into the application and check for new messages.",
    llm=my_llm,
    sensitive_data=my_secrets
)

# In your code or prompt, you'd instruct the agent to do this:
# agent.multi_act([
#   {"input_text": {"text": "{{login_username}}", "element": 12}},
#   {"input_text": {"text": "{{login_password}}", "element": 15}},
#   {"click_element": {"element": 18}}
# ])
```
The agent will replace `{{login_username}}` and `{{login_password}}` with the actual values before typing them into the browser.

## Domain-Specific Credentials

For enhanced security, you can scope credentials to specific domains. This ensures that a given secret can only be used on websites that match a specific pattern.

```python
# Keys are domain patterns, values are dictionaries of secrets
domain_specific_secrets = {
    "*.google.com": {
        "google_password": "abc"
    },
    "https://github.com": {
        "gh_token": "xyz",
        "gh_password": "123"
    }
}

agent = Agent(
    task="...",
    llm=my_llm,
    sensitive_data=domain_specific_secrets
)
```
In this case, `{{google_password}}` can only be used on pages whose URL matches `*.google.com`.

## ⚠️ Critical Security Warning

Using `sensitive_data` introduces a potential security risk. If the agent is compromised by a prompt-injection attack on a malicious website, it could be tricked into sending your secrets to an attacker.

To mitigate this, you **MUST** lock down the agent's navigation scope using the `allowed_domains` parameter in the `BrowserProfile`. This acts as a firewall, preventing the agent from visiting any domain not on the list.

<Card title="Security Best Practice: Always Combine sensitive_data with allowed_domains" icon="triangle-exclamation" color="#D83D3D">
```python
from aery_browser import Agent, BrowserSession, BrowserProfile

# Define the secrets
my_secrets = {"login_password": "my-secret-password"}

# Define the profile with a strict domain firewall
# This agent can ONLY visit my-app.com
profile = BrowserProfile(
    allowed_domains=["my-app.com"]
)

async with BrowserSession(browser_profile=profile) as session:
    agent = Agent(
        task="Log into my-app.com",
        llm=my_llm,
        browser_session=session,
        sensitive_data=my_secrets  # Pass the secrets here
    )
    await agent.run()
```
If you provide `sensitive_data` without `allowed_domains`, Aery Browser will log a prominent security warning and pause before continuing. In future versions, this will become a hard error.
</Card> 