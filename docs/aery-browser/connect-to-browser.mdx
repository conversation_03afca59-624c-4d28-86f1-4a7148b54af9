---
title: 'Connect to an Existing Browser'
---

For development, debugging, or complex workflows, you might want the agent to use a browser that is already running on your machine. This avoids launching a new browser instance for every run and allows you to inspect the state of the browser manually.

Aery Browser supports several ways to connect to an existing browser, primarily by passing connection details to the `BrowserSession` object.

## Using a CDP (Chrome DevTools Protocol) URL

This is the most common and reliable method. You can launch a Chromium-based browser (like Chrome or Brave) from your terminal with a remote debugging port enabled.

**1. Launch the Browser from Your Terminal**

```bash
# For Google Chrome on macOS
/Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --remote-debugging-port=9222

# For Brave on macOS
/Applications/Brave\\ Browser.app/Contents/MacOS/Brave\\ Browser --remote-debugging-port=9222

# For Chrome on Linux
google-chrome --remote-debugging-port=9222

# For Chrome on Windows
"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --remote-debugging-port=9222
```

This will start your browser with a debugging port open on `localhost:9222`.

**2. Connect the Agent**

Now, you can tell the `BrowserSession` to connect to this port using the `cdp_url` parameter.

```python
from aery_browser import Agent, BrowserSession

# The CDP URL from the browser you launched manually
cdp_url = "http://127.0.0.1:9222"

async with BrowserSession(cdp_url=cdp_url) as session:
    agent = Agent(
        task="Your task here",
        llm=my_llm,
        browser_session=session
    )
    # The agent will now use the browser you launched
    await agent.run()
```

## Using a Persistent User Profile

Another powerful method is to use a persistent user data directory. This tells Playwright to launch the browser using a specific profile, which preserves cookies, sessions, and extensions between runs. This is not strictly "connecting" to a running browser, but it achieves a similar goal of session persistence.

Set the `user_data_dir` in a `BrowserProfile`.

```python
from aery_browser import Agent, BrowserSession, BrowserProfile

# Path to your Chrome profile
# On macOS, this is typically: '~/Library/Application Support/Google/Chrome'
# On Windows: '~/AppData/Local/Google/Chrome/User Data'
user_data_path = 'path/to/your/chrome/profile'

profile = BrowserProfile(
    user_data_dir=user_data_path,
    headless=False # Must be headful to use a persistent profile
)

async with BrowserSession(browser_profile=profile) as session:
    agent = Agent(
        task="Login to a website and check my messages",
        llm=my_llm,
        browser_session=session
    )
    # The agent will launch Chrome with your profile,
    # and you will already be logged in to your sites.
    await agent.run()
```

## Other Connection Methods

Aery Browser also supports connecting via a **WSS (WebSocket Secure) endpoint** (`wss_url`) or a **Process ID** (`browser_pid`), although these are less common for typical development workflows. The principle is the same: pass the corresponding parameter to the `BrowserSession` constructor. 