---
title: 'Introduction'
---

# <PERSON>ery Browser

Aery Browser is a custom implementation of `browser-use` designed to provide enhanced browser automation capabilities for the Qak platform. This library offers a powerful agent-based model for controlling browsers, executing complex tasks, and extracting information from web pages.

This documentation provides a comprehensive guide to the features, modules, and APIs available in Aery Browser. 