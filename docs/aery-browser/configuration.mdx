---
title: 'Configuration'
---

import { <PERSON>, Cards } from 'mintlify/components';

# Configuration

A<PERSON> Browser is highly configurable, allowing you to tailor its behavior to your specific needs. Configuration is managed through a combination of environment variables and Pydantic model instances passed during initialization.

## Environment Variables

The core library behavior, such as logging levels, file paths, and API keys, is configured via environment variables. These are read at runtime by the `CONFIG` object from `browser_use.config`.

### Logging

| Variable                    | Default | Description                                 |
| --------------------------- | ------- | ------------------------------------------- |
| `BROWSER_USE_LOGGING_LEVEL` | `info`  | Sets the logging level (e.g., `debug`, `info`). |

### Paths

| Variable                        | Default                            | Description                                        |
| ------------------------------- | ---------------------------------- | -------------------------------------------------- |
| `BROWSER_USE_CONFIG_DIR`        | `~/.config/browseruse`             | The root directory for all configuration files.    |
| `BROWSER_USE_PROFILES_DIR`      | `~/.config/browseruse/profiles`    | Directory to store browser user profiles.          |
| `BROWSER_USE_DEFAULT_USER_DATA_DIR` | `~/.config/browseruse/profiles/default` | The default path for the persistent browser profile. |
| `WIN_FONT_DIR`                  | `C:\\Windows\\Fonts`               | (Windows-only) Path to system fonts.               |

### LLM API Keys

| Variable                      | Description                                                  |
| ----------------------------- | ------------------------------------------------------------ |
| `OPENAI_API_KEY`              | Your API key for OpenAI models.                              |
| `ANTHROPIC_API_KEY`           | Your API key for Anthropic models.                           |
| `GOOGLE_API_KEY`              | Your API key for Google AI models.                           |
| `DEEPSEEK_API_KEY`            | Your API key for DeepSeek models.                            |
| `GROK_API_KEY`                | Your API key for Grok models.                                |
| `AZURE_OPENAI_ENDPOINT`       | Your endpoint for Azure OpenAI services.                     |
| `AZURE_OPENAI_KEY`            | Your API key for Azure OpenAI services.                      |
| `SKIP_LLM_API_KEY_VERIFICATION`| Set to `true` to skip the check for API keys on startup.       |

### Storage Backend (for Semantic Memory)

This configures where the vector store for semantic memory is located.

| Variable                 | Default              | Description                                                      |
| ------------------------ | -------------------- | ---------------------------------------------------------------- |
| `STORAGE_BACKEND`        | `filesystem`         | The backend to use: `filesystem`, `upstash`, or `redis`.         |
| `UPSTASH_REDIS_URL`      |                      | URL for your Upstash Redis instance.                             |
| `UPSTASH_REDIS_TOKEN`    |                      | Token for your Upstash Redis instance.                           |
| `REDIS_URL`              | `redis://localhost:6379` | URL for a self-hosted Redis instance.                          |
| `STORAGE_TTL`            | `14400`              | Time-to-live in seconds for stored items (default: 4 hours).     |

## Pydantic Configuration Models

For more granular control over the agent, browser, and memory, Aery Browser uses Pydantic models. These are passed as arguments during initialization.

<CardGroup>
    <Card title="AgentSettings" href="/aery-browser/agent#agentsettings">
        Controls the agent's core logic, such as `max_failures`, `max_input_tokens`, and enabling the `planner`.
    </Card>
    <Card title="BrowserProfile" href="https://github.com/aery-support/browser-use/blob/main/libs/browser_use/browser/profile.py">
        Controls the browser's behavior, including `headless` mode, `user_data_dir`, `viewport` size, and more. *(Note: This class is extensive and best referenced directly from the source code for now.)*
    </Card>
    <Card title="MemoryConfig" href="/aery-browser/memory#memoryconfig">
        Configures the semantic memory system, including the `embedder` and `vector_store` settings.
    </Card>
</CardGroup> 