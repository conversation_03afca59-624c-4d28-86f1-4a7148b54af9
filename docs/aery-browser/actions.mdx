---
title: 'Actions'
---

import { Card, Cards } from 'mintlify/components';

# Actions

Aery Browser comes with a set of built-in actions that the agent can use to interact with web pages. These actions are defined in the `Controller` and can be extended with custom actions.

## Navigation Actions

<CardGroup>
    <Card title="search_google">
        **Description:** Search the query in Google, the query should be a search query like humans search in Google, concrete and not vague or super long.
        
        **Parameters:**
        - `query: str`
    </Card>
    <Card title="go_to_url">
        **Description:** Navigate to URL in the current tab.

        **Parameters:**
        - `url: str`
    </Card>
    <Card title="go_back">
        **Description:** Go back in the browser history.

        **Parameters:** None
    </Card>
</CardGroup>

## Interaction Actions

<CardGroup>
    <Card title="click_element_by_index">
        **Description:** Click element by index.

        **Parameters:**
        - `index: int`
        - `xpath: str | None`
    </Card>
    <Card title="input_text">
        **Description:** Click and input text into a input interactive element.

        **Parameters:**
        - `index: int`
        - `text: str`
        - `xpath: str | None`
    </Card>
    <Card title="send_keys">
        **Description:** Send strings of special keys like Escape,Backspace, Insert, PageDown, Delete, Enter, Shortcuts such as `Control+o`, `Control+Shift+T` are supported as well.

        **Parameters:**
        - `keys: str`
    </Card>
    <Card title="scroll_down">
        **Description:** Scroll down the page by pixel amount - if none is given, scroll one page.
        
        **Parameters:**
        - `amount: int | None`
    </Card>
    <Card title="scroll_up">
        **Description:** Scroll up the page by pixel amount - if none is given, scroll one page.
        
        **Parameters:**
        - `amount: int | None`
    </Card>
    <Card title="select_dropdown_option">
        **Description:** Select dropdown option for interactive element index by the text of the option you want to select.

        **Parameters:**
        - `index: int`
        - `text: str`
    </Card>
</CardGroup>


## Tab Management

<CardGroup>
    <Card title="switch_tab">
        **Description:** Switch to a different tab.

        **Parameters:**
        - `page_id: int`
    </Card>
    <Card title="open_tab">
        **Description:** Open a specific url in new tab.

        **Parameters:**
        - `url: str`
    </Card>
    <Card title="close_tab">
        **Description:** Close an existing tab.

        **Parameters:**
        - `page_id: int`
    </Card>
</CardGroup>


## Content Extraction

<CardGroup>
    <Card title="extract_structured_data">
        **Description:** Extract structured, semantic data (e.g. product description, price, all information about XYZ) from the current webpage based on a textual query.

        **Parameters:**
        - `query: str`
    </Card>
    <Card title="get_ax_tree">
        **Description:** Get the accessibility tree of the page in the format "role name".

        **Parameters:**
        - `number_of_elements: int`
    </Card>
     <Card title="save_pdf">
        **Description:** Save the current page as a PDF file.

        **Parameters:** None
    </Card>
</CardGroup>

## Task Completion

<CardGroup>
    <Card title="done">
        **Description:** Complete task - provide a summary of results for the user. Set success=True if task completed successfully, false otherwise.

        **Parameters:**
        - `text: str`
        - `success: bool`
        - `files_to_display: list[str] | None`
    </Card>
</CardGroup>

## Extended Actions

Aery Browser includes a powerful set of extended actions for handling more complex scenarios like:
- **Alerts and Modals:** Handle javascript alerts, confirms, and custom modal dialogs.
- **Advanced File Uploads:** Upload files using different strategies like input fields or drag-and-drop.
- **HTTP Requests:** Make direct HTTP requests using the browser's context (cookies, headers).
- **Smart Forms:** Intelligently fill out forms by detecting and mapping fields.
- **Smart Dropdowns:** Interact with custom and native dropdowns with fallback mechanisms.
- **Google Sheets:** Read and write data to Google Sheets.

These will be documented in a separate section. 