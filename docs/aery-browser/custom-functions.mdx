---
title: 'Custom Functions'
---

import { Card } from 'mintlify/components';

While Aery Browser comes with a rich set of built-in actions, you will often need to extend its capabilities with your own custom logic. You can do this by creating **Custom Functions** (also known as custom actions).

This allows you to integrate third-party APIs, perform complex calculations, or interact with local system resources, all from within the agent's control loop.

## Creating a Custom Function

A custom function is an `async` Python function that you register with a `Controller` instance.

The most important part is the function's **docstring**. The docstring is sent to the LLM as the description of the action, so it must be clear and concise, explaining what the action does and what its parameters are for.

```python
import asyncio
from aery_browser.controller import Controller
from aery_browser.agent.views import ActionResult

# 1. Create a Controller instance
my_controller = Controller()

# 2. Define your async function and decorate it
@my_controller.registry.action("Get the current weather for a given city.")
async def get_weather(city: str) -> ActionResult:
    """
    Asynchronously retrieves the current weather for a specified city.
    
    This docstring is for your own code readability; the description in 
    the decorator is what the LLM will see.
    """
    # In a real implementation, you would call a weather API here.
    await asyncio.sleep(1) # Simulate API call
    
    if city.lower() == "london":
        result = "The weather in London is cloudy with a chance of rain."
    elif city.lower() == "tokyo":
        result = "The weather in Tokyo is sunny and warm."
    else:
        result = f"Sorry, I don't have the weather for {city}."
        
    # 3. Return an ActionResult
    return ActionResult(
        extracted_content=result,
        long_term_memory=f"Retrieved weather for {city}: {result}"
    )
```

## Using the Custom Function

Once you have registered your function with the controller, you must pass that controller instance to the `Agent`. The agent will then be able to use your new function just like any built-in action.

```python
from aery_browser import Agent

# Assume my_controller and get_weather are defined as above

# 4. Pass the controller to the Agent
agent = Agent(
    task="What is the weather like in London?",
    llm=my_llm,
    controller=my_controller # Use your custom controller
)

# The agent can now decide to call get_weather
# It would generate an action like:
# {"action": [{"get_weather": {"city": "London"}}]}

history = await agent.run()

print(history.final_result())
# Expected Output: The weather in London is cloudy with a chance of rain.
```

## Key Considerations

<Card title="Use Type Hinting" icon="circle-check">
Always use Python type hints for your function's parameters (e.g., `city: str`). Aery Browser uses these hints to validate the parameters provided by the LLM, preventing errors and ensuring the data is in the correct format.
</Card>

<Card title="Return an ActionResult" icon="arrow-left">
Your function must return an `ActionResult` object. This object is how you provide feedback to the agent.
- `extracted_content`: The main result of your function. This is shown to the agent in the `read_state` for the *next step only*.
- `long_term_memory`: A concise summary of the action's outcome. This is added to the agent's long-term memory to inform future steps.
- `error`: If something went wrong, provide an error message here.
</Card> 