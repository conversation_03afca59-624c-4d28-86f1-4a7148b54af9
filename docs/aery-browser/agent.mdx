---
title: 'Agent'
---

import { <PERSON>, Cards } from 'mintlify/components';

# Agent

The `Agent` class is the core component of Aery Browser. It's responsible for executing tasks by interacting with a web browser. It uses a Language Model (LLM) to decide the next actions to take based on the current state of the browser and the given task.

## Overview

The `Agent` class orchestrates the entire browser automation process. It manages the browser session, message history with the LLM, and the execution of actions. It's designed to be highly configurable to adapt to different tasks and scenarios.

## Initialization

To create an agent, you need to instantiate the `Agent` class. The constructor accepts a variety of parameters to configure its behavior.

```python
from aery_browser.agent import Agent
from langchain_openai import ChatOpenAI

# Initialize the LLM
llm = ChatOpenAI(model="gpt-4-turbo", temperature=0)

# Create an agent instance
agent = Agent(
    task="Go to mintlify.com and tell me what it is.",
    llm=llm,
)

# Run the agent
history = await agent.run()
```

### Parameters

The `__init__` method of the `Agent` class has many parameters. Here are some of the most important ones:

<CardGroup>
    <Card title="task">
        **Type:** `str`

        The main goal or task for the agent to accomplish.
    </Card>
    <Card title="llm">
        **Type:** `BaseChatModel`

        An instance of a LangChain chat model (e.g., `ChatOpenAI`, `ChatGoogleGenerativeAI`). This model will be used to decide the agent's actions.
    </Card>
    <Card title="browser_session">
        **Type:** `BrowserSession` (optional)

        An existing `BrowserSession` to reuse. If not provided, a new one will be created.
    </Card>
    <Card title="controller">
        **Type:** `Controller` (optional)

        A controller instance that manages the available actions for the agent.
    </Card>
     <Card title="use_vision">
        **Type:** `bool` (optional, default: `True`)

       Enable vision capabilities for the agent, allowing it to process screenshots of the page.
    </Card>
    <Card title="memory_config">
        **Type:** `MemoryConfig` (optional)

        Configuration for the agent's memory system.
    </Card>
</CardGroup>


## Main Methods

Here are the main methods to control the agent's lifecycle:

<CardGroup>
    <Card title="run">
        `async def run(max_steps: int = 100) -> AgentHistoryList:`

        Executes the task until it's completed or the `max_steps` limit is reached.
    </Card>
    <Card title="step">
       `async def step() -> None:`

        Executes a single step of the task.
    </Card>
    <Card title="pause">
        `def pause() -> None:`

        Pauses the agent execution.
    </Card>
    <Card title="resume">
        `def resume() -> None:`

        Resumes a paused agent.
    </Card>
    <Card title="stop">
       `def stop() -> None:`

        Stops the agent execution permanently.
    </Card>
</CardGroup>

## Configuration and State

-   **`AgentSettings`**: This Pydantic model holds the configuration for the agent, such as `use_vision`, `max_failures`, etc. An instance of this class is available at `agent.settings`.
-   **`AgentState`**: This model holds the dynamic state of the agent, including `n_steps`, `history`, and `last_result`. An instance is available at `agent.state`. 