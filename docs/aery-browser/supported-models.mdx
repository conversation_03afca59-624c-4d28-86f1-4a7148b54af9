---
title: 'Supported Models'
---

<PERSON><PERSON> is designed to be model-agnostic, meaning it can work with any Large Language Model (LLM) that is compatible with the [LangChain `BaseChatModel`](https://python.langchain.com/docs/modules/model_io/chat/) interface. This flexibility allows you to choose the best model for your specific task, whether it's for performance, cost, or specific capabilities like vision.

## How it Works

When you initialize the `Agent`, you pass an instantiated LLM object. <PERSON><PERSON>er then automatically inspects the model to determine the best way to interact with it, a process that includes:

1.  **Tool Calling Detection:** The agent tests different methods (`function_calling`, `tools`, `json_mode`, `raw`) to find the most capable one supported by your LLM for structured output. This ensures reliable action execution.
2.  **API Key Verification:** It checks if the necessary environment variables for the model's API key are set.
3.  **Vision Support:** If you enable `use_vision=True`, it will check if the model can process images.

## Recommended Models

While any LangChain-compatible model should work, here are examples for some of the most commonly used providers.

### OpenAI

OpenAI models are well-tested and generally provide the best performance for complex web automation tasks, especially with their advanced function calling capabilities.

```python
from langchain_openai import ChatOpenAI
from aery_browser.agent import Agent

# Required environment variable: OPENAI_API_KEY
llm = ChatOpenAI(model="gpt-4-turbo", temperature=0)

agent = Agent(task="Your task here", llm=llm)
```

### Anthropic (Claude)

Anthropic's Claude models, particularly Claude 3, are also a strong choice.

```python
from langchain_anthropic import ChatAnthropic
from aery_browser.agent import Agent

# Required environment variable: ANTHROPIC_API_KEY
llm = ChatAnthropic(model="claude-3-opus-20240229", temperature=0)

agent = Agent(task="Your task here", llm=llm)
```

### Google (Gemini)

Google's Gemini models are another powerful option.

```python
from langchain_google_genai import ChatGoogleGenerativeAI
from aery_browser.agent import Agent

# Required environment variable: GOOGLE_API_KEY
llm = ChatGoogleGenerativeAI(model="gemini-1.5-pro-latest", temperature=0)

agent = Agent(task="Your task here", llm=llm)
```

### Local Models with Ollama

You can run models locally using Ollama. This is a great option for privacy, cost-saving, or offline use. Keep in mind that the quality of the agent's performance will heavily depend on the capability of the local model you choose.

```python
from langchain_community.chat_models import ChatOllama
from aery_browser.agent import Agent

# No API key needed, but Ollama must be running
llm = ChatOllama(model="llama3")

# Vision is not typically supported by base local models
agent = Agent(task="Your task here", llm=llm, use_vision=False)
``` 