---
title: Installation
description: How to install and set up the QAK environment.
---

# Installation Guide

It is highly recommended to use a virtual environment to avoid dependency conflicts.

## Prerequisites

- Python 3.11 or higher
- A Google Gemini API Key

## Setup in Linux/MacOS

1.  **Create a virtual environment** (ensure you are using Python 3.11+):
    ```bash
    python3.11 -m venv .venv
    ```

2.  **Activate the virtual environment**:
    ```bash
    source .venv/bin/activate
    ```

3.  **Install dependencies**:
    ```bash
    pip install -r requirements.txt
    ```

4.  **(Optional)** If you see a conflict error with `google-ai-generativelanguage`:
    ```bash
    pip install --force-reinstall --no-deps google-generativeai
    ```

5.  **Install Playwright browsers** (required for browser automation):
    ```bash
    playwright install
    ```

## Setup in Windows (PowerShell)

1.  **Create a virtual environment**:
    ```powershell
    python -m venv .venv
    ```

2.  **Activate the virtual environment**:
    ```powershell
    .\.venv\Scripts\Activate.ps1
    ```
    > If you encounter an execution policy error in PowerShell, run:
    > `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

3.  **Install dependencies**:
    ```powershell
    pip install -r requirements.txt
    ```

## Configure API Key

Create a `.env` file in the root of the project and add your Google Gemini API Key:

```env
GOOGLE_API_KEY=your_api_key_here
LLM_MODEL=gemini-2.5-flash
PROMPT_LANGUAGE=es
```

Once the setup is complete, you can run the application. 