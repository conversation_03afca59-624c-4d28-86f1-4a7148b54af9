---
title: Introduction to QAK
description: AI-powered test automation that transforms user stories into executable code.
---

# Introduction to QAK

QAK is an AI-powered test automation tool that transforms user stories into executable automation code, enabling the generation and execution of automated tests from natural language instructions.

## Key Features

- **Full Test Process**: Complete workflow from user stories to automation code.
- **Smoke Tests**: Direct execution of tests without intermediate steps.
- **Multiple Frameworks**: Support for various automation frameworks like Selenium, Playwright, and Cypress.
- **Real Browser Execution**: Tests are run in a real browser, powered by Playwright.
- **Project Management**: Organize your tests into projects and suites.
- **Test History**: Detailed logging and history of all test executions.

This documentation will guide you through installing, configuring, and using the full potential of QAK for your testing needs. 