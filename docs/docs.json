{"$schema": "https://mintlify.com/docs.json", "name": "QAK Platform", "theme": "mint", "colors": {"primary": "#4D45A9", "light": "#6F6C9E", "dark": "#2A2859"}, "navigation": {"groups": [{"group": "Introduction", "icon": "book-open", "pages": ["introduction"]}, {"group": "Getting Started", "icon": "rocket", "pages": ["getting-started/installation", "cli-reference"]}, {"group": "API Reference", "icon": "code", "pages": [{"group": "Endpoints", "openapi": "openapi.json"}]}, {"group": "<PERSON><PERSON>", "icon": "desktop", "pages": ["aery-browser/introduction", "aery-browser/configuration", "aery-browser/supported-models", {"group": "Agent", "icon": "user", "pages": ["aery-browser/agent", "aery-browser/agent-settings", "aery-browser/system-prompt", "aery-browser/memory", "aery-browser/planner"]}, {"group": "Browser", "icon": "browser", "pages": ["aery-browser/browser-settings", "aery-browser/connect-to-browser"]}, {"group": "Actions", "icon": "lightning", "pages": ["aery-browser/actions", "aery-browser/extended-actions"]}, {"group": "Extensibility", "icon": "puzzle-piece", "pages": ["aery-browser/custom-functions", "aery-browser/lifecycle-hooks"]}, {"group": "Handling Data", "icon": "shield-check", "pages": ["aery-browser/sensitive-data", "aery-browser/outputs"]}]}]}, "footerSocials": {"github": "https://github.com/nahuelcio"}}