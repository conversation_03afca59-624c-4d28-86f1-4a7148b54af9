# 🧠 Browser-Use Agent System – Quick & Friendly Guide

> "I just want to know **how the magic works** without reading 2 000 lines of code."

This document is a **bird-eye explanation** of the main pieces you will see in the `libs/browser_use` package.  It skips deep implementation details and focuses on **the mental model** you need to extend or debug the agents.

---

## 1. Big Picture

```
┌───────────┐        requests / tasks        ┌────────────┐
│  *You*    │  ───────────────────────────▶  │   Agent    │
└───────────┘                                 │(LLM brain) │
       ▲                                      │            │
       │                           ┌────────▶ │ Controller │
       │                           │          └─────┬──────┘
       │                           │                │ performs
       │                           │                ▼
       │                       available      ┌───────────┐
       │                       actions        │  Browser  │
       │                           │          │  Session  │
       │                           │          └─────┬──────┘
       │                           │                │ DOM / network
result / files                     │                ▼
       │                           │          ┌────────────┐
       └──────────────────────────────────────│   Web      │
                                              └────────────┘
```

1. **Agent (`agent/service.py`)** – Orchestrates everything; lives in an async loop → _Think – Act – Observe_.
2. **Controller (`controller/service.py`)** – Exposes **actions** the LLM can call (click, type, read file…).  It is basically a library of tools.
3. **BrowserSession (`browser/session.py`)** – Thin wrapper around Playwright/Patchright that handles pages, tabs & screenshots.
4. **MessageManager** – Keeps the chat history under the token limit and injects the current state (DOM, files, goals…).
5. **Memory modules** –
   * _Procedural Memory_ (summaries of previous steps)
   * _Semantic Memory_ (patterns learnt over multiple runs)
6. **Extras** – Recovery system, efficiency scorer, dynamic prompt helper… these improve reliability but are **optional to understand the core loop**.

---

## 2. The Agent Loop (high level)

| Step | What happens | Important classes |
|------|--------------|-------------------|
| 1 | Capture **browser state** (DOM, screenshot, URL, file system, history) | `BrowserSession`, `DomService`, `BrowserStateSummary` |
| 2 | Send a **prompt** to the LLM that includes:<br>• user request<br>• current state<br>• list of available `actions` | `MessageManager`, `SystemPrompt`, `AgentMessagePrompt` |
| 3 | LLM returns **JSON** → `AgentOutput` with a list of actions |  |
| 4 | `Controller` validates & executes those actions (Playwright calls, file ops, etc.) | `Registry`, `Controller.act` |
| 5 | Results/errors are fed back, loop continues until the agent calls **`done`** | `ActionResult`, history objects |

The loop is **asynchronous** and wrapped with many helpers (retry, token counting, GIF creation, etc.) but the above is the essence.

---

## 3. Key Concepts in Plain Words

### Actions & Registry
* Think of the **registry** as a _menu_ handed to the LLM.  Each menu item maps to an async Python function.
* Actions automatically get Pydantic param models so the LLM knows required fields.
* You can add a new action with:
  ```python
  @controller.registry.action("Describe the action in natural language")
  async def my_action(foo: str, page: Page):
      ...
  ```

### BrowserSession
* Wraps Playwright so that the rest of the code never deals with raw `page` objects.
* Handles: new tabs, scrolling, screenshots, security (allowed domains), saving cookies.
* Provides **get_state_summary()** → builds the tree that the LLM sees.

### Memory
* **Procedural** – every *N* steps turns verbose history → concise summary to save tokens.
* **Semantic** – optional Mongo-backed store that learns patterns (e.g., common login flow selectors).

### Dynamic Prompts & Optimizers
* `DynamicPromptManager` — detects page type (login, search, form) → injects a tiny hint like "group login actions".
* `ActionSequencePredictor` — if the LLM forgot to group `[email,password,submit]`, it can auto-group.

### Recovery & Verification
* `IntelligentRecoverySystem` – tries to fix common failures (element not found, navigation timeout, captcha).
* `PostActionVerifier` – after a critical flow (login, form submit) double-checks success indicators.

---

## 4. Typical Control Flow in Code

```python
async with BrowserSession(headless=False) as browser:
    agent = Agent(
        task="Login to Gmail and grab inbox count",
        llm=openai_chat,
        browser=browser,
    )
    history = await agent.run(max_steps=20)
    print(history.final_result())
```

1. `Agent.__init__` sets up FileSystem, Controller actions, MessageManager, Memory…
2. `Agent.run()` starts the loop.  Each iteration:
   * **state = browser_session.get_state_summary()**
   * **llm_output = self.get_next_action(messages)**
   * **results = controller.act(llm_output.action)**
3. The loop stops when an action of type `done` is executed or `max_steps` reached.

---

## 5. Where to Tweak Things

| I want to… | File / Class |
|------------|-------------|
| Add a new tool/action | `controller/service.py` (or an extension in `controller/extensions/…`) |
| Change the system prompt | `agent/system_prompt.md` |
| Alter grouping rules (login/search) | `agent/action_predictor.py` |
| Plug a new storage backend | `filesystem/storage_backend.py` |
| Disable memory | pass `enable_memory=False` to `Agent` |

---

## 6. Glossary

* **LLM** – Large Language Model (ChatGPT, Claude, Gemini…)
* **Playwright** – Headless browser automation library used under the hood.
* **Patchright** – Stealth fork of Playwright (used when `stealth=True`).
* **Controller** – Collection of callable actions exposed to the LLM.
* **Registry** – Internal store that maps action names ↔ functions.
* **AgentOutput** – JSON schema returned by the LLM describing thinking + actions.
* **ActionResult** – What Python got back after executing an action.

---

## 7. Next Steps

1. **Run the CLI**: `python -m libs.browser_use.cli --prompt "Search cat pictures"`
2. **Read `controller/extensions`** to see advanced, plug-and-play actions (CAPTCHA handler, smart forms…).
3. **Create your own action** and watch it appear automatically in the prompt!

Enjoy automating the web 🚀 