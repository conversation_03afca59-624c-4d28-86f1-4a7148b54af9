# 🌐 Hybrid Execution Modes in **browser_use**

> **Goal**: Let developers choose _how much autonomy_ the LLM agent has, from fully scripted to fully autonomous, with graceful fall-back in between.

This guide covers **three canonical modes** and shows, line-by-line, how to activate each one.

1. **Pure LLM / Conversational** – _"Just tell the task and let the agent figure it out."_
2. **Pure Script (Deterministic Replay)** – _"Run exactly this Playwright flow, no extra thinking."_
3. **Hybrid (Script → LLM Fallback)** – _"Follow my script but improvise if the DOM changes."_

---

## 0. Boilerplate Setup

```python
from browser_use.agent.service import Agent
from browser_use.browser.session import BrowserSession
from langchain.chat_models import Chat<PERSON><PERSON>AI  # or any other LLM you prefer

llm = ChatOpenAI(model_name="gpt-4o", temperature=0.2)

# Use a persistent profile so cookies survive between modes (optional)
profile = {
    "user_data_dir": "~/.config/browseruse/profiles/gmail",
    "headless": False,
}
browser = BrowserSession(**profile)  # async context manager compatible
```

You can now pick one of the following modes ↓

---

## 1. Pure LLM Mode

```python
async with browser:
    agent = Agent(
        task="Login to Gmail and grab inbox count",
        llm=llm,
        browser=browser,
    )
    history = await agent.run(max_steps=25)
    print(history.final_result())
```

* Agent starts **with an empty plan**.
* `MessageManager` injects DOM + file system context.
* LLM chooses tools from the `Controller.registry`.
* Resilience via recovery, semantic memory, dynamic prompts…

When to use: rapid prototyping, unknown UI, exploratory tasks.

---

## 2. Pure Script Mode (Deterministic Replay)

### 2.1 Record once

Run the agent in mode 1 and save its successful history:

```python
history.save_to_file("gmail_login_history.json")
```

*(Soon there will be a helper: `agent.save_history()`)*

### 2.2 Replay later

```python
from browser_use.agent.views import AgentOutput
from browser_use.agent.views import AgentHistoryList

history = AgentHistoryList.load_from_file("gmail_login_history.json", AgentOutput)

async with browser:
    agent = Agent(task="Replay", llm=llm, browser=browser)
    results = await agent.rerun_history(history, skip_failures=False)
    print(results[-1].extracted_content)
```

• **No LLM calls** – The JSON already contains every `ActionModel`.  
• `_update_action_indices()` auto-fixes minor index shifts.  
• If an element disappeared → raises error (unless `skip_failures=True`).

When to use: smoke-tests, performance, demos where UI rarely changes.

---

## 3. Hybrid Mode (Script + LLM Fallback)

### 3.1 Option A – `initial_actions` parameter

```python
initial = [
    {"go_to_url": {"url": "https://mail.google.com"}},
    {"input_text": {"index": 12, "text": "<EMAIL>"}},
    {"input_text": {"index": 14, "text": "••••••"}},
    {"click_element_by_index": {"index": 23}},
]

async with browser:
    agent = Agent(
        task="Check Gmail inbox count",
        llm=llm,
        browser=browser,
        initial_actions=initial,          # ← our mini-script
    )
    history = await agent.run(max_steps=20)
```

Flow:
1. Agent executes `initial_actions` **verbatim** via `multi_act()`.*
2. If any action errors, `_handle_step_error` triggers recovery.
3. Whether errors or not, the **normal LLM loop** resumes afterwards, so the agent can click new buttons, scroll, etc.

> Tip — Combine with `PostActionVerifier` to confirm login success.

### 3.2 Option B – Custom registry action wrapping Playwright script

1. **Create a file** `my_playwright_flow.py` (pure Playwright commands).  
2. **Wrap** it in an action:
   ```python
   # controller/extensions/custom_scripts.py
   from browser_use.controller.registry.service import Controller
   from playwright.async_api import async_playwright

   def register_custom_script_actions(controller: Controller):
       @controller.registry.action("Run my Gmail login script and fallback to AI")
       async def run_gmail_script(script_path: str, browser_session):
           try:
               # Re-use the running browser via CDP URL
               async with async_playwright() as p:
                   browser = await p.chromium.connect_over_cdp(browser_session.cdp_url)
                   context = browser.contexts[0]
                   page = context.pages[0]
                   exec(open(script_path).read(), {"page": page})
               return "Script executed OK"
           except Exception as e:
               # Return no error → agent continues thinking
               return f"Script failed: {e} (AI will recover)"
   ```
3. **Register** the extension at agent creation (`Controller(exclude_actions=[])` loads them automatically if in `extensions`).
4. In the prompt (or programmatically) call:
   ```json
   {
     "action": [{"run_gmail_script": {"script_path": "my_playwright_flow.py"}}]
   }
   ```

If the script partially works but some selector is stale, the next LLM step receives fresh DOM and continues.

### 3.3 Option C – Smart `rerun_history` with fallback

```python
history = AgentHistoryList.load_from_file("gmail_login_history.json", AgentOutput)

async with browser:
    agent = Agent(task="Hybrid replay", llm=llm, browser=browser)
    # skip_failures=True  →  when mismatch occurs the LLM loop starts automatically
    await agent.rerun_history(history, skip_failures=True)
    # Now call run() but with fewer steps – most of the job is done
    await agent.run(max_steps=10)
```

---

## 4. Choosing the Right Mode

| Criteria | Recommended Mode |
|----------|------------------|
| Unknown website, exploratory task | **Pure LLM** |
| Stable internal dashboard, daily smoke test | **Pure Script** |
| Public site that changes UI occasionally | **Hybrid** |
| You have legacy Selenium/Playwright tests but want resilience | Wrap them as **Hybrid Option B** |

---

## 5. FAQs

### Q – Can I switch mode on the fly?
**Yes.** You can call `agent.pause()`, perform `agent.rerun_history(...)`, then `agent.resume()`.

### Q – How do I generate a Playwright script automatically?
Call `history.save_as_playwright_script("flow.py")` (feature in progress).

### Q – What about performance?
* Replaying actions skips LLM calls → near-zero token cost.  
* Hybrid avoids **most** tokens while still healing minor UI drift.

---

## 6. Next Experiments

* Combine **Benchmarks** (`browser_use.testing.benchmarks`) with Hybrid mode to measure speedups.  
* Store successful flows in `filesystem` → let the LLM pick between *"use cached script"* vs *"think from scratch"*.

Happy automation! 🚀 