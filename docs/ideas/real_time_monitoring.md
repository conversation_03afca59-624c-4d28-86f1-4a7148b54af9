# 📡 Real-Time Monitoring, Pause/Resume & On-The-Fly Commands

This document shows **one practical way** to let you:

1. Watch each agent step (thinking, actions, screenshot) live in your browser.
2. Click **Pause**, issue extra instructions, then **Resume** the same run.

It re-uses hooks that already exist (`register_new_step_callback`, `pause`, `resume`) and adds:

* A **WebSocket** channel  (`/ws/agent/{session_id}`) that streams JSON per step.
* Simple **REST endpoints** to pause/resume or enqueue new goals.
* A tiny **Next.js** page that visualises the stream and exposes controls.

> You can swap FastAPI/Next.js for any framework – the pattern is agnostic.

---

## 0. Quick Mental Model

```
┌────────────┐    JSON events     ┌───────────────┐    WebSocket → 🔵 Browser
│  Agent     │ ───────────────▶  │ FastAPI WS    │ ────────────────────────────▶  React/Next UI
│ (Python)   │                   │ endpoint      │
└────┬───────┘                   └──────┬────────┘
     │  pause/resume (REST)             │
     │  enqueue new goal (REST)         │
     ▼                                  ▼
"task_queue"                    HTML buttons / input
```

* **Agent** publishes events every time `_make_history_item()` is called (end of each step).
* **FastAPI** broadcasts the payload to everybody connected to that session.
* U<PERSON> receives: _step #_, _thinking_, _actions_, _result_, _screenshot b64_, …
* UI can hit `POST /agent/{id}/pause`, `POST /agent/{id}/resume`, or `POST /agent/{id}/add_task`.

---

## 1. Backend – Streaming Events

### 1.1 WebSocket endpoint

```python
# src/api/v2/realtime_routes.py
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from uuid import UUID

router = APIRouter(prefix="/ws")

active_connections: dict[UUID, set[WebSocket]] = {}

@router.websocket("/agent/{session_id}")
async def agent_ws(ws: WebSocket, session_id: UUID):
    await ws.accept()
    active_connections.setdefault(session_id, set()).add(ws)
    try:
        while True:
            await ws.receive_text()  # keep-alive – ignore content
    except WebSocketDisconnect:
        active_connections[session_id].remove(ws)
```

### 1.2 Helper to push

```python
# libs/realtime/broadcast.py
from uuid import UUID
from .realtime_routes import active_connections
import json, asyncio

async def broadcast_step(session_id: UUID, payload: dict):
    """Send payload to all sockets for a session, best-effort."""
    conns = active_connections.get(session_id, set())
    if not conns:
        return
    data = json.dumps(payload, default=str)
    for ws in conns.copy():
        try:
            await ws.send_text(data)
        except Exception:
            conns.discard(ws)
```

### 1.3 Wire into `Agent`

```python
from libs.realtime.broadcast import broadcast_step

async def on_step(browser_state: BrowserStateSummary, model_out: AgentOutput, n: int):
    payload = {
        "step": n,
        "thinking": model_out.thinking,
        "actions": [a.model_dump(exclude_unset=True) for a in model_out.action],
        "url": browser_state.url,
        "title": browser_state.title,
        "screenshot": browser_state.screenshot,  # b64 PNG
        "result": [r.model_dump(exclude_unset=True) for r in self.state.last_result],
    }
    await broadcast_step(self.session_id, payload)

agent = Agent(
    task="Login …",
    llm=llm,
    browser_session=session,
    register_new_step_callback=on_step,
)
```

Notes:
• `register_new_step_callback` can be **async**.  
• If you run multiple agents, include `agent.id` or `task_id`.

---

## 2. Backend – Pause / Resume / Add Task

```python
# src/api/v2/agent_control_routes.py
from fastapi import APIRouter, HTTPException
from uuid import UUID

router = APIRouter(prefix="/agent")

tagents: dict[UUID, Agent] = {}

@router.post("/{id}/pause")
async def pause_agent(id: UUID):
    agent = agents.get(id)
    if not agent:
        raise HTTPException(404)
    agent.pause()
    return {"status": "paused"}

@router.post("/{id}/resume")
async def resume_agent(id: UUID):
    agent = agents.get(id) or _err()
    agent.resume()
    return {"status": "running"}

@router.post("/{id}/add_task")
async def add_subtask(id: UUID, body: dict):
    txt = body.get("instruction")
    agent = agents.get(id) or _err()
    agent.add_new_task(txt)  # re-uses existing loop
    return {"status": "queued", "instruction": txt}
```

> In many cases you'll keep `agents` inside something like **execution_context**.

---

## 3. Front-End (Next.js)

A minimal React hook to attach to the socket:

```tsx
// web/src/hooks/useAgentStream.ts
import { useEffect, useState } from "react";

export interface StepEvent {
  step: number;
  thinking: string;
  actions: any[];
  url: string;
  title: string;
  screenshot?: string;
  result: any[];
}

export function useAgentStream(sessionId: string) {
  const [events, setEvents] = useState<StepEvent[]>([]);

  useEffect(() => {
    const ws = new WebSocket(`ws://localhost:8000/ws/agent/${sessionId}`);
    ws.onmessage = (ev) => {
      const data: StepEvent = JSON.parse(ev.data);
      setEvents((prev) => [...prev, data]);
    };
    return () => ws.close();
  }, [sessionId]);

  return events;
}
```

Then a page:

```tsx
// web/src/app/agent-debugger/page.tsx
"use client";
import { useAgentStream } from "../../hooks/useAgentStream";
import Image from "next/image";

export default function Debugger() {
  const sessionId = "<uuid-from-backend>";
  const steps = useAgentStream(sessionId);

  return (
    <main className="p-4 flex gap-4">
      <section className="w-1/2 h-screen overflow-auto bg-gray-900 text-green-200 font-mono p-2">
        {steps.map((s) => (
          <div key={s.step} className="mb-4">
            <h3>Step {s.step}: {s.title}</h3>
            <pre>{s.thinking}</pre>
            <pre>{JSON.stringify(s.actions, null, 2)}</pre>
            <pre>{JSON.stringify(s.result, null, 2)}</pre>
          </div>
        ))}
      </section>
      <section className="w-1/2">
        {steps.length > 0 && steps[steps.length - 1].screenshot && (
          <Image
            src={`data:image/png;base64,${steps[steps.length - 1].screenshot}`}
            alt="screenshot"
            width={800}
            height={600}
          />
        )}
      </section>
    </main>
  );
}
```

Add Pause/Resume buttons that `fetch('/agent/{id}/pause', {method:'POST'})`.

---

## 4. Putting It All Together

1. **Backend**: Mount both routers in `src/api/api.py`:
   ```python
   app.include_router(realtime_routes.router)
   app.include_router(agent_control_routes.router)
   ```
2. **Run**: `uvicorn src.api.api:app --reload` and Next.js dev server (`npm run dev`).
3. Start an agent through any channel, store it in `agents` dict.
4. Open `/agent-debugger` – you see steps live, can pause/resume, add instructions.

---

## 5. Advanced Tips

* **Streaming token-level thinking** – also broadcast `parsed.current_state.thinking` _before_ executing actions by tapping into `get_next_action`.
* **Multiple agents** – namespace by `task_id` or add a room param.
* **Security** – add auth headers or signed JWT for WS/REST.
* **Replay** – persist events to Redis → let late joiners catch up.

---

### Minimum Patch Summary

1. `docs/real_time_monitoring.md` (this file)
2. `src/api/v2/realtime_routes.py` & `agent_control_routes.py`
3. `libs/realtime/broadcast.py`
4. Modify `Agent` creation: pass `register_new_step_callback`.
5. React hook + page under `web/src/app/agent-debugger`.

That's it – you now have a live dashboard and **interactive control** over the agent loop. 🎛️ 