# VNC Remote Access - Guía de Inicio Rápido

## ¿Qué es VNC en QAK?

VNC (Virtual Network Computing) en QAK permite acceder remotamente al navegador de Playwright CodeGen desde cualquier lugar, especialmente útil en:

- 🖥️ **Servidores sin pantalla** (headless servers)
- 🐳 **Contenedores Docker** en la nube
- 🌐 **Desarrollo remoto** desde cualquier dispositivo
- 📱 **Acceso móvil** a través del navegador web

## Inicio Rápido (5 minutos)

### Paso 1: Verificar Dependencias

```bash
# Verificar si VNC está disponible
curl http://localhost:8000/api/codegen/vnc-dependencies
```

Si faltan dependencias, instalar automáticamente:

```bash
# macOS
brew install --cask xquartz
brew install x11vnc dwm
npm install -g websockify

# Linux (Ubuntu/Debian)
sudo apt-get install xvfb x11vnc fluxbox
sudo npm install -g websockify
```

### Paso 2: Crear Se<PERSON> con VNC

#### Opción A: Desde la Interfaz Web
1. Ir a http://localhost:8000/codegen
2. Hacer clic en "Nueva Sesión"
3. ✅ Marcar "Force VNC Mode"
4. Completar URL y configuración
5. Hacer clic en "Iniciar Sesión"

#### Opción B: Via API
```bash
curl -X POST http://localhost:8000/api/codegen/start \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com",
    "target_language": "javascript",
    "force_vnc": true
  }'
```

### Paso 3: Acceder al Navegador Remoto

1. **Desde la Interfaz QAK**: 
   - Ir a la sesión creada
   - Hacer clic en la pestaña "Remote Browser"
   - El navegador aparecerá embebido en la página

2. **Acceso Directo**:
   - VNC Web: http://localhost:6080
   - VNC Cliente: vnc://localhost:5900

## Casos de Uso Principales

### 1. Desarrollo en Servidor Remoto

```bash
# Conectar a servidor via SSH
ssh user@remote-server

# Iniciar QAK con VNC
cd /path/to/qak
source .venv/bin/activate  # Activar entorno virtual
python app.py

# Acceder desde navegador local
# http://remote-server:8000
```

### 2. Deployment en Docker

```bash
# Construir imagen con VNC
docker build -f Dockerfile.vnc -t qak-vnc .

# Ejecutar contenedor
docker run -d \
  --name qak-vnc \
  -p 8000:8000 \
  -p 6080:6080 \
  qak-vnc

# Acceder desde cualquier dispositivo
# http://docker-host:8000
```

### 3. Testing en CI/CD

```yaml
# .github/workflows/test.yml
- name: Run QAK Tests with VNC
  run: |
    export QAK_FORCE_VNC=true
    python -m pytest tests/test_codegen_vnc.py
```

## Configuración Avanzada

### Variables de Entorno

```bash
# Forzar VNC siempre
export QAK_FORCE_VNC=true

# Cambiar puertos base
export QAK_VNC_BASE_PORT=5900
export QAK_NOVNC_BASE_PORT=6080

# Resolución de pantalla
export VNC_RESOLUTION="1920x1080x24"
```

### Configuración de Seguridad (Producción)

```bash
# Crear contraseña VNC
mkdir -p ~/.vnc
x11vnc -storepasswd tu_password ~/.vnc/passwd

# Configurar firewall
sudo ufw allow from TRUSTED_IP to any port 5900:5910
sudo ufw allow from TRUSTED_IP to any port 6080:6090
```

## Solución Rápida de Problemas

### ❌ Error: "VNC dependencies not available"

```bash
# Instalar dependencias automáticamente
./scripts/setup-vnc-server.sh
```

### ❌ Error: "Port already in use"

```bash
# Limpiar procesos VNC huérfanos
pkill x11vnc
pkill Xvfb
pkill websockify

# Reiniciar QAK
source .venv/bin/activate  # Activar entorno virtual
python app.py
```

### ❌ Error: "Cannot connect to VNC"

```bash
# Verificar procesos
ps aux | grep -E "(xvfb|x11vnc|websockify)"

# Verificar puertos
netstat -tulpn | grep -E "(5900|6080)"

# Reiniciar sesión
curl -X POST http://localhost:8000/api/codegen/session/SESSION_ID/stop
```

### ❌ Pantalla negra en VNC

```bash
# Verificar window manager
ps aux | grep fluxbox

# Reiniciar display
export DISPLAY=:0
fluxbox &
```

## FAQ - Preguntas Frecuentes

### ¿Puedo usar VNC en mi máquina local con pantalla?

**Sí**, usando `force_vnc: true` en la configuración de sesión. Útil para:
- Testing de la funcionalidad VNC
- Grabación de sesiones sin interferir con tu pantalla principal
- Desarrollo y debugging

### ¿Cuántas sesiones VNC simultáneas puedo tener?

**Por defecto 10 sesiones**, configurable en `RemoteCodegenService`:
- Cada sesión usa puertos únicos (VNC: 5900+, Web: 6080+)
- Consumo de memoria: ~100-200MB por sesión
- Recomendado monitorear recursos del servidor

### ¿Funciona en Windows?

**Actualmente solo Linux y macOS**. Para Windows:
- Usar Docker con Linux containers
- WSL2 con distribución Linux
- Máquina virtual Linux

### ¿Qué navegadores soporta VNC?

**Todos los navegadores modernos** para el cliente web:
- Chrome/Chromium (recomendado)
- Firefox
- Safari
- Edge

Para el navegador remoto (Playwright):
- Chromium (default)
- Firefox
- WebKit/Safari

### ¿Es seguro para producción?

**Con configuraciones apropiadas, sí**:
- ✅ Usar contraseñas VNC
- ✅ Configurar firewall
- ✅ VPN/SSH tunneling
- ✅ Límites de recursos
- ❌ No usar sin autenticación en internet público

## Soporte y Recursos

### Documentación Completa
- 📚 [VNC Technical Documentation](./vnc-remote-access.md)
- 🐳 [Docker Deployment Guide](../docker/)
- 🔧 [API Reference](./api-reference.md)

### Logs y Debugging
```bash
# Logs detallados
export LOG_LEVEL=DEBUG
python app.py

# Verificar estado VNC
curl http://localhost:8000/api/codegen/vnc-sessions

# Monitorear recursos
docker stats qak-vnc  # Si usando Docker
top -p $(pgrep -f "x11vnc|Xvfb")  # Uso de CPU/memoria
```

### Obtener Ayuda

1. **Issues del Proyecto**: GitHub Issues para bugs y feature requests
2. **Documentación**: Revisar `/docs/` para guías detalladas
3. **Logs**: Siempre incluir logs cuando reportes problemas
4. **Environment**: Especificar OS, versión Python, y configuración

---

**¡Listo!** 🎉 Ahora puedes acceder a Playwright CodeGen remotamente desde cualquier lugar.
