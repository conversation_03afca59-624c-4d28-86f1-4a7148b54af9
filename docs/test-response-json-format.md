# Formato de Respuesta JSON de Tests en QAK

Este documento describe el **formato JSON de respuesta** de la ejecución de tests en QAK, cubriendo:
- Cómo se genera desde `browser_use`
- Transformaciones en el backend
- El formato final que interpreta el frontend (Next.js)
- Compatibilidad con formatos legacy y el modelo estándar actual

---

## 1. Flujo General

1. **Ejecución**: El agente de `browser_use` ejecuta los pasos del test y genera un historial detallado (`AgentHistoryList`).
2. **Backend**: El backend transforma este historial al formato estándar (`StandardResult`) usando `result_transformer.py` y lo adapta para el frontend.
3. **Frontend**: Next.js espera un formato unificado, pero soporta tanto el formato estándar como el legacy.

---

## 2. Formato Estándar: `StandardResult`

Definido en `src/models/standard_result.py`. Es el formato recomendado y futuro para todas las respuestas.

### Estructura principal

```jsonc
{
  "execution_id": "string",
  "test_type": "smoke|full|case|suite|codegen",
  "test_id": "string|null",
  "suite_id": "string|null",
  "project_id": "string|null",
  "status": "success|failure|error|running|pending|paused|cancelled",
  "started_at": "ISO8601 string",
  "completed_at": "ISO8601 string|null",
  "duration_ms": 1234,
  "summary": {
    "total_steps": 5,
    "successful_steps": 4,
    "failed_steps": 1,
    "total_test_cases": 0,
    "passed_test_cases": 0,
    "failed_test_cases": 0,
    "success_rate": 0.8
  },
  "steps": [
    {
      "step_number": 1,
      "action_type": "navigate|click|input|done|...",
      "description": "Descripción del paso o acción realizada",
      "success": true,
      "duration_ms": 123,
      "error_message": null,
      "screenshot_url": "string|null",
      "element_info": { /* info del elemento DOM */ },
      "url": "string|null",
      "metadata": { /* info adicional */ }
    }
    // ... más pasos
  ],
  "errors": ["string", ...],
  "artifacts": {
    "screenshots": ["url", ...],
    "videos": ["url", ...],
    "logs": ["url", ...],
    "generated_code": "string|null",
    "history_file": "string|null",
    "gherkin_scenarios": ["string", ...]
  },
  "configuration": { /* config de browser usada */ },
  "success": true,
  "message": "string|null",
  "error": "string|null",
  "raw_data": { /* datos crudos para debug */ },
  "raw_result": [ /* resultados crudos */ ],
  "metadata": { /* metadatos adicionales */ }
}
```

### Campos clave
- **steps**: Array de pasos, cada uno con tipo, descripción, éxito, error, screenshot, etc.
- **summary**: Estadísticas agregadas.
- **artifacts**: URLs de screenshots, videos, logs, código generado, etc.
- **success**: Booleano global (para compatibilidad).
- **status**: Estado detallado (éxito, error, corriendo, etc).
- **metadata**: Metadatos adicionales (IDs, nombres, contexto).

### Ejemplo real
```json
{
  "execution_id": "abc123",
  "test_type": "smoke",
  "status": "success",
  "started_at": "2024-06-01T12:00:00Z",
  "completed_at": "2024-06-01T12:00:10Z",
  "duration_ms": 10000,
  "summary": {
    "total_steps": 3,
    "successful_steps": 3,
    "failed_steps": 0,
    "success_rate": 1.0
  },
  "steps": [
    {
      "step_number": 1,
      "action_type": "navigate",
      "description": "Navega a la página de login",
      "success": true,
      "duration_ms": 200,
      "screenshot_url": "https://.../api/artifacts/xyz.png"
    },
    {
      "step_number": 2,
      "action_type": "input",
      "description": "Completa el usuario y contraseña",
      "success": true
    },
    {
      "step_number": 3,
      "action_type": "done",
      "description": "Verifica login exitoso",
      "success": true
    }
  ],
  "artifacts": {
    "screenshots": ["https://.../api/artifacts/xyz.png"],
    "videos": [],
    "logs": []
  },
  "success": true
}
```

---

## 3. Formato Legacy: `TestExecutionHistoryData`

Usado para compatibilidad y en algunos endpoints antiguos. Definido en `web/src/ai/schemas/test-execution-history-schema.ts`.

### Estructura
```jsonc
{
  "actions": [
    { "step": 1, "type": "navigate", "details": "..." },
    { "step": 2, "type": "input", "details": "..." }
  ],
  "results": [
    { "step": 1, "content": "Navegó a login", "success": true },
    { "step": 2, "content": "Completó formulario", "success": true }
  ],
  "elements": [ /* info de elementos DOM */ ],
  "urls": [ /* urls visitadas */ ],
  "errors": ["string", ...],
  "screenshots": ["string", ...],
  "metadata": {
    "start_time": "ISO8601",
    "end_time": "ISO8601",
    "total_steps": 2,
    "success": true
  },
  "test_id": "string",
  "execution_id": "string"
}
```

### Diferencias clave
- No tiene campos como `artifacts`, `summary` ni `configuration`.
- Los pasos están divididos en `actions` y `results`.
- El frontend soporta ambos formatos, pero se recomienda migrar al estándar.

---

## 4. Transformaciones en el Backend

- El backend **convierte** el historial de browser_use al formato `StandardResult` usando `result_transformer.py`.
- Se agregan URLs accesibles para screenshots/logs (no paths locales).
- Se limpian y serializan fechas, paths, y se agregan metadatos.
- Para endpoints legacy, puede usarse el formato `TestExecutionHistoryData`.

---

## 5. Consumo en el Frontend (Next.js)

- El frontend detecta el formato recibido y usa el viewer adecuado (`RichResultsViewer` para estándar, `ResultsTab` para legacy).
- El esquema TypeScript/Zod está en `web/src/ai/schemas/test-execution-history-schema.ts`.
- El frontend espera:
  - **StandardResult**: campos como `steps`, `summary`, `artifacts`, `success`, `status`.
  - **Legacy**: campos como `actions`, `results`, `metadata`.

---

## 6. Referencias y Mejoras

- Modelo estándar: `src/models/standard_result.py`
- Transformador: `src/core/result_transformer.py`
- Adaptador frontend: `src/utilities/response_transformers.py`
- Esquema frontend: `web/src/ai/schemas/test-execution-history-schema.ts`

### Ideas de mejora
- Unificar completamente el formato estándar y eliminar legacy.
- Documentar todos los posibles valores de `action_type` y campos de `element_info`.
- Incluir siempre `artifacts` y `summary` aunque estén vacíos.
- Mejorar la trazabilidad de errores y logs.

---

**Este documento es una referencia viva y debe actualizarse si se agregan campos o cambia el flujo de datos.** 