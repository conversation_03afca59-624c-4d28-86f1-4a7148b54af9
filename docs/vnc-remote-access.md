# VNC Remote Access - Documentación Técnica

## Introducción

El sistema QAK incluye una integración completa de VNC (Virtual Network Computing) que permite acceder remotamente al navegador de Playwright CodeGen desde cualquier entorno, incluyendo servidores sin interfaz gráfica (headless). Esta funcionalidad es esencial para:

- **Servidores de producción** sin interfaz gráfica
- **Contenedores Docker** en entornos cloud
- **Desarrollo remoto** desde máquinas sin GUI
- **Escalabilidad** en infrastructuras distribuidas

## Arquitectura del Sistema VNC

### Componentes Principales

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   Frontend React    │    │   Backend Python    │    │   VNC Infrastructure │
│                     │    │                     │    │                     │
│ ┌─────────────────┐ │    │ ┌─────────────────┐ │    │ ┌─────────────────┐ │
│ │   VncViewer     │ │◄──►│ │RemoteCodegenSvc │ │◄──►│ │      Xvfb       │ │
│ │   Component     │ │    │ │                 │ │    │ │   (Display)     │ │
│ └─────────────────┘ │    │ └─────────────────┘ │    │ └─────────────────┘ │
│                     │    │                     │    │ ┌─────────────────┐ │
│ ┌─────────────────┐ │    │ ┌─────────────────┐ │    │ │     x11vnc      │ │
│ │  SessionDetails │ │    │ │ API Endpoints   │ │    │ │   (VNC Server)  │ │
│ │     (VNC Tab)   │ │    │ │                 │ │    │ └─────────────────┘ │
│ └─────────────────┘ │    │ └─────────────────┘ │    │ ┌─────────────────┐ │
└─────────────────────┘    └─────────────────────┘    │ │   websockify    │ │
                                                      │ │ (Web Gateway)   │ │
                                                      │ └─────────────────┘ │
                                                      │ ┌─────────────────┐ │
                                                      │ │    fluxbox      │ │
                                                      │ │(Window Manager) │ │
                                                      │ └─────────────────┘ │
                                                      │ ┌─────────────────┐ │
                                                      │ │   Playwright    │ │
                                                      │ │   CodeGen       │ │
                                                      │ └─────────────────┘ │
                                                      └─────────────────────┘
```

### Flujo de Trabajo

1. **Detección de Entorno**: El sistema detecta automáticamente si está ejecutándose en un entorno sin GUI
2. **Inicialización VNC**: Se crean los procesos necesarios (Xvfb, x11vnc, websockify, window manager)
3. **Lanzamiento de Playwright**: CodeGen se ejecuta en el display virtual
4. **Acceso Web**: El usuario accede al navegador a través del cliente VNC embebido en la interfaz web
5. **Interacción Remota**: Las acciones del usuario se transmiten al navegador remoto en tiempo real

## Configuración Técnica

### Dependencias del Sistema

#### Linux (Ubuntu/Debian)
```bash
# Servidor X virtual
sudo apt-get install xvfb

# Servidor VNC
sudo apt-get install x11vnc

# Window Manager ligero
sudo apt-get install fluxbox

# Gateway WebSocket para VNC
sudo npm install -g websockify
```

#### macOS
```bash
# XQuartz (servidor X11)
brew install --cask xquartz

# Servidor VNC
brew install x11vnc

# Window Manager
brew install dwm

# Gateway WebSocket
npm install -g websockify
```

#### Alpine Linux (Docker)
```bash
apk add xvfb x11vnc fluxbox
npm install -g websockify
```

### Configuración de Puertos

| Servicio | Puerto | Descripción |
|----------|--------|-------------|
| VNC Server | 5900+ | Puerto base para conexiones VNC directas |
| noVNC Web | 6080+ | Puerto base para acceso web via websockify |
| QAK Backend | 8000 | API principal del sistema |

**Ejemplo de asignación:**
- Session 1: VNC 5900, Web 6080, Display :0
- Session 2: VNC 5901, Web 6081, Display :1
- Session 3: VNC 5902, Web 6082, Display :2

### Variables de Entorno

```bash
# Forzar modo VNC (para desarrollo/testing)
export QAK_FORCE_VNC=true

# Habilitar VNC globalmente
export QAK_VNC_ENABLED=true

# Puerto base para VNC
export QAK_VNC_BASE_PORT=5900

# Puerto base para noVNC
export QAK_NOVNC_BASE_PORT=6080
```

## Archivos de Configuración

### Backend - RemoteCodegenService

**Ubicación**: `/src/core/remote_codegen_service.py`

**Funcionalidades principales:**
- Gestión de sesiones VNC
- Detección automática de entornos headless
- Monitoreo de procesos en background
- Limpieza automática de recursos
- Verificación de dependencias

```python
# Ejemplo de inicialización
vnc_service = RemoteCodegenService()
vnc_info = await vnc_service.start_remote_codegen_session(
    session_id="uuid-session",
    cmd=["playwright", "codegen", "https://example.com"],
    session_dir="/tmp/session-dir"
)
```

### Frontend - VncViewer Component

**Ubicación**: `/web/src/components/codegen/VncViewer.tsx`

**Características:**
- Cliente VNC embebido usando `@novnc/novnc`
- Estados de conexión en tiempo real
- Modo pantalla completa
- Reconexión automática
- Integración con el sistema de temas (dark/light mode)

```typescript
// Ejemplo de uso
<VncViewer 
  vncInfo={session.vnc_info} 
  className="mt-4"
/>
```

### API Endpoints

#### Verificar Dependencias VNC
```bash
GET /api/codegen/vnc-dependencies

Response:
{
  "all_dependencies_available": true,
  "system": "Darwin",
  "dependencies": {
    "x11vnc": {
      "available": true,
      "path": "/opt/homebrew/bin/x11vnc"
    },
    "websockify": {
      "available": true,
      "path": "/Users/<USER>/.nvm/versions/node/v22.16.0/bin/websockify"
    }
  },
  "installation_commands": {
    "macos": [
      "brew install --cask xquartz",
      "brew install x11vnc dwm",
      "npm install -g websockify"
    ]
  }
}
```

#### Listar Sesiones VNC Activas
```bash
GET /api/codegen/vnc-sessions

Response:
{
  "vnc_enabled": true,
  "total_sessions": 2,
  "sessions": [
    {
      "session_id": "uuid-1",
      "vnc_port": 5900,
      "novnc_port": 6080,
      "web_vnc_url": "http://localhost:6080/vnc.html?host=localhost&port=6080",
      "status": "running",
      "created_at": "2025-06-28T00:15:30.123456"
    }
  ]
}
```

#### Crear Sesión con VNC
```bash
POST /api/codegen/start
Content-Type: application/json

{
  "url": "https://example.com",
  "target_language": "javascript", 
  "headless": false,
  "force_vnc": true
}

Response:
{
  "session_id": "uuid-session",
  "status": "starting",
  "vnc_info": {
    "session_id": "uuid-session",
    "vnc_port": 5900,
    "novnc_port": 6080,
    "display_number": 0,
    "vnc_url": "vnc://localhost:5900",
    "web_vnc_url": "http://localhost:6080/vnc.html?host=localhost&port=6080",
    "status": "running"
  }
}
```

## Despliegue

### Docker - Método Recomendado

#### 1. Construcción de la Imagen
```bash
# Usar Dockerfile.vnc especializado
docker build -f Dockerfile.vnc -t qak-vnc .
```

#### 2. Ejecución del Contenedor
```bash
docker run -d \
  --name qak-vnc \
  -p 8000:8000 \
  -p 5900:5900 \
  -p 6080:6080 \
  -e QAK_VNC_ENABLED=true \
  qak-vnc
```

#### 3. Acceso a la Aplicación
- **Interface Web**: http://localhost:8000
- **VNC Directo**: vnc://localhost:5900
- **VNC Web**: http://localhost:6080

### Servidor Manual

#### 1. Script de Configuración Automática
```bash
# Ejecutar script de setup
chmod +x scripts/setup-vnc-server.sh
./scripts/setup-vnc-server.sh
```

#### 2. Inicio del Servidor
```bash
# Modo desarrollo
python app.py

# Modo producción
gunicorn -w 4 -k uvicorn.workers.UvicornWorker app:app --bind 0.0.0.0:8000
```

### Verificación de Instalación

```bash
# Verificar dependencias
curl http://localhost:8000/api/codegen/vnc-dependencies | jq

# Crear sesión de prueba
curl -X POST http://localhost:8000/api/codegen/start \
  -H "Content-Type: application/json" \
  -d '{"url":"https://example.com","force_vnc":true}' | jq

# Verificar sesiones activas
curl http://localhost:8000/api/codegen/vnc-sessions | jq
```

## Seguridad y Consideraciones de Producción

### Autenticación VNC

**Estado Actual**: Sin contraseña (desarrollo)
```bash
# Configuración actual (desarrollo)
x11vnc -nopw -forever -shared
```

**Recomendación Producción**: Habilitar autenticación
```bash
# Crear archivo de contraseña
x11vnc -storepasswd secret /etc/vnc/passwd

# Usar contraseña en producción
x11vnc -rfbauth /etc/vnc/passwd -forever -shared
```

### Firewall y Red

```bash
# Permitir solo conexiones locales (recomendado)
x11vnc -localhost -nopw

# Para acceso remoto (configurar firewall apropiadamente)
iptables -A INPUT -p tcp --dport 5900:5910 -s TRUSTED_IP -j ACCEPT
iptables -A INPUT -p tcp --dport 6080:6090 -s TRUSTED_IP -j ACCEPT
```

### Límites de Recursos

```python
# Configuración en RemoteCodegenService
MAX_CONCURRENT_VNC_SESSIONS = 10
VNC_SESSION_TIMEOUT = 3600  # 1 hora
CLEANUP_INTERVAL = 300      # 5 minutos
```

## Solución de Problemas

### Problemas Comunes

#### 1. Error "DISPLAY not set"
```bash
# Verificar variable DISPLAY
echo $DISPLAY

# Configurar manualmente
export DISPLAY=:0

# Verificar Xvfb
ps aux | grep Xvfb
```

#### 2. Puerto VNC en uso
```bash
# Verificar puertos ocupados
netstat -tulpn | grep :5900

# Matar procesos VNC huérfanos
pkill x11vnc
pkill Xvfb
```

#### 3. Dependencias faltantes
```bash
# Verificar disponibilidad
which xvfb-run
which x11vnc
which websockify

# Instalar dependencias faltantes
./scripts/setup-vnc-server.sh
```

#### 4. Conexión WebSocket fallida
```bash
# Verificar websockify
ps aux | grep websockify

# Verificar conectividad
curl -I http://localhost:6080

# Revisar logs
journalctl -u websockify -f
```

### Logs y Debugging

#### Habilitar Logging Detallado
```python
# En app.py o configuración
import logging
logging.getLogger('src.core.remote_codegen_service').setLevel(logging.DEBUG)
```

#### Comandos de Diagnóstico
```bash
# Estado de procesos VNC
ps aux | grep -E "(xvfb|x11vnc|websockify|fluxbox)"

# Conexiones de red activas
netstat -tulpn | grep -E "(5900|6080)"

# Verificar displays X11
ls /tmp/.X*-lock

# Logs del sistema
tail -f /var/log/syslog | grep -E "(vnc|xvfb)"
```

## Rendimiento y Optimización

### Configuraciones Recomendadas

#### Para Desarrollo
```python
VNC_DISPLAY_RESOLUTION = "1024x768x24"
VNC_REFRESH_RATE = "30fps"
COMPRESSION_LEVEL = 6
```

#### Para Producción
```python
VNC_DISPLAY_RESOLUTION = "1920x1080x24"
VNC_REFRESH_RATE = "15fps"
COMPRESSION_LEVEL = 9
```

### Monitoreo de Recursos

```bash
# Uso de memoria por sesión VNC
ps -o pid,vsz,rss,comm -p $(pgrep -f "x11vnc|Xvfb")

# Conexiones simultáneas
netstat -an | grep :5900 | grep ESTABLISHED | wc -l

# Espacio en disco (screenshots y logs)
du -sh /tmp/qak_codegen/
```

## Roadmap y Mejoras Futuras

### Funcionalidades Planificadas

1. **Autenticación Robusta**
   - Integración con sistema de usuarios QAK
   - Tokens de sesión temporales
   - Autenticación OAuth/LDAP

2. **Escalabilidad Horizontal**
   - Load balancer para sesiones VNC
   - Distribución automática de carga
   - Clustering de servidores

3. **Monitoreo Avanzado**
   - Métricas de performance en tiempo real
   - Alertas automáticas por sobrecarga
   - Dashboard de estado de sesiones

4. **Calidad de Video**
   - Codificación H.264 para mejor compresión
   - Adaptación automática de calidad según ancho de banda
   - Soporte para audio (futuro)

### Contribución

Para contribuir a la funcionalidad VNC:

1. Fork del repositorio
2. Crear branch feature/vnc-improvement
3. Implementar mejoras en `src/core/remote_codegen_service.py`
4. Añadir tests en `tests/test_vnc_integration.py`
5. Actualizar documentación
6. Submit Pull Request

---

**Nota**: Esta documentación corresponde a la versión actual del sistema QAK con integración VNC completa. Para actualizaciones y nuevas funcionalidades, consultar el changelog del proyecto.
