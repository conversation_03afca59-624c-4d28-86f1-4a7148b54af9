---
title: "CLI Reference"
description: "A guide to using the QAK command-line interface (CLI) for test execution and code generation."
---

import { Card, CardGroup } from '@mintlify/components';

# CLI Reference Guide

The QAK Command-Line Interface (CLI) allows you to run tests and generate automation code directly from your terminal, without needing the web interface.

## Commands

<CardGroup>
    <Card title="smoke" icon="fire" href="#smoke-test">
        Runs a quick smoke test from natural language instructions.
    </Card>
    <Card title="full" icon="tasks" href="#full-test">
        Runs a comprehensive test from a Gherkin scenario.
    </Card>
    <Card title="generate-code" icon="code" href="#generate-code">
        Generates automation code from an existing test history file.
    </Card>
</CardGroup>


---

### Smoke Test

The `smoke` command executes a quick test based on a set of instructions. It's ideal for validating critical paths.

```bash
python cli.py smoke --instructions "Log in with user 'admin' and password 'admin123'" --url "https://example.com/login"
```

#### Arguments

| Argument | Description | Required |
|---|---|---|
| `--instructions` | The natural language instructions for the test. | Yes |
| `--url` | The starting URL for the test. | No |
| `--user-story` | An optional user story to provide more context. | No |
| `--generate-code` | Framework to generate code for (`selenium`, `playwright`, etc.). | No |

---

### Full Test

The `full` command executes a test based on a provided Gherkin scenario.

```bash
python cli.py full --gherkin-file "path/to/your/test.feature" --url "https://example.com"
```

#### Arguments

| Argument | Description | Required |
|---|---|---|
| `--gherkin` | The Gherkin scenario as a string. | One of `gherkin` or `gherkin-file` |
| `--gherkin-file` | The path to a file containing the Gherkin scenario. | One of `gherkin` or `gherkin-file` |
| `--url` | The starting URL for the test. | No |
| `--generate-code` | Framework to generate code for. | No |

---

### Generate Code

The `generate-code` command generates automation code from the `history.json` file of a previous test run.

```bash
python cli.py generate-code --history-path "tests/smoke_test_20231027120000/history.json" --framework "playwright"
```

#### Arguments

| Argument | Description | Required |
|---|---|---|
| `--history-path` | The path to the `history.json` file from a previous run. | Yes |
| `--framework` | The target framework for code generation. | Yes |
| `--output` | The optional output file path for the generated code. | No | 