# Environment Variables Reference

This document summarizes the environment variables referenced in the **QAK** code-base.

## Boolean variables (accept "true" / "false", "1" / "0", or "yes" / "no")

| Variable | Default | Description |
|----------|---------|-------------|
| `ARTIFACT_R2_ONLY_MODE` | `false` | When **true**, the artifact collector writes **only** to R2 storage (no local copy). |
| `ARTIFACT_SKIP_LOCAL_STORAGE` | `false` | Skip saving a local copy of artifacts; useful in containerized / cloud executions. |
| `DEBUG_CONFIG` | `false` | Enables extra debug output for prompt execution. |
| `HISTORY_ANALYSIS_USE_LLM` | `false` | Use an LLM to analyse test execution history. |
| `HISTORY_ANALYSIS_WRITEBACK` | `true` | Persist generated history analysis back to the database. |
| `SAVE_SMOKE_TEST_CONVERSATIONS` | `false` | Persist smoke-test conversation JSON files under `conversations/`. |
| `BROWSER_USE_SEMANTIC_MEMORY` | `false` | Activate semantic memory when running the **browser-use** agent. |
| `BROWSER_USE_LOGFIRE` | `false` | Send structured logs to Logfire when enabled. |
| `SKIP_LLM_API_KEY_VERIFICATION` | `false` | Skip the start-up check that validates LLM API keys. |
| `IN_DOCKER` | `false` | Automatically detected, or can be forced to **true** when running inside Docker. |
| `IS_IN_EVALS` | `false` | Signals that the process is running under an evaluation harness. |
| `DISABLE_LLM_VALIDATION` | `false` | Disables (fast-path) validation of LLM responses. |

> Note: All boolean variables are parsed case-insensitively. Any of the following spellings are accepted: `true`, `1`, `yes` (for **true**) and `false`, `0`, `no` (for **false**).

## Non-boolean variables

Below is a non-exhaustive list of additional environment variables that configure the application. Their default values are shown where defined in the code-base.

| Variable | Default | Purpose |
|----------|---------|---------|
| `ARTIFACT_STORAGE_BACKEND` | `local` | Selects the artifact storage backend (`local`, `r2`, `s3`, `azure`, `gcs`). |
| `STORAGE_BACKEND` | `local` | Fallback used by enhanced artifact collector when `ARTIFACT_STORAGE_BACKEND` is not set. |
| `LLM_MODEL` | `gemini-2.5-flash` | Default LLM model to use (Gemini, ChatGPT, etc.). |
| `PROMPT_LANGUAGE` | `en` | Default language for generated prompts. |
| `EMBEDDING_MODEL` | *(none)* | Embedding model ID (e.g. `models/text-embedding-004`). |
| `EMBEDDING_PROVIDER` | `gemini` | Provider for embeddings (`gemini`, `openai`, …). |
| `EMBEDDING_DIMS` | `768` | Expected dimensionality of embeddings. |
| `SMOKE_TEST_CONVERSATIONS_PATH` | `./smoke_test_conversations` | Folder where smoke-test conversations are saved. |
| `BROWSER_TEST_ENV` | `development` | Runtime environment for browser tests (`development`, `production`, …). |
| `ALLOWED_DOMAINS` | *(empty)* | Comma-separated whitelist of domains the browser agent may access. |
| `GOOGLE_API_KEY` | *(none)* | API key for Google Gemini. |
| `OPENAI_API_KEY` | *(none)* | API key for OpenAI. |
| `ANTHROPIC_API_KEY` | *(none)* | API key for Anthropic. |
| `R2_BUCKET_NAME` | *(none)* | Cloudflare R2 bucket name. |
| `R2_ACCOUNT_ID` | *(none)* | Cloudflare R2 account ID. |
| `R2_ACCESS_KEY_ID` | *(none)* | Cloudflare R2 access key. |
| `R2_SECRET_ACCESS_KEY` | *(none)* | Cloudflare R2 secret key. |
| `R2_PUBLIC_URL` | *(none)* | Public URL to served R2 objects (optional). |
| `AWS_S3_BUCKET` | *(none)* | Amazon S3 bucket used for artifact storage. |
| `AWS_DEFAULT_REGION` | `us-east-1` | AWS region for S3. |
| `AWS_ACCESS_KEY_ID` | *(none)* | AWS access key. |
| `AWS_SECRET_ACCESS_KEY` | *(none)* | AWS secret key. |
| `AZURE_STORAGE_ACCOUNT_NAME` | *(none)* | Azure storage account. |
| `AZURE_STORAGE_ACCOUNT_KEY` | *(none)* | Azure storage key. |
| `AZURE_STORAGE_CONTAINER` | `artifacts` | Azure container for artifacts. |
| `GCS_BUCKET_NAME` | *(none)* | Google Cloud Storage bucket. |
| `BROWSER_USE_LOGGING_LEVEL` | `info` | Runtime logging verbosity for browser-use components. |
| `API_HOST` | `0.0.0.0` | Host address for FastAPI server. |
| `API_PORT` | `8000` | Listening port for FastAPI server. |
| `DEEPSEEK_API_KEY` | *(none)* | API key for DeepSeek models. |
| `GROK_API_KEY` | *(none)* | API key for Grok. |
| `NOVITA_API_KEY` | *(none)* | API key for Novita. |
| `AZURE_OPENAI_ENDPOINT` | *(none)* | Base URL of the Azure OpenAI service. |
| `AZURE_OPENAI_KEY` | *(none)* | Access key for Azure OpenAI. |
| `BROWSER_USE_CONFIG_DIR` | `~/.config/browseruse` | Directory that stores **browser-use** config & profiles. |
| `XDG_CACHE_HOME` | `~/.cache` | XDG cache directory used to resolve `BROWSER_USE_CONFIG_DIR` defaults. |
| `XDG_CONFIG_HOME` | `~/.config` | XDG config directory used to resolve `BROWSER_USE_CONFIG_DIR` defaults. |
| `WIN_FONT_DIR` | `C:\\Windows\\Fonts` | Windows font directory (used when running on Windows). |
| `UPSTASH_REDIS_URL` | *(none)* | Redis URL for Upstash vector storage. |
| `UPSTASH_REDIS_TOKEN` | *(none)* | Access token for Upstash Redis REST API. |
| `UPSTASH_REDIS_REST_URL` | *(none)* | Upstash REST endpoint (alternative to direct Redis). |
| `UPSTASH_REDIS_REST_TOKEN` | *(none)* | REST API token for Upstash. |
| `REDIS_URL` | `redis://localhost:6379` | Fallback Redis connection string. |
| `STORAGE_TTL` | `14400` | Default TTL (in seconds) for cached resources. |
| `QAK_API_CALL_DELAY` | `0` | Artificial delay (ms) inserted before external API calls (rate-limiting aid). |
| `LOGFIRE_TOKEN` | *(none)* | Token used to send logs to Logfire. |
| `LOGFIRE_PROJECT_NAME` | `browser-use` | Project name used in Logfire events. |
| `LOGFIRE_SERVICE_NAME` | `browser-use-agent` | Service name reported to Logfire / OTEL. |
| `LOGFIRE_ENVIRONMENT` | `development` | Environment tag for Logfire (`development`, `prod`, …). |
| `GITHUB_TOKEN` | *(none)* | GitHub personal access token (PR analysis). |
| `GITHUB_REPO` | *(none)* | `<owner>/<repo>` string used by PR diff analyser. |

*(Add any newly introduced variables to this document to keep it up-to-date.)* 