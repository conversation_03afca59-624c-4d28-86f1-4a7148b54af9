# =============================================================================
# API KEYS - CONFIGURACIÓN PRINCIPAL DE LLM
# =============================================================================

# Google Gemini API Key (REQUERIDA) - Principal proveedor LLM
# Obténla en: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here

# Modelo principal para todas las operaciones de QAK
# Opciones: gemini-2.5-flash, gemini-1.5-pro, gemini-1.5-flash
LLM_MODEL=gemini-2.5-flash

# Proveedores alternativos de IA (opcionales)
# Solo necesarios si usas fallback o comparación de modelos
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
GROQ_API_KEY=

# =============================================================================
# OPENROUTER - CONFIGURACIÓN DE COSTO-EFECTIVIDAD LLM
# =============================================================================

# OpenRouter API Key para acceso a múltiples proveedores con costo optimizado
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Feature flags para migración gradual a OpenRouter (todas habilitadas)
USE_OPENROUTER_FOR_GHERKIN=true      # Generación de Gherkin desde user stories
USE_OPENROUTER_FOR_VALIDATION=true   # Validación de resultados de tests
USE_OPENROUTER_FOR_TRANSLATION=true  # Traducción de prompts entre idiomas
USE_OPENROUTER_FOR_ENHANCEMENT=true  # Mejora y refinamiento de tests

# Configuraciones de optimización de costos
OPENROUTER_PREFER_FREE_MODELS=true        # Preferir modelos gratuitos cuando sea posible
OPENROUTER_MAX_COST_PER_1K_TOKENS=0.01    # Límite máximo de costo por 1K tokens
RESPECT_RATE_LIMITS=true                  # Respetar límites de tasa de API
RATE_LIMIT_BUFFER=0.2                     # Buffer de 20% para rate limits

# =============================================================================
# CONFIGURACIÓN DE IDIOMA Y LOCALIZACIÓN
# =============================================================================

# Idioma principal para prompts y respuestas
# Opciones: en (English), es (Spanish)
PROMPT_LANGUAGE=es

# =============================================================================
# GITHUB INTEGRATION - PARA COMMITS Y REPORTES
# =============================================================================

# Token de GitHub para crear commits automáticos y reportes
GITHUB_TOKEN=your_github_token_here
GITHUB_REPO=your_username/your_repo

# =============================================================================
# OPTIMIZACIÓN DE RENDIMIENTO Y RATE LIMITING
# =============================================================================

# Delay en segundos entre llamadas a API LLM para evitar rate limits
# Ajusta según tu plan de API (menor para planes premium)
QAK_API_CALL_DELAY=10

# =============================================================================
# REDIS CONFIGURATION - CELERY BACKGROUND JOBS & MULTI-AGENT COORDINATION
# =============================================================================

# Para DESARROLLO LOCAL (recomendado) - Descomenta para usar Redis local:
REDIS_URL=redis://localhost:6379/0# Requiere: redis-server redis-local.conf

# Para PRODUCCIÓN con Upstash Redis:
# IMPORTANTE: Upstash requiere SSL (rediss://) en puerto 6380, no 6379
# REDIS_URL=rediss://default:<EMAIL>:6380?ssl_cert_reqs=none

# REST API configuration para browser-use y otras integraciones directas
UPSTASH_REDIS_REST_URL=https://your-instance.upstash.io
UPSTASH_REDIS_REST_TOKEN=your_upstash_token

# Backend de almacenamiento para multi-agent system
STORAGE_BACKEND=upstash

# =============================================================================
# BROWSER-USE - CONFIGURACIÓN OPTIMIZADA PARA GEMINI 2.5 FLASH
# =============================================================================

# Rate limiting optimizado para Gemini 2.5 Flash Preview
# Límites reales: 15 RPM, 1M TPM - Configuración conservadora
BROWSER_USE_RATE_LIMIT_AGGRESSIVE=false
BROWSER_USE_GEMINI_RPM=12          # 15 RPM real - 3 de margen
BROWSER_USE_GEMINI_TPM=800000      # 1M TPM real - 200k de margen

# Configuración para embeddings usados en memoria a largo plazo
EMBEDDING_PROVIDER=huggingface
EMBEDDING_MODEL=all-MiniLM-L6-v2
EMBEDDING_DIMS=384

# El planner ayuda a tomar decisiones más inteligentes
BROWSER_USE_PLANNER_INTERVAL=3     # Ejecutar planner cada 3 steps
BROWSER_USE_PLANNER_REASONING=true # Habilitar chain-of-thought

# Optimizaciones de contexto para mejor rendimiento
BROWSER_USE_MAX_CONTEXT_TOKENS=120000  # Ajustado para Gemini (128k max)
BROWSER_USE_VISION_QUALITY=medium      # low, medium, high

# Logging level para browser automation
BROWSER_USE_LOGGING_LEVEL=debug

# Modelos específicos para diferentes componentes
GEMINI_MAIN_MODEL=gemini-2.5-flash
GEMINI_PLANNER_MODEL=gemini-2.5-flash
GEMINI_EMBEDDING_MODEL=models/text-embedding-004

# Configuración de conversaciones y memoria
SAVE_SMOKE_TEST_CONVERSATIONS=true
SMOKE_TEST_CONVERSATIONS_PATH="./conversations/smoke_tests"
BROWSER_USE_SEMANTIC_MEMORY=false

# =============================================================================
# LOGFIRE - MONITORING Y OBSERVABILIDAD
# =============================================================================

# Habilitar integración con Logfire para monitoring
BROWSER_USE_LOGFIRE=true

# Token de Logfire (crear en Settings → Write tokens)
LOGFIRE_TOKEN=your_logfire_token_here

# Configuración de servicio
LOGFIRE_SERVICE_NAME="aery-browser"
LOGFIRE_ENVIRONMENT="dev"
LOGFIRE_SERVICE_VERSION="0.1.0"
LOGFIRE_PROJECT=your_username/your_project
LOGFIRE_LEVEL=DEBUG

# =============================================================================
# MONGODB - BASE DE DATOS PRINCIPAL QAK
# =============================================================================

# URI de conexión a MongoDB Atlas (producción)
# Formato: mongodb+srv://username:<EMAIL>/database
MONGODB_URI="mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority&appName=YourApp"

# Entorno de base de datos (production/development/testing)
MONGODB_ENVIRONMENT=development

# =============================================================================
# QAK DATABASE FEATURE FLAGS
# =============================================================================

# Habilitar uso de base de datos MongoDB (vs almacenamiento local)
QAK_USE_DATABASE=true

# Habilitar sistema de proyectos en base de datos
QAK_DATABASE_PROJECTS=true

# Deshabilitar validación LLM para desarrollo más rápido
DISABLE_LLM_VALIDATION=true

# =============================================================================
# CLOUDFLARE R2 CONFIGURATION - QAK ARTIFACTS STORAGE
# =============================================================================
# Configuración para almacenar artifacts de test en Cloudflare R2
# 🚀 Beneficios: Sin costos de egress, performance global, 90% más barato que S3

# R2 Bucket Configuration
R2_BUCKET_NAME=your-bucket-name
R2_ACCOUNT_ID=your_account_id

# R2 API Credentials
# Ve a: Cloudflare R2 → Manage R2 API tokens → Create token
# Permisos: Admin Read & Write en tu bucket
R2_ACCESS_KEY_ID=your_access_key
R2_SECRET_ACCESS_KEY=your_secret_key

# Endpoint URL específico para tu account
R2_ENDPOINT_URL=https://your_account_id.r2.cloudflarestorage.com

# Optional: Custom domain for direct access (configurar en Cloudflare)
# R2_PUBLIC_URL=https://artifacts.yourdomain.com

# QAK Artifact Configuration
ARTIFACT_STORAGE_BACKEND=r2
ARTIFACT_AUTO_MIGRATE=true
ARTIFACT_MIGRATE_AFTER_MINUTES=30
ARTIFACT_RETENTION_DAYS=90
ARTIFACT_MAX_STORAGE_GB=100

# Enhanced Features
ARTIFACT_AUTO_COMPRESS=true
ARTIFACT_GENERATE_THUMBNAILS=true
ARTIFACT_MAX_CONCURRENT_UPLOADS=5
ARTIFACT_REMOVE_LOCAL_AFTER_MIGRATION=true

# R2-Only Storage Configuration (NO LOCAL FILES)
ARTIFACT_R2_ONLY_MODE=true
ARTIFACT_SKIP_LOCAL_STORAGE=true

# Configuraciones S3 compatibles para R2
R2_USE_PATH_STYLE=false
R2_SIGNATURE_VERSION=s3v4
R2_REGION_HINT=auto

# =============================================================================
# AI ANALYSIS COST OPTIMIZATION
# =============================================================================
# Control de costos para análisis de IA
# Opciones: aggressive, high, medium, low, none
AI_ANALYSIS_COST_OPTIMIZATION=high

# =============================================================================
# CONFIGURACIONES AVANZADAS (OPCIONAL)
# =============================================================================

# Saltar verificación de API keys (solo para testing)
# SKIP_LLM_API_KEY_VERIFICATION=false

# Configuración de directorios
# XDG_CACHE_HOME=~/.cache
# XDG_CONFIG_HOME=~/.config
# BROWSER_USE_CONFIG_DIR=~/.config/browseruse

# =============================================================================
# NOTAS DE USO IMPORTANTES
# =============================================================================
# 1. GOOGLE_API_KEY es obligatoria - obténla en Google AI Studio
# 2. Para desarrollo local, usa Redis local en lugar de Upstash
# 3. Los límites de rate son conservadores, ajústalos según tu plan
# 4. El planner consume más API calls pero mejora la precisión
# 5. La memoria requiere: pip install "browser-use[memory]"
# 6. Para development, puedes desactivar planner y memoria para ahorrar API calls

# =============================================================================
# CONFIGURACIÓN SEGÚN ENTORNO
# =============================================================================

# DESARROLLO (menos API calls)
# BROWSER_USE_PLANNER_INTERVAL=5
# BROWSER_USE_GEMINI_RPM=8
# REDIS_URL=redis://localhost:6379/0
# MONGODB_ENVIRONMENT=development

# PRODUCCIÓN (más agresivo pero controlado)
# BROWSER_USE_GEMINI_RPM=15
# BROWSER_USE_PLANNER_INTERVAL=2
# BROWSER_USE_MAX_CONTEXT_TOKENS=130000
# MONGODB_ENVIRONMENT=production