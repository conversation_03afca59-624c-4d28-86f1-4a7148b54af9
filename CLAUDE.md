# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

QAK (Quality Assurance Kit) is an AI-powered test automation platform that transforms user stories into executable automation code using browser automation and LLM integration.

## Architecture

### Core Components

- **Frontend**: Next.js 15 with TypeScript, Tailwind CSS, Radix UI (port 9001)
- **Backend**: FastAPI with Python 3.11+ (port 8000)
- **Database**: MongoDB with <PERSON><PERSON> ODM (async)
- **Browser Automation**: Custom browser-use library with <PERSON>wright
- **LLM Integration**: OpenRouter-based multi-provider architecture

### Key Directories

- `app.py` - Main FastAPI application entry point
- `cli.py` - Command-line interface for smoke tests
- `src/` - Main source code
  - `api/` - REST API endpoints and routes
  - `core/` - Core business logic and services
  - `database/` - MongoDB models and repositories
  - `services/` - Service layer (including modern LLM architecture)
  - `utilities/` - Helper utilities
- `web/` - Next.js frontend application
- `libs/browser_use/` - Custom browser automation library
- `memory-bank/` - Project context and AI documentation
- `prompts/` - Versioned markdown-based prompt templates

## Common Development Commands

### Backend Development
```bash
# Setup virtual environment (recommended)
python3.11 -m venv .venv
source .venv/bin/activate  # Linux/macOS
# .\.venv\Scripts\Activate.ps1  # Windows PowerShell

# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers (required for browser-use)
playwright install

# Start API server
python app.py
```

### Frontend Development
```bash
# Start Next.js dev server (run from web/ directory)
cd web && npm run dev

# Build frontend
cd web && npm run build

# Type checking
cd web && npm run typecheck

# Linting
cd web && npm run lint
```

### Docker Development
```bash
# Development mode with hot reload
docker-compose -f docker-compose.dev.yml up --build -d

# Production mode
docker-compose up --build -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop containers
docker-compose -f docker-compose.dev.yml down
```

## LLM Architecture (Modern)

The codebase uses a modern LLM architecture centered around OpenRouter:

### Key Services
- `src/services/llm/` - Modern LLM service architecture
- `src/services/llm/llm_service_factory.py` - Factory for LLM provider selection
- `src/services/llm/providers/openrouter_service.py` - Primary LLM provider
- `src/services/llm/use_cases/` - Specific LLM use cases (Gherkin, translation, etc.)

### Configuration
- Primary: OpenRouter with automatic fallback to requests-based implementation
- Fallback: Direct Gemini integration
- Prompts: Versioned markdown files in `prompts/` directory

## Test Execution Architecture

### V2 Modern Architecture (Current)
- **Endpoint**: `/api/v2/tests/execute` - Unified execution endpoint
- **Orchestrator**: `src/core/execution_orchestrator.py` - Centralized async coordination
- **Strategies**: `src/core/execution_strategies.py` - Pattern-based execution
  - `SmokeTestStrategy` - Direct instruction execution
  - `FullTestStrategy` - Complete pipeline from user story to code
  - `TestCaseStrategy` - Individual test case execution
  - `SuiteStrategy` - Batch execution with parallel support

### Execution Types
- **Smoke Test**: Direct execution from natural language instructions
- **Full Test**: Complete pipeline (user story → manual tests → Gherkin → execution → code generation)
- **Suite Execution**: Batch execution of multiple test cases
- **Code Generation**: Multi-framework automation code generation

## Database Models

### Key Models (MongoDB with Beanie ODM)
- `Project` - Container for test organization
- `TestSuite` - Collection of related test cases
- `TestCase` - Individual test with Gherkin scenarios or instructions
- `Execution` - Test execution records with results and artifacts
- `CodegenSession` - Code generation session management

### Database Connection
- Async MongoDB connection via Motor driver
- Models in `src/database/models/`
- Repositories in `src/database/repositories/`

## Browser Automation

### Custom Browser-Use Library
- Location: `libs/browser_use/`
- Based on modified browser-use with AI-powered browser interaction
- EventBus system for agent communication
- Memory system for contextual browser sessions
- Automatic screenshot capture and artifact generation

### Key Features
- Real browser automation with Chrome/Chromium
- AI-powered element interaction
- Pattern learning and recognition
- Contextual memory between sessions

## Code Generation

### Supported Frameworks
- Selenium + PyTest BDD (Python)
- Playwright (Python)
- Cypress (JavaScript)
- Robot Framework
- Selenium + Cucumber (Java)

### Generation Process
1. Execute test in browser with AI agent
2. Capture interaction patterns and screenshots
3. Generate framework-specific code using LLM
4. Store in persistent `codegen_sessions/artifacts/` directory

## Environment Configuration

### Required Environment Variables
```bash
# First, ensure virtual environment is activated
source .venv/bin/activate  # Linux/macOS
# .\.venv\Scripts\Activate.ps1  # Windows PowerShell

# Then set environment variables
GOOGLE_API_KEY=your_google_api_key
OPENROUTER_API_KEY=your_openrouter_key  # Optional, falls back to Gemini
LLM_MODEL=gemini-2.5-flash-exp  # Default model
PROMPT_LANGUAGE=es  # Prompt language (es/en)
```

### Optional Configuration
- `OPENAI_API_KEY` - For OpenAI provider
- `ANTHROPIC_API_KEY` - For Anthropic provider
- `REDIS_URL` - For caching (optional)
- `MONGODB_URL` - MongoDB connection (defaults to local)

## Memory Bank System

The project uses a structured Memory Bank system in `memory-bank/` for persistent AI context:

- `activeContext.md` - Current work focus and recent changes
- `project-brief.md` - Project goals and scope
- `productContext.md` - Product features and user needs
- `techContext.md` - Technical implementation details
- `systemPatterns.md` - Architecture and design patterns
- `progress.md` - Current status and known issues

## Recent Major Changes

### LLM Architecture Modernization (2025-07-06)
- Complete migration to OpenRouter-based LLM architecture
- Versioned prompt system with markdown templates
- Multi-provider support with intelligent fallback
- All services migrated except browser-use (intentionally preserved)

### Legacy Code Cleanup (2025-07-05)
- Eliminated 900+ lines of legacy test execution code
- Migrated to modern V2 architecture with ExecutionOrchestrator
- Standardized result format across all execution types

### Service Separation (2025-07-05)
- Logging optimization with separate Logfire services
- `qak-api` service for main application logs
- `aery-browser` service for browser automation logs

## Development Workflow

### Memory Bank Integration
- Always read Memory Bank files when starting new sessions
- Update `activeContext.md` after significant changes
- Use Memory Bank context for understanding project decisions

### Testing Strategy
- No traditional unit tests - platform tests itself via browser automation
- Integration testing through live browser execution
- LLM-powered result validation

### Code Patterns
- Async/await patterns throughout (FastAPI + MongoDB)
- Factory pattern for LLM provider selection
- Strategy pattern for execution types
- Repository pattern for data access
- Clean architecture with service layer abstraction

## Troubleshooting

### Common Issues
- **EventBus conflicts**: Use unique task_id for each BrowserAgent instance
- **Screenshot loading**: Ensure artifact routes are properly configured
- **LLM rate limits**: Check Gemini API quota (250 requests/day on free tier)
- **Browser installation**: Run `playwright install` for browser automation

### Debug Commands
```bash
# Ensure virtual environment is activated first
source .venv/bin/activate  # Linux/macOS
# .\.venv\Scripts\Activate.ps1  # Windows PowerShell

# Test logging configuration
curl http://localhost:8000/test-logfire

# Check API health
curl http://localhost:8000/health

# View API documentation
open http://localhost:8000/docs
```

## IDE Configuration

The project includes extensive Cursor IDE rules in `.cursor/rules/` and GitHub Copilot instructions in `.github/copilot-instructions.md` for AI-assisted development.