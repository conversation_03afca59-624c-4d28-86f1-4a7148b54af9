# AgentQA - Diagrama de Arquitectura de Alto Nivel

## Arquitectura General del Sistema

```mermaid
graph TB
    subgraph "🌐 Frontend Layer"
        WEB["🎨 Next.js Web Interface<br/>• Dashboard<br/>• Project Management<br/>• QA Assistant<br/>• Browser Config"]
        CLI["⚡ Python CLI Interface<br/>• cli.py main()<br/>• Interactive commands"]
        API_CLIENT["📱 API Client Libraries<br/>• Custom hooks<br/>• React Query integration"]
    end

    subgraph "🔌 API Layer - FastAPI Routes"
        FASTAPI["🚀 FastAPI Application<br/>• app = FastAPI()<br/>• CORS middleware<br/>• Static file serving"]
        
        PROJECT_ROUTES["📁 Project Routes<br/>• create_project()<br/>• get_all_projects()<br/>• update_project()<br/>• delete_project()"]
        
        SUITE_ROUTES["📋 Suite Routes<br/>• create_test_suite()<br/>• get_test_suite()<br/>• update_test_suite()<br/>• execute_suite()"]
        
        TESTCASE_ROUTES["🧪 TestCase Routes<br/>• create_test_case()<br/>• get_test_case()<br/>• execute_test_case()<br/>• update_status()"]
        
        PROMPT_ROUTES["📝 Prompt Routes<br/>• list_prompts()<br/>• get_prompt_detail()<br/>• update_prompt()<br/>• validate_prompt()"]
        
        CONFIG_ROUTES["⚙️ Config Routes<br/>• get_predefined_configurations()<br/>• create_custom_configuration()<br/>• validate_configuration()<br/>• test_configuration()"]
        
        TRANSLATION_ROUTES["🌍 Translation Routes<br/>• translate_prompt()<br/>• PromptTranslationService"]
        
        EXECUTION_ROUTES["🎯 V2 Execution Routes<br/>• /api/v2/tests/execute<br/>• ExecutionOrchestrator integration<br/>• StandardResult format"]
    end

    subgraph "🔧 Service Layer"
        TEST_SERVICE["🎯 TestService Core<br/>• create_project()<br/>• execute_test_case() (via ExecutionOrchestrator)<br/>• generate_code()<br/>• CLI compatibility methods"]
        
        PROJECT_MANAGER["📊 ProjectManagerService<br/>• save_project()<br/>• load_project()<br/>• delete_project()<br/>• list_projects()"]
        
        PROMPT_SERVICE["📚 PromptService<br/>• load_prompt()<br/>• generate_gherkin()<br/>• generate_code()<br/>• enhance_story()"]
        
        EXECUTION_ORCHESTRATOR["⚡ ExecutionOrchestrator<br/>• submit_execution_request()<br/>• get_execution_by_id()<br/>• ExecutionStrategies management<br/>• StandardResult processing"]
        
        EXECUTION_STRATEGIES["🎯 ExecutionStrategies<br/>• TestCaseStrategy<br/>• SuiteStrategy<br/>• SmokeTestStrategy<br/>• FullTestStrategy"]
    end

    subgraph "🤖 Agent Layer"
        STORY_AGENT["📖 StoryAgent<br/>• enhance_story()<br/>• generate_manual_tests()<br/>• generate_gherkin()"]
        
        BROWSER_AGENT["🌐 BrowserAutomationAgent<br/>• execute_scenario()<br/>• _create_browser_controller()<br/>• _run_with_timeout()"]
        
        BROWSER_USE["🖥️ browser-use Library<br/>• Agent.run()<br/>• Browser.new_context()<br/>• Controller methods"]
    end

    subgraph "🧠 AI Integration Layer"
        LANGCHAIN["🔗 LangChain Framework<br/>• ChatGoogleGenerativeAI<br/>• prompt templates<br/>• chain execution"]
        
        AI_PROVIDERS["🤖 AI Providers<br/>• Google Gemini (primary)<br/>• OpenAI GPT-4<br/>• Anthropic Claude<br/>• Groq"]
    end

    subgraph "💾 Storage Layer"
        PROJECT_FILES["📁 JSON Project Files<br/>• projects/{uuid}.json<br/>• Suite & TestCase data<br/>• Metadata & timestamps"]
        
        PROMPT_TEMPLATES["📋 Markdown Templates<br/>• prompts/**/*.md<br/>• Structured prompts<br/>• Multi-language support"]
        
        TEST_HISTORY["📊 Test Execution History<br/>• tests/smoke_test_*/history.json<br/>• Screenshots<br/>• Error logs"]
        
        CONFIG_STORAGE["⚙️ Configuration Storage<br/>• config/custom/*.json<br/>• Browser configurations<br/>• Custom settings"]
    end

    subgraph "🔍 Analysis & Utils Layer"
        TEST_ANALYZER["📈 TestAnalyzer<br/>• analyze_test_result()<br/>• determine_test_failure()<br/>• categorize_error()"]
        
        FILE_MANAGER["📁 TestFileManager<br/>• save_history_json()<br/>• extract_screenshots_from_json()<br/>• _create_clean_history_data()"]
        
        RESPONSE_TRANSFORMERS["🔄 Response Transformers<br/>• transform_backend_response_to_frontend_format()<br/>• clean_data_for_json_serialization()<br/>• convert_screenshot_paths_to_urls()"]
    end

    %% Connections
    WEB --> FASTAPI
    CLI --> TEST_SERVICE
    API_CLIENT --> FASTAPI
    
    FASTAPI --> PROJECT_ROUTES
    FASTAPI --> SUITE_ROUTES
    FASTAPI --> TESTCASE_ROUTES
    FASTAPI --> PROMPT_ROUTES
    FASTAPI --> CONFIG_ROUTES
    FASTAPI --> TRANSLATION_ROUTES
    FASTAPI --> EXECUTION_ROUTES
    
    PROJECT_ROUTES --> TEST_SERVICE
    SUITE_ROUTES --> TEST_SERVICE
    TESTCASE_ROUTES --> TEST_SERVICE
    PROMPT_ROUTES --> PROMPT_SERVICE
    CONFIG_ROUTES --> PROJECT_MANAGER
    EXECUTION_ROUTES --> EXECUTION_ORCHESTRATOR
    
    TEST_SERVICE --> PROJECT_MANAGER
    TEST_SERVICE --> EXECUTION_ORCHESTRATOR
    TEST_SERVICE --> PROMPT_SERVICE
    
    EXECUTION_ORCHESTRATOR --> EXECUTION_STRATEGIES
    EXECUTION_STRATEGIES --> STORY_AGENT
    EXECUTION_STRATEGIES --> BROWSER_AGENT
    TEST_EXECUTOR --> TEST_ANALYZER
    TEST_EXECUTOR --> FILE_MANAGER
    
    STORY_AGENT --> LANGCHAIN
    BROWSER_AGENT --> BROWSER_USE
    BROWSER_USE --> AI_PROVIDERS
    LANGCHAIN --> AI_PROVIDERS
    
    PROJECT_MANAGER --> PROJECT_FILES
    PROMPT_SERVICE --> PROMPT_TEMPLATES
    TEST_EXECUTOR --> TEST_HISTORY
    CONFIG_ROUTES --> CONFIG_STORAGE
    
    TEST_EXECUTOR --> RESPONSE_TRANSFORMERS
    EXECUTION_ROUTES --> RESPONSE_TRANSFORMERS

    %% Styling
    style FASTAPI fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    style TEST_SERVICE fill:#f3e5f5,stroke:#4a148c,stroke-width:3px
    style BROWSER_USE fill:#fff3e0,stroke:#e65100,stroke-width:3px
    style AI_PROVIDERS fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px
    style TEST_EXECUTOR fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    style STORY_AGENT fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    style BROWSER_AGENT fill:#f1f8e9,stroke:#33691e,stroke-width:2px
```

## Flujo de Datos Principal

### 1. Pipeline Completo (Full Test)
```
User Story Input → StoryAgent.enhance_story() → StoryAgent.generate_manual_tests() 
→ StoryAgent.generate_gherkin() → PromptService.generate_code() 
→ BrowserAutomationAgent.execute_scenario() → TestAnalyzer.analyze_test_result()
→ TestFileManager.save_history_json() → Response Transform
```

### 2. Smoke Test Pipeline
```
Test Instructions → BrowserAutomationAgent.execute_scenario() 
→ TestAnalyzer.analyze_test_result() → TestFileManager.save_history_json() 
→ Response Transform
```

### 3. Project Management Flow
```
Frontend Request → FastAPI Router → TestService → ProjectManagerService 
→ JSON File Operations → Response
```

## Funciones Clave por Componente

### 🎯 TestService (Servicio Principal - Migrado a V2)
- `create_project(name, description, tags)` - Gestión de proyectos
- `execute_test_case(project_id, suite_id, test_id)` - Ejecución via ExecutionOrchestrator
- **CLI Compatibility Methods (V2 internally):**
  - `run_smoke_test()` - Compatibilidad CLI usando ExecutionOrchestrator
  - `create_gherkin_scenario()` - Generación para CLI
- **Project Management:**
  - `create_test_suite()`, `update_test_case()`, etc.

### ⚡ ExecutionOrchestrator (Sistema de Ejecución V2)
- `submit_execution_request(request)` - Procesa solicitudes de ejecución
- `get_execution_by_id(execution_id)` - Obtiene estado de ejecución
- `_execute_with_strategy(strategy, context)` - Ejecuta usando estrategias
- **Strategies Management:**
  - Selecciona estrategia apropiada (TestCase, Suite, Smoke, Full)

### 🎯 ExecutionStrategies (Patrones de Ejecución)
- **TestCaseStrategy:** `execute(context)` - Ejecución de casos individuales
- **SuiteStrategy:** `execute(context)` - Ejecución de suites completas
- **SmokeTestStrategy:** `execute(context)` - Pruebas rápidas
- **FullTestStrategy:** `execute(context)` - Pruebas completas con Gherkin

### 🤖 StoryAgent (Procesamiento de Historias)
- `enhance_story(user_story)` - Mejora historias de usuario
- `generate_manual_tests(enhanced_story)` - Genera casos de prueba manuales
- `generate_gherkin(manual_tests)` - Convierte a formato Gherkin

### 🌐 BrowserAutomationAgent (Automatización)
- `execute_scenario(scenario)` - Ejecuta pruebas en navegador real
- `_create_browser_controller()` - Configura controlador del navegador
- `_run_with_timeout(agent, task, timeout)` - Ejecución con límite de tiempo

### 📊 ProjectManagerService (Gestión de Proyectos)
- `save_project(project_data)` - Guarda proyecto en JSON
- `load_project(project_id)` - Carga proyecto desde JSON
- `delete_project(project_id)` - Elimina proyecto
- `list_projects()` - Lista todos los proyectos

### 📈 TestAnalyzer (Análisis de Resultados)
- `analyze_test_result(history, gherkin_scenario)` - Analiza resultados
- `determine_test_failure(history)` - Determina si el test falló
- `categorize_error(error_msg)` - Categoriza tipos de errores

### 📁 TestFileManager (Gestión de Archivos)
- `save_history_json(history_data, test_dir)` - Guarda historial
- `extract_screenshots_from_json(json_data)` - Extrae capturas
- `_create_clean_history_data(history_data)` - Limpia datos para serialización

### 🔄 Response Transformers (Transformación de Datos)
- `transform_backend_response_to_frontend_format(result)` - Transforma respuestas
- `clean_data_for_json_serialization(data)` - Limpia para serialización JSON
- `convert_screenshot_paths_to_urls(screenshots)` - Convierte rutas a URLs

### ⚙️ Configuration Management (Gestión de Configuración)
- `get_predefined_configurations()` - Obtiene configuraciones predefinidas
- `create_custom_configuration(config_data)` - Crea configuración personalizada
- `validate_configuration(config)` - Valida configuración
- `test_configuration(config)` - Prueba configuración

### 🔌 API Routes (Endpoints REST)
**Project Routes:**
- `create_project()`, `get_all_projects()`, `update_project()`, `delete_project()`

**Suite Routes:**
- `create_test_suite()`, `get_test_suite()`, `execute_suite()`

**TestCase Routes:**
- `create_test_case()`, `execute_test_case()`, `update_status()`

**V2 Execution Routes (Arquitectura Moderna):**
- `POST /api/v2/tests/execute` - Endpoint unificado para toda ejecución
- `GET /api/v2/tests/execution/{execution_id}` - Estado de ejecución
- **Soporte:** TestCaseRequest, SuiteRequest, SmokeTestRequest, FullTestRequest

**Legacy Routes (Eliminadas):**
- ~~`/api/tests/smoke`~~ - Migrado a V2
- ~~`/api/tests/full`~~ - Migrado a V2
- ~~`/api/tests/summarize`~~ - Eliminado

## Patrones de Diseño Implementados

1. **Service Layer Pattern** - TestService centraliza lógica de negocio
2. **Repository Pattern** - ProjectManagerService maneja persistencia
3. **Strategy Pattern** - ExecutionStrategies para diferentes tipos de ejecución
4. **Orchestrator Pattern** - ExecutionOrchestrator coordina ejecución
5. **Template Method Pattern** - Pipeline de ejecución definido en estrategias
6. **Agent Pattern** - Agentes especializados para diferentes tareas
7. **Command Pattern** - Requests encapsulan operaciones de ejecución

## Características Técnicas (Arquitectura V2)

- **Arquitectura Asíncrona**: FastAPI + async/await + ExecutionOrchestrator
- **Multi-proveedor de IA**: Gemini, OpenAI, Claude, Groq
- **Ejecución en Navegador Real**: browser-use library con BrowserPool
- **Soporte Multi-framework**: Selenium, Playwright, Cypress, Robot Framework
- **Persistencia Híbrida**: JSON + MongoDB para escalabilidad
- **StandardResult Format**: Formato unificado de resultados
- **Artifact Management**: Sistema robusto de manejo de screenshots y archivos
- **CLI Compatibility**: Mantiene compatibilidad mientras usa arquitectura V2
