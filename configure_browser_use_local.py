"""
Script de configuración para usar browser_use local en lugar de la librería pip.
Importar este módulo al inicio de cualquier archivo que use browser_use.
"""
import sys
import os
from pathlib import Path

def setup_local_browser_use():
    """Configura el path para usar browser_use local desde libs/"""
    # Obtener el directorio raíz del proyecto
    current_file = Path(__file__).resolve()
    project_root = current_file.parent
    libs_path = project_root / "libs"
    
    # Agregar libs/ al path de Python si no está ya incluido
    libs_str = str(libs_path)
    if libs_str not in sys.path:
        sys.path.insert(0, libs_str)
    
    return libs_str

# Configurar automáticamente al importar
libs_path = setup_local_browser_use()

# Initialize browser_use logging configuration
try:
    from browser_use.logging_config import setup_logging
    setup_logging()
    print(f"✅ Browser-use logging configured from {libs_path}")
except Exception as e:
    print(f"⚠️ Could not configure browser-use logging: {e}") 