# Agents QA - Automatización de Pruebas con IA

Herramienta de automatización de pruebas impulsada por IA que transforma historias de usuario en código de automatización ejecutable, permitiendo la generación y ejecución de pruebas automatizadas a partir de instrucciones en lenguaje natural.

## Características Principales

- **Full Test**: Proceso completo desde historias de usuario hasta código de automatización
- **Smoke Test**: Ejecución directa de pruebas sin pasos intermedios
- **Múltiples Frameworks**: Soporte para diversos frameworks de automatización
- **Ejecución en Navegador Real**: Pruebas ejecutadas en navegador real
- **Gestión de Proyectos**: Organización de pruebas en proyectos y suites
- **Historial de Tests**: Registro detallado de la ejecución de pruebas

## Requisitos Previos

- Python 3.11 o superior (requerido para browser-use)
- Google Gemini API Key

## Instalación

### Configuración del Entorno Virtual (Recomendado)

Es altamente recomendable usar un entorno virtual para evitar conflictos de dependencias:

#### En Windows (PowerShell):
```powershell
# 1. Crear entorno virtual
python -m venv .venv

# 2. Activar entorno virtual
.\.venv\Scripts\Activate.ps1

# 3. Instalar dependencias
pip install -r requirements.txt
```

#### En Linux/MacOS (Terminal):
```bash
# 1. Crear entorno virtual (asegúrate de usar Python 3.11+)
python3.11 -m venv .venv

# 2. Activar entorno virtual
source .venv/bin/activate

# 3. Instalar dependencias
pip install -r requirements.txt

# 4. (Opcional) Si aparece error de conflicto con google-ai-generativelanguage:
pip install --force-reinstall --no-deps google-generativeai

# 5. Instalar navegadores de Playwright (requerido para browser-use)
playwright install
```

#### Alternativas de Activación:

**Windows (si PowerShell no funciona):**
```cmd
# Usando Command Prompt (CMD)
.venv\Scripts\activate.bat
```

**macOS (si python3 no está disponible):**
```bash
# Usando python en lugar de python3
python -m venv .venv
source .venv/bin/activate
```

> **Nota**: Si encuentras problemas con la política de ejecución en PowerShell, ejecuta:
> ```powershell
> Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
> ```

### Instalación Directa (Sin entorno virtual)

1. Instala las dependencias:

```bash
pip install -r requirements.txt
```

> **Nota sobre conflicto de dependencias**: Si ves un error sobre `google-ai-generativelanguage`, ejecuta:
> ```bash
> pip install --force-reinstall --no-deps google-generativeai
> ```
> Este conflicto es conocido y no afecta el funcionamiento de la aplicación.

2. Configura la API Key de Google Gemini:

Crea un archivo `.env` en la raíz del proyecto y añade tu API Key:

```
GOOGLE_API_KEY=tu_api_key_aqui
LLM_MODEL=ModeloLLM (Default si no esta seteado es gemini 2.0 flash)
PROMPT_LANGUAGE=es (Idioma del prompt)
```

### Ejecución de la Aplicación

Una vez instaladas las dependencias y configurado el archivo `.env`:

```powershell
# Asegurase de que el entorno virtual esté activado
.\.venv\Scripts\Activate.ps1

# Ejecutar la API backend
python run_api.py
```

El backend se ejecutará en `http://localhost:8000` y el frontend Next.js está disponible en `http://localhost:9002`

## Modo de Uso

### Interfaz Web (Next.js)

La aplicación utiliza un frontend moderno en Next.js que se comunica con la API FastAPI:

```bash
# Terminal 1: Ejecutar el backend
python run_api.py

# Terminal 2: Ejecutar el frontend (en directorio web/)
cd web/
npm run dev
```

El frontend estará disponible en `http://localhost:9002` donde podrás:

1. Gestionar proyectos y suites de pruebas
2. Usar el QA Assistant con herramientas AI
3. Ejecutar Smoke Tests desde el Playground
4. Configurar ajustes del navegador
5. Editar y traducir prompts
6. Ver el historial de ejecuciones

### Línea de Comandos (CLI)

La aplicación también puede ejecutarse desde la línea de comandos:

#### Ejecutar un Smoke Test:

```bash
python cli.py smoke --url "https://www.ejemplo.com" --instructions "Iniciar sesión con usuario 'admin' y contraseña 'password'" --user-story "Como administrador, quiero acceder al panel de control"
```

#### Generar código de automatización desde una ejecución previa:

```bash
python cli.py generate-code --history-path "tests/smoke_test_YYYYMMDDHHMMSS/history.json" --framework "playwright"
```

#### Generar código durante la ejecución de un Smoke Test:

```bash
python cli.py smoke --url "https://www.ejemplo.com" --instructions "Iniciar sesión con usuario 'admin'" --generate-code "playwright"
```

## Frameworks Soportados

La aplicación soporta la generación de código de automatización para los siguientes frameworks:

- **Selenium + PyTest BDD (Python)**: Popular framework Python combinando Selenium WebDriver con PyTest BDD para desarrollo dirigido por comportamiento.
- **Playwright (Python)**: Framework moderno con soporte asíncrono integrado y pruebas cross-browser.
- **Cypress (JavaScript)**: Framework moderno de JavaScript para pruebas end-to-end con recarga en tiempo real.
- **Robot Framework**: Framework keyword-driven que utiliza sintaxis tabular simple.
- **Selenium + Cucumber (Java)**: Combinación robusta de Selenium WebDriver con Cucumber para Java.

## Ejemplo de Uso Completo

### Modo Full Test:

1. Ingresa una historia de usuario
2. Mejora la historia con el botón "Mejorar Historia"
3. Genera casos de prueba manuales
4. Genera escenarios Gherkin desde esos casos
5. Ejecuta las pruebas en navegador real
6. Genera código para el framework de tu elección
7. Guarda los resultados en tu proyecto

### Modo Smoke Test:

1. Ingresa la URL de la aplicación a probar
2. Ingresa las instrucciones de prueba en lenguaje natural
3. Ejecuta la prueba directamente
4. Visualiza los resultados y capturas de pantalla
5. Genera código de automatización si lo deseas

## Organización del Proyecto

- **run_api.py**: API principal FastAPI 
- **cli.py**: Interfaz de línea de comandos
- **web/**: Frontend Next.js con TypeScript
- **src/**: Código fuente de la aplicación
  - **API/**: Endpoints y rutas de la API
  - **Agents/**: Agentes de automatización
  - **Core/**: Servicios centrales del sistema
  - **Utilities/**: Utilidades y servicios
- **memory-bank/**: Contexto persistente para GitHub Copilot
- **tests/**: Directorio donde se almacenan los resultados de las pruebas

## Solución de Problemas Comunes

### Error de Política de Ejecución en PowerShell
Si encuentras el error "cannot be loaded because running scripts is disabled on this system":
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Error de Dependencias
Si tienes problemas con las dependencias, intenta:
```powershell
# Actualizar pip
python -m pip install --upgrade pip

# Reinstalar dependencias
pip install -r requirements.txt --force-reinstall
```

### Error de API Key
Asegúrate de que tu archivo `.env` esté en la raíz del proyecto y contenga una API key válida de Google Gemini.

### Problemas con el Navegador
La aplicación requiere que tengas Chrome o Chromium instalado para las pruebas automatizadas.

## 🐳 Ejecución con Docker

AgentQA puede ejecutarse completamente con Docker, lo que simplifica la instalación y garantiza un entorno consistente.

### Requisitos Previos

- Docker y Docker Compose instalados
- Archivo `.env` configurado (ver sección de instalación)

### Opciones de Ejecución

#### **Modo Desarrollo (Recomendado para desarrollo)**

```bash
# Opción 1: Usando Make (más simple)
make dev-up

# Opción 2: Usando Docker Compose directamente
docker-compose -f docker-compose.dev.yml up --build -d
```

**Características:**
- ✅ Hot reload habilitado
- ✅ Volúmenes montados para desarrollo
- ✅ Variables de entorno de desarrollo
- ✅ Logs más verbosos

#### **Modo Producción**

```bash
# Opción 1: Usando Make
make prod-up

# Opción 2: Usando Docker Compose directamente
docker-compose up --build -d
```

#### **Acceso a la Aplicación**

Una vez que los contenedores estén ejecutándose:

- **Frontend Web**: `http://localhost:9002`
- **Backend API**: `http://localhost:8000`
- **Documentación API**: `http://localhost:8000/docs`

### Comandos de Gestión Docker

```bash
# Ver estado de contenedores
make status
docker-compose -f docker-compose.dev.yml ps

# Ver logs en tiempo real
make dev-logs
docker-compose -f docker-compose.dev.yml logs -f

# Detener contenedores
make dev-down
docker-compose -f docker-compose.dev.yml down

# Limpiar recursos de Docker
make clean

# Acceder al shell del contenedor API
make shell-api

# Acceder al shell del contenedor Web
make shell-web

# Reconstruir contenedores (útil después de cambios en dependencias)
make dev-build
docker-compose -f docker-compose.dev.yml build --no-cache
```

### Configuración de Entorno

Asegúrate de que tu archivo `.env` esté configurado antes de ejecutar Docker:

```bash
# Copia el archivo de ejemplo y configúralo
cp .env.example .env
# Edita el archivo .env con tus API keys
```

### Solución de Problemas Docker

#### Error de permisos en Linux/macOS
```bash
sudo chown -R $USER:$USER .
```

#### Limpiar todo el entorno Docker
```bash
# Detener y limpiar todo
docker-compose -f docker-compose.dev.yml down --volumes --remove-orphans
docker system prune -a

# Reconstruir desde cero
make dev-build
make dev-up
```

#### Ver logs específicos de un servicio
```bash
# Solo logs del API
docker-compose -f docker-compose.dev.yml logs -f api-dev

# Solo logs del frontend
docker-compose -f docker-compose.dev.yml logs -f web-dev
```

---

**Desarrollado con ❤️ para matar a los QA**

# QAK Platform

QAK is a comprehensive testing automation platform that combines browser automation, AI-powered test generation, and intelligent test management.

## 🚀 Quick Start with Docker (Recommended)

The easiest way to run the entire QAK Platform is with Docker. You only need a Google API key to get started.

### Step 0: Install Docker Desktop (One-Time Setup)
First, you need to install Docker Desktop, a free application that manages containers.

1.  Go to the official website: **[https://www.docker.com/products/docker-desktop/](https://www.docker.com/products/docker-desktop/)**
2.  Download and install the version for your operating system (macOS or Windows).
3.  Open the Docker Desktop application. Make sure it's running (you'll see the whale icon in your system tray).

### Step 1: Open Your Terminal
You'll need to open a command-line tool. All the commands below will be run here.
-   **On macOS:** Open the **Terminal** app (you can find it in `Applications/Utilities` or by searching for it).
-   **On Windows:** Open **PowerShell** (you can find it in the Start Menu).

### Step 2: Pull the Docker Image
Copy and paste the following command into your terminal and press Enter. This downloads the official image from Docker Hub.

```bash
docker pull cioffinahuel/qak-platform:latest
```

### Step 3: Create your Environment File
Create a file named `.env` to store your API key. Copy, paste, and run this command. **Remember to replace `your_google_api_key_here` with your actual key.**

```bash
echo "GOOGLE_API_KEY=your_google_api_key_here" > .env
```
You can add other keys like `OPENAI_API_KEY` to this file on new lines.

### Step 4: Run the Platform
This command starts the container and maps port 80 on your machine to the application.

```bash
docker run -d --name qak-platform -p 80:80 --env-file .env cioffinahuel/qak-platform:latest
```
> **Note:** If port 80 is busy, you can use another one, e.g., `-p 8080:80`.

### Step 5: Open in Browser
Navigate to **[http://localhost](http://localhost)** (or `http://localhost:8080` if you changed the port).

---

## ⚙️ For Advanced Use: Docker Compose
For a more persistent setup where your data (like projects and test results) is saved even if you remove the container, you can use Docker Compose.

1.  **Download the configuration file:**
    ```bash
    curl -O https://raw.githubusercontent.com/your-repo/qak/main/docker-compose.yml
    ```
    *Note: Replace `your-repo/qak` with the actual path to your repository.*

2.  **Create your `.env` file** (as shown in Step 2 above).

3.  **Start with Compose:**
    ```bash
    docker-compose up -d
    ```

## 🛠️ Local Development (Without Docker)

<...>
