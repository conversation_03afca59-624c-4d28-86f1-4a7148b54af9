noVNC is Copyright (C) 2022 The noVNC authors
(./AUTHORS)

The noVNC core library files are licensed under the MPL 2.0 (Mozilla
Public License 2.0). The noVNC core library is composed of the
Javascript code necessary for full noVNC operation. This includes (but
is not limited to):

    core/**/*.js
    app/*.js
    test/playback.js

The HTML, CSS, font and images files that included with the noVNC
source distibution (or repository) are not considered part of the
noVNC core library and are licensed under more permissive licenses.
The intent is to allow easy integration of noVNC into existing web
sites and web applications.

The HTML, CSS, font and image files are licensed as follows:

    *.html                     : 2-Clause BSD license

    app/styles/*.css           : 2-Clause BSD license

    app/styles/Orbitron*       : SIL Open Font License 1.1
                                 (Copyright 2009 Matt <PERSON>)

    app/images/                : Creative Commons Attribution-ShareAlike
                                 http://creativecommons.org/licenses/by-sa/3.0/

Some portions of noVNC are copyright to their individual authors.
Please refer to the individual source files and/or to the noVNC commit
history: https://github.com/novnc/noVNC/commits/master

The are several files and projects that have been incorporated into
the noVNC core library. Here is a list of those files and the original
licenses (all MPL 2.0 compatible):

    core/base64.js          : MPL 2.0

    core/des.js             : Various BSD style licenses

    vendor/pako/            : MIT

Any other files not mentioned above are typically marked with
a copyright/license header at the top of the file. The default noVNC
license is MPL-2.0.

The following license texts are included:

    docs/LICENSE.MPL-2.0
    docs/LICENSE.OFL-1.1
    docs/LICENSE.BSD-3-Clause (New BSD)
    docs/LICENSE.BSD-2-Clause (Simplified BSD / FreeBSD)
    vendor/pako/LICENSE (MIT)

Or alternatively the license texts may be found here:

    http://www.mozilla.org/MPL/2.0/
    http://scripts.sil.org/OFL
    http://en.wikipedia.org/wiki/BSD_licenses
    https://opensource.org/licenses/MIT
