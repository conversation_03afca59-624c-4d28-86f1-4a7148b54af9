name: Test

on: [push, pull_request]

jobs:
  test:
    strategy:
      matrix:
        os:
          - ubuntu-latest
          - windows-latest
        browser:
          - ChromeHeadless
          - FirefoxHeadless
        include:
          - os: macos-latest
            browser: Safari
          - os: windows-latest
            browser: EdgeHeadless
      fail-fast: false
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
      - run: npm update
      - run: npm run test
        env:
          TEST_BROWSER_NAME: ${{ matrix.browser }}
