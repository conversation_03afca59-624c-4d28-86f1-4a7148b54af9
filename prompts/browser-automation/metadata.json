{"category": "browser-automation", "displayName": "Browser Automation", "description": "Prompts for executing <PERSON>herkin scenarios through browser automation agents with intelligent element location and interaction strategies", "version": "1.0.0", "maintainer": "AgentQA Team", "tags": ["browser-automation", "g<PERSON>kin", "web-testing", "element-identification", "test-execution"], "defaultLanguage": "en", "supportedLanguages": ["en"], "commonVariables": {"scenario": {"type": "string", "description": "Gherkin scenario to be executed by the browser automation agent", "required": true, "example": "Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in successfully"}, "base_url": {"type": "string", "description": "Base URL for the application under test", "required": false, "example": "https://example.com"}, "url_preservation_instructions": {"type": "string", "description": "Special instructions for preserving exact URLs found in scenarios", "required": false, "example": "The following URLs must be preserved exactly: https://example.com/login"}}, "prompts": [{"id": "task-generation", "name": "Browser Automation Task", "displayName": "Browser Automation Task", "description": "Generates detailed browser automation instructions for executing Gherkin scenarios with robust element identification", "file": "task-generation.md", "languages": ["en"], "variables": ["scenario", "base_url", "url_preservation_instructions"], "outputs": ["automation_instructions"], "examples": [{"description": "Login scenario automation", "input": {"scenario": "Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in"}, "expectedOutput": "Detailed step-by-step browser automation instructions with element identification strategies"}]}], "validationRules": {"requiredSections": ["## Prompt", "## Variables", "## Expected Output"], "allowedLanguages": ["en"], "variableFormat": "{{variable_name}}", "translationRequired": false}}