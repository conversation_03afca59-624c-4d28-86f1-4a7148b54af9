# Browser Automation Task Generation

## Purpose
Generates comprehensive browser automation instructions for executing Gherkin scenarios with intelligent element identification, robust interaction strategies, and detailed logging.

## Input Format
- Gherkin scenario to be executed by the browser automation agent
- Base URL for the application under test (optional)
- Special instructions for preserving exact URLs found in scenarios (optional)

## Output Format
- Detailed step-by-step interpretation of each Gherkin step
- Element identification strategy with multiple selector types
- Action execution with proper interaction methods
- Verification procedures for Then steps

## English Prompt

{url_preservation_instructions}

gherkin:
{scenario}


## Variables
- `scenario`: Gherkin scenario to be executed by the browser automation agent
- `base_url`: Base URL for the application under test
- `url_preservation_instructions`: Special instructions for preserving exact URLs found in scenarios

## Examples

This section demonstrates how to generate browser automation instructions from Gherkin scenarios.

### Input:
```
scenario: "Given I am on the login page\nWhen I enter '<EMAIL>' in the email field\nAnd I enter 'password123' in the password field\nAnd I click the login button\nThen I should see the dashboard"
url_preservation_instructions: "Use exact URL: https://app.example.com/login"
```

### Output:
Detailed step-by-step automation execution with element identification, actions performed, verifications made, and comprehensive logging of the entire process.
