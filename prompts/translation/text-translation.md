# Text Translation Prompt

## Purpose
Translates text between languages while preserving technical terms and formatting.

## Input Format
- Source text in any language
- Target language specification
- Optional context about technical terms

## Output Format
- Clean translated text
- Preserves original formatting and structure
- Maintains technical terms unchanged

## English Prompt

Translate the following text from {source_language} to {target_language}:

{text}

Requirements:
- Provide only the translation, no explanations
- Maintain the original formatting and structure
{preserve_technical_terms_instruction}
- Use natural, fluent {target_language}
- Keep the same tone and style as the original

## Variables

*   `text`: (String, Required) The text to be translated
*   `source_language`: (String, Required) Source language (e.g., "English", "Spanish", "auto-detected")
*   `target_language`: (String, Required) Target language (e.g., "English", "Spanish")
*   `preserve_technical_terms_instruction`: (String, Optional) Additional instruction about preserving technical terms

## Examples

### Input
```
text: "Esta función permite al usuario iniciar sesión en el sistema"
source_language: "Spanish"
target_language: "English"
preserve_technical_terms_instruction: "- Preserve API names and technical terms unchanged"
```

### Output
```
This function allows the user to log into the system
```
