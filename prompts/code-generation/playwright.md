# Playwright Python Code Generation

## Purpose
Generates Playwright Python code with async/await syntax and Page Object Model design pattern based on Gherkin scenarios and agent execution details.

## Input Format
- Gherkin scenario steps to convert into automated test code
- Base URL of the application under test
- Element selectors identified during agent execution
- Actions performed by the agent during execution

## Output Format
- Complete Python test automation code with async/await syntax
- Page Object classes with async methods
- Multi-browser configuration and setup

## English Prompt
Generate Playwright Python code based on the following:

Gherkin Steps:
```gherkin
{gherkin_steps}
```

Agent Execution Details:
- Base URL: {base_url}
- Element Selectors: {selectors}
- Actions Performed: {actions}
- Extracted Content: {extracted_content}

The code should follow these requirements:
1. Use Playwright's Python API with async/await syntax
2. Implement a Page Object Model design pattern
3. Utilize <PERSON><PERSON>'s auto-waiting capabilities
4. Include proper error handling and reporting
5. Support multiple browsers (Chromium, Firefox, WebKit)
6. Be well-commented and maintainable
7. Include setup and teardown routines

## Variables

*   `gherkin_steps`: (String, Required) Gherkin scenario steps to convert into automated test code. This should be a string containing the Gherkin steps, typically including Given, When, and Then clauses.
    *   Example: "Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in"
*   `base_url`: (String, Required) The base URL of the application under test.
    *   Example: "https://example.com"
*   `selectors`: (String, Required) Element selectors identified during agent execution. This should be a comma-separated string of CSS selectors or XPath expressions.
    *   Example: "#email, #password, .login-button"
*   `actions`: (String, Required) Actions performed by the agent during execution. This should be a comma-separated string describing the sequence of actions.
    *   Example: "Navigate to login page, fill email field, fill password field, click login button"
*   `extracted_content`: (String, Optional) Content extracted during agent execution for validation purposes. This could be text from elements, page titles, etc.
    *   Example: "Welcome message on dashboard"

## Examples

This section demonstrates how to generate Playwright Python code from Gherkin scenarios.

### Input:
```
gherkin_steps: "Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in"
base_url: "https://example.com"
selectors: "#email, #password, .login-button"
actions: "Navigate to login page, fill email field, fill password field, click login button"
extracted_content: "Welcome message on dashboard"
```

### Output:
Complete Python test automation project with Playwright async implementation, page objects, and multi-browser configuration.

## Validation Rules
- All variables (`gherkin_steps`, `base_url`, `selectors`, `actions`) must be non-empty strings.
- `base_url` should be a valid URL format.
- `selectors` should be a comma-separated list of valid CSS or XPath selectors.

## Version History
- 1.0.0 (2023-10-27): Initial version.
