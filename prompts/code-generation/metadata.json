{"category": "code-generation", "displayName": "Code Generation", "description": "Prompts for generating automated test code in various frameworks from Gherkin scenarios and agent execution details", "version": "1.0.0", "maintainer": "AgentQA Team", "tags": ["code-generation", "automation", "frameworks", "g<PERSON>kin", "test-automation"], "defaultLanguage": "en", "supportedLanguages": ["en"], "commonVariables": {"gherkin_steps": {"type": "string", "description": "<PERSON>herkin scenario steps to convert into automated test code", "required": true, "example": "Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in"}, "base_url": {"type": "string", "description": "The base URL of the application under test", "required": true, "example": "https://example.com"}, "selectors": {"type": "string", "description": "Element selectors identified during agent execution", "required": true, "example": "#email, #password, .login-button"}, "actions": {"type": "string", "description": "Actions performed by the agent during execution", "required": true, "example": "Navigate to login page, fill email field, fill password field, click login button"}, "extracted_content": {"type": "string", "description": "Content extracted during agent execution for validation", "required": false, "example": "Welcome message, user dashboard, error messages"}, "execution_context": {"type": "string", "description": "Rich context from real browser automation execution including steps, element interactions, and URLs", "required": true, "example": "Detailed execution history with actual browser interactions, element locators, and test results"}, "framework": {"type": "string", "description": "Target automation framework for script generation", "required": true, "example": "playwright_python, selenium_python, cypress"}, "include_screenshots": {"type": "boolean", "description": "Whether to include screenshot capture in generated script", "required": false, "example": "true"}, "include_waits": {"type": "boolean", "description": "Whether to include explicit waits in generated script", "required": false, "example": "true"}, "include_validations": {"type": "boolean", "description": "Whether to include validation assertions in generated script", "required": false, "example": "true"}, "add_error_handling": {"type": "boolean", "description": "Whether to include try-catch error handling in generated script", "required": false, "example": "true"}, "use_best_locators": {"type": "boolean", "description": "Whether to prioritize most reliable locator strategies in generated script", "required": false, "example": "true"}, "add_comments": {"type": "boolean", "description": "Whether to include explanatory comments in generated script", "required": false, "example": "true"}, "page_object_pattern": {"type": "boolean", "description": "Whether to use Page Object Model pattern in generated script", "required": false, "example": "true"}}, "prompts": [{"id": "selenium-pytest", "name": "Selenium PyTest BDD", "displayName": "Selenium PyTest BDD", "description": "Generates Selenium PyTest BDD code with Page Object Model", "file": "selenium-pytest.md", "languages": ["en"], "variables": ["gherkin_steps", "base_url", "selectors", "actions", "extracted_content"], "outputs": ["python_code"], "frameworks": ["selenium", "pytest", "pytest-bdd"]}, {"id": "playwright", "name": "Playwright <PERSON>", "displayName": "Playwright <PERSON>", "description": "Generates Playwright Python code with async/await syntax", "file": "playwright.md", "languages": ["en"], "variables": ["gherkin_steps", "base_url", "selectors", "actions", "extracted_content"], "outputs": ["python_code"], "frameworks": ["playwright", "python"]}, {"id": "cypress", "name": "Cypress JavaScript", "displayName": "Cypress JavaScript", "description": "Generates Cypress JavaScript code with Cucumber integration", "file": "cypress.md", "languages": ["en"], "variables": ["gherkin_steps", "base_url", "selectors", "actions", "extracted_content"], "outputs": ["javascript_code"], "frameworks": ["cypress", "cucumber", "javascript"]}, {"id": "robot-framework", "name": "Robot Framework", "displayName": "Robot Framework", "description": "Generates Robot Framework code with SeleniumLibrary", "file": "robot-framework.md", "languages": ["en"], "variables": ["gherkin_steps", "base_url", "selectors", "actions", "extracted_content"], "outputs": ["robot_code"], "frameworks": ["robot-framework", "selenium"]}, {"id": "java-selenium", "name": "Java Selenium Cucumber", "displayName": "Java Selenium Cucumber", "description": "Generates Java Selenium code with Cucumber and Page Object Model", "file": "java-selenium.md", "languages": ["en"], "variables": ["gherkin_steps", "base_url", "selectors", "actions", "extracted_content"], "outputs": ["java_code"], "frameworks": ["selenium", "cucumber", "java", "maven"]}, {"id": "script-generation-playwright-python", "name": "Playwright <PERSON> Script Generator", "displayName": "AI-Generated Playwright <PERSON>", "description": "Generates Playwright Python automation scripts from real execution history using AI", "file": "script-generation-playwright-python.md", "languages": ["en"], "variables": ["execution_context", "framework", "include_screenshots", "include_waits", "include_validations", "add_error_handling", "use_best_locators", "add_comments", "page_object_pattern"], "outputs": ["python_code"], "frameworks": ["playwright", "python", "ai-generated"]}, {"id": "script-generation-selenium-python", "name": "Selenium Python Script Generator", "displayName": "AI-Generated Selenium Python Script", "description": "Generates Selenium Python automation scripts from real execution history using AI", "file": "script-generation-selenium-python.md", "languages": ["en"], "variables": ["execution_context", "framework", "include_screenshots", "include_waits", "include_validations", "add_error_handling", "use_best_locators", "add_comments", "page_object_pattern"], "outputs": ["python_code"], "frameworks": ["selenium", "python", "ai-generated"]}, {"id": "script-generation-playwright-js", "name": "Playwright JavaScript Script Generator", "displayName": "AI-Generated Playwright JavaScript Script", "description": "Generates Playwright JavaScript automation scripts from real execution history using AI", "file": "script-generation-playwright-js.md", "languages": ["en"], "variables": ["execution_context", "framework", "include_screenshots", "include_waits", "include_validations", "add_error_handling", "use_best_locators", "add_comments", "page_object_pattern"], "outputs": ["javascript_code"], "frameworks": ["playwright", "javascript", "ai-generated"]}, {"id": "script-generation-cypress", "name": "Cypress Script Generator", "displayName": "AI-Generated Cypress Script", "description": "Generates Cypress automation scripts from real execution history using AI", "file": "script-generation-cypress.md", "languages": ["en"], "variables": ["execution_context", "framework", "include_screenshots", "include_waits", "include_validations", "add_error_handling", "use_best_locators", "add_comments", "page_object_pattern"], "outputs": ["javascript_code"], "frameworks": ["cypress", "javascript", "ai-generated"]}], "validationRules": {"requiredSections": ["## Prompt", "## Variables", "## Expected Output"], "allowedLanguages": ["en"], "variableFormat": "{{variable_name}}", "translationRequired": false}}