{"category": "test-analysis", "displayName": "Test Analysis", "description": "Prompts for analyzing and summarizing test execution results and performance metrics", "version": "1.0.0", "maintainer": "AgentQA Team", "tags": ["test-analysis", "reporting", "summarization", "test-results"], "defaultLanguage": "en", "supportedLanguages": ["en"], "commonVariables": {"test_results": {"type": "string", "description": "Test execution results to be analyzed and summarized", "required": true, "example": "Test Suite: Login Tests\n- test_valid_login: PASSED\n- test_invalid_email: FAILED (Error: Element not found)\n- test_empty_fields: PASSED"}, "analysis_input": {"type": "object", "description": "JSON object containing step information, test context, browser state, and execution results for step validation", "required": true, "example": "{\"step\": {\"number\": 1, \"action\": \"click\", \"description\": \"Click login button\"}, \"execution\": {\"status\": \"success\", \"duration\": 150}, \"browser\": {\"url\": \"https://app.example.com/login\", \"title\": \"Login Page\"}}"}, "completion_input": {"type": "object", "description": "JSON object containing complete test execution data, context, step summaries, and artifacts for test completion analysis", "required": true, "example": "{\"test_info\": {\"name\": \"User Login Test\", \"suite\": \"Authentication\"}, \"execution_summary\": {\"result\": \"PASS\", \"duration\": 45}, \"step_analyses\": [...], \"artifacts\": {\"screenshots\": [...]}}"}}, "prompts": [{"id": "results-summary", "name": "Test Results Summary", "displayName": "Test Results Summary", "description": "Analyzes and summarizes test execution results with actionable insights", "file": "results-summary.md", "languages": ["en"], "variables": ["test_results"], "outputs": ["summary_report"], "examples": [{"description": "Login test results analysis", "input": {"test_results": "Login Tests: 2 passed, 1 failed\nFailure: Invalid email validation not working"}, "expectedOutput": "Structured summary with status, findings, and recommendations"}]}, {"id": "step-validation", "name": "Step Validation Analysis", "displayName": "AI Step Validation", "description": "Analyzes individual test steps for technical and functional validation in AgentQA browser automation", "file": "step-validation.md", "languages": ["en"], "variables": ["analysis_input"], "outputs": ["step_analysis_json"], "examples": [{"description": "Step validation with browser context", "input": {"analysis_input": "{\"step\": {\"number\": 1, \"action\": \"click\", \"description\": \"Click login button\"}, \"execution\": {\"status\": \"success\", \"duration\": 150}, \"browser\": {\"url\": \"https://app.example.com/login\", \"title\": \"Login Page\"}}"}, "expectedOutput": "JSON with technical and functional validation results including confidence scores"}]}, {"id": "test-completion-analysis", "name": "Test Completion Analysis", "displayName": "AI Test Completion Analysis", "description": "Comprehensive analysis of complete test execution to determine final verdict with detailed reasoning for AgentQA platform", "file": "test-completion-analysis.md", "languages": ["en"], "variables": ["completion_input"], "outputs": ["completion_analysis_json"], "examples": [{"description": "Complete test analysis with quality assessment", "input": {"completion_input": "{\"test_info\": {\"name\": \"Product Purchase Flow\", \"suite\": \"E-commerce\"}, \"execution_summary\": {\"final_result\": \"PASS\", \"duration\": 120}, \"step_analyses\": [{\"step_number\": 1, \"overall_assessment\": \"success\"}]}"}, "expectedOutput": "JSON with final PASS/FAIL/INCONCLUSIVE verdict, confidence scores, and strategic recommendations"}]}], "validationRules": {"requiredSections": ["## Prompt", "## Variables", "## Expected Output"], "allowedLanguages": ["en"], "variableFormat": "{{variable_name}}", "translationRequired": false}}