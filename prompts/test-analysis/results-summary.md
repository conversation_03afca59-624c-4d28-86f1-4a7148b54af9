# Test Results Summary

## Purpose
Analyzes and summarizes test execution results to provide clear, actionable insights for stakeholders and team members.

## Input Format
- Test execution results including test names, status (passed/failed), error messages, and execution details

## Output Format
- Comprehensive, well-structured test summary report
- Executive summary with overall test status
- Detailed analysis of failures and successes
- Actionable recommendations for next steps

## English Prompt
You are an AI assistant specialized in analyzing and summarizing test execution results. Your task is to create a clear, concise, and insightful summary of the provided test results.

Test Results to Analyze:
{test_results}

Please provide a comprehensive summary that includes:

1. **Overall Test Status**: Did the tests pass or fail overall?
2. **Key Findings**: What are the main outcomes or discoveries from the test execution?
3. **Failures and Issues**: If there were any failures, what were the specific problems encountered?
4. **Success Highlights**: What worked well or passed successfully?
5. **Recommendations**: Based on the results, what actions should be taken next?
6. **Risk Assessment**: Are there any critical issues that need immediate attention?

Guidelines for the summary:
- Be concise but thorough
- Use clear, non-technical language that stakeholders can understand
- Highlight the most important information first
- Provide actionable insights
- If there are multiple test cases, group similar issues together
- Include specific error messages or failure details when relevant
- Suggest next steps or remediation actions

Format the response as a well-structured summary that can be easily shared with team members and stakeholders.

## Variables

*   `test_results`: (String, Required) Test execution results to be analyzed and summarized. This should be a string containing details about test names, their status (e.g., passed, failed), any error messages, and execution times or other relevant details.
    *   Example: "Test Suite: E-commerce Login\n- test_valid_login: PASSED (200ms)\n- test_invalid_email: FAILED (Error: Email validation not working)\n- test_empty_password: PASSED (150ms)\n- test_sql_injection: FAILED (Security vulnerability detected)\nTotal: 2 passed, 2 failed"

## Examples

This section demonstrates how to analyze and summarize test execution results.

### Input:
```
test_results: "Test Suite: E-commerce Login
- test_valid_login: PASSED (200ms)
- test_invalid_email: FAILED (Error: Email validation not working)
- test_empty_password: PASSED (150ms)
- test_sql_injection: FAILED (Security vulnerability detected)
Total: 2 passed, 2 failed"
```

### Output:
```
## Test Execution Summary

**Overall Status**: 🔴 FAILED (50% pass rate)

**Key Findings**:
- Basic login functionality works correctly
- Critical security and validation issues identified

**Failures**:
1. Email validation system malfunction
2. SQL injection vulnerability detected

**Successes**:
- Valid login process working properly
- Empty password validation functioning

**Recommendations**:
1. URGENT: Fix SQL injection vulnerability
2. Repair email validation system
3. Re-run security tests after fixes

**Risk Assessment**: HIGH - Security vulnerability requires immediate attention
```

## Validation Rules
- The `test_results` variable must be a non-empty string.
- The input string should contain recognizable patterns of test results (e.g., test names, statuses like PASSED/FAILED).

## Version History
- 1.0.0 (2023-10-27): Initial version.
