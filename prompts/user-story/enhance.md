# User Story Enhancement

## Purpose
Improve user stories to follow standard format and include complete acceptance criteria.

## Input Format
- Raw user story text
- Basic feature description

## Output Format  
- Standard format: "As a [role], I want [functionality] to [benefit]"
- Clear acceptance criteria
- Specific examples when applicable

## English Prompt

Your task is to improve the following user story to be clearer, more complete, and follow the standard format 'As a [role], I want [functionality] to [benefit]'.

Make sure to include:
1. The specific user role (Who)
2. The desired functionality (What)  
3. The expected benefit or value (Why)
4. Clear and specific acceptance criteria
5. Concrete examples if possible

Original story:
{{{user_story}}}

IMPORTANT: Provide ONLY the improved user story content. Do not include any introductory text, explanations, or markdown formatting. Return only the clean, enhanced user story text.

## Variables

*   `user_story`: (String, Required) The original user story text to be enhanced. This should be a concise description of a feature or functionality from the user's perspective.
    *   Example: "As a customer, I want to be able to reset my password if I forget it."

## Examples

### Input
```
Login feature
```

### Output
```
As a registered user, I want to log into the system using my email and password so that I can access my personal account and manage my data securely.

Acceptance Criteria:
- User can enter email and password in the login form
- System validates credentials against the database
- Successful login redirects to the user dashboard
- Failed login shows appropriate error message
- Password field is masked for security
- Login session expires after 30 minutes of inactivity
```

## Validation Rules
- Must contain "As a [role], I want [functionality] to [benefit]" structure
- Must include at least 3 acceptance criteria
- User role must be specific (not just "user")
- Functionality must be clear and actionable
- Benefit must be measurable or observable

## Version History
- v1.0.0 (2025-05-30): Initial creation from user_story_prompts.py migration
