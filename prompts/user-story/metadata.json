{"category": "user-story", "version": "1.0.0", "description": "User story enhancement and improvement prompts", "prompts": [{"id": "enhance", "name": "User Story Enhancement", "description": "Improves user stories to follow standard format and include acceptance criteria", "file": "enhance.md", "languages": ["en"], "variables": ["user_story"], "tags": ["enhancement", "user-story", "format", "acceptance-criteria"], "lastModified": "2025-05-30T12:00:00Z", "author": "AgentQA Team", "dependencies": [], "complexity": "low", "estimatedTokens": 500}], "shared_variables": {"user_story": {"type": "string", "description": "The original user story text to be enhanced", "required": true, "examples": ["Login feature", "User can search products", "Payment processing"]}}, "validationRules": {"requiredSections": ["## Prompt", "## Variables", "## Expected Output"], "allowedLanguages": ["en"], "variableFormat": "{{variable_name}}", "translationRequired": false}}