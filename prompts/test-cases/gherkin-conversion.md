# Gherkin Conversion Prompt

## Purpose
Converts manual test cases into well-structured Gherkin scenarios using BDD (Behavior-Driven Development) format.

## Input Format
- Manual test cases in markdown format
- Each test case should have: title, preconditions, actions, and expected results

## Output Format
- Well-structured Gherkin scenarios
- Feature description at the top
- Individual scenarios for each test case
- Clear Given-When-Then structure

## English Prompt
Your task is to convert the following manual test cases into well-structured Gherkin scenarios.

Manual test cases:
{manual_test_cases_markdown}

Convert these test cases into Gherkin scenarios following these guidelines:

1. Use the keywords: Feature, Scenario, Given, When, Then, And, But
2. Create a Feature that represents the general functionality
3. Create individual scenarios for each test case
4. Keep the steps clear, concise, and in present imperative format
5. Maintain the same preconditions, actions, and expected results
6. Use business-oriented language, not technical implementation details
7. If there are example tables or scenario outlines, use them appropriately
8. Preserve any URLs that appear in the original test cases
9. Adequately represent negative test cases

**CRITICAL LANGUAGE REQUIREMENT:** 
- ALWAYS generate Gherkin scenarios in English, regardless of the input language
- If the input test cases are in Spanish, Portuguese, French, or any other language, translate the content to English while preserving the meaning and functionality
- Use standard English Gherkin keywords: Feature, Scenario, Given, When, Then, And, But
- All step descriptions, feature names, and scenario titles must be in English
- Translate technical terms and business concepts appropriately to English

IMPORTANT: Return ONLY the clean Gherkin scenarios in English. Do not include any introductory text, explanations, or markdown code blocks (```gherkin). Return only the pure Gherkin content.

## Variables

*   `manual_test_cases_markdown`: (String, Required) The input manual test cases in markdown format. This should be a string containing one or more test cases, each with a title, preconditions, actions/steps, and expected results.
    *   Example:
        ```markdown
        ## Test Case 1: User Login
        **Preconditions:** User has valid credentials
        **Actions:**
        1. Navigate to login page
        2. Enter username and password
        3. Click login button
        **Expected Results:** User should be redirected to dashboard
        ```

## Examples

This section demonstrates how to convert manual test cases to Gherkin scenarios, including translation from other languages to English.

### Example 1: English Input
```
## Test Case 1: User Login
**Preconditions:** User has valid credentials
**Actions:**
1. Navigate to login page
2. Enter username and password
3. Click login button
**Expected Results:** User should be redirected to dashboard
```

### Output (English)
```gherkin
Feature: User Authentication

  Scenario: Successful user login with valid credentials
    Given the user has valid credentials
    And the user is on the login page
    When the user enters their username and password
    And the user clicks the login button
    Then the user should be redirected to the dashboard
```

### Example 2: Spanish Input (Translated to English Output)
```
## Caso de Prueba 1: Inicio de Sesión de Usuario
**Precondiciones:** El usuario tiene credenciales válidas
**Acciones:**
1. Navegar a la página de inicio de sesión
2. Ingresar nombre de usuario y contraseña
3. Hacer clic en el botón de iniciar sesión
**Resultados Esperados:** El usuario debe ser redirigido al panel de control
```

### Output (Always English)
```gherkin
Feature: User Authentication

  Scenario: Successful user login with valid credentials
    Given the user has valid credentials
    And the user is on the login page
    When the user enters their username and password
    And the user clicks the login button
    Then the user should be redirected to the dashboard
```

## Validation Rules
- The `manual_test_cases_markdown` variable must be a non-empty string.
- The input markdown must follow a recognizable structure for test cases (e.g., including sections for actions and expected results).
- The output must ALWAYS be in English, regardless of the input language.
- All Gherkin keywords must be in English (Feature, Scenario, Given, When, Then, And, But).
- Content translation must preserve the original meaning and technical accuracy.

## Version History
- 1.0.0 (2023-10-27): Initial version.
- 1.1.0 (2025-06-14): Added mandatory English output requirement with translation support for multilingual inputs.
