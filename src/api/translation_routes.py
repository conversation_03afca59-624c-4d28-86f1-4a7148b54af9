"""
API endpoint for translating prompts from Spanish to English
Uses the new unified LLM architecture with OpenRouter optimization
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional
import os
import logging
# Use the new TextTranslator instead of direct Gemini
from src.services.llm.use_cases import TextTranslator

logger = logging.getLogger(__name__)

router = APIRouter()

class TranslationRequest(BaseModel):
    text: str
    source: str = "es"
    target: str = "en"
    type: str = "prompt"  # prompt, general, technical

class TranslationResponse(BaseModel):
    translatedText: str
    sourceLanguage: str
    targetLanguage: str
    type: str
    modelUsed: Optional[str] = None
    provider: Optional[str] = None
    costEstimate: Optional[dict] = None

def get_text_translator():
    """Get the text translator service."""
    return TextTranslator()

class PromptTranslationService:
    """Service for translating prompts using the new unified LLM architecture."""
    
    def __init__(self):
        """Initialize the translation service."""
        self.translator = TextTranslator()
    
    def translate_prompt(self, text: str, source: str, target: str, prompt_type: str) -> dict:
        """
        Translate prompt using the new unified architecture with OpenRouter.
        
        Args:
            text: Text to translate
            source: Source language code
            target: Target language code  
            prompt_type: Type of prompt (prompt, general, technical)
            
        Returns:
            Dict with translation result and metadata
        """
        # Use the new TextTranslator with versioned prompts
        result = self.translator.translate(
            text=text,
            target_language=target,
            source_language=source,
            preserve_technical_terms=(prompt_type in ["prompt", "technical"])
        )
        
        return result
    
    def _build_prompt_translation_prompt(self, text: str, source: str, target: str, prompt_type: str) -> str:
        """Build specialized translation prompt for technical content."""
        
        language_names = {
            "es": "Spanish",
            "en": "English",
            "fr": "French",
            "de": "German"
        }
        
        source_name = language_names.get(source, source)
        target_name = language_names.get(target, target)
        
        if prompt_type == "prompt":
            instruction_type = "AI prompt instructions"
            special_instructions = """
CRITICAL REQUIREMENTS for AI Prompt Translation:
1. Preserve ALL technical terms (APIs, frameworks, file names, etc.)
2. Maintain the exact structure and formatting
3. Keep placeholders like {variable_name} unchanged
4. Preserve markdown formatting (##, -, *, etc.)
5. Maintain imperative tone for instructions
6. Keep technical specifications precise
7. Preserve any code examples or snippets exactly
8. Maintain the logical flow and step-by-step structure
"""
        else:
            instruction_type = "technical content"
            special_instructions = """
REQUIREMENTS:
1. Maintain technical accuracy
2. Preserve key terminology
3. Keep professional tone
4. Ensure clarity and precision
"""

        return f"""You are a professional technical translator specializing in AI and software development content.

Translate the following {instruction_type} from {source_name} to {target_name}.

{special_instructions}

Original {source_name} text:
{text}

Translated {target_name} text:"""

# Initialize the service
translation_service = PromptTranslationService()

@router.post("/translate-prompt", response_model=TranslationResponse)
async def translate_prompt(request: TranslationRequest):
    """
    Translate text using the new unified LLM architecture with OpenRouter optimization.
    
    Especially useful for translating Spanish prompts to English
    while preserving technical accuracy and structure.
    """
    try:
        result = translation_service.translate_prompt(
            request.text,
            request.source, 
            request.target,
            request.type
        )
        
        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"Translation failed: {result.get('error', 'Unknown error')}")
        
        return TranslationResponse(
            translatedText=result["translated_text"],
            sourceLanguage=result["source_language"],
            targetLanguage=result["target_language"],
            type=request.type,
            modelUsed=result.get("model_used"),
            provider=result.get("provider"),
            costEstimate=result.get("cost_estimate")
        )
        
    except Exception as e:
        logger.error(f"Translation endpoint error: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Translation failed: {str(e)}"
        )
