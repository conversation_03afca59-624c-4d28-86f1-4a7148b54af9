"""Rutas de API para generación de código y Gherkin."""

from fastapi import APIRouter, HTTPException, Depends
from src.api.models import GherkinRequest, CodeGenerationRequest
# Use the new unified LLM services
from src.services.llm.use_cases import GherkinGenerator
from src.core.prompt_service import PromptService

# Router para generación de código y Gherkin
router = APIRouter(tags=["Generation"])

def get_gherkin_generator():
    """Get the Gherkin generator service."""
    return GherkinGenerator()

def get_prompt_service():
    """Get the PromptService for code generation."""
    return PromptService()


@router.post("/gherkin", summary="Generar escenario Gherkin")
async def create_gherkin_scenario(
    request: GherkinRequest,
    gherkin_generator: GherkinGenerator = Depends(get_gherkin_generator)
):
    """Genera un escenario Gherkin usando la nueva arquitectura LLM con OpenRouter."""
    try:
        from src.utilities.response_cleaner import clean_gherkin_response

        # Use the new GherkinGenerator with versioned prompts
        result = gherkin_generator.generate_from_instructions(
            instructions=request.instructions,
            user_story=request.user_story,
            url=request.url,
            language=getattr(request, 'language', 'en')
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"Error generating Gherkin: {result.get('error', 'Unknown error')}")

        # Clean the response
        clean_gherkin = clean_gherkin_response(result["gherkin"])

        return {
            "gherkin": clean_gherkin,
            "model_used": result.get("model_used"),
            "cost_estimate": result.get("cost_estimate"),
            "provider": result.get("provider")
        }
        
        # Generate test cases first, then Gherkin scenarios
        if request.user_story:
            # If we have a user story, enhance it and generate manual tests first
            enhanced_story = prompt_service.enhance_user_story(
                user_story=request.user_story,
                language=request.language
            )
            manual_tests = prompt_service.generate_manual_test_cases(
                enhanced_story=enhanced_story,
                language=request.language
            )
            gherkin = prompt_service.generate_gherkin(
                test_cases=manual_tests,
                language=request.language,
                **context
            )
        else:
            # For direct instructions, create a basic test case format
            test_cases = f"Test Case: {request.instructions}"
            gherkin = prompt_service.generate_gherkin(
                test_cases=test_cases,
                language=request.language,
                **context
            )

        # Clean the response
        clean_gherkin = clean_gherkin_response(gherkin)

        return {"gherkin": clean_gherkin}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/code", summary="Generar código de automatización")
async def generate_code(
    request: CodeGenerationRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera código de automatización para un framework específico."""
    try:
        # Use the new PromptService for code generation
        code = prompt_service.generate_code(
            framework=request.framework,
            gherkin_scenario=request.gherkin_scenario,
            history=request.test_history
        )
        return {"code": code}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
