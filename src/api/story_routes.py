"""Rutas de API para gestión de historias de usuario."""

import json
import re
from fastapi import APIRouter, HTTPException, Depends, Body
from typing import List

from src.api.models import (
    EnhanceStoryRequest,
    GenerateManualTestsRequest,
    GenerateGherkinRequest,
    SaveHistoryRequest
)
# Use the new unified LLM service instead of PromptService
from src.services.llm.use_cases import GherkinGenerator, StoryEnhancer, ManualTestCaseGenerator
from src.core.test_service import TestService
import os

# Router para historias de usuario
router = APIRouter(tags=["User Stories"])

# In-memory storage for user stories (for demonstration purposes)
user_story_store = {}


def get_story_enhancer():
    """Get the story enhancer service."""
    return StoryEnhancer()


def get_manual_test_generator():
    """Get the manual test case generator service."""
    return ManualTestCaseGenerator()


def get_gherkin_generator():
    """Get the Gherkin generator service."""
    return GherkinGenerator()


def get_test_service():
    """Crea y devuelve una instancia del servicio de pruebas."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))


@router.post("/enhance", summary="Mejorar historia de usuario")
async def enhance_story(
    request: EnhanceStoryRequest,
    story_enhancer: StoryEnhancer = Depends(get_story_enhancer)
):
    """Mejora una historia de usuario usando la nueva arquitectura LLM con OpenRouter."""
    try:
        from src.utilities.response_cleaner import clean_user_story_response

        # Use the new StoryEnhancer with versioned prompts and OpenRouter
        result = story_enhancer.enhance_story(
            story=request.user_story,
            language=request.language
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"Error enhancing story: {result.get('error', 'Unknown error')}")

        # Clean the response
        cleaned_story = clean_user_story_response(result["enhanced_story"])

        return {
            "enhanced_story": cleaned_story,
            "model_used": result.get("model_used"),
            "cost_estimate": result.get("cost_estimate"),
            "provider": result.get("provider")
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate-manual-tests", summary="Generar casos de prueba manuales")
async def generate_manual_tests(
    request: GenerateManualTestsRequest,
    manual_test_generator: ManualTestCaseGenerator = Depends(get_manual_test_generator)
):
    """Genera casos de prueba manuales usando la nueva arquitectura LLM con OpenRouter."""
    try:
        # Use the new ManualTestCaseGenerator with versioned prompts
        result = manual_test_generator.generate_from_story(
            enhanced_story=request.enhanced_story,
            language=request.language
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"Error generating manual tests: {result.get('error', 'Unknown error')}")

        return {
            "manual_tests": result["test_cases"],
            "model_used": result.get("model_used"),
            "cost_estimate": result.get("cost_estimate"),
            "provider": result.get("provider")
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate-gherkin", summary="Generar escenarios Gherkin desde casos manuales")
async def generate_gherkin_from_manual(
    request: GenerateGherkinRequest,
    gherkin_generator: GherkinGenerator = Depends(get_gherkin_generator)
):
    """Genera escenarios Gherkin usando la nueva arquitectura LLM con OpenRouter."""
    try:
        from src.utilities.response_cleaner import clean_gherkin_response

        # Use the new GherkinGenerator with versioned prompts
        result = gherkin_generator.generate_from_instructions(
            instructions=request.manual_tests,
            user_story=getattr(request, 'user_story', ''),
            url=getattr(request, 'url', ''),
            language=request.language
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"Error generating Gherkin: {result.get('error', 'Unknown error')}")

        # Clean the response
        clean_gherkin = clean_gherkin_response(result["gherkin"])

        return {
            "gherkin": clean_gherkin,
            "model_used": result.get("model_used"),
            "cost_estimate": result.get("cost_estimate"),
            "provider": result.get("provider")
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
