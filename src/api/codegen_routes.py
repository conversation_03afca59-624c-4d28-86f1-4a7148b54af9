"""Rutas para integración con Playwright Codegen."""

from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, BackgroundTasks
from datetime import datetime

from src.api.models import (
    PlaywrightCodegenRequest,
    CodegenSessionInfo,
    CodegenTestCaseRequest,
    CodegenStatsResponse
)
from src.core.service_container import get_codegen_service, get_executor_service

router = APIRouter(tags=["Codegen"])

@router.post("/start", response_model=CodegenSessionInfo, operation_id="start_codegen_session")
async def start_codegen_session(request: PlaywrightCodegenRequest) -> CodegenSessionInfo:
    """
    Inicia una nueva sesión de Playwright Codegen.
    
    Esta sesión abre un navegador con el Inspector de Playwright para grabar
    interacciones del usuario y generar código automáticamente.
    """
    try:
        session_info = await get_codegen_service().start_session(request)
        return session_info
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error iniciando sesión de codegen: {str(e)}")

@router.get("/session/{session_id}", operation_id="get_codegen_session_status")
async def get_codegen_session(session_id: str) -> Dict[str, Any]:
    """Obtiene el estado actual de una sesión de codegen con información de ejecuciones."""
    
    session = await get_codegen_service().get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Sesión no encontrada")
    
    # Convertir session a dict si es necesario
    if hasattr(session, 'model_dump'):
        session_data = session.model_dump()
    elif hasattr(session, 'dict'):
        session_data = session.dict()
    else:
        session_data = dict(session) if hasattr(session, '__dict__') else session
    
    # Obtener ejecuciones para esta sesión
    try:
        executor_service = get_executor_service()
        executions = executor_service.list_executions()
        
        # Filtrar ejecuciones para esta sesión específica
        session_executions = [
            exec_info for exec_info in executions
            if exec_info.get("session_id") == session_id
        ]
        
        # Agregar información de ejecuciones
        session_data.update({
            "executions": session_executions,
            "last_execution": session_executions[-1] if session_executions else None,
            "total_executions": len(session_executions)
        })
        
    except Exception as e:
        logger.warning(f"Error obteniendo ejecuciones para sesión {session_id}: {str(e)}")
        # Si falla la obtención de ejecuciones, continuar sin ellas
        session_data.update({
            "executions": [],
            "last_execution": None,
            "total_executions": 0
        })
    
    return session_data

@router.post("/session/{session_id}/stop", operation_id="stop_codegen_session")
async def stop_codegen_session(session_id: str) -> Dict[str, str]:
    """Detiene una sesión de codegen activa."""
    
    success = await get_codegen_service().stop_session(session_id)
    if not success:
        raise HTTPException(status_code=404, detail="Sesión no encontrada o ya detenida")
    
    return {"message": "Sesión detenida exitosamente", "session_id": session_id}

@router.get("/session/{session_id}/code", operation_id="get_codegen_generated_code")
async def get_generated_code(session_id: str) -> Dict[str, Any]:
    """Obtiene el código generado de una sesión."""
    
    session = await get_codegen_service().get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Sesión no encontrada")
    
    generated_code = await get_codegen_service().get_generated_code(session_id)
    
    return {
        "session_id": session_id,
        "status": session.status,
        "generated_code": generated_code,
        "target_language": session.target_language,
        "created_at": session.created_at,
        "updated_at": session.updated_at
    }

@router.post("/session/{session_id}/convert", response_model=Dict[str, Any], operation_id="convert_codegen_to_testcase")
async def convert_to_testcase(request: CodegenTestCaseRequest) -> Dict[str, Any]:
    """
    Convierte el código generado por codegen en un caso de prueba QAK.
    
    Adapta el código para integración con el ecosistema QAK y 
    opcionalmente lo guarda en un proyecto específico.
    """
    try:
        testcase_data = await get_codegen_service().convert_to_testcase(request)
        return testcase_data
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error convirtiendo código: {str(e)}")

@router.delete("/session/{session_id}", operation_id="cleanup_codegen_session")
async def cleanup_codegen_session(session_id: str) -> Dict[str, str]:
    """Limpia los recursos de una sesión de codegen."""
    
    success = await get_codegen_service().cleanup_session(session_id)
    if not success:
        raise HTTPException(status_code=404, detail="Sesión no encontrada")
    
    return {"message": "Sesión limpiada exitosamente", "session_id": session_id}

@router.get("/sessions", operation_id="list_codegen_sessions")
async def list_active_sessions() -> Dict[str, Any]:
    """Lista todas las sesiones activas y completadas de codegen."""
    
    try:
        # Obtener sesiones del codegen service
        sessions = await get_codegen_service().get_sessions_history_async()
        
        # Obtener ejecuciones del executor service
        executor_service = get_executor_service()
        executions = executor_service.list_executions()
        
        # Procesar y enriquecer las sesiones con sus ejecuciones
        enriched_sessions = []
        for session in sessions:
            # Encontrar las ejecuciones correspondientes a esta sesión
            session_executions = [
                exec_info for exec_info in executions
                if exec_info.get("session_id") == session.get("session_id")
            ]
            
            # Agregar las ejecuciones a la información de la sesión
            session_data = {
                **session,
                "executions": session_executions,
                "last_execution": session_executions[-1] if session_executions else None,
                "total_executions": len(session_executions)
            }
            enriched_sessions.append(session_data)
        
        return {
            "total_sessions": len(enriched_sessions),
            "sessions": enriched_sessions
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error listando sesiones y ejecuciones: {str(e)}"
        )

@router.get("/stats", response_model=CodegenStatsResponse, operation_id="get_codegen_statistics")
async def get_codegen_stats() -> CodegenStatsResponse:
    """Obtiene estadísticas de uso del servicio de Playwright Codegen."""
    
    return await get_codegen_service().get_stats()

@router.post("/bulk-cleanup", operation_id="bulk_cleanup_codegen_sessions")
async def bulk_cleanup_sessions() -> Dict[str, Any]:
    """Limpia todas las sesiones completadas o fallidas."""
    
    cleanup_count = 0
    sessions_to_cleanup = []
    
    for session_id, session in get_codegen_service().active_sessions.items():
        if session.status in ["completed", "failed", "stopped"]:
            sessions_to_cleanup.append(session_id)
    
    for session_id in sessions_to_cleanup:
        success = await get_codegen_service().cleanup_session(session_id)
        if success:
            cleanup_count += 1
    
    return {
        "message": f"Limpiadas {cleanup_count} sesiones",
        "sessions_cleaned": cleanup_count,
        "sessions_remaining": len(get_codegen_service().active_sessions)
    }

@router.get("/health", operation_id="codegen_health_check")
async def codegen_health_check() -> Dict[str, Any]:
    """Verifica el estado de salud del servicio de codegen."""
    
    try:
        # Verificar si Playwright está instalado
        import subprocess
        result = subprocess.run(
            ["playwright", "--version"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        playwright_available = result.returncode == 0
        playwright_version = result.stdout.strip() if playwright_available else None
        
    except Exception:
        playwright_available = False
        playwright_version = None
    
    stats = await get_codegen_service().get_stats()
    
    return {
        "service_status": "healthy",
        "playwright_available": playwright_available,
        "playwright_version": playwright_version,
        "active_sessions": stats.active_sessions,
        "total_sessions": stats.total_sessions,
        "service_uptime": datetime.now().isoformat()
    }

@router.get("/history", operation_id="get_codegen_history")
async def get_codegen_history(limit: int = 50) -> Dict[str, Any]:
    """Obtiene el historial de sesiones de codegen."""
    
    history = await get_codegen_service().get_sessions_history_async(limit)
    
    return {
        "total_sessions": len(history),
        "sessions": history
    }

@router.get("/history/{session_id}", operation_id="get_codegen_history_session")
async def get_codegen_history_session(session_id: str) -> Dict[str, Any]:
    """Obtiene una sesión específica del historial."""

    session = await get_codegen_service().get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Sesión no encontrada en el historial")

    return session.model_dump()

@router.get("/diagnose", operation_id="diagnose_codegen_setup")
async def diagnose_codegen_setup() -> Dict[str, Any]:
    """Ejecuta un diagnóstico completo del setup de Playwright Codegen."""

    try:
        # Verificar instalación de Playwright
        await get_codegen_service()._verify_playwright_installation()

        return {
            "status": "success",
            "message": "Playwright Codegen está correctamente configurado",
            "playwright_available": True,
            "browsers_installed": True,
            "codegen_command_working": True
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Error en configuración: {str(e)}",
            "playwright_available": False,
            "error_details": str(e)
        }

@router.post("/execute", operation_id="execute_codegen_test")
async def execute_codegen_test(request: Dict[str, Any]) -> Dict[str, Any]:
    """
    Ejecuta un test generado por CodeGen usando browser-use.
    
    Convierte el código de Playwright generado por CodeGen en instrucciones
    para browser-use y las ejecuta.
    """
    try:
        session_id = request.get("session_id")
        if not session_id:
            raise HTTPException(status_code=400, detail="session_id es requerido")
        
        # Obtener configuración opcional
        config_id = request.get("config_id")
        configuration = request.get("configuration", {})
        
        # Ejecutar usando el servicio executor
        execution_info = await get_executor_service().execute_codegen_test(
            session_id=session_id,
            config_id=config_id,
            configuration=configuration
        )
        
        return execution_info
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error ejecutando test de codegen: {str(e)}")

@router.get("/execution/{execution_id}", operation_id="get_codegen_execution_status")
async def get_codegen_execution_status(execution_id: str) -> Dict[str, Any]:
    """Obtiene el estado de una ejecución de test CodeGen."""
    
    execution = get_executor_service().get_execution(execution_id)
    if not execution:
        raise HTTPException(status_code=404, detail="Ejecución no encontrada")
    
    return execution

@router.get("/executions", operation_id="list_codegen_executions")
async def list_codegen_executions() -> Dict[str, Any]:
    """Lista todas las ejecuciones de tests CodeGen."""
    
    try:
        executions = await get_executor_service().get_all_executions()
        return {
            "total_executions": len(executions),
            "executions": executions
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo ejecuciones: {str(e)}")

@router.get("/vnc-sessions", operation_id="list_vnc_sessions")
async def list_vnc_sessions() -> Dict[str, Any]:
    """Lista todas las sesiones VNC activas para CodeGen remoto."""
    
    try:
        codegen_service = get_codegen_service()
        
        # Verificar si el servicio tiene VNC habilitado
        if not hasattr(codegen_service, '_vnc_service'):
            return {
                "vnc_enabled": False,
                "sessions": [],
                "message": "VNC no está habilitado o no se han iniciado sesiones remotas"
            }
        
        vnc_sessions = codegen_service._vnc_service.list_active_vnc_sessions()
        
        return {
            "vnc_enabled": True,
            "total_sessions": len(vnc_sessions),
            "sessions": vnc_sessions
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo sesiones VNC: {str(e)}")

@router.get("/vnc-session/{session_id}", operation_id="get_vnc_session_info")
async def get_vnc_session_info(session_id: str) -> Dict[str, Any]:
    """Obtiene información detallada de una sesión VNC específica."""
    
    try:
        codegen_service = get_codegen_service()
        
        if not hasattr(codegen_service, '_vnc_service'):
            raise HTTPException(status_code=404, detail="Servicio VNC no disponible")
        
        vnc_info = codegen_service._vnc_service.get_vnc_session_info(session_id)
        
        if not vnc_info:
            raise HTTPException(status_code=404, detail="Sesión VNC no encontrada")
        
        return vnc_info
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo sesión VNC: {str(e)}")

@router.post("/vnc-session/{session_id}/stop", operation_id="stop_vnc_session")
async def stop_vnc_session(session_id: str) -> Dict[str, str]:
    """Detiene una sesión VNC específica."""
    
    try:
        codegen_service = get_codegen_service()
        
        if not hasattr(codegen_service, '_vnc_service'):
            raise HTTPException(status_code=404, detail="Servicio VNC no disponible")
        
        success = await codegen_service._vnc_service.stop_vnc_session(session_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Sesión VNC no encontrada")
        
        return {
            "message": "Sesión VNC detenida exitosamente",
            "session_id": session_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deteniendo sesión VNC: {str(e)}")

@router.get("/vnc-dependencies", operation_id="check_vnc_dependencies")
async def check_vnc_dependencies() -> Dict[str, Any]:
    """Verifica las dependencias necesarias para VNC (acceso remoto)."""
    
    try:
        from src.core.remote_codegen_service import RemoteCodegenService
        
        vnc_service = RemoteCodegenService()
        deps_check = await vnc_service.check_vnc_dependencies()
        
        return deps_check
        
    except Exception as e:
        return {
            "all_dependencies_available": False,
            "error": str(e),
            "message": "Error verificando dependencias VNC"
        }
