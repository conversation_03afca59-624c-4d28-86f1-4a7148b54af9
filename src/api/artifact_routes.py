"""
Rutas de API para servir artifacts desde storage backends (R2, S3, etc.)
"""

import logging
from fastapi import APIRouter, HTTPException, Response
from fastapi.responses import StreamingResponse, FileResponse, RedirectResponse
from typing import Optional
import io
import os
import glob
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path

from src.core.enhanced_artifact_collector import get_shared_enhanced_collector
from src.database.repositories.artifact_repository import ArtifactRepository
from src.database.models.artifact import Artifact as ArtifactModel

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/artifacts/screenshot_{timestamp}_{number}.png")
async def get_screenshot_by_name(response: Response, timestamp: str, number: str):
    """
    Handle direct screenshot requests from frontend.
    These come in the format /artifacts/screenshot_20250702_132310_003.png
    """
    try:
        screenshot_name = f"screenshot_{timestamp}_{number}.png"
        logger.info(f"🔍 Searching for screenshot: {screenshot_name}")
        # Set CORS headers so browser can follow redirect / fetch image
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "GET, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
        
        # Check if we're in R2-only mode
        r2_only_mode = os.getenv("ARTIFACT_R2_ONLY_MODE", "false").lower() == "true"
        
        if r2_only_mode:
            logger.info(f"🔧 R2-only mode enabled, searching in cloud storage for {screenshot_name}")
            
            # Get the collector to access R2
            collector = await get_shared_enhanced_collector()
            if not collector.blob_storage_backend:
                raise HTTPException(status_code=503, detail="No cloud storage backend configured")
            
            # Search for the screenshot in R2 using various potential blob keys
            # Since we don't know the exact execution ID, we'll need to search the database
            from src.database.repositories.artifact_repository import ArtifactRepository
            artifact_repo = ArtifactRepository()
            
            # Search for artifacts with matching original name
            artifacts = await artifact_repo.find({"original_name": screenshot_name})
            if not artifacts:
                logger.warning(f"🔍 Screenshot {screenshot_name} not found in database")
                raise HTTPException(status_code=404, detail=f"Screenshot {screenshot_name} not found")
            
            # Use the first match (most recent)
            artifact = artifacts[0]
            blob_key = artifact.blob_key or artifact.metadata.get("blob_key")
            if not blob_key:
                logger.error(f"❌ Artifact {artifact.artifact_id} has no blob_key")
                raise HTTPException(status_code=404, detail=f"Screenshot {screenshot_name} missing blob_key")

            # If we already have a valid (non-expired) presigned URL, redirect immediately
            now = datetime.utcnow()
            if artifact.presigned_url and artifact.presigned_url_expires and artifact.presigned_url_expires > now:
                logger.info("↪️  Redirecting to existing presigned URL for %s", blob_key)
                return RedirectResponse(url=artifact.presigned_url, status_code=307)

            # Otherwise generate a new presigned URL, update artifact and redirect
            try:
                client = await collector.blob_storage_backend.s3_client
                presigned_url_tmp = client.generate_presigned_url(
                    "get_object",
                    Params={
                        "Bucket": collector.blob_storage_backend.bucket_name,
                        "Key": blob_key,
                    },
                    ExpiresIn=3600,
                )
                # Handle potential coroutine from aiobotocore
                if hasattr(presigned_url_tmp, '__await__'):
                    presigned_url = await presigned_url_tmp
                else:
                    presigned_url = presigned_url_tmp
                    
                artifact.presigned_url = presigned_url
                artifact.presigned_url_expires = now + timedelta(hours=1)
                await artifact.save()
                logger.info("🔗 Generated new presigned URL for %s", blob_key)
                return RedirectResponse(url=presigned_url, status_code=307)
            except Exception as e:
                logger.error("💥 Failed to generate presigned URL: %s", e, exc_info=True)
                raise HTTPException(status_code=500, detail="Failed to generate download URL")
        
        else:
            # Fallback to local filesystem (legacy mode)
            logger.info(f"📁 Local mode, searching filesystem for {screenshot_name}")
            base_dir = Path(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            artifacts_dir = base_dir / "artifacts"
            
            # Pattern for finding the screenshot in the folder structure
            search_pattern = str(artifacts_dir / "**" / screenshot_name)
            
            # Find all matching files
            matching_files = glob.glob(search_pattern, recursive=True)
            
            if not matching_files:
                logger.warning(f"🔍 Screenshot {screenshot_name} not found in the filesystem")
                raise HTTPException(status_code=404, detail=f"Screenshot {screenshot_name} not found")
            
            # Use the first match
            screenshot_path = matching_files[0]
            logger.info(f"🖼️ Found screenshot at {screenshot_path}")
            
            # Serve the file
            return FileResponse(
                screenshot_path, 
                media_type="image/png",
                headers={"Content-Disposition": f"inline; filename={screenshot_name}"}
            )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"💥 Error serving screenshot {timestamp}_{number}.png: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/artifacts/{year}/{month}/{day}/{execution_id}/ArtifactType.SCREENSHOT/{filename}")
async def get_artifact_by_full_path(year: str, month: str, day: str, execution_id: str, filename: str):
    """
    Handle requests for artifacts using their full path structure.
    This handles paths like /artifacts/2025/07/02/f0e2dd7c/ArtifactType.SCREENSHOT/screenshot_*.png
    """
    try:
        logger.info(f"🔍 Searching for artifact: {filename} in execution {execution_id}")
        
        # Check if we're in R2-only mode
        r2_only_mode = os.getenv("ARTIFACT_R2_ONLY_MODE", "false").lower() == "true"
        
        if r2_only_mode:
            logger.info(f"🔧 R2-only mode enabled, searching in cloud storage for {filename}")
            
            # Get the collector to access R2
            collector = await get_shared_enhanced_collector()
            if not collector.blob_storage_backend:
                raise HTTPException(status_code=503, detail="No cloud storage backend configured")
            
            # Search for the artifact in database by original name and execution ID
            from src.database.repositories.artifact_repository import ArtifactRepository
            artifact_repo = ArtifactRepository()
            
            # Search for artifacts with matching original name and execution ID
            artifacts = await artifact_repo.find({
                "original_name": filename,
                "execution_id": execution_id
            })
            
            if not artifacts:
                # Try searching by filename only as fallback
                artifacts = await artifact_repo.find({"original_name": filename})
            
            if not artifacts:
                logger.warning(f"🔍 Artifact {filename} not found in database")
                raise HTTPException(status_code=404, detail=f"File {filename} not found")
            
            # Use the first match and get content from R2
            artifact = artifacts[0]
            blob_key = artifact.metadata.get("blob_key")
            if not blob_key:
                logger.error(f"❌ Artifact {artifact.artifact_id} has no blob_key")
                raise HTTPException(status_code=404, detail=f"File {filename} missing blob_key")
            
            content = await collector.blob_storage_backend.download_artifact(blob_key)
            if not content:
                logger.error(f"❌ Content for {blob_key} not found in R2")
                raise HTTPException(status_code=404, detail=f"File {filename} not found in cloud storage")
            
            # Determine content type based on file extension
            content_type = "image/png"  # Default
            if filename.endswith(".jpg") or filename.endswith(".jpeg"):
                content_type = "image/jpeg"
            elif filename.endswith(".json"):
                content_type = "application/json"
                
            logger.info(f"✅ Serving file {filename} from R2 (blob_key: {blob_key})")
            return StreamingResponse(
                io.BytesIO(content),
                media_type=content_type,
                headers={
                    "Content-Disposition": f"inline; filename={filename}",
                    "Cache-Control": "public, max-age=3600"
                }
            )
        
        else:
            # Fallback to local filesystem (legacy mode)
            logger.info(f"📁 Local mode, searching filesystem for {filename}")
            base_dir = Path(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            file_path = base_dir / "artifacts" / year / month / day / execution_id / "ArtifactType.SCREENSHOT" / filename
            
            # Check if file exists
            if not file_path.exists():
                logger.warning(f"🔍 File not found: {file_path}")
                raise HTTPException(status_code=404, detail=f"File {filename} not found")
            
            logger.info(f"🖼️ Serving file from {file_path}")
            
            # Determine content type based on file extension
            content_type = "image/png"  # Default
            if filename.endswith(".jpg") or filename.endswith(".jpeg"):
                content_type = "image/jpeg"
            elif filename.endswith(".json"):
                content_type = "application/json"
                
            # Serve the file
            return FileResponse(
                str(file_path),
                media_type=content_type,
                headers={
                    "Content-Disposition": f"inline; filename={filename}",
                    "Cache-Control": "public, max-age=3600"
                }
            )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"💥 Error serving file {filename}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/artifacts/{artifact_id}")
async def get_artifact_content(artifact_id: str):
    """
    Obtiene el contenido de un artifact dinámicamente desde el storage backend.
    """
    try:
        logger.info(f"🔍 Searching for artifact metadata in DB for artifact_id: {artifact_id}")
        
        # 1. Obtener metadatos del artifact desde la DB
        artifact_repo = ArtifactRepository()
        artifact: Optional[ArtifactModel] = await artifact_repo.get_by_artifact_id(artifact_id)
        
        if not artifact:
            logger.warning(f"🤔 Artifact metadata not found in DB for {artifact_id}. Falling back to key guessing.")
            # Fallback a la lógica anterior si no se encuentra en la DB (para compatibilidad)
            return await get_artifact_content_legacy(artifact_id)

        logger.info(f"✅ Found artifact metadata for {artifact_id}. Status: {artifact.status}")
        logger.info(f"🔍 DEBUG: Artifact metadata keys: {list(artifact.metadata.keys()) if artifact.metadata else 'None'}")
        logger.info(f"🔍 DEBUG: Artifact blob_key field: {artifact.blob_key}")
        if artifact.metadata:
            logger.info(f"🔍 DEBUG: blob_key in metadata: {artifact.metadata.get('blob_key')}")

        # 2. Determinar la clave del blob - check dedicated field first, then metadata
        blob_key = artifact.blob_key or (artifact.metadata.get("blob_key") if artifact.metadata else None)
        logger.info(f"🔍 DEBUG: blob_key from artifact: {blob_key}")
        
        # Si no hay blob_key pero el path es r2-only, construirlo.
        if not blob_key and artifact.file_path and artifact.file_path.startswith("r2-only://"):
             # Ejemplo de file_path: "r2-only://<exec_id>/<artifact_id>_original_name.png"
             # Ejemplo de blob_key: "artifacts/2025/07/01/<exec_id_short>/screenshot/<artifact_id>_original_name.png"
             # La reconstrucción del path completo es compleja, así que asumimos que blob_key debe existir.
            logger.error(f"❌ Artifact {artifact_id} has r2-only path but no blob_key in metadata.")
            raise HTTPException(status_code=404, detail=f"Artifact {artifact_id} is missing blob_key for retrieval.")

        if not blob_key:
            logger.error(f"❌ Artifact {artifact_id} does not have a blob_key in its metadata.")
            raise HTTPException(status_code=404, detail=f"Artifact {artifact_id} is not available in cloud storage.")

        logger.info(f"🔑 Using blob_key: {blob_key} for artifact {artifact_id}")

        # 3. Obtener el backend de storage y descargar
        collector = await get_shared_enhanced_collector()
        if not collector.blob_storage_backend:
            raise HTTPException(status_code=503, detail="No cloud storage backend configured")

        content = await collector.blob_storage_backend.download_artifact(blob_key)

        if not content:
            logger.error(f"❌ Content for blob_key {blob_key} (artifact {artifact_id}) not found in cloud storage.")
            raise HTTPException(status_code=404, detail=f"Artifact content for {artifact_id} not found in cloud storage.")

        # 4. Servir el contenido
        content_type = artifact.metadata.get("content_type", "image/png")
        filename = artifact.original_name or blob_key.split('/')[-1]

        logger.info(f"📤 Serving artifact {artifact_id} ({filename}) as {content_type}")
        return StreamingResponse(
            io.BytesIO(content),
            media_type=content_type,
            headers={"Content-Disposition": f"inline; filename=\"{filename}\""}
        )

    except HTTPException:
        raise # Re-raise HTTPException para no envolverla en un 500
    except Exception as e:
        logger.error(f"💥 Error serving artifact {artifact_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


async def get_artifact_content_legacy(artifact_id: str):
    """
    Lógica de fallback que intenta adivinar el path (como estaba antes).
    """
    logger.info(f"~ Using legacy artifact retrieval for {artifact_id}")
    collector = await get_shared_enhanced_collector()

    if not collector.blob_storage_backend:
        raise HTTPException(status_code=503, detail="No cloud storage backend configured")

    # Lista de blob_keys extraídas de los logs de ejecuciones recientes
    # ESTO ES INSEGURO Y DEBE SER REMOVIDO EVENTUALMENTE
    known_blob_keys = [
        f"artifacts/2025/07/01/c1d1bccc/screenshot/{artifact_id}_step_step_1_screenshot.png",
        f"artifacts/2025/07/01/c1d1bccc/screenshot/{artifact_id}_step_step_2_screenshot.png",
        f"artifacts/2025/07/01/c1d1bccc/screenshot/{artifact_id}_step_step_3_screenshot.png",
        f"artifacts/2025/07/01/f7b3b9a7/screenshot/{artifact_id}_step_step_1_screenshot.png",
        f"artifacts/2025/07/01/f7b3b9a7/screenshot/{artifact_id}_step_step_2_screenshot.png",
        f"artifacts/2025/07/01/ba92fc35/screenshot/{artifact_id}_step_step_1_screenshot.png",
        f"artifacts/2025/07/01/9370eaba/screenshot/{artifact_id}_step_step_1_screenshot.png",
        f"artifacts/2025/07/01/70dfdac8/screenshot/{artifact_id}_step_step_1_screenshot.png",
        f"artifacts/2025/07/01/70dfdac8/screenshot/{artifact_id}_step_step_2_screenshot.png",
        f"artifacts/2025/07/01/8293508c/screenshot/{artifact_id}_step_step_1_screenshot.png",
        f"artifacts/2025/07/01/8293508c/screenshot/{artifact_id}_step_step_2_screenshot.png",
        f"artifacts/2025/07/01/8293508c/screenshot/{artifact_id}_step_step_3_screenshot.png",
        f"artifacts/2025/07/01/8293508c/screenshot/{artifact_id}_step_step_4_screenshot.png",
    ]

    content = None
    found_key = None
    for key in known_blob_keys:
        try:
            logger.info(f"  > Trying legacy key: {key}")
            content = await collector.blob_storage_backend.download_artifact(key)
            if content:
                found_key = key
                logger.info(f"✅ Found artifact with legacy key: {key}")
                break
        except Exception:
            continue

    if not content:
        logger.error(f"❌ Artifact {artifact_id} not found with any legacy key.")
        raise HTTPException(status_code=404, detail=f"Artifact {artifact_id} not found")

    content_type = "image/png"
    filename = found_key.split('/')[-1]

    logger.info(f"📤 Serving artifact {artifact_id} as {content_type}")
    return StreamingResponse(
        io.BytesIO(content),
        media_type=content_type,
        headers={"Content-Disposition": f"inline; filename=\"{filename}\""}
    )

@router.get("/artifacts/key/{blob_key:path}")
async def get_artifact_by_key(blob_key: str):
    """
    Obtiene un artifact usando su blob_key directamente desde R2.
    
    Útil cuando se conoce la clave exacta en el storage backend.
    """
    try:
        collector = await get_shared_enhanced_collector()
        
        if not collector.blob_storage_backend:
            raise HTTPException(status_code=503, detail="No cloud storage backend configured")
        
        # Descargar directamente usando blob_key
        content = await collector.blob_storage_backend.download_artifact(blob_key)
        if not content:
            raise HTTPException(status_code=404, detail=f"Content for key {blob_key} not found in R2")
        
        # Inferir content type del blob_key
        content_type = "application/octet-stream"
        if blob_key.endswith(('.png', '.jpg', '.jpeg')):
            ext = blob_key.split('.')[-1].lower()
            content_type = f"image/{ext}"
        elif blob_key.endswith('.json'):
            content_type = "application/json"
        
        return StreamingResponse(
            io.BytesIO(content),
            media_type=content_type,
            headers={
                "Content-Disposition": f"inline; filename={blob_key.split('/')[-1]}",
                "Cache-Control": "public, max-age=3600"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving artifact by key {blob_key}: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving artifact: {str(e)}")

@router.get("/artifacts/{path:path}")
async def get_artifacts_fallback(path: str):
    """
    Fallback route that handles any artifact path.
    In R2-only mode, searches the database and serves from cloud storage.
    In local mode, looks it up in the filesystem.
    """
    try:
        logger.info(f"🔍 Fallback route handling path: {path}")
        
        # Check if we're in R2-only mode
        r2_only_mode = os.getenv("ARTIFACT_R2_ONLY_MODE", "false").lower() == "true"
        
        if r2_only_mode:
            logger.info(f"🔧 R2-only mode enabled, searching in cloud storage for {path}")
            
            # Get the collector to access R2
            collector = await get_shared_enhanced_collector()
            if not collector.blob_storage_backend:
                raise HTTPException(status_code=503, detail="No cloud storage backend configured")
            
            # Extract filename from path
            filename = Path(path).name
            
            # Search for the artifact in database by original name
            from src.database.repositories.artifact_repository import ArtifactRepository
            artifact_repo = ArtifactRepository()
            
            # Search for artifacts with matching original name
            artifacts = await artifact_repo.find({"original_name": filename})
            
            if not artifacts:
                logger.warning(f"🔍 Artifact {filename} not found in database")
                raise HTTPException(status_code=404, detail=f"File {path} not found")
            
            # Use the first match and get content from R2
            artifact = artifacts[0]
            blob_key = artifact.metadata.get("blob_key")
            if not blob_key:
                logger.error(f"❌ Artifact {artifact.artifact_id} has no blob_key")
                raise HTTPException(status_code=404, detail=f"File {path} missing blob_key")
            
            content = await collector.blob_storage_backend.download_artifact(blob_key)
            if not content:
                logger.error(f"❌ Content for {blob_key} not found in R2")
                raise HTTPException(status_code=404, detail=f"File {path} not found in cloud storage")
            
            # Determine content type based on file extension
            content_type = "application/octet-stream"  # Default
            if filename.endswith((".png")):
                content_type = "image/png"
            elif filename.endswith((".jpg", ".jpeg")):
                content_type = "image/jpeg"
            elif filename.endswith(".json"):
                content_type = "application/json"
            
            logger.info(f"✅ Serving file {filename} from R2 (blob_key: {blob_key})")
            return StreamingResponse(
                io.BytesIO(content),
                media_type=content_type,
                headers={"Content-Disposition": f"inline; filename={filename}"}
            )
        
        else:
            # Fallback to local filesystem (legacy mode)
            logger.info(f"📁 Local mode, searching filesystem for {path}")
            base_dir = Path(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            file_path = base_dir / "artifacts" / path
            
            # First check if the exact path exists
            if file_path.exists():
                logger.info(f"🖼️ Found file at exact path: {file_path}")
                
                # Determine content type based on file extension
                content_type = "application/octet-stream"  # Default
                if file_path.name.endswith((".png")):
                    content_type = "image/png"
                elif file_path.name.endswith((".jpg", ".jpeg")):
                    content_type = "image/jpeg"
                elif file_path.name.endswith(".json"):
                    content_type = "application/json"
                
                return FileResponse(
                    str(file_path),
                    media_type=content_type,
                    headers={"Content-Disposition": f"inline; filename={file_path.name}"}
                )
            
            # If not found, try to search for the file name recursively
            filename = Path(path).name
            search_pattern = str(base_dir / "artifacts" / "**" / filename)
            matching_files = glob.glob(search_pattern, recursive=True)
            
            if matching_files:
                found_path = matching_files[0]
                logger.info(f"🔍 Found file by searching recursively: {found_path}")
                
                # Determine content type based on file extension
                content_type = "application/octet-stream"  # Default
                if filename.endswith((".png")):
                    content_type = "image/png"
                elif filename.endswith((".jpg", ".jpeg")):
                    content_type = "image/jpeg"
                elif filename.endswith(".json"):
                    content_type = "application/json"
                
                return FileResponse(
                    found_path,
                    media_type=content_type,
                    headers={"Content-Disposition": f"inline; filename={filename}"}
                )
            
            # Log detailed information for debugging
            logger.warning(f"❌ File not found: {path}")
            logger.warning(f"❌ Full path tried: {file_path}")
            logger.warning(f"❌ Search pattern: {search_pattern}")
            
            # Try to list some files in the artifacts directory for debugging
            try:
                if (base_dir / "artifacts").exists():
                    year_dirs = list((base_dir / "artifacts").iterdir())
                    logger.warning(f"📁 Available year directories: {year_dirs}")
                    if year_dirs:
                        first_year = year_dirs[0]
                        month_dirs = list(first_year.iterdir())
                        logger.warning(f"📁 Available month directories in {first_year.name}: {month_dirs}")
            except Exception as e:
                logger.error(f"Error while listing directories: {e}")
            
            raise HTTPException(status_code=404, detail=f"File {path} not found")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"💥 Error serving file {path}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
