"""Rutas de API para métricas y analíticas de pruebas."""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Query, Depends

from src.core.test_service import TestService
from src.utilities.project_manager_service import load_test_history
from src.utilities.response_transformers import clean_data_for_json_serialization
import os

router = APIRouter(tags=["Analytics"])

# Dependency

def get_test_service():
    """Devuelve instancia de TestService."""
    api_key = os.environ.get("GOOGLE_API_KEY", "")
    return TestService(api_key=api_key)


@router.get("/analytics", summary="Obtener métricas agregadas de ejecución de tests")
async def get_analytics(
    start_date: Optional[str] = Query(None, description="Fecha inicio en formato YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="Fecha fin en formato YYYY-MM-DD"),
    project_id: Optional[str] = Query(None, description="Filtrar por ID de proyecto"),
    suite_id: Optional[str] = Query(None, description="Filtrar por ID de suite"),
    test_service: TestService = Depends(get_test_service),
    detailed: bool = Query(False, description="Incluir lista detallada de tests en la respuesta"),
    include_history: bool = Query(False, description="Analizar archivos history.json para obtener duración (costoso)")
) -> Dict[str, Any]:
    """
    Calcula estadísticas básicas, de rango de fechas y de vida (lifetime) para el dashboard de Analytics.
    """
    try:
        # 1. Parse date range
        today = datetime.utcnow().date()
        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").date()
        else:
            end_dt = today
        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").date()
        else:
            start_dt = end_dt - timedelta(days=29)

        # 2. Initialize accumulators
        total_tests_in_scope = 0
        tests_run_in_range = 0
        passed_tests_in_range = 0
        durations: list[float] = []
        daily_map: Dict[str, Dict[str, int]] = {}
        suite_info_map: Dict[str, Dict[str, Any]] = {}
        test_details: list[Dict[str, Any]] = []

        def _add_to_daily(date_key: str, status: str):
            if date_key not in daily_map:
                daily_map[date_key] = {"passed": 0, "failed": 0}
            if status == "Passed":
                daily_map[date_key]["passed"] += 1
            else:
                daily_map[date_key]["failed"] += 1

        # 3. Iterate over projects and suites to gather data
        projects = await test_service.project_manager.get_all_projects()
        for proj in projects:
            if project_id and proj.project_id != project_id:
                continue
            
            suites = proj.get_all_test_suites()
            for suite in suites:
                if suite_id and suite.suite_id != suite_id:
                    continue

                # Initialize suite info with lifetime stats from project JSON
                summary = suite.execution_summary or {}
                suite_info_map[suite.suite_id] = {
                    "suiteId": suite.suite_id,
                    "suiteName": suite.name,
                    # Lifetime stats
                    "lifetimeTests": len(suite.get_all_test_cases()),
                    "lifetimeRuns": summary.get("total_test_runs", 0),
                    "lifetimePassed": summary.get("successful_test_runs", 0),
                    "lifetimeFailed": summary.get("failed_test_runs", 0),
                    "lifetimeSuccessRate": summary.get("success_rate", 0) / 100.0 if summary.get("success_rate") is not None else 0,
                    "lastExecutionAt": summary.get("last_execution_at"),
                    # Date range specific stats (to be populated below)
                    "runsInDateRange": 0,
                    "passedInDateRange": 0,
                }

                tests = suite.get_all_test_cases()
                total_tests_in_scope += len(tests)

                for test in tests:
                    if not test.last_execution:
                        continue
                    
                    try:
                        exec_dt = datetime.fromisoformat(test.last_execution).date()
                    except (ValueError, TypeError):
                        continue

                    # Filter by date range
                    if not (start_dt <= exec_dt <= end_dt):
                        continue

                    # Update date-range stats
                    tests_run_in_range += 1
                    suite_info_map[suite.suite_id]["runsInDateRange"] += 1
                    if test.status == "Passed":
                        passed_tests_in_range += 1
                        suite_info_map[suite.suite_id]["passedInDateRange"] += 1

                    _add_to_daily(exec_dt.strftime("%Y-%m-%d"), test.status)

                    # Accumulate detailed test info if requested
                    if detailed:
                        test_details.append({
                            "testId": test.test_id,
                            "testName": test.name,
                            "suiteName": suite.name,
                            "suiteId": suite.suite_id,
                            "projectId": proj.project_id,
                            "status": test.status,
                            "lastExecution": test.last_execution,
                        })

                    # Lightweight duration calculation (optional)
                    if include_history and test.history_files:
                        history_path = test.history_files[-1]
                        try:
                            import json, os
                            if os.path.exists(history_path):
                                with open(history_path, "r", encoding="utf-8") as hf:
                                    hjson = json.load(hf)
                                steps = hjson.get("history", [])
                                if steps:
                                    first_meta = steps[0].get("metadata", {})
                                    last_meta = steps[-1].get("metadata", {})
                                    start_ts = first_meta.get("step_start_time")
                                    end_ts = last_meta.get("step_end_time")
                                    if start_ts and end_ts:
                                        start_time = datetime.fromisoformat(str(start_ts))
                                        end_time = datetime.fromisoformat(str(end_ts))
                                        durations.append(max(0.0, (end_time - start_time).total_seconds()))
                        except Exception:
                            pass

        # 4. Final Processing
        daily_executions = []
        current = start_dt
        while current <= end_dt:
            key = current.strftime("%Y-%m-%d")
            counts = daily_map.get(key, {"passed": 0, "failed": 0})
            daily_executions.append({
                "date": current.strftime("%b %d"),
                "passed": counts["passed"],
                "failed": counts["failed"],
                "total": counts["passed"] + counts["failed"],
            })
            current += timedelta(days=1)

        avg_duration = sum(durations) / len(durations) if durations else 0

        # 5. Build final response
        response = {
            "totalTests": total_tests_in_scope,
            "testsRun": tests_run_in_range,
            "passRate": (passed_tests_in_range / tests_run_in_range) if tests_run_in_range else 0,
            "avgDuration": avg_duration,
            "dailyExecutions": daily_executions,
            "suiteStats": list(suite_info_map.values()),
        }

        if detailed:
            response["testDetails"] = test_details

        return clean_data_for_json_serialization(response)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 