import logging
"""API routes for prompt management."""

import os
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Body
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from pydantic import BaseModel, <PERSON>

from src.core.prompt_service import PromptService
from src.core.prompt_validator import PromptValidator
from src.core.markdown_prompt_loader import Markdown<PERSON>romptLoader
from src.core.prompt_markdown_parser import PromptMarkdownParser

router = APIRouter(tags=["Prompts"])

# Pydantic models for request/response
class PromptListResponse(BaseModel):
    """Response model for listing prompts."""
    categories: Dict[str, List[Dict[str, Any]]]
    total_prompts: int

class PromptDetailResponse(BaseModel):
    """Response model for prompt details."""
    category: str
    prompt_id: str
    metadata: Dict[str, Any]
    content: Dict[str, str]  # sections of the markdown file
    file_path: str

class PromptUpdateRequest(BaseModel):
    """Request model for updating prompts."""
    content: Dict[str, str] = Field(..., description="Markdown sections to update")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata updates")
    commit_message: Optional[str] = Field(None, description="Commit message for changes")

class PromptCreateRequest(BaseModel):
    """Request model for creating new prompts."""
    category: str
    prompt_id: str
    name: str
    description: str
    languages: List[str] = ["en", "es"]
    content: Dict[str, str]
    metadata: Optional[Dict[str, Any]] = None

class ValidationResponse(BaseModel):
    """Response model for validation results."""
    valid: bool
    errors: List[str]
    warnings: List[str]

# Dependency to get prompt service
def get_prompt_service():
    """Get PromptService instance."""
    return PromptService()

# Dependency to get validator
def get_validator():
    """Get PromptValidator instance."""
    return PromptValidator()

# In-memory storage for prompts (for demonstration purposes)
prompt_store = {}

@router.get("/", summary="List all prompts", operation_id="list_all_prompts")
async def list_prompts() -> PromptListResponse:
    """List all available prompts organized by category."""
    try:
        prompts_dir = Path("prompts")
        categories = {}
        total_prompts = 0

        for category_dir in prompts_dir.iterdir():
            if category_dir.is_dir() and category_dir.name != "shared":
                metadata_file = category_dir / "metadata.json"
                if metadata_file.exists():
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)

                    category_prompts = []
                    for prompt_info in metadata.get("prompts", []):
                        category_prompts.append({
                            "id": prompt_info.get("id"),
                            "name": prompt_info.get("name"),
                            "description": prompt_info.get("description"),
                            "languages": prompt_info.get("languages", []),
                            "file": prompt_info.get("file"),
                            "last_modified": _get_file_modification_time(
                                category_dir / prompt_info.get("file", "")
                            )
                        })

                    categories[category_dir.name] = category_prompts
                    total_prompts += len(category_prompts)

        return PromptListResponse(
            categories=categories,
            total_prompts=total_prompts
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing prompts: {str(e)}")

@router.get("/{category}/{prompt_id}", summary="Get prompt details")
async def get_prompt_details(category: str, prompt_id: str) -> PromptDetailResponse:
    """Get detailed information about a specific prompt."""
    try:
        prompts_dir = Path("prompts")
        category_dir = prompts_dir / category

        if not category_dir.exists():
            raise HTTPException(status_code=404, detail=f"Category '{category}' not found")

        # Load metadata
        metadata_file = category_dir / "metadata.json"
        if not metadata_file.exists():
            raise HTTPException(status_code=404, detail=f"Metadata not found for category '{category}'")

        with open(metadata_file, 'r', encoding='utf-8') as f:
            category_metadata = json.load(f)

        # Find prompt in metadata
        prompt_metadata = None
        for prompt in category_metadata.get("prompts", []):
            if prompt.get("id") == prompt_id:
                prompt_metadata = prompt
                break

        if not prompt_metadata:
            raise HTTPException(status_code=404, detail=f"Prompt '{prompt_id}' not found")

        # Load and parse the markdown file
        file_path = category_dir / prompt_metadata["file"]
        if not file_path.exists():
            raise HTTPException(status_code=404, detail=f"Prompt file not found: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Simple markdown parsing without external dependencies
        sections = _parse_markdown_simple(content)

        # Fix relative path calculation
        try:
            relative_path = file_path.relative_to(Path.cwd())
        except ValueError:
            # If relative_to fails, use the absolute path or a fallback
            relative_path = file_path

        return PromptDetailResponse(
            category=category,
            prompt_id=prompt_id,
            metadata=prompt_metadata,
            content=sections,
            file_path=str(relative_path)
        )

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        raise HTTPException(status_code=500, detail=f"Error getting prompt details: {str(e)}\nTrace: {error_trace}")

@router.put("/{category}/{prompt_id}", summary="Update prompt")
async def update_prompt(
    category: str,
    prompt_id: str,
    request: PromptUpdateRequest,
    validator: PromptValidator = Depends(get_validator)
) -> JSONResponse:
    """Update an existing prompt."""
    try:
        prompts_dir = Path("prompts")
        category_dir = prompts_dir / category

        if not category_dir.exists():
            raise HTTPException(status_code=404, detail=f"Category '{category}' not found")

        # Load metadata to find the file
        metadata_file = category_dir / "metadata.json"
        if not metadata_file.exists():
            raise HTTPException(status_code=404, detail=f"Metadata not found for category '{category}'")

        with open(metadata_file, 'r', encoding='utf-8') as f:
            category_metadata = json.load(f)

        # Find prompt in metadata
        prompt_metadata = None
        prompt_index = None
        for i, prompt in enumerate(category_metadata.get("prompts", [])):
            if prompt.get("id") == prompt_id:
                prompt_metadata = prompt
                prompt_index = i
                break

        if not prompt_metadata:
            raise HTTPException(status_code=404, detail=f"Prompt '{prompt_id}' not found")

        # Update metadata if provided
        if request.metadata:
            prompt_metadata.update(request.metadata)
            category_metadata["prompts"][prompt_index] = prompt_metadata

            # Save updated metadata
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(category_metadata, f, indent=2, ensure_ascii=False)

        # Update markdown file
        file_path = category_dir / prompt_metadata["file"]

        # Read current content
        with open(file_path, 'r', encoding='utf-8') as f:
            current_content = f.read()

        parser = PromptMarkdownParser()
        current_sections = parser.parse_markdown_content(current_content, str(file_path))

        # Update sections with new content
        updated_sections = {**current_sections, **request.content}

        # Generate new markdown content
        new_content = _generate_markdown_content(updated_sections, prompt_metadata)

        # Validate before saving
        temp_file = file_path.with_suffix('.tmp')
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(new_content)

        validation_result = validator.validate_file_structure(str(temp_file))
        if not validation_result["valid"]:
            temp_file.unlink()  # Clean up temp file
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "errors": validation_result["errors"],
                    "warnings": validation_result.get("warnings", [])
                }
            )

        # Save the validated content
        temp_file.replace(file_path)

        # Add version history entry
        _add_version_history_entry(file_path, request.commit_message or f"Updated via API at {datetime.now().isoformat()}")

        return JSONResponse(content={
            "success": True,
            "message": f"Prompt '{prompt_id}' updated successfully",
            "validation": {
                "errors": validation_result.get("errors", []),
                "warnings": validation_result.get("warnings", [])
            }
        })

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating prompt: {str(e)}")

@router.post("/", summary="Create new prompt")
async def create_prompt(
    request: PromptCreateRequest,
    validator: PromptValidator = Depends(get_validator)
) -> JSONResponse:
    """Create a new prompt."""
    try:
        prompts_dir = Path("prompts")
        category_dir = prompts_dir / request.category

        # Create category directory if it doesn't exist
        category_dir.mkdir(parents=True, exist_ok=True)

        # Load or create metadata
        metadata_file = category_dir / "metadata.json"
        if metadata_file.exists():
            with open(metadata_file, 'r', encoding='utf-8') as f:
                category_metadata = json.load(f)
        else:
            category_metadata = {
                "category": request.category,
                "version": "1.0.0",
                "prompts": []
            }

        # Check if prompt already exists
        for existing_prompt in category_metadata.get("prompts", []):
            if existing_prompt.get("id") == request.prompt_id:
                raise HTTPException(
                    status_code=409,
                    detail=f"Prompt '{request.prompt_id}' already exists in category '{request.category}'"
                )

        # Create file name
        file_name = f"{request.prompt_id}.md"
        file_path = category_dir / file_name

        # Create prompt metadata
        prompt_metadata = {
            "id": request.prompt_id,
            "name": request.name,
            "description": request.description,
            "file": file_name,
            "languages": request.languages,
            "created": datetime.now().isoformat(),
            **(request.metadata or {})
        }

        # Generate markdown content
        markdown_content = _generate_markdown_content(request.content, prompt_metadata)

        # Validate content
        temp_file = file_path.with_suffix('.tmp')
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        validation_result = validator.validate_file_structure(str(temp_file))
        if not validation_result["valid"]:
            temp_file.unlink()  # Clean up temp file
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "errors": validation_result["errors"],
                    "warnings": validation_result.get("warnings", [])
                }
            )

        # Save the validated content
        temp_file.replace(file_path)

        # Update metadata
        category_metadata["prompts"].append(prompt_metadata)
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(category_metadata, f, indent=2, ensure_ascii=False)

        return JSONResponse(
            status_code=201,
            content={
                "success": True,
                "message": f"Prompt '{request.prompt_id}' created successfully",
                "prompt_id": request.prompt_id,
                "category": request.category,
                "file_path": str(file_path.relative_to(Path.cwd())),
                "validation": {
                    "errors": validation_result.get("errors", []),
                    "warnings": validation_result.get("warnings", [])
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating prompt: {str(e)}")

@router.delete("/{category}/{prompt_id}", summary="Delete prompt")
async def delete_prompt(category: str, prompt_id: str) -> JSONResponse:
    """Delete a prompt."""
    try:
        prompts_dir = Path("prompts")
        category_dir = prompts_dir / category

        if not category_dir.exists():
            raise HTTPException(status_code=404, detail=f"Category '{category}' not found")

        # Load metadata
        metadata_file = category_dir / "metadata.json"
        if not metadata_file.exists():
            raise HTTPException(status_code=404, detail=f"Metadata not found for category '{category}'")

        with open(metadata_file, 'r', encoding='utf-8') as f:
            category_metadata = json.load(f)

        # Find and remove prompt from metadata
        prompt_metadata = None
        updated_prompts = []
        for prompt in category_metadata.get("prompts", []):
            if prompt.get("id") == prompt_id:
                prompt_metadata = prompt
            else:
                updated_prompts.append(prompt)

        if not prompt_metadata:
            raise HTTPException(status_code=404, detail=f"Prompt '{prompt_id}' not found")

        # Delete the file
        file_path = category_dir / prompt_metadata["file"]
        if file_path.exists():
            file_path.unlink()

        # Update metadata
        category_metadata["prompts"] = updated_prompts
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(category_metadata, f, indent=2, ensure_ascii=False)

        return JSONResponse(content={
            "success": True,
            "message": f"Prompt '{prompt_id}' deleted successfully"
        })

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting prompt: {str(e)}")

@router.post("/{category}/{prompt_id}/validate", summary="Validate prompt")
async def validate_prompt(
    category: str,
    prompt_id: str,
    validator: PromptValidator = Depends(get_validator)
) -> ValidationResponse:
    """Validate a specific prompt."""
    try:
        validation_result = validator.validate_prompt(category, prompt_id)

        return ValidationResponse(
            valid=validation_result["valid"],
            errors=validation_result["errors"],
            warnings=validation_result["warnings"]
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error validating prompt: {str(e)}")

@router.post("/validate-all", summary="Validate all prompts")
async def validate_all_prompts(
    validator: PromptValidator = Depends(get_validator)
) -> Dict[str, Any]:
    """Validate all prompts in the system."""
    try:
        return validator.validate_all_prompts()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error validating prompts: {str(e)}")

# Helper functions
def _parse_markdown_simple(content: str) -> Dict[str, str]:
    """Simple markdown parser that extracts sections without external dependencies."""
    sections = {}
    current_section = None
    current_content = []

    lines = content.split('\n')

    for line in lines:
        # Check if this is a section header (## Section Name)
        if line.startswith('## '):
            # Save previous section if exists
            if current_section:
                sections[current_section] = '\n'.join(current_content).strip()

            # Start new section
            current_section = line[3:].strip()
            current_content = []
        else:
            # Add line to current section content
            if current_section:
                current_content.append(line)

    # Save the last section
    if current_section:
        sections[current_section] = '\n'.join(current_content).strip()

    return sections

def _get_file_modification_time(file_path: Path) -> Optional[str]:
    """Get file modification time as ISO string."""
    try:
        if file_path.exists():
            mtime = file_path.stat().st_mtime
            return datetime.fromtimestamp(mtime).isoformat()
    except:
        pass
    return None

def _generate_markdown_content(sections: Dict[str, str], metadata: Dict[str, Any]) -> str:
    """Generate markdown content from sections."""
    content = []

    # Title
    title = metadata.get("name", "Prompt")
    content.append(f"# {title}\n")

    # Add sections in order
    section_order = [
        "Purpose",
        "Input Format",
        "Output Format",
        "Variables",
        "English Prompt",
        "Spanish Prompt",
        "Examples",
        "Validation Rules",
        "Version History"
    ]

    for section_name in section_order:
        if section_name in sections and sections[section_name].strip():
            content.append(f"## {section_name}\n")
            content.append(f"{sections[section_name].strip()}\n")

    return "\n".join(content)

def _add_version_history_entry(file_path: Path, message: str):
    """Add entry to version history section."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        parser = PromptMarkdownParser()
        sections = parser.parse_markdown_content(content, str(file_path))

        # Add to version history
        history_entry = f"- {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: {message}"

        if "Version History" in sections:
            sections["Version History"] += f"\n{history_entry}"
        else:
            sections["Version History"] = history_entry

        # Regenerate content
        metadata = {"name": "Updated Prompt"}  # Basic metadata for regeneration
        new_content = _generate_markdown_content(sections, metadata)

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)

    except Exception as e:
        # Don't fail the main operation if version history update fails
        logging.info(f"Warning: Could not update version history: {e}")
