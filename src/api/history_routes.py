import logging
"""Rutas de API para gestión de historial de pruebas."""

import os
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional

from src.api.models import SaveHistoryRequest
from src.core.test_service import TestService
from src.utilities.response_transformers import (
    clean_data_for_json_serialization,
    convert_screenshot_paths_to_urls
)
from src.services.execution_service import ExecutionService
from src.services.project_service import ProjectService

# Router para historial de pruebas
router = APIRouter(tags=["History"])

# In-memory storage for history (for demonstration purposes)
history_store = []


def get_test_service():
    """Crea y devuelve una instancia del servicio de pruebas."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))


def get_execution_service():
    return ExecutionService()

def get_project_service():
    return ProjectService()


@router.post("/projects/save-history", summary="Guardar historial en proyecto")
async def save_history_to_project(
    request: SaveHistoryRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Guarda un historial de prueba en un proyecto."""
    try:
        test_case = test_service.save_history_to_project(
            project_id=request.project_id,
            suite_id=request.suite_id,
            test_history=request.test_history,
            name=request.name,
            description=request.description,
            gherkin=request.gherkin
        )
        return {"test_case": test_case}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/history/{history_path:path}", summary="Obtener historial de prueba procesado")
async def get_processed_test_history(history_path: str):
    """
    Obtiene un historial de prueba procesado con capturas de pantalla extraídas.
    
    Args:
        history_path: Ruta al archivo de historial (ej: tests/smoke_test_20250611231818/history.json)
    
    Returns:
        TestExecutionHistoryData: Historial procesado con capturas de pantalla convertidas a URLs
    """
    try:
        from src.utilities.project_manager_service import load_test_history
        
        # Construir la ruta completa al archivo
        if not history_path.startswith(os.sep) and not os.path.isabs(history_path):
            # Es una ruta relativa, construir desde el directorio de trabajo
            full_path = os.path.join(os.getcwd(), history_path)
        else:
            full_path = history_path
        
        # Verificar que el archivo existe
        if not os.path.exists(full_path):
            raise HTTPException(status_code=404, detail=f"History file not found: {history_path}")
        
        # Cargar y procesar el historial
        processed_history = load_test_history(full_path)
        
        # Convertir las rutas de capturas de pantalla a URLs servibles
        if processed_history.get("screenshots"):
            converted_screenshots = convert_screenshot_paths_to_urls(processed_history["screenshots"])
            processed_history["screenshots"] = converted_screenshots
        
        # Limpiar los datos para serialización JSON
        cleaned_history = clean_data_for_json_serialization(processed_history)
        
        return cleaned_history
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing history file: {str(e)}")


@router.delete("/executions/history", summary="Eliminar historial de ejecución")
async def delete_execution_history(
    history_path: str = Query(..., description="Ruta del archivo de historial"),
    project_id: str = Query(..., description="ID del proyecto"),
    suite_id: str = Query(..., description="ID de la suite"),
    test_id: str = Query(..., description="ID del caso de prueba"),
    execution_service: ExecutionService = Depends(get_execution_service),
    project_service: ProjectService = Depends(get_project_service)
):
    """Elimina el documento de ejecución y su archivo legacy, y quita la referencia del caso de prueba."""
    try:
        await execution_service.delete_by_history_path(history_path)
    except Exception as exc:
        logger = execution_service.logger if hasattr(execution_service, "logger") else None
        if logger:
            logger.warning(f"Error deleting execution by history path (DB): {exc}")

    # Remove reference in DB first; if that fails or DB disabled, fallback to legacy JSON
    removed_ref = False
    try:
        await project_service.remove_history_reference(project_id, suite_id, test_id, history_path)
        removed_ref = True
    except Exception as exc:
        if hasattr(project_service, "logger"):
            project_service.logger.warning(f"DB remove_history_reference failed, will try legacy JSON: {exc}")

    if not removed_ref:
        # Legacy fallback
        try:
            from src.utilities.project_manager_service import ProjectManagerService
            pm = ProjectManagerService()
            proj = pm.get_project(project_id)
            if proj:
                suite = proj.get_test_suite(suite_id)
                if suite:
                    case = suite.get_test_case(test_id)
                    if case and getattr(case, "history_files", None):
                        case.history_files = [h for h in case.history_files if h != history_path]
                        pm.save_project(proj)
        except Exception as exc:
            logging.info(f"⚠️ Legacy fallback failed to remove history reference: {exc}")

    return {"success": True}
