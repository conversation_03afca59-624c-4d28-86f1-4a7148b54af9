"""
Script Generation API Routes

Endpoints for generating automation scripts from execution history.
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Optional
from pydantic import BaseModel
from enum import Enum
from datetime import datetime

from src.services.script_generator_service import (
    ScriptGeneratorService, 
    ScriptFramework, 
    ScriptGenerationOptions
)
from src.database.repositories.execution_repository import ExecutionRepository


class ScriptGenerationRequest(BaseModel):
    """Request model for script generation."""
    execution_id: str
    framework: ScriptFramework
    include_assertions: bool = True
    include_waits: bool = True
    include_screenshots: bool = False
    add_error_handling: bool = True
    use_best_locators: bool = True
    add_comments: bool = True
    page_object_pattern: bool = False


class ScriptGenerationResponse(BaseModel):
    """Response model for generated script."""
    script_content: str
    framework: str
    execution_id: str
    generated_at: str
    steps_count: int
    target_url: Optional[str]
    success_rate: float
    metadata: dict
    # Add missing fields that frontend expects
    editable_steps: list = []
    script_name: Optional[str] = None
    description: Optional[str] = None
    script_summary: Optional[str] = None  # AI-generated human-readable summary
    total_steps: int = 0
    enabled_steps: int = 0
    validations_count: int = 0
    include_screenshots: bool = False
    include_waits: bool = True
    include_validations: bool = True
    raw_script_content: Optional[str] = None


router = APIRouter(prefix="/api/v2/script-generation", tags=["Script Generation"])


@router.post("/generate", response_model=ScriptGenerationResponse)
async def generate_script_from_execution(request: ScriptGenerationRequest):
    """
    Generate automation script from execution history.
    
    Takes an execution ID and generates a script in the specified framework
    that can reproduce the same test interactions.
    """
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"🚀 Starting script generation for execution {request.execution_id} with framework {request.framework}")
        
        # Get execution from database
        execution_repo = ExecutionRepository()
        logger.info(f"📦 Created execution repository")
        
        execution = await execution_repo.get_by_execution_id(request.execution_id)
        logger.info(f"🔍 Retrieved execution: {execution is not None}")
        
        if execution:
            logger.info(f"✅ Execution found - ID: {execution.execution_id}, Status: {execution.status}")
        else:
            logger.error(f"❌ Execution not found for ID: {request.execution_id}")
        
        if not execution:
            raise HTTPException(
                status_code=404, 
                detail=f"Execution {request.execution_id} not found"
            )
        
        # Create generation options
        logger.info(f"🔧 Creating generation options for framework: {request.framework}")
        options = ScriptGenerationOptions(
            framework=request.framework,
            include_assertions=request.include_assertions,
            include_waits=request.include_waits,
            include_screenshots=request.include_screenshots,
            add_error_handling=request.add_error_handling,
            use_best_locators=request.use_best_locators,
            add_comments=request.add_comments,
            page_object_pattern=request.page_object_pattern
        )
        logger.info(f"✅ Generation options created: {options}")
        
        # Generate script
        logger.info(f"🏭 Creating script generator service")
        script_generator = ScriptGeneratorService()
        
        logger.info(f"⚙️ Calling generate_script_from_execution...")
        result = await script_generator.generate_script_from_execution(execution, options, save_to_execution=True)
        logger.info(f"✅ Script generation completed successfully")
        
        logger.info(f"📄 Creating response...")
        logger.info(f"📊 Result keys: {list(result.keys())}")
        logger.info(f"📊 Result generated_at: {result.get('generated_at')}")
        
        # Ensure all required fields are present and valid
        if not result.get('generated_at'):
            result['generated_at'] = datetime.now().isoformat()
        
        response = ScriptGenerationResponse(**result)
        logger.info(f"🎉 Response created successfully")
        
        return response
        
    except ValueError as e:
        logger.error(f"❌ ValueError during script generation: {str(e)}", exc_info=True)
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Unexpected error during script generation: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Script generation failed: {str(e)}")


@router.get("/frameworks")
async def get_supported_frameworks():
    """Get list of supported automation frameworks."""
    return {
        "frameworks": [
            {
                "id": "playwright_python",
                "name": "Playwright (Python)",
                "description": "Modern Python automation with Playwright",
                "file_extension": ".py"
            },
            {
                "id": "playwright_js", 
                "name": "Playwright (JavaScript)",
                "description": "Modern JavaScript automation with Playwright",
                "file_extension": ".js"
            },
            {
                "id": "selenium_python",
                "name": "Selenium (Python)", 
                "description": "Traditional Python automation with Selenium WebDriver",
                "file_extension": ".py"
            },
            {
                "id": "cypress",
                "name": "Cypress",
                "description": "Modern JavaScript testing framework",
                "file_extension": ".cy.js"
            },
            {
                "id": "robot_framework",
                "name": "Robot Framework",
                "description": "Keyword-driven automation framework",
                "file_extension": ".robot"
            }
        ]
    }


@router.get("/executions/{execution_id}/scripts/{framework}")
async def get_existing_script(execution_id: str, framework: str):
    """
    Get an existing generated script for an execution.
    
    Returns 404 if no script exists yet for this execution/framework combination.
    """
    try:
        script_generator = ScriptGeneratorService()
        saved_scripts = await script_generator.get_saved_scripts_for_execution(execution_id)
        
        if framework not in saved_scripts:
            raise HTTPException(
                status_code=404, 
                detail=f"No existing script found for execution {execution_id} with framework {framework}"
            )
        
        script = saved_scripts[framework]
        return {
            "execution_id": execution_id,
            "framework": framework,
            "script": script.model_dump() if hasattr(script, 'model_dump') else script
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving script: {str(e)}"
        )


@router.put("/executions/{execution_id}/scripts/{framework}")
async def save_script(execution_id: str, framework: str, script_data: dict):
    """
    Save a modified script for an execution.
    
    This endpoint allows users to save their modifications to generated scripts.
    """
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        from src.models.generated_script import GeneratedScript
        
        logger.info(f"🔄 Saving script for execution {execution_id}, framework {framework}")
        logger.info(f"📊 Script data keys: {list(script_data.keys())}")
        
        # Validate script data and create GeneratedScript
        if 'generated_script' in script_data:
            logger.info("📦 Using 'generated_script' from request data")
            script_dict = script_data['generated_script']
            
            # Ensure required fields are present
            script_dict.setdefault('execution_id', execution_id)
            script_dict.setdefault('framework', framework)
            script_dict.setdefault('generated_at', datetime.now())
            script_dict.setdefault('raw_script_content', script_data.get('script_content', ''))
            script_dict.setdefault('editable_steps', [])
            
            generated_script = GeneratedScript(**script_dict)
        else:
            logger.info("📦 Creating minimal GeneratedScript from basic data")
            # Create a more complete GeneratedScript from the available data
            generated_script = GeneratedScript(
                execution_id=execution_id,
                framework=framework,
                raw_script_content=script_data.get('script_content', script_data.get('raw_script_content', '')),
                script_name=script_data.get('script_name', 'Generated Test Script'),
                description=script_data.get('description', ''),
                script_summary=script_data.get('script_summary', ''),
                target_url=script_data.get('target_url'),
                editable_steps=script_data.get('editable_steps', []),
                include_screenshots=script_data.get('include_screenshots', False),
                include_waits=script_data.get('include_waits', True),
                include_validations=script_data.get('include_validations', True),
                generated_at=datetime.now()
            )
        
        logger.info(f"✅ GeneratedScript created: {generated_script.script_name}")
        
        # Update the script using the service
        script_generator = ScriptGeneratorService()
        success = await script_generator.update_editable_script(
            execution_id=execution_id,
            framework=framework,
            updated_script=generated_script
        )
        
        if not success:
            logger.error(f"❌ Failed to update script - execution not found: {execution_id}")
            raise HTTPException(
                status_code=404,
                detail=f"Execution {execution_id} not found"
            )
        
        logger.info(f"✅ Script saved successfully for execution {execution_id}")
        return {
            "message": "Script saved successfully",
            "execution_id": execution_id,
            "framework": framework,
            "saved_at": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error saving script: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error saving script: {str(e)}"
        )


@router.get("/executions/{execution_id}/preview")
async def preview_script_generation(
    execution_id: str,
    framework: ScriptFramework = Query(ScriptFramework.PLAYWRIGHT_PYTHON)
):
    """
    Preview what elements and actions would be generated for a script.
    
    Useful for understanding what information is available before generating the full script.
    """
    try:
        # Get execution
        execution_repo = ExecutionRepository()
        execution = await execution_repo.get_by_execution_id(execution_id)
        
        if not execution:
            raise HTTPException(status_code=404, detail=f"Execution {execution_id} not found")
        
        # Analyze steps for preview
        script_generator = ScriptGeneratorService()
        enhanced_steps = await script_generator._enhance_steps_for_generation(execution.steps)
        
        preview_data = {
            "execution_id": execution_id,
            "total_steps": len(enhanced_steps),
            "target_url": execution.target_url,
            "steps_preview": []
        }
        
        for step in enhanced_steps[:10]:  # Preview first 10 steps
            step_preview = {
                "step_number": step.step_number,
                "action_type": step.action_type,
                "description": step.description,
                "has_element_info": bool(step.element_info),
                "available_locators": {},
                "input_data": step.input_data,
                "success": step.success
            }
            
            if step.element_locators:
                for attr in ['testid_selector', 'css_selector', 'xpath', 'text_selector', 'label_selector']:
                    value = getattr(step.element_locators, attr, None)
                    if value:
                        step_preview["available_locators"][attr] = value
            
            preview_data["steps_preview"].append(step_preview)
        
        # Add statistics
        locator_stats = script_generator._get_used_strategies(enhanced_steps)
        preview_data["locator_statistics"] = locator_stats
        preview_data["generation_feasibility"] = {
            "can_generate": len(enhanced_steps) > 0,
            "missing_element_info_count": sum(1 for s in enhanced_steps if not s.element_info),
            "success_rate": execution.summary.success_rate if execution.summary else 0.0
        }
        
        return preview_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Preview generation failed: {str(e)}")


@router.get("/executions/{execution_id}/locator-analysis")
async def analyze_execution_locators(execution_id: str):
    """
    Analyze the quality and reliability of locators in an execution.
    
    Provides insights into which locator strategies are available and their reliability.
    """
    try:
        execution_repo = ExecutionRepository()
        execution = await execution_repo.get_by_execution_id(execution_id)
        
        if not execution:
            raise HTTPException(status_code=404, detail=f"Execution {execution_id} not found")
        
        script_generator = ScriptGeneratorService()
        enhanced_steps = await script_generator._enhance_steps_for_generation(execution.steps)
        
        analysis = {
            "execution_id": execution_id,
            "total_steps": len(enhanced_steps),
            "steps_with_elements": sum(1 for s in enhanced_steps if s.element_info),
            "locator_quality": {
                "high_quality": 0,  # Steps with test-id or aria-label
                "medium_quality": 0,  # Steps with CSS or text
                "low_quality": 0,  # Steps with only XPath
                "no_locators": 0
            },
            "available_strategies": {},
            "recommendations": []
        }
        
        for step in enhanced_steps:
            if not step.element_locators:
                analysis["locator_quality"]["no_locators"] += 1
                continue
            
            # Determine quality
            if step.element_locators.testid_selector or step.element_locators.label_selector:
                analysis["locator_quality"]["high_quality"] += 1
            elif step.element_locators.css_selector or step.element_locators.text_selector:
                analysis["locator_quality"]["medium_quality"] += 1
            elif step.element_locators.xpath:
                analysis["locator_quality"]["low_quality"] += 1
            else:
                analysis["locator_quality"]["no_locators"] += 1
            
            # Count strategies
            for attr in ['testid_selector', 'css_selector', 'xpath', 'text_selector', 'label_selector']:
                if getattr(step.element_locators, attr, None):
                    analysis["available_strategies"][attr] = analysis["available_strategies"].get(attr, 0) + 1
        
        # Generate recommendations
        if analysis["locator_quality"]["no_locators"] > 0:
            analysis["recommendations"].append(
                f"{analysis['locator_quality']['no_locators']} steps have no locator information. Consider re-running execution with enhanced element capture."
            )
        
        if analysis["locator_quality"]["low_quality"] > analysis["locator_quality"]["high_quality"]:
            analysis["recommendations"].append(
                "Most locators are XPath-based. Consider adding data-testid attributes to improve script reliability."
            )
        
        if analysis["available_strategies"].get("testid_selector", 0) == 0:
            analysis["recommendations"].append(
                "No test-id locators found. Adding data-testid attributes would greatly improve script maintainability."
            )
        
        return analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Locator analysis failed: {str(e)}")


@router.post("/bulk-generate")
async def bulk_generate_scripts(execution_ids: list[str], framework: ScriptFramework):
    """
    Generate scripts for multiple executions in bulk.
    
    Useful for converting multiple test executions to automation scripts at once.
    """
    try:
        execution_repo = ExecutionRepository()
        script_generator = ScriptGeneratorService()
        results = []
        
        for execution_id in execution_ids:
            try:
                execution = await execution_repo.get_by_execution_id(execution_id)
                if not execution:
                    results.append({
                        "execution_id": execution_id,
                        "success": False,
                        "error": "Execution not found"
                    })
                    continue
                
                options = ScriptGenerationOptions(framework=framework)
                result = await script_generator.generate_script_from_execution(execution, options)
                
                results.append({
                    "execution_id": execution_id,
                    "success": True,
                    "script_length": len(result["script_content"]),
                    "steps_count": result["steps_count"],
                    "download_url": f"/api/v2/script-generation/download/{execution_id}?framework={framework.value}"
                })
                
            except Exception as e:
                results.append({
                    "execution_id": execution_id,
                    "success": False,
                    "error": str(e)
                })
        
        return {
            "total_requested": len(execution_ids),
            "successful": sum(1 for r in results if r["success"]),
            "failed": sum(1 for r in results if not r["success"]),
            "results": results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Bulk generation failed: {str(e)}")