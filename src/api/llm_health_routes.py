"""
Health Check Endpoint for LLM Services
Test endpoint to verify new LLM architecture is working
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any

router = APIRouter(tags=["LLM Health"])

@router.get("/llm/health", summary="Check LLM services health")
async def check_llm_health():
    """Check health status of all LLM providers and services."""
    try:
        from src.services.llm.llm_service_factory import get_llm_factory
        from src.services.llm.llm_config import LLMConfig
        
        factory = get_llm_factory()
        health_status = factory.get_health_status()
        config_status = LLMConfig.get_status()
        
        return {
            "status": "healthy",
            "factory": health_status,
            "configuration": config_status,
            "available_providers": factory.get_available_providers(),
            "timestamp": "2025-07-05"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"LLM health check failed: {str(e)}")

@router.post("/llm/test", summary="Test LLM request")
async def test_llm_request(test_request: Dict[str, Any]):
    """Test LLM request through new architecture."""
    try:
        from src.services.llm.llm_service_factory import get_llm_factory
        from src.services.llm.base_llm_service import LLMRequest
        
        factory = get_llm_factory()
        
        # Create test request
        request = LLMRequest(
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": test_request.get("message", "Hello, can you respond?")}
            ],
            use_case=test_request.get("use_case", "general"),
            language=test_request.get("language", "en"),
            max_tokens=100
        )
        
        # Make request
        response = factory.make_request(request)
        
        return {
            "success": response.success,
            "content": response.content,
            "model_used": response.model_used,
            "usage": response.usage,
            "error": response.error
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"LLM test failed: {str(e)}")

@router.get("/llm/usage", summary="Get LLM usage statistics")
async def get_llm_usage():
    """Get usage statistics from all LLM providers."""
    try:
        from src.services.llm.llm_service_factory import get_llm_factory
        
        factory = get_llm_factory()
        usage_stats = factory.get_usage_stats()
        
        return {
            "status": "success",
            "usage_statistics": usage_stats,
            "timestamp": "2025-07-05"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get usage stats: {str(e)}")

@router.get("/llm/test-gherkin", summary="Test Gherkin generation")
async def test_gherkin_generation():
    """Test Gherkin generation with new architecture."""
    try:
        from src.services.llm.use_cases.gherkin_generator import GherkinGenerator
        
        generator = GherkinGenerator()
        result = generator.generate_from_instructions(
            instructions="User should be able to login with valid credentials",
            user_story="As a user, I want to login to access my account",
            url="https://example.com/login",
            language="en"
        )
        
        return {
            "success": result["success"],
            "gherkin": result.get("gherkin", ""),
            "model_used": result.get("model_used", ""),
            "error": result.get("error", "")
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Gherkin test failed: {str(e)}")
