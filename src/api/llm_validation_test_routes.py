"""
Test endpoint para probar la validación LLM.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional
import logging
import os

logger = logging.getLogger(__name__)

router = APIRouter()


class LLMValidationTestRequest(BaseModel):
    execution_id: str
    test_objective: Optional[str] = None
    gherkin_scenario: Optional[str] = None


@router.post("/test-llm-validation")
async def test_llm_validation(request: LLMValidationTestRequest):
    """Test endpoint para validar resultados con LLM."""
    try:
        # Buscar el resultado por execution_id
        # Por ahora, vamos a usar datos de prueba
        
        mock_result_data = {
            "execution_id": request.execution_id,
            "success": False,  # Simular un resultado marcado como fallido
            "steps": [
                {
                    "step_number": 1,
                    "action_type": "navigate",
                    "thinking": "I need to navigate to the login page",
                    "url": "https://web-agent-playground.lovable.app",
                    "success": True
                },
                {
                    "step_number": 2,
                    "action_type": "click",
                    "thinking": "I'll click on the login button",
                    "success": True
                },
                {
                    "step_number": 3,
                    "action_type": "type",
                    "thinking": "I'll enter the email address",
                    "success": True
                },
                {
                    "step_number": 4,
                    "action_type": "type",
                    "thinking": "I'll enter the password",
                    "success": True
                },
                {
                    "step_number": 5,
                    "action_type": "click",
                    "thinking": "I'll click submit to login",
                    "success": True
                },
                {
                    "step_number": 6,
                    "action_type": "done",
                    "thinking": "Login successful. The user is on the dashboard page.",
                    "success": True
                }
            ],
            "final_result": "Login successful. The user is on the dashboard page.",
            "error": None,
            "metadata": {
                "total_steps": 6,
                "visited_urls": ["https://web-agent-playground.lovable.app"],
                "interacted_elements": []
            }
        }
        
        # Importar y usar el validador LLM
        from src.utilities.llm_result_validator import create_llm_validator
        
        api_key = os.environ.get("GOOGLE_API_KEY")
        if not api_key:
            raise HTTPException(status_code=500, detail="GOOGLE_API_KEY not configured")
        
        validator = create_llm_validator(api_key)
        
        # Aplicar validación
        validation_result = validator.validate_execution_result(
            result_data=mock_result_data,
            test_objective=request.test_objective or "User should be able to login with valid credentials",
            gherkin_scenario=request.gherkin_scenario or "Given user is on login page, When user enters valid credentials, Then user should be logged in successfully"
        )
        
        return {
            "original_result": mock_result_data,
            "llm_validation": validation_result,
            "recommendation": {
                "should_override": validation_result.get("validated_success") != mock_result_data["success"],
                "confidence": validation_result.get("validation_confidence"),
                "reasoning": validation_result.get("validation_reasoning")
            }
        }
        
    except Exception as e:
        logger.exception(f"LLM validation test failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
