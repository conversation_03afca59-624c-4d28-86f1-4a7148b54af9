import logging
"""
Artifact Repository for QAK MongoDB Implementation

Repository for artifact operations with storage management, deduplication,
analytics, and integration with ArtifactCollector system.
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from beanie import PydanticObjectId
from beanie.operators import And, Or, In
from pymongo import DESCENDING, ASCENDING

from .base import BaseRepository
from ..models.artifact import Artifact, ArtifactType, ArtifactStatus, StorageBackend


class ArtifactRepository(BaseRepository[Artifact]):
    """
    Repository for artifact document operations.
    
    Provides specialized queries for artifacts, storage management, deduplication,
    and analytics with integration to existing ArtifactCollector system.
    """
    
    def __init__(self):
        super().__init__("artifacts")
    
    def to_document(self, model: Artifact) -> Dict[str, Any]:
        """Convert Artifact model to MongoDB document compatible with Pydantic v1 y v2."""
        if hasattr(model, "model_dump"):
            return model.model_dump()
        # Fallback para modelos basados en Pydantic v1 / Beanie <v2
        return model.dict()
    
    def from_document(self, document: Dict[str, Any]) -> Artifact:
        """Convert MongoDB document to Artifact model."""
        return Artifact(**document)
    
    def get_document_id(self, model: Artifact) -> str:
        """Get the document ID from Artifact model."""
        return str(model.id) if model.id else ""
    
    document_model = Artifact
    
    async def get_by_artifact_id(self, artifact_id: str) -> Optional[Artifact]:
        """
        Get artifact by artifact_id.
        
        Args:
            artifact_id: Artifact ID to search for
            
        Returns:
            Artifact: Found document or None
        """
        # Use find method instead of Beanie query which has attribute access issues
        artifacts = await self.find({"artifact_id": artifact_id})
        return artifacts[0] if artifacts else None
    
    async def find_by_execution_id(
        self, 
        execution_id: str,
        artifact_type: Optional[ArtifactType] = None,
        status: Optional[ArtifactStatus] = None,
        limit: int = 100
    ) -> List[Artifact]:
        """
        Find artifacts by execution ID with optional filters.
        
        Args:
            execution_id: Execution ID to search for
            artifact_type: Optional artifact type filter
            status: Optional status filter
            limit: Maximum number of results
            
        Returns:
            List of Artifact
        """
        query = Artifact.execution_id == execution_id
        
        if artifact_type:
            query = And(query, Artifact.type == artifact_type)
        if status:
            query = And(query, Artifact.status == status)
        
        return await Artifact.find(query)\
            .sort([("collected_at", ASCENDING)])\
            .limit(limit)\
            .to_list()
    
    async def find_by_session_id(
        self, 
        session_id: str,
        artifact_type: Optional[ArtifactType] = None,
        limit: int = 100
    ) -> List[Artifact]:
        """
        Find artifacts by session ID.
        
        Args:
            session_id: Session ID to search for
            artifact_type: Optional artifact type filter
            limit: Maximum number of results
            
        Returns:
            List of Artifact
        """
        query = Artifact.session_id == session_id
        
        if artifact_type:
            query = And(query, Artifact.type == artifact_type)
        
        return await Artifact.find(query)\
            .sort([("collected_at", ASCENDING)])\
            .limit(limit)\
            .to_list()
    
    async def find_by_project_id(
        self, 
        project_id: str,
        artifact_type: Optional[ArtifactType] = None,
        limit: int = 200
    ) -> List[Artifact]:
        """
        Find artifacts by project ID.
        
        Args:
            project_id: Project ID to search for
            artifact_type: Optional artifact type filter
            limit: Maximum number of results
            
        Returns:
            List of Artifact
        """
        query = Artifact.project_id == project_id
        
        if artifact_type:
            query = And(query, Artifact.type == artifact_type)
        
        return await Artifact.find(query)\
            .sort([("collected_at", DESCENDING)])\
            .limit(limit)\
            .to_list()
    
    async def find_by_type(self, artifact_type: ArtifactType, limit: int = 100) -> List[Artifact]:
        """
        Find artifacts by type.
        
        Args:
            artifact_type: Type to filter by
            limit: Maximum number of results
            
        Returns:
            List of Artifact
        """
        return await Artifact.find(
            Artifact.type == artifact_type
        ).sort([("collected_at", DESCENDING)]).limit(limit).to_list()
    
    async def find_by_status(self, status: ArtifactStatus, limit: int = 100) -> List[Artifact]:
        """
        Find artifacts by status.
        
        Args:
            status: Status to filter by
            limit: Maximum number of results
            
        Returns:
            List of Artifact
        """
        return await Artifact.find(
            Artifact.status == status
        ).sort([("collected_at", DESCENDING)]).limit(limit).to_list()
    
    async def find_pending_artifacts(self, limit: int = 50) -> List[Artifact]:
        """
        Find all pending artifacts.
        
        Args:
            limit: Maximum number of results
            
        Returns:
            List of pending Artifact
        """
        return await Artifact.find(
            Artifact.status == ArtifactStatus.PENDING
        ).sort([("collected_at", ASCENDING)]).limit(limit).to_list()
    
    async def find_failed_artifacts(self, limit: int = 50) -> List[Artifact]:
        """
        Find failed artifacts.
        
        Args:
            limit: Maximum number of results
            
        Returns:
            List of failed Artifact
        """
        return await Artifact.find(
            Artifact.status == ArtifactStatus.FAILED
        ).sort([("collected_at", DESCENDING)]).limit(limit).to_list()
    
    async def find_by_content_hash(self, content_hash: str) -> List[Artifact]:
        """
        Find artifacts with the same content hash (duplicates).
        
        Args:
            content_hash: Content hash to search for
            
        Returns:
            List of Artifact with same hash
        """
        return await Artifact.find(
            Artifact.content_hash == content_hash
        ).sort([("collected_at", ASCENDING)]).to_list()
    
    async def find_duplicates(self) -> Dict[str, List[Artifact]]:
        """
        Find all duplicate artifacts grouped by content hash.
        
        Returns:
            Dict mapping content_hash to list of duplicate artifacts
        """
        pipeline = [
            {"$match": {"content_hash": {"$ne": None}}},
            {
                "$group": {
                    "_id": "$content_hash",
                    "count": {"$sum": 1},
                    "artifact_ids": {"$push": "$_id"}
                }
            },
            {"$match": {"count": {"$gt": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        result = await Artifact.aggregate(pipeline).to_list()
        
        duplicates = {}
        for group in result:
            content_hash = group["_id"]
            artifact_ids = group["artifact_ids"]
            
            # Fetch the actual documents
            artifacts = await Artifact.find(
                In(Artifact.id, artifact_ids)
            ).to_list()
            
            duplicates[content_hash] = artifacts
        
        return duplicates
    
    async def find_large_artifacts(
        self,
        min_size_mb: float = 10.0,
        limit: int = 50
    ) -> List[Artifact]:
        """
        Find artifacts larger than specified size.
        
        Args:
            min_size_mb: Minimum size in megabytes
            limit: Maximum number of results
            
        Returns:
            List of large Artifact
        """
        min_size_bytes = int(min_size_mb * 1024 * 1024)
        
        return await Artifact.find(
            Artifact.size_bytes >= min_size_bytes
        ).sort([("size_bytes", DESCENDING)]).limit(limit).to_list()
    
    async def find_by_storage_backend(self, backend: StorageBackend, limit: int = 100) -> List[Artifact]:
        """
        Find artifacts by storage backend.
        
        Args:
            backend: Storage backend to filter by
            limit: Maximum number of results
            
        Returns:
            List of Artifact
        """
        return await Artifact.find(
            Artifact.storage_backend == backend
        ).sort([("collected_at", DESCENDING)]).limit(limit).to_list()
    
    async def get_storage_statistics(self) -> Dict[str, Any]:
        """
        Get storage statistics.
        
        Returns:
            Dict with storage statistics
        """
        pipeline = [
            {
                "$group": {
                    "_id": None,
                    "total_artifacts": {"$sum": 1},
                    "total_size_bytes": {"$sum": "$size_bytes"},
                    "avg_size_bytes": {"$avg": "$size_bytes"},
                    "max_size_bytes": {"$max": "$size_bytes"},
                    "min_size_bytes": {"$min": "$size_bytes"},
                    "artifacts_with_thumbnails": {
                        "$sum": {"$cond": [{"$ne": ["$thumbnail_path", None]}, 1, 0]}
                    },
                    "artifacts_with_compression": {
                        "$sum": {"$cond": [{"$ne": ["$compressed_path", None]}, 1, 0]}
                    },
                    "artifacts_with_hash": {
                        "$sum": {"$cond": [{"$ne": ["$content_hash", None]}, 1, 0]}
                    }
                }
            }
        ]
        
        result = await Artifact.aggregate(pipeline).to_list()
        
        if result:
            stats = result[0]
            total_size_bytes = stats.get("total_size_bytes", 0)
            
            stats["total_size_mb"] = total_size_bytes / (1024 * 1024)
            stats["total_size_gb"] = total_size_bytes / (1024 * 1024 * 1024)
            
            return stats
        
        return {
            "total_artifacts": 0,
            "total_size_bytes": 0,
            "total_size_mb": 0,
            "total_size_gb": 0,
            "avg_size_bytes": 0,
            "max_size_bytes": 0,
            "min_size_bytes": 0,
            "artifacts_with_thumbnails": 0,
            "artifacts_with_compression": 0,
            "artifacts_with_hash": 0
        }
    
    async def get_type_statistics(self) -> List[Dict[str, Any]]:
        """
        Get statistics by artifact type.
        
        Returns:
            List of type statistics
        """
        pipeline = [
            {
                "$group": {
                    "_id": "$type",
                    "count": {"$sum": 1},
                    "total_size_bytes": {"$sum": "$size_bytes"},
                    "avg_size_bytes": {"$avg": "$size_bytes"},
                    "ready_count": {
                        "$sum": {"$cond": [{"$eq": ["$status", "ready"]}, 1, 0]}
                    },
                    "pending_count": {
                        "$sum": {"$cond": [{"$eq": ["$status", "pending"]}, 1, 0]}
                    },
                    "failed_count": {
                        "$sum": {"$cond": [{"$eq": ["$status", "failed"]}, 1, 0]}
                    }
                }
            },
            {"$sort": {"count": -1}}
        ]
        
        results = await Artifact.aggregate(pipeline).to_list()
        
        return [
            {
                "type": result["_id"],
                "count": result["count"],
                "total_size_bytes": result["total_size_bytes"],
                "total_size_mb": result["total_size_bytes"] / (1024 * 1024),
                "avg_size_bytes": result["avg_size_bytes"],
                "ready_count": result["ready_count"],
                "pending_count": result["pending_count"],
                "failed_count": result["failed_count"],
                "success_rate": round(
                    result["ready_count"] / result["count"] * 100, 2
                ) if result["count"] > 0 else 0
            }
            for result in results
        ]
    
    async def find_artifacts_in_range(
        self,
        start_date: datetime,
        end_date: datetime,
        artifact_type: Optional[ArtifactType] = None,
        status: Optional[ArtifactStatus] = None,
        limit: int = 100,
        skip: int = 0
    ) -> List[Artifact]:
        """
        Find artifacts within a date range with optional filters.
        
        Args:
            start_date: Start date
            end_date: End date
            artifact_type: Optional artifact type filter
            status: Optional status filter
            limit: Maximum number of results
            skip: Number of results to skip
            
        Returns:
            List of Artifact
        """
        query = And(
            Artifact.collected_at >= start_date,
            Artifact.collected_at <= end_date
        )
        
        if artifact_type:
            query = And(query, Artifact.type == artifact_type)
        if status:
            query = And(query, Artifact.status == status)
        
        return await Artifact.find(query)\
            .sort([("collected_at", DESCENDING)])\
            .limit(limit)\
            .skip(skip)\
            .to_list()
    
    async def find_recent_artifacts(
        self,
        hours: int = 24,
        artifact_type: Optional[ArtifactType] = None,
        limit: int = 50
    ) -> List[Artifact]:
        """
        Find recent artifacts within specified hours.
        
        Args:
            hours: Number of hours to look back
            artifact_type: Optional artifact type filter
            limit: Maximum number of results
            
        Returns:
            List of recent Artifact
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        query = Artifact.collected_at >= cutoff_time
        
        if artifact_type:
            query = And(query, Artifact.type == artifact_type)
        
        return await Artifact.find(query)\
            .sort([("collected_at", DESCENDING)])\
            .limit(limit)\
            .to_list()
    
    async def find_expired_artifacts(self, limit: int = 100) -> List[Artifact]:
        """
        Find artifacts that have expired and can be cleaned up.
        
        Args:
            limit: Maximum number of results
            
        Returns:
            List of expired Artifact
        """
        now = datetime.now()
        
        return await Artifact.find(
            And(
                Artifact.expires_at < now,
                Artifact.is_permanent == False,
                Artifact.status != ArtifactStatus.DELETED
            )
        ).sort([("expires_at", ASCENDING)]).limit(limit).to_list()
    
    async def find_old_artifacts(
        self,
        days_old: int = 90,
        exclude_permanent: bool = True,
        limit: int = 100
    ) -> List[Artifact]:
        """
        Find old artifacts that could be cleaned up.
        
        Args:
            days_old: Number of days old
            exclude_permanent: Whether to exclude permanent artifacts
            limit: Maximum number of results
            
        Returns:
            List of old Artifact
        """
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        query = And(
            Artifact.collected_at < cutoff_date,
            Artifact.status != ArtifactStatus.DELETED
        )
        
        if exclude_permanent:
            query = And(query, Artifact.is_permanent == False)
        
        return await Artifact.find(query)\
            .sort([("collected_at", ASCENDING)])\
            .limit(limit)\
            .to_list()
    
    async def cleanup_expired_artifacts(self, dry_run: bool = True) -> Dict[str, Any]:
        """
        Clean up expired artifacts and return statistics.
        
        Args:
            dry_run: If True, only count what would be deleted
            
        Returns:
            Dict with cleanup results
        """
        expired_artifacts = await self.find_expired_artifacts()
        
        cleanup_stats = {
            "expired_artifacts": len(expired_artifacts),
            "dry_run": dry_run,
            "by_type": {},
            "total_size_freed_bytes": 0
        }
        
        # Count by type and calculate size
        for artifact in expired_artifacts:
            artifact_type = artifact.type.value
            if artifact_type not in cleanup_stats["by_type"]:
                cleanup_stats["by_type"][artifact_type] = {
                    "count": 0,
                    "size_bytes": 0
                }
            
            cleanup_stats["by_type"][artifact_type]["count"] += 1
            cleanup_stats["by_type"][artifact_type]["size_bytes"] += artifact.size_bytes
            cleanup_stats["total_size_freed_bytes"] += artifact.size_bytes
        
        cleanup_stats["total_size_freed_mb"] = cleanup_stats["total_size_freed_bytes"] / (1024 * 1024)
        cleanup_stats["total_size_freed_gb"] = cleanup_stats["total_size_freed_bytes"] / (1024 * 1024 * 1024)
        
        # Actually mark as deleted if not dry run
        if not dry_run and expired_artifacts:
            deleted_count = 0
            for artifact in expired_artifacts:
                artifact.mark_deleted()
                await artifact.save()
                deleted_count += 1
            
            cleanup_stats["deleted_count"] = deleted_count
        
        return cleanup_stats
    
    async def mark_artifacts_for_cleanup(
        self,
        artifact_ids: List[str],
        expires_at: Optional[datetime] = None
    ) -> int:
        """
        Mark specific artifacts for cleanup.
        
        Args:
            artifact_ids: List of artifact IDs to mark
            expires_at: When artifacts expire (defaults to 7 days)
            
        Returns:
            Number of artifacts updated
        """
        if expires_at is None:
            expires_at = datetime.now() + timedelta(days=7)  # Default 7 days
        
        updated_count = 0
        for artifact_id in artifact_ids:
            artifact = await self.get_by_artifact_id(artifact_id)
            if artifact and not artifact.is_permanent:
                artifact.set_expiry(expires_at)
                await artifact.save()
                updated_count += 1
        
        return updated_count
    
    async def search_artifacts(
        self,
        search_term: str,
        artifact_type: Optional[ArtifactType] = None,
        execution_id: Optional[str] = None,
        limit: int = 50,
        skip: int = 0
    ) -> List[Artifact]:
        """
        Search artifacts by filename, tags, or metadata.
        
        Args:
            search_term: Term to search for
            artifact_type: Optional artifact type filter
            execution_id: Optional execution ID filter
            limit: Maximum number of results
            skip: Number of results to skip
            
        Returns:
            List of matching Artifact
        """
        # Build text search query
        search_query = Or(
            Artifact.original_name.regex(search_term, "i"),
            Artifact.step_name.regex(search_term, "i"),
            In(search_term, Artifact.tags)
        )
        
        if artifact_type:
            search_query = And(search_query, Artifact.type == artifact_type)
        if execution_id:
            search_query = And(search_query, Artifact.execution_id == execution_id)
        
        return await Artifact.find(search_query)\
            .sort([("collected_at", DESCENDING)])\
            .limit(limit)\
            .skip(skip)\
            .to_list()
    
    async def create_from_collector(self, collector_artifact: Dict[str, Any]) -> Artifact:
        """
        Create Artifact from ArtifactCollector data.
        
        Args:
            collector_artifact: Legacy artifact data from ArtifactCollector
            
        Returns:
            Artifact: Created document
        """
        doc = Artifact.from_artifact_collector(collector_artifact)
        return await self.create(doc)
    
    async def bulk_create_from_collector(self, collector_artifacts: List[Dict[str, Any]]) -> List[Artifact]:
        """
        Bulk create artifacts from ArtifactCollector data.
        
        Args:
            collector_artifacts: List of legacy artifact data
            
        Returns:
            List of created Artifact
        """
        artifacts = []
        for collector_artifact in collector_artifacts:
            try:
                artifact = await self.create_from_collector(collector_artifact)
                artifacts.append(artifact)
            except Exception as e:
                logging.info(f"Failed to create artifact {collector_artifact.get('artifact_id', 'unknown')}: {e}")
        
        return artifacts
    
    async def update_artifact_status(
        self,
        artifact_id: str,
        status: ArtifactStatus,
        error_message: Optional[str] = None
    ) -> Optional[Artifact]:
        """
        Update artifact status.
        
        Args:
            artifact_id: Artifact ID to update
            status: New status
            error_message: Error message if failed
            
        Returns:
            Updated Artifact or None if not found
        """
        artifact = await self.get_by_artifact_id(artifact_id)
        if artifact:
            if status == ArtifactStatus.READY:
                artifact.mark_ready()
            elif status == ArtifactStatus.FAILED and error_message:
                artifact.mark_failed(error_message)
            elif status == ArtifactStatus.PROCESSING:
                artifact.mark_processing()
            elif status == ArtifactStatus.ARCHIVED:
                artifact.mark_archived()
            elif status == ArtifactStatus.DELETED:
                artifact.mark_deleted()
            else:
                artifact.status = status
                artifact.update_timestamp()
            
            await artifact.save()
            return artifact
        return None
    
    async def get_deduplication_opportunities(self) -> Dict[str, Any]:
        """
        Analyze deduplication opportunities.
        
        Returns:
            Dict with deduplication analysis
        """
        duplicates = await self.find_duplicates()
        
        total_duplicates = 0
        potential_savings_bytes = 0
        
        for content_hash, artifacts in duplicates.items():
            if len(artifacts) > 1:
                # Keep the first one, count the rest as duplicates
                duplicate_count = len(artifacts) - 1
                total_duplicates += duplicate_count
                
                # Calculate potential savings (size of duplicates)
                for i in range(1, len(artifacts)):
                    potential_savings_bytes += artifacts[i].size_bytes
        
        return {
            "unique_hashes_with_duplicates": len(duplicates),
            "total_duplicate_artifacts": total_duplicates,
            "potential_savings_bytes": potential_savings_bytes,
            "potential_savings_mb": potential_savings_bytes / (1024 * 1024),
            "potential_savings_gb": potential_savings_bytes / (1024 * 1024 * 1024),
            "duplicate_groups": {
                hash_val: len(artifacts) for hash_val, artifacts in duplicates.items()
            }
        }

    async def create(self, model: Artifact) -> Artifact:
        """
        Create a new artifact with validation.
        
        Args:
            model: Artifact model to create
            
        Returns:
            Artifact: Created artifact
            
        Raises:
            ValueError: If artifact_id is missing or already exists
        """
        # Validaciones
        if not model.artifact_id:
            raise ValueError("Artifact must have an artifact_id")
        
        # Verificar si ya existe
        existing = await self.get_by_artifact_id(model.artifact_id)
        if existing:
            logger.warning(f"Artifact {model.artifact_id} already exists, skipping creation")
            return existing
        
        # Validar campos requeridos
        if not model.type:
            raise ValueError("Artifact must have a type")
        
        if not model.file_path:
            raise ValueError("Artifact must have a file_path")
        
        # Crear el artifact
        created = await super().create(model)
        logger.info(f"Created artifact {model.artifact_id} in database")
        return created


class ArtifactMigrationService:
    """
    Service for migrating legacy artifact data to MongoDB.
    """
    
    def __init__(self, artifact_repo: ArtifactRepository):
        self.artifact_repo = artifact_repo
    
    async def migrate_collector_artifacts(self, collector_artifacts: List[Dict[str, Any]]) -> List[Artifact]:
        """
        Migrate artifacts from ArtifactCollector to MongoDB.
        
        Args:
            collector_artifacts: List of ArtifactCollector artifacts
            
        Returns:
            List of migrated Artifact
        """
        return await self.artifact_repo.bulk_create_from_collector(collector_artifacts)
    
    async def validate_migration(self, expected_artifact_count: int) -> Dict[str, Any]:
        """
        Validate artifact migration results.
        
        Args:
            expected_artifact_count: Expected number of artifacts
            
        Returns:
            Dict with validation results
        """
        # Count migrated documents
        artifact_count = await self.artifact_repo.count({})
        
        return {
            "expected": expected_artifact_count,
            "migrated": artifact_count,
            "success": artifact_count >= expected_artifact_count,
            "migration_complete": artifact_count >= expected_artifact_count
        }