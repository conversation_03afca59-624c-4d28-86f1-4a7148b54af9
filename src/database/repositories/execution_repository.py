"""
Execution Repository for QAK MongoDB Implementation

Repository for execution-related database operations with specialized queries,
legacy data migration, and integration with StandardResult model.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from beanie import PydanticObjectId
from beanie.operators import And, Or, In
from pymongo import DESCENDING, ASCENDING

from .base import BaseRepository, PaginationResult
from ..models.execution import Execution
from src.models.standard_result import StandardResult, ExecutionStatus, TestType
from ..exceptions import DocumentNotFoundError, DocumentExistsError, DatabaseError
from src.database.repositories.project_repository import ProjectRepository

logger = logging.getLogger(__name__)


class ExecutionRepository(BaseRepository[Execution]):
    """
    Repository for execution document operations.
    
    Provides specialized queries for executions, analytics, and legacy data migration.
    """
    
    document_model = Execution
    
    def __init__(self):
        super().__init__("executions")
    
    def to_document(self, model: Execution) -> Dict[str, Any]:
        """Convert Execution model to MongoDB document."""
        return model.model_dump()
    
    def from_document(self, document: Dict[str, Any]) -> Execution:
        """Convert MongoDB document to Execution model."""
        return Execution(**document)
    
    def get_document_id(self, model: Execution) -> str:
        """Get the document ID from Execution model."""
        return str(model.id) if model.id else ""
    
    async def get_executions_by_environment(
        self, 
        project_id: str, 
        environment_id: str, 
        limit: int = 50, 
        offset: int = 0
    ) -> List[Execution]:
        """
        Get executions filtered by environment.
        
        Args:
            project_id: Project ID to filter by
            environment_id: Environment ID to filter by
            limit: Maximum number of executions to return
            offset: Number of executions to skip
            
        Returns:
            List[Execution]: List of executions for the environment
        """
        try:
            query = {
                "project_id": project_id,
                "environment_id": environment_id
            }
            
            executions = await Execution.find(query) \
                .sort([("started_at", DESCENDING)]) \
                .skip(offset) \
                .limit(limit) \
                .to_list()
            
            logger.info(f"Retrieved {len(executions)} executions for environment {environment_id}")
            return executions
            
        except Exception as e:
            logger.error(f"Failed to get executions by environment: {e}")
            return []
    
    async def create_from_standard_result(self, result: StandardResult) -> Execution:
        """
        Create and save an execution from StandardResult.
        
        Args:
            result: StandardResult instance to persist
            
        Returns:
            Execution: Saved document
        """
        doc = Execution.from_standard_result(result)
        return await self.create(doc)
    
    async def create_execution(self, result: StandardResult) -> Execution:
        """Alias for create_from_standard_result to match orchestrator calls."""
        return await self.create_from_standard_result(result)
    
    async def save_artifacts(self, artifacts: List['Artifact']):
        """
        Bulk insert artifact documents into the database.
        
        Args:
            artifacts: A list of Artifact model instances.
        """
        from src.database.models.artifact import Artifact as ArtifactModel
        
        if not artifacts:
            return
        
        # Beanie's insert_many expects model instances
        await ArtifactModel.insert_many(artifacts)

    async def add_execution_to_test_history(self, test_id: str, execution_id: str, project_id: Optional[str] = None) -> bool:
        """
        Append an execution ID to a test case's history.
        
        Args:
            test_id: The ID of the test case to update.
            execution_id: The execution ID to add to the history.
            
        Returns:
            bool: True if the update was successful, False otherwise.
        """
        project_repo = ProjectRepository()
        
        # 1️⃣ First try with provided project_id (fast-path)
        project = None
        if project_id:
            project = await project_repo.get_by_project_id(project_id)
            if not project:
                logger.warning(f"Project with id {project_id} not found – falling back to test_id search")
        
        # 2️⃣ Fallback – expensive nested search by test_id across all projects
        if not project:
            # This part is tricky as we can't do a deep query easily without Beanie.
            # We'll fetch all projects and filter in Python. This is inefficient but
            # will work for now. A better solution would be to refactor how test
            # cases are stored or add a dedicated method in the repository.
            all_projects = await project_repo.get_all()
            for p in all_projects:
                if p.get_test_case_by_id(test_id):
                    project = p
                    break

        if not project:
            logger.warning(f"No project found containing test case {test_id} (project_id={project_id})")
            return False
            
        try:
            # Find the specific test case within the project structure
            test_case_to_update = project.get_test_case_by_id(test_id)
            
            if test_case_to_update:
                # Initialize execution_history if it doesn't exist
                if not hasattr(test_case_to_update, 'execution_history') or test_case_to_update.execution_history is None:
                    test_case_to_update.execution_history = []
                
                # Add the execution ID to the history if not already present
                if execution_id not in test_case_to_update.execution_history:
                    test_case_to_update.execution_history.append(execution_id)
                    
                    # Save the updated project
                    await project_repo.update_project(project)
                    logger.info(f"Added execution {execution_id} to test case {test_id} history")
                    return True
                else:
                    logger.info(f"Execution {execution_id} already in test case {test_id} history")
                    return True
            
            logger.warning(f"Test case {test_id} not found in project structure")
            return False
            
        except Exception as e:
            logger.error(f"Failed to save test case history: {e}")
            return False
    
    async def update_from_standard_result(self, execution_id: str, result: StandardResult) -> Optional[Execution]:
        """
        Update an existing execution with StandardResult data.
        
        Args:
            execution_id: ID of execution to update
            result: Updated StandardResult data
            
        Returns:
            dict: Updated document or None if not found
        """
        doc = Execution.from_standard_result(result)
        doc.execution_id = execution_id  # Ensure ID consistency
        return await self.update_by_execution_id(execution_id, doc.model_dump(exclude_unset=True))
    
    async def update_by_execution_id(self, execution_id: str, update_data: Dict[str, Any]) -> Optional[Execution]:
        """
        Update an execution document by execution_id.
        
        Args:
            execution_id: The execution_id to search for
            update_data: Dictionary with fields to update
            
        Returns:
            Execution: Updated document or None if not found
        """
        try:
            collection = await self.collection
            
            # Add updated_at timestamp
            update_data["updated_at"] = datetime.utcnow()
            
            # Remove _id if present to avoid update conflicts
            update_data.pop("_id", None)
            
            result = await collection.update_one(
                {"execution_id": execution_id},
                {"$set": update_data}
            )
            
            if result.matched_count == 0:
                logger.warning(f"No execution found with execution_id: {execution_id}")
                return None
                
            if not result.acknowledged:
                raise DatabaseError(f"Failed to update execution in {self.collection_name}")
            
            # Get the updated document
            updated_document = await collection.find_one({"execution_id": execution_id})
            if not updated_document:
                raise DatabaseError(f"Failed to retrieve updated execution from {self.collection_name}")
                
            logger.info(f"✅ Updated execution by execution_id: {execution_id}")
            # Return the document directly to avoid Beanie initialization issues in Celery context
            return updated_document
            
        except Exception as e:
            logger.error(f"Failed to update execution by execution_id {execution_id}: {str(e)}")
            raise DatabaseError(f"Failed to update execution by execution_id: {str(e)}")
    
    async def get_by_execution_id(self, execution_id: str) -> Optional[Execution]:
        """
        Get execution by execution_id.
        
        Args:
            execution_id: Execution ID to search for
            
        Returns:
            Execution: Found document or None
        """
        return await Execution.find_one(
            Execution.execution_id == execution_id
        )
    
    async def get_by_project(
        self,
        project_id: str,
        status: Optional[ExecutionStatus] = None,
        test_type: Optional[TestType] = None,
        environment_id: Optional[str] = None,
        application_version: Optional[str] = None,
        limit: int = 50,
        skip: int = 0
    ) -> List[Execution]:
        """
        Get executions for a specific project.
        
        Args:
            project_id: Project ID to filter by
            status: Optional status filter
            test_type: Optional test type filter
            environment_id: Optional environment ID filter
            application_version: Optional application version filter
            limit: Maximum number of results
            skip: Number of results to skip
            
        Returns:
            List of Execution
        """
        query_conditions = [Execution.project_id == project_id]
        
        if status:
            query_conditions.append(Execution.status == status)
        
        if test_type:
            query_conditions.append(Execution.test_type == test_type)
        
        if environment_id:
            query_conditions.append(Execution.environment_id == environment_id)

        if application_version:
            query_conditions.append(Execution.application_version == application_version)

        query = And(*query_conditions)
        
        return await Execution.find(query)\
            .sort([("started_at", DESCENDING)])\
            .limit(limit)\
            .skip(skip)\
            .to_list()
    
    async def get_by_session(self, session_id: str) -> List[Execution]:
        """
        Get all executions for a codegen session.
        
        Args:
            session_id: Session ID to filter by
            
        Returns:
            List of Execution
        """
        return await Execution.find(
            Execution.session_id == session_id
        ).sort([("started_at", ASCENDING)]).to_list()
    
    async def get_by_test_suite(self, suite_id: str) -> List[Execution]:
        """
        Get all executions for a test suite.
        
        Args:
            suite_id: Suite ID to filter by
            
        Returns:
            List of Execution
        """
        return await Execution.find(
            Execution.suite_id == suite_id
        ).sort([("started_at", DESCENDING)]).to_list()
    
    async def get_recent(
        self,
        hours: int = 24,
        status: Optional[ExecutionStatus] = None,
        test_type: Optional[TestType] = None,
        limit: int = 100
    ) -> List[Execution]:
        """
        Get recent executions within specified time window.
        
        Args:
            hours: Number of hours to look back
            status: Optional status filter
            test_type: Optional test type filter
            limit: Maximum number of results
            
        Returns:
            List of Execution
        """
        since = datetime.now() - timedelta(hours=hours)
        query = Execution.started_at >= since
        
        if status:
            query = And(query, Execution.status == status)
        
        if test_type:
            query = And(query, Execution.test_type == test_type)
        
        return await Execution.find(query)\
            .sort([("started_at", DESCENDING)])\
            .limit(limit)\
            .to_list()
    
    async def get_failed_executions(
        self,
        project_id: Optional[str] = None,
        since: Optional[datetime] = None,
        limit: int = 50
    ) -> List[Execution]:
        """
        Get failed executions for analysis.
        
        Args:
            project_id: Optional project filter
            since: Optional time filter
            limit: Maximum number of results
            
        Returns:
            List of failed Execution
        """
        query = Execution.status == ExecutionStatus.FAILURE
        
        if project_id:
            query = And(query, Execution.project_id == project_id)
        
        if since:
            query = And(query, Execution.started_at >= since)
        
        return await Execution.find(query)\
            .sort([("started_at", DESCENDING)])\
            .limit(limit)\
            .to_list()
    
    async def get_execution_statistics(
        self,
        project_id: Optional[str] = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get execution statistics and analytics.
        
        Args:
            project_id: Optional project filter
            days: Number of days to analyze
            
        Returns:
            Dict with execution statistics
        """
        since = datetime.now() - timedelta(days=days)
        
        # Build base query
        match_query = {"started_at": {"$gte": since}}
        if project_id:
            match_query["project_id"] = project_id
        
        # Aggregation pipeline
        pipeline = [
            {"$match": match_query},
            {
                "$group": {
                    "_id": None,
                    "total_executions": {"$sum": 1},
                    "successful_executions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "success"]}, 1, 0]}
                    },
                    "failed_executions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "failure"]}, 1, 0]}
                    },
                    "avg_duration_ms": {"$avg": "$duration_ms"},
                    "max_duration_ms": {"$max": "$duration_ms"},
                    "min_duration_ms": {"$min": "$duration_ms"}
                }
            }
        ]
        
        result = await Execution.aggregate(pipeline).to_list()
        
        if result:
            stats = result[0]
            total = stats.get("total_executions", 0)
            successful = stats.get("successful_executions", 0)
            
            return {
                "total_executions": total,
                "successful_executions": successful,
                "failed_executions": stats.get("failed_executions", 0),
                "success_rate": round(successful / total * 100, 2) if total > 0 else 0,
                "avg_duration_ms": stats.get("avg_duration_ms"),
                "max_duration_ms": stats.get("max_duration_ms"),
                "min_duration_ms": stats.get("min_duration_ms"),
                "analysis_period_days": days
            }
        
        return {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "success_rate": 0,
            "avg_duration_ms": None,
            "max_duration_ms": None,
            "min_duration_ms": None,
            "analysis_period_days": days
        }
    
    async def get_execution_trends(
        self,
        project_id: Optional[str] = None,
        days: int = 7
    ) -> List[Dict[str, Any]]:
        """
        Get daily execution trends.
        
        Args:
            project_id: Optional project filter
            days: Number of days to analyze
            
        Returns:
            List of daily trend data
        """
        since = datetime.now() - timedelta(days=days)
        
        match_query = {"started_at": {"$gte": since}}
        if project_id:
            match_query["project_id"] = project_id
        
        pipeline = [
            {"$match": match_query},
            {
                "$group": {
                    "_id": {
                        "year": {"$year": "$started_at"},
                        "month": {"$month": "$started_at"},
                        "day": {"$dayOfMonth": "$started_at"}
                    },
                    "total": {"$sum": 1},
                    "successful": {
                        "$sum": {"$cond": [{"$eq": ["$status", "success"]}, 1, 0]}
                    },
                    "failed": {
                        "$sum": {"$cond": [{"$eq": ["$status", "failure"]}, 1, 0]}
                    },
                    "avg_duration": {"$avg": "$duration_ms"}
                }
            },
            {"$sort": {"_id.year": 1, "_id.month": 1, "_id.day": 1}}
        ]
        
        results = await Execution.aggregate(pipeline).to_list()
        
        return [
            {
                "date": f"{result['_id']['year']}-{result['_id']['month']:02d}-{result['_id']['day']:02d}",
                "total_executions": result["total"],
                "successful_executions": result["successful"],
                "failed_executions": result["failed"],
                "success_rate": round(result["successful"] / result["total"] * 100, 2) if result["total"] > 0 else 0,
                "avg_duration_ms": result["avg_duration"]
            }
            for result in results
        ]
    
    async def search_executions(
        self,
        query: str,
        project_id: Optional[str] = None,
        test_type: Optional[TestType] = None,
        status: Optional[ExecutionStatus] = None,
        limit: int = 50
    ) -> List[Execution]:
        """
        Search executions by text query.
        
        Args:
            query: Text to search for
            project_id: Optional project filter
            test_type: Optional test type filter
            status: Optional status filter
            limit: Maximum number of results
            
        Returns:
            List of matching Execution
        """
        # Build search conditions
        conditions = []
        
        if project_id:
            conditions.append(Execution.project_id == project_id)
        
        if test_type:
            conditions.append(Execution.test_type == test_type)
        
        if status:
            conditions.append(Execution.status == status)
        
        # Text search conditions
        text_conditions = Or(
            Execution.execution_id.regex(query, "i"),
            Execution.test_id.regex(query, "i") if query else None,
            Execution.message.regex(query, "i") if query else None,
            Execution.error.regex(query, "i") if query else None
        )
        
        conditions.append(text_conditions)
        
        # Combine all conditions
        final_query = And(*conditions) if len(conditions) > 1 else conditions[0]
        
        return await Execution.find(final_query)\
            .sort([("started_at", DESCENDING)])\
            .limit(limit)\
            .to_list()
    
    async def get_long_running_executions(
        self,
        threshold_ms: int = 300000,  # 5 minutes
        status: ExecutionStatus = ExecutionStatus.RUNNING
    ) -> List[Execution]:
        """
        Get executions that have been running for longer than threshold.
        
        Args:
            threshold_ms: Threshold in milliseconds
            status: Status to filter by (default: RUNNING)
            
        Returns:
            List of long-running Execution
        """
        threshold_time = datetime.now() - timedelta(milliseconds=threshold_ms)
        
        return await Execution.find(
            And(
                Execution.status == status,
                Execution.started_at <= threshold_time
            )
        ).sort([("started_at", ASCENDING)]).to_list()
    
    async def cleanup_old_executions(
        self,
        days: int = 90,
        dry_run: bool = True
    ) -> Dict[str, Any]:
        """
        Clean up old execution records.
        
        Args:
            days: Number of days to keep
            dry_run: If True, only count what would be deleted
            
        Returns:
            Dict with cleanup results
        """
        cutoff_date = datetime.now() - timedelta(days=days)
        
        query = Execution.started_at < cutoff_date
        
        if dry_run:
            count = await Execution.find(query).count()
            return {
                "dry_run": True,
                "records_to_delete": count,
                "cutoff_date": cutoff_date.isoformat()
            }
        
        # Get IDs to delete
        docs_to_delete = await Execution.find(query).to_list()
        delete_count = len(docs_to_delete)
        
        # Perform deletion
        if delete_count > 0:
            await Execution.find(query).delete()
        
        return {
            "dry_run": False,
            "records_deleted": delete_count,
            "cutoff_date": cutoff_date.isoformat()
        }
    
    async def delete_by_execution_id(self, execution_id: str) -> bool:
        """
        Delete an execution document by its execution_id.
        
        Args:
            execution_id: The ID of the execution to delete.
            
        Returns:
            bool: True if deletion was successful, False otherwise.
        """
        execution = await self.get_by_execution_id(execution_id)
        if execution:
            await execution.delete()
            logger.info(f"Deleted execution with execution_id: {execution_id}")
            return True
        logger.warning(f"Execution with execution_id: {execution_id} not found for deletion.")
        return False
    
    async def get_executions_by_test_id(self, test_id: str) -> List[Execution]:
        """
        Get all executions for a specific test case.
        
        Args:
            test_id: Test case ID
            
        Returns:
            List[Execution]: List of execution documents for this test case
        """
        try:
            # Buscar ejecuciones que tengan test_id en metadata o directamente en el campo test_id
            executions = await Execution.find(
                Or(
                    And(Execution.test_id == test_id, Execution.test_type == "case"),
                    {"metadata.test_id": test_id}
                )
            ).sort([("completed_at", DESCENDING)]).to_list()
            
            return executions
        except Exception as e:
            logger.error(f"Failed to get executions for test {test_id}: {e}")
            return []


# CodegenSessionRepository is now defined in codegen_repository.py to avoid duplication


class ExecutionMigrationService:
    """
    Service for migrating legacy execution data to MongoDB.
    """
    
    def __init__(self, execution_repo: ExecutionRepository):
        self.execution_repo = execution_repo
    
    async def migrate_legacy_execution(self, json_data: Dict[str, Any]) -> Execution:
        """
        Migrate a single legacy execution JSON to MongoDB.
        
        Args:
            json_data: Legacy execution JSON data
            
        Returns:
            Execution: Saved document
        """
        # Convert legacy JSON to Execution
        doc = Execution.from_legacy_json(json_data)
        
        # Save to database
        return await self.execution_repo.create(doc)
    
    # Session migration is now handled by CodegenMigrationService in codegen_repository.py
    
    async def migrate_executions_index(self, index_data: Dict[str, Any]) -> List[Execution]:
        """
        Migrate executions from legacy index file.
        
        Args:
            index_data: Executions index JSON data
            
        Returns:
            List of migrated Execution
        """
        migrated = []
        
        for execution_id, execution_data in index_data.items():
            try:
                doc = await self.migrate_legacy_execution(execution_data)
                migrated.append(doc)
            except Exception as e:
                logging.info(f"Failed to migrate execution {execution_id}: {e}")
        
        return migrated
    
    async def validate_migration(self, legacy_execution_count: int) -> Dict[str, Any]:
        """
        Validate execution migration results.
        
        Args:
            legacy_execution_count: Expected number of executions
            
        Returns:
            Dict with validation results
        """
        # Count migrated documents
        execution_count = await self.execution_repo.count({})
        
        return {
            "expected": legacy_execution_count,
            "migrated": execution_count,
            "success": execution_count >= legacy_execution_count,
            "migration_complete": execution_count >= legacy_execution_count
        }
