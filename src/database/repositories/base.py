"""
Base Repository for QAK MongoDB Integration

Abstract base class for all repositories with standard CRUD operations.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, TypeVar, Generic, Tuple
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorDatabase, AsyncIOMotorCollection
from bson import ObjectId
from pymongo.errors import PyMongoError
from pymongo import ASCENDING, DESCENDING

from ..exceptions import (
    DatabaseError, DocumentNotFoundError, DocumentExistsError, 
    QueryError, handle_pymongo_error
)
from ..connection import get_database

logger = logging.getLogger(__name__)

# Type variable for repository models
T = TypeVar('T')


class PaginationResult(Generic[T]):
    """Result container for paginated queries."""
    
    def __init__(
        self, 
        items: List[T], 
        total: int, 
        page: int, 
        page_size: int,
        has_next: bool = False,
        has_previous: bool = False
    ):
        self.items = items
        self.total = total
        self.page = page
        self.page_size = page_size
        self.has_next = has_next
        self.has_previous = has_previous
        
    @property
    def total_pages(self) -> int:
        """Calculate total pages."""
        return (self.total + self.page_size - 1) // self.page_size
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "items": self.items,
            "pagination": {
                "total": self.total,
                "page": self.page,
                "page_size": self.page_size,
                "total_pages": self.total_pages,
                "has_next": self.has_next,
                "has_previous": self.has_previous,
            }
        }


class BaseRepository(ABC, Generic[T]):
    """Abstract base repository with standard CRUD operations."""
    
    def __init__(self, collection_name: str):
        """Initialize repository with collection name."""
        self.collection_name = collection_name
        self._database: Optional[AsyncIOMotorDatabase] = None
        self._collection: Optional[AsyncIOMotorCollection] = None
        
    @property
    async def collection(self) -> AsyncIOMotorCollection:
        """Get the MongoDB collection, ensuring database connection."""
        if self._collection is None:
            self._database = await get_database()
            self._collection = self._database[self.collection_name]
            
        return self._collection
    
    @abstractmethod
    def to_document(self, model: T) -> Dict[str, Any]:
        """Convert model to MongoDB document."""
        pass
    
    @abstractmethod
    def from_document(self, document: Dict[str, Any]) -> T:
        """Convert MongoDB document to model."""
        pass
    
    @abstractmethod
    def get_document_id(self, model: T) -> str:
        """Get the document ID from model."""
        pass
    
    # Standard CRUD Operations
    
    async def create(self, model: T) -> T:
        """Create a new document."""
        try:
            collection = await self.collection
            document = self.to_document(model)
            
            # Add timestamps
            document["created_at"] = datetime.utcnow()
            document["updated_at"] = datetime.utcnow()
            
            result = await collection.insert_one(document)
            
            if not result.acknowledged:
                raise DatabaseError(f"Failed to create document in {self.collection_name}")
            
            # Get the inserted document
            created_document = await collection.find_one({"_id": result.inserted_id})
            if not created_document:
                raise DatabaseError(f"Failed to retrieve created document from {self.collection_name}")
                
            logger.info(f"✅ Created document in {self.collection_name}: {result.inserted_id}")
            return self.from_document(created_document)
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"create in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to create document in {self.collection_name}: {str(e)}")
    
    async def get_by_id(self, document_id: str) -> Optional[T]:
        """Get document by ID."""
        try:
            collection = await self.collection
            document = await collection.find_one({"_id": ObjectId(document_id)})
            
            if document:
                return self.from_document(document)
            return None
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"get_by_id in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to get document from {self.collection_name}: {str(e)}")
    
    async def get_by_field(self, field: str, value: Any) -> Optional[T]:
        """Get document by specific field."""
        try:
            collection = await self.collection
            document = await collection.find_one({field: value})
            
            if document:
                return self.from_document(document)
            return None
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"get_by_field in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to get document by {field} from {self.collection_name}: {str(e)}")
    
    async def update(self, model: T) -> T:
        """Update an existing document."""
        try:
            collection = await self.collection
            document_id = self.get_document_id(model)
            
            if not document_id:
                raise DatabaseError("Cannot update document without ID")
            
            document = self.to_document(model)
            document["updated_at"] = datetime.utcnow()
            
            # Remove _id if present to avoid update conflicts
            document.pop("_id", None)
            
            result = await collection.update_one(
                {"_id": ObjectId(document_id)},
                {"$set": document}
            )
            
            if result.matched_count == 0:
                raise DocumentNotFoundError(self.collection_name, document_id)
                
            if not result.acknowledged:
                raise DatabaseError(f"Failed to update document in {self.collection_name}")
            
            # Get the updated document
            updated_document = await collection.find_one({"_id": ObjectId(document_id)})
            if not updated_document:
                raise DatabaseError(f"Failed to retrieve updated document from {self.collection_name}")
                
            logger.info(f"✅ Updated document in {self.collection_name}: {document_id}")
            return self.from_document(updated_document)
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"update in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to update document in {self.collection_name}: {str(e)}")
    
    async def delete_by_id(self, document_id: str) -> bool:
        """Delete document by ID."""
        try:
            collection = await self.collection
            result = await collection.delete_one({"_id": ObjectId(document_id)})
            
            if result.deleted_count > 0:
                logger.info(f"🗑️ Deleted document from {self.collection_name}: {document_id}")
                return True
            else:
                logger.warning(f"⚠️ Document not found for deletion in {self.collection_name}: {document_id}")
                return False
                
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"delete_by_id in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to delete document from {self.collection_name}: {str(e)}")
    
    async def find(
        self, 
        query: Dict[str, Any] = None, 
        sort: List[Tuple[str, int]] = None,
        limit: Optional[int] = None,
        skip: Optional[int] = None
    ) -> List[T]:
        """Find documents matching query."""
        try:
            collection = await self.collection
            cursor = collection.find(query or {})
            
            if sort:
                cursor = cursor.sort(sort)
            if skip:
                cursor = cursor.skip(skip)
            if limit:
                cursor = cursor.limit(limit)
            
            documents = await cursor.to_list(length=limit)
            return [self.from_document(doc) for doc in documents]
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"find in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to find documents in {self.collection_name}: {str(e)}")
    
    async def find_paginated(
        self,
        query: Dict[str, Any] = None,
        page: int = 1,
        page_size: int = 20,
        sort: List[Tuple[str, int]] = None
    ) -> PaginationResult[T]:
        """Find documents with pagination."""
        try:
            collection = await self.collection
            query = query or {}
            
            # Get total count
            total = await collection.count_documents(query)
            
            # Calculate pagination
            skip = (page - 1) * page_size
            has_previous = page > 1
            has_next = skip + page_size < total
            
            # Get documents
            cursor = collection.find(query)
            
            if sort:
                cursor = cursor.sort(sort)
                
            cursor = cursor.skip(skip).limit(page_size)
            documents = await cursor.to_list(length=page_size)
            
            items = [self.from_document(doc) for doc in documents]
            
            return PaginationResult(
                items=items,
                total=total,
                page=page,
                page_size=page_size,
                has_next=has_next,
                has_previous=has_previous
            )
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"find_paginated in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to find paginated documents in {self.collection_name}: {str(e)}")
    
    async def count(self, query: Dict[str, Any] = None) -> int:
        """Count documents matching query."""
        try:
            collection = await self.collection
            return await collection.count_documents(query or {})
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"count in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to count documents in {self.collection_name}: {str(e)}")
    
    async def exists(self, query: Dict[str, Any]) -> bool:
        """Check if document exists matching query."""
        try:
            collection = await self.collection
            document = await collection.find_one(query, {"_id": 1})
            return document is not None
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"exists in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to check document existence in {self.collection_name}: {str(e)}")
    
    # Utility methods
    
    async def bulk_create(self, models: List[T]) -> List[T]:
        """Create multiple documents in bulk."""
        try:
            if not models:
                return []
                
            collection = await self.collection
            documents = []
            
            for model in models:
                document = self.to_document(model)
                document["created_at"] = datetime.utcnow()
                document["updated_at"] = datetime.utcnow()
                documents.append(document)
            
            result = await collection.insert_many(documents)
            
            if not result.acknowledged:
                raise DatabaseError(f"Failed to bulk create documents in {self.collection_name}")
            
            # Get inserted documents
            inserted_documents = await collection.find(
                {"_id": {"$in": result.inserted_ids}}
            ).to_list(length=len(result.inserted_ids))
            
            logger.info(f"✅ Bulk created {len(inserted_documents)} documents in {self.collection_name}")
            return [self.from_document(doc) for doc in inserted_documents]
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"bulk_create in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to bulk create documents in {self.collection_name}: {str(e)}")
    
    async def delete_many(self, query: Dict[str, Any]) -> int:
        """Delete multiple documents matching query."""
        try:
            collection = await self.collection
            result = await collection.delete_many(query)
            
            logger.info(f"🗑️ Deleted {result.deleted_count} documents from {self.collection_name}")
            return result.deleted_count
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"delete_many in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to delete multiple documents from {self.collection_name}: {str(e)}")
    
    # Aggregation helpers
    
    async def aggregate(self, pipeline: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Run aggregation pipeline."""
        try:
            collection = await self.collection
            cursor = collection.aggregate(pipeline)
            return await cursor.to_list(length=None)
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"aggregate in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to run aggregation in {self.collection_name}: {str(e)}")
    
    # Index management
    
    async def create_index(self, keys: List[Tuple[str, int]], **kwargs) -> str:
        """Create index on collection."""
        try:
            collection = await self.collection
            return await collection.create_index(keys, **kwargs)
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"create_index in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to create index in {self.collection_name}: {str(e)}")
    
    async def list_indexes(self) -> List[Dict[str, Any]]:
        """List all indexes on collection."""
        try:
            collection = await self.collection
            return await collection.list_indexes().to_list(length=None)
            
        except PyMongoError as e:
            raise handle_pymongo_error(e, f"list_indexes in {self.collection_name}")
        except Exception as e:
            raise DatabaseError(f"Failed to list indexes in {self.collection_name}: {str(e)}")
    
    # ------------------------------------------------------------------
    # Utilidades adicionales
    # ------------------------------------------------------------------

    async def get_all(self, sort: List[Tuple[str, int]] | None = None) -> List[T]:
        """Devuelve todos los documentos de la colección.

        Args:
            sort: Lista opcional de tuplas (campo, ASC/DESC) para ordenar la salida.

        Returns:
            List[T]: Lista de modelos convertidos.
        """
        return await self.find(query={}, sort=sort) 