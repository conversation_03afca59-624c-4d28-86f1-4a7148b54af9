"""
Semantic Pattern Repository for QAK MongoDB Implementation
"""

from typing import List, Optional
from ..models.semantic_pattern import SemanticPattern

class SemanticPatternRepository:
    """
    Repository for semantic pattern document operations using Beanie ODM.
    """

    async def get_by_pattern_id(self, pattern_id: str) -> Optional[SemanticPattern]:
        """Get a pattern by its ID."""
        return await SemanticPattern.find_one({"pattern_id": pattern_id})

    async def get_all_patterns(self, limit: int = 1000) -> List[SemanticPattern]:
        """Get all patterns, sorted by confidence and usage."""
        return await SemanticPattern.find().sort(
            [("confidence_score", -1), ("usage_count", -1)]
        ).limit(limit).to_list()

    async def upsert_pattern(self, pattern: SemanticPattern) -> SemanticPattern:
        """Update a pattern if it exists, otherwise create it."""
        existing = await self.get_by_pattern_id(pattern.pattern_id)
        if existing:
            # Update existing pattern
            existing.pattern_type = pattern.pattern_type
            existing.description = pattern.description
            existing.context = pattern.context
            existing.success_indicators = pattern.success_indicators
            existing.failure_indicators = pattern.failure_indicators
            existing.confidence_score = pattern.confidence_score
            existing.usage_count = pattern.usage_count
            existing.last_used = pattern.last_used
            existing.merged_from = pattern.merged_from
            await existing.save()
            return existing
        else:
            # Create new pattern
            await pattern.save()
            return pattern

    async def count(self, query: dict = None) -> int:
        """Count patterns matching query."""
        if query:
            return await SemanticPattern.find(query).count()
        return await SemanticPattern.find().count() 