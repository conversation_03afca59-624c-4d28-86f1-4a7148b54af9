"""
Semantic Interaction Repository for QAK MongoDB Implementation
"""

from typing import List, Optional
from datetime import datetime
from ..models.semantic_interaction import SemanticInteraction

class SemanticInteractionRepository:
    """
    Repository for semantic interaction document operations using Beanie <PERSON>.
    """

    async def add_interaction(self, interaction: SemanticInteraction) -> SemanticInteraction:
        """Add a new interaction."""
        await interaction.save()
        return interaction

    async def get_recent_interactions(self, limit: int = 100) -> List[SemanticInteraction]:
        """Get the most recent interactions."""
        return await SemanticInteraction.find().sort(
            [("timestamp", -1)]
        ).limit(limit).to_list()

    async def get_interactions_by_agent(self, agent_id: str, limit: int = 100) -> List[SemanticInteraction]:
        """Get recent interactions for a specific agent."""
        return await SemanticInteraction.find(
            {"agent_id": agent_id}
        ).sort([("timestamp", -1)]).limit(limit).to_list()

    async def get_interactions_after(self, cutoff_date: datetime, limit: int = 1000) -> List[SemanticInteraction]:
        """Get interactions after a certain date."""
        return await SemanticInteraction.find(
            {"timestamp": {"$gte": cutoff_date}}
        ).sort([("timestamp", -1)]).limit(limit).to_list()

    async def delete_interactions_before(self, cutoff_date: datetime) -> int:
        """Delete interactions older than a certain date."""
        result = await SemanticInteraction.find(
            {"timestamp": {"$lt": cutoff_date}}
        ).delete()
        return result.deleted_count

    async def count(self, query: dict = None) -> int:
        """Count interactions matching query."""
        if query:
            return await SemanticInteraction.find(query).count()
        return await SemanticInteraction.find().count() 