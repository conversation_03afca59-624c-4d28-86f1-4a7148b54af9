"""
Database Exceptions for QAK MongoDB Integration

Custom exceptions for database operations with specific error handling.
"""

from typing import Optional, Any, Dict
from pymongo.errors import PyMongoError


class DatabaseError(Exception):
    """Base exception for database operations."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}


class ConnectionError(DatabaseError):
    """Exception raised for database connection issues."""
    pass


class DocumentNotFoundError(DatabaseError):
    """Exception raised when a document is not found."""
    
    def __init__(self, collection: str, document_id: str, details: Optional[Dict[str, Any]] = None):
        message = f"Document not found in {collection}: {document_id}"
        super().__init__(message, details)
        self.collection = collection
        self.document_id = document_id


class DocumentExistsError(DatabaseError):
    """Exception raised when trying to create a document that already exists."""
    
    def __init__(self, collection: str, document_id: str, details: Optional[Dict[str, Any]] = None):
        message = f"Document already exists in {collection}: {document_id}"
        super().__init__(message, details)
        self.collection = collection
        self.document_id = document_id


class ValidationError(DatabaseError):
    """Exception raised for data validation errors."""
    
    def __init__(self, field: str, value: Any, reason: str, details: Optional[Dict[str, Any]] = None):
        message = f"Validation failed for field '{field}' with value '{value}': {reason}"
        super().__init__(message, details)
        self.field = field
        self.value = value
        self.reason = reason


class QueryError(DatabaseError):
    """Exception raised for database query errors."""
    
    def __init__(self, query: Dict[str, Any], error: str, details: Optional[Dict[str, Any]] = None):
        message = f"Query failed: {error}"
        super().__init__(message, details)
        self.query = query
        self.error = error


class TransactionError(DatabaseError):
    """Exception raised for database transaction errors."""
    pass


class MigrationError(DatabaseError):
    """Exception raised during data migration operations."""
    
    def __init__(self, migration_name: str, error: str, details: Optional[Dict[str, Any]] = None):
        message = f"Migration '{migration_name}' failed: {error}"
        super().__init__(message, details)
        self.migration_name = migration_name
        self.error = error


class IndexError(DatabaseError):
    """Exception raised for database index operations."""
    
    def __init__(self, collection: str, index_name: str, error: str, details: Optional[Dict[str, Any]] = None):
        message = f"Index operation failed on {collection}.{index_name}: {error}"
        super().__init__(message, details)
        self.collection = collection
        self.index_name = index_name
        self.error = error


def handle_pymongo_error(error: PyMongoError, operation: str = "database operation") -> DatabaseError:
    """Convert PyMongo errors to custom database exceptions."""
    error_msg = str(error)
    
    if "ServerSelectionTimeoutError" in error_msg or "ConnectionFailure" in error_msg:
        return ConnectionError(f"Database connection failed during {operation}: {error_msg}")
    elif "DuplicateKeyError" in error_msg:
        return DocumentExistsError("unknown", "unknown", {"original_error": error_msg})
    elif "ValidationError" in error_msg:
        return ValidationError("unknown", "unknown", error_msg, {"original_error": error_msg})
    else:
        return DatabaseError(f"Database error during {operation}: {error_msg}", {"original_error": error_msg}) 