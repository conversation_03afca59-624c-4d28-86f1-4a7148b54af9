"""
Database Migration System for QAK MongoDB Integration

Basic migration system foundation for data migration and schema changes.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from abc import ABC, abstractmethod
from motor.motor_asyncio import AsyncIOMotorDatabase
from .connection import get_database
from .exceptions import MigrationError, DatabaseError

logger = logging.getLogger(__name__)


class Migration(ABC):
    """Abstract base class for database migrations."""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.executed_at: Optional[datetime] = None
        
    @abstractmethod
    async def up(self, database: AsyncIOMotorDatabase) -> bool:
        """Execute the migration."""
        pass
    
    @abstractmethod
    async def down(self, database: AsyncIOMotorDatabase) -> bool:
        """Rollback the migration."""
        pass
    
    def __str__(self) -> str:
        return f"Migration({self.name}): {self.description}"


class MigrationRunner:
    """Manages and executes database migrations."""
    
    def __init__(self):
        self.migrations: List[Migration] = []
        self._database: Optional[AsyncIOMotorDatabase] = None
        
    async def database(self) -> AsyncIOMotorDatabase:
        """Get database connection."""
        if self._database is None:
            self._database = await get_database()
        return self._database
    
    def add_migration(self, migration: Migration):
        """Add a migration to the runner."""
        self.migrations.append(migration)
        logger.info(f"➕ Added migration: {migration.name}")
    
    async def ensure_migration_collection(self):
        """Ensure the migrations collection exists."""
        try:
            db = await self.database()
            collection = db["migrations"]
            
            # Create index on migration name
            await collection.create_index("name", unique=True)
            logger.info("📊 Migration collection initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize migration collection: {e}")
            raise MigrationError("migration_collection_init", str(e))
    
    async def get_executed_migrations(self) -> List[str]:
        """Get list of executed migration names."""
        try:
            db = await self.database()
            collection = db["migrations"]
            
            cursor = collection.find({}, {"name": 1})
            documents = await cursor.to_list(length=None)
            
            return [doc["name"] for doc in documents]
            
        except Exception as e:
            logger.error(f"❌ Failed to get executed migrations: {e}")
            raise MigrationError("get_executed_migrations", str(e))
    
    async def mark_migration_executed(self, migration: Migration):
        """Mark a migration as executed."""
        try:
            db = await self.database()
            collection = db["migrations"]
            
            document = {
                "name": migration.name,
                "description": migration.description,
                "executed_at": datetime.utcnow(),
            }
            
            await collection.insert_one(document)
            logger.info(f"✅ Marked migration as executed: {migration.name}")
            
        except Exception as e:
            logger.error(f"❌ Failed to mark migration as executed: {e}")
            raise MigrationError(migration.name, str(e))
    
    async def unmark_migration_executed(self, migration_name: str):
        """Unmark a migration as executed (for rollback)."""
        try:
            db = await self.database()
            collection = db["migrations"]
            
            result = await collection.delete_one({"name": migration_name})
            if result.deleted_count > 0:
                logger.info(f"🔄 Unmarked migration as executed: {migration_name}")
            else:
                logger.warning(f"⚠️ Migration not found in executed list: {migration_name}")
                
        except Exception as e:
            logger.error(f"❌ Failed to unmark migration: {e}")
            raise MigrationError(migration_name, str(e))
    
    async def run_migrations(self, dry_run: bool = False) -> Dict[str, Any]:
        """Run all pending migrations."""
        try:
            await self.ensure_migration_collection()
            executed_migrations = await self.get_executed_migrations()
            
            pending_migrations = [
                m for m in self.migrations 
                if m.name not in executed_migrations
            ]
            
            if not pending_migrations:
                logger.info("✅ No pending migrations to run")
                return {
                    "status": "success",
                    "executed": [],
                    "pending": 0,
                    "dry_run": dry_run
                }
            
            logger.info(f"🚀 Running {len(pending_migrations)} pending migrations (dry_run={dry_run})")
            
            executed = []
            for migration in pending_migrations:
                logger.info(f"⚡ Executing migration: {migration.name}")
                
                if not dry_run:
                    try:
                        db = await self.database()
                        success = await migration.up(db)
                        
                        if success:
                            await self.mark_migration_executed(migration)
                            migration.executed_at = datetime.utcnow()
                            executed.append(migration.name)
                            logger.info(f"✅ Migration completed: {migration.name}")
                        else:
                            raise MigrationError(migration.name, "Migration returned False")
                            
                    except Exception as e:
                        logger.error(f"❌ Migration failed: {migration.name} - {e}")
                        raise MigrationError(migration.name, str(e))
                else:
                    logger.info(f"🔍 [DRY RUN] Would execute migration: {migration.name}")
                    executed.append(f"{migration.name} (dry-run)")
            
            result = {
                "status": "success",
                "executed": executed,
                "pending": len(pending_migrations),
                "dry_run": dry_run
            }
            
            if dry_run:
                logger.info(f"🔍 [DRY RUN] Would execute {len(pending_migrations)} migrations")
            else:
                logger.info(f"✅ Successfully executed {len(executed)} migrations")
                
            return result
            
        except Exception as e:
            logger.error(f"❌ Migration run failed: {e}")
            raise MigrationError("run_migrations", str(e))
    
    async def rollback_migration(self, migration_name: str, dry_run: bool = False) -> Dict[str, Any]:
        """Rollback a specific migration."""
        try:
            migration = next((m for m in self.migrations if m.name == migration_name), None)
            if not migration:
                raise MigrationError(migration_name, "Migration not found")
            
            executed_migrations = await self.get_executed_migrations()
            if migration_name not in executed_migrations:
                raise MigrationError(migration_name, "Migration was not executed")
            
            logger.info(f"🔄 Rolling back migration: {migration_name} (dry_run={dry_run})")
            
            if not dry_run:
                try:
                    db = await self.database()
                    success = await migration.down(db)
                    
                    if success:
                        await self.unmark_migration_executed(migration_name)
                        logger.info(f"✅ Migration rollback completed: {migration_name}")
                    else:
                        raise MigrationError(migration_name, "Migration rollback returned False")
                        
                except Exception as e:
                    logger.error(f"❌ Migration rollback failed: {migration_name} - {e}")
                    raise MigrationError(migration_name, str(e))
            else:
                logger.info(f"🔍 [DRY RUN] Would rollback migration: {migration_name}")
            
            return {
                "status": "success",
                "migration": migration_name,
                "dry_run": dry_run
            }
            
        except Exception as e:
            logger.error(f"❌ Migration rollback failed: {e}")
            raise MigrationError(migration_name, str(e))
    
    async def get_migration_status(self) -> Dict[str, Any]:
        """Get the status of all migrations."""
        try:
            await self.ensure_migration_collection()
            executed_migrations = await self.get_executed_migrations()
            
            migration_status = []
            for migration in self.migrations:
                is_executed = migration.name in executed_migrations
                migration_status.append({
                    "name": migration.name,
                    "description": migration.description,
                    "executed": is_executed,
                    "executed_at": migration.executed_at.isoformat() if migration.executed_at else None
                })
            
            pending_count = len([m for m in self.migrations if m.name not in executed_migrations])
            
            return {
                "total_migrations": len(self.migrations),
                "executed_migrations": len(executed_migrations),
                "pending_migrations": pending_count,
                "migrations": migration_status
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get migration status: {e}")
            raise MigrationError("get_migration_status", str(e))


# Global migration runner instance
_migration_runner: Optional[MigrationRunner] = None


def get_migration_runner() -> MigrationRunner:
    """Get the global migration runner instance."""
    global _migration_runner
    
    if _migration_runner is None:
        _migration_runner = MigrationRunner()
        logger.info("🔧 Migration runner initialized")
        
    return _migration_runner


async def run_migrations(dry_run: bool = False) -> Dict[str, Any]:
    """Run all pending migrations."""
    runner = get_migration_runner()
    return await runner.run_migrations(dry_run=dry_run)


async def rollback_migration(migration_name: str, dry_run: bool = False) -> Dict[str, Any]:
    """Rollback a specific migration."""
    runner = get_migration_runner()
    return await runner.rollback_migration(migration_name, dry_run=dry_run)


async def get_migration_status() -> Dict[str, Any]:
    """Get the status of all migrations."""
    runner = get_migration_runner()
    return await runner.get_migration_status() 