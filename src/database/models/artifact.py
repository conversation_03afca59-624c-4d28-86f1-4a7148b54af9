"""
Artifact Model for QAK MongoDB Implementation

Beanie ODM model for test execution artifacts metadata.
Files remain on filesystem, only metadata is stored in database.
"""

from typing import Dict, List, Optional, Any, Set
from datetime import datetime
from enum import Enum
from beanie import Document, Indexed
from pydantic import Field, field_validator, ConfigDict
import uuid


class ArtifactType(str, Enum):
    """Types of artifacts that can be collected."""
    SCREENSHOT = "screenshot"
    VIDEO = "video"
    LOG = "log"
    HTML_SNAPSHOT = "html_snapshot"
    NETWORK_HAR = "network_har"
    CONSOLE_OUTPUT = "console_output"
    ERROR_REPORT = "error_report"
    GENERATED_CODE = "generated_code"
    TEST_REPORT = "test_report"
    EXECUTION_METADATA = "execution_metadata"
    CUSTOM = "custom"


class ArtifactStatus(str, Enum):
    """Status of artifact processing."""
    PENDING = "pending"
    PROCESSING = "processing"
    READY = "ready"
    ARCHIVED = "archived"
    FAILED = "failed"
    DELETED = "deleted"


class StorageBackend(str, Enum):
    """Storage backend types."""
    LOCAL = "local"
    S3 = "s3"
    GCS = "gcs"
    AZURE = "azure"
    R2 = "r2"  # Cloudflare R2


class Artifact(Document):
    """
    MongoDB document model for test execution artifacts metadata.
    
    Files remain on filesystem, only metadata is stored in database.
    Provides comprehensive artifact management with deduplication, cleanup, and analytics.
    """
    
    # Artifact identification - indexed for performance
    artifact_id: Indexed(str, unique=True) = Field(default_factory=lambda: str(uuid.uuid4()))
    
    # Relationships - indexed for queries
    execution_id: Optional[Indexed(str)] = None
    session_id: Optional[Indexed(str)] = None
    project_id: Optional[Indexed(str)] = None
    
    # Artifact details - indexed for filtering
    type: Indexed(str) = Field(...)
    status: Indexed(str) = Field(default=ArtifactStatus.PENDING.value)
    
    # File information
    file_path: str = Field(...)
    original_name: str = Field(...)
    size_bytes: int = Field(default=0)
    content_hash: Optional[Indexed(str)] = None  # Indexed for deduplication
    mime_type: Optional[str] = None
    
    # Storage information
    storage_backend: str = Field(default=StorageBackend.LOCAL.value)
    thumbnail_path: Optional[str] = None
    compressed_path: Optional[str] = None
    
    # Cloud storage fields
    blob_url: Optional[str] = None  # Public URL for cloud-stored artifacts
    blob_key: Optional[str] = None  # Storage key/path in cloud backend
    presigned_url: Optional[str] = None  # Temporary access URL
    presigned_url_expires: Optional[datetime] = None  # When presigned URL expires
    
    # Processing timestamps - indexed for time-based queries
    collected_at: Indexed(datetime) = Field(default_factory=datetime.now)
    processed_at: Optional[datetime] = None
    
    # Organization
    tags: List[str] = Field(default_factory=list)
    
    # Metadata and context
    metadata: Dict[str, Any] = Field(default_factory=dict)
    step_name: Optional[str] = None
    step_number: Optional[int] = None
    
    # Cleanup and retention - indexed for cleanup queries
    expires_at: Optional[Indexed(datetime)] = None
    is_permanent: bool = Field(default=False)
    
    # Campos que estaban en BaseDocument (created_at está como collected_at)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # MongoDB document settings
    class Settings:
        name = "artifacts"
        indexes = []
    
    model_config = ConfigDict(
        use_enum_values=True,
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        },
        extra="ignore"
    )
    
    @field_validator('artifact_id')
    @classmethod
    def validate_artifact_id(cls, v):
        """Validate artifact_id format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("artifact_id cannot be empty")
        return v.strip()
    
    @field_validator('type', mode='before')
    @classmethod
    def validate_type(cls, v):
        """Validate and convert type to enum value."""
        if isinstance(v, str):
            return ArtifactType(v).value
        elif isinstance(v, ArtifactType):
            return v.value
        return v
    
    @field_validator('status', mode='before')
    @classmethod
    def validate_status(cls, v):
        """Validate and convert status to enum value."""
        if isinstance(v, str):
            return ArtifactStatus(v).value
        elif isinstance(v, ArtifactStatus):
            return v.value
        return v
    
    @field_validator('storage_backend', mode='before')
    @classmethod
    def validate_storage_backend(cls, v):
        """Validate and convert storage_backend to enum value."""
        if isinstance(v, str):
            return StorageBackend(v).value
        elif isinstance(v, StorageBackend):
            return v.value
        return v
    
    @field_validator('file_path')
    @classmethod
    def validate_file_path(cls, v):
        """Validate file_path is not empty."""
        if not v or len(v.strip()) == 0:
            raise ValueError("file_path cannot be empty")
        return v.strip()
    
    @field_validator('size_bytes')
    @classmethod
    def validate_size_bytes(cls, v):
        """Validate size_bytes is non-negative."""
        if v < 0:
            raise ValueError("size_bytes must be non-negative")
        return v
    
    def update_timestamp(self):
        """Update the last update timestamp in metadata."""
        self.metadata["updated_at"] = datetime.now().isoformat()
    
    def mark_processing(self):
        """Mark artifact as being processed."""
        self.status = ArtifactStatus.PROCESSING
        self.update_timestamp()
    
    def mark_ready(self, processed_at: Optional[datetime] = None):
        """Mark artifact as ready."""
        self.status = ArtifactStatus.READY
        self.processed_at = processed_at or datetime.now()
        self.update_timestamp()
    
    def mark_failed(self, error_message: str = ""):
        """Mark artifact as failed."""
        self.status = ArtifactStatus.FAILED
        if error_message:
            self.metadata["error_message"] = error_message
        self.update_timestamp()
    
    def mark_archived(self):
        """Mark artifact as archived."""
        self.status = ArtifactStatus.ARCHIVED
        self.update_timestamp()
    
    def mark_deleted(self):
        """Mark artifact as deleted."""
        self.status = ArtifactStatus.DELETED
        self.update_timestamp()
    
    def add_tag(self, tag: str):
        """Add a tag to the artifact."""
        if tag not in self.tags:
            self.tags.append(tag)
            self.update_timestamp()
    
    def remove_tag(self, tag: str):
        """Remove a tag from the artifact."""
        if tag in self.tags:
            self.tags.remove(tag)
            self.update_timestamp()
    
    def set_thumbnail(self, thumbnail_path: str):
        """Set thumbnail path for the artifact."""
        self.thumbnail_path = thumbnail_path
        self.update_timestamp()
    
    def set_compressed(self, compressed_path: str, compressed_size: int):
        """Set compressed version details."""
        self.compressed_path = compressed_path
        self.metadata["compressed_size_bytes"] = compressed_size
        self.metadata["compression_ratio"] = compressed_size / self.size_bytes if self.size_bytes > 0 else 0
        self.update_timestamp()
    
    def set_expiry(self, expires_at: datetime):
        """Set when the artifact expires."""
        self.expires_at = expires_at
        self.update_timestamp()
    
    def make_permanent(self):
        """Mark artifact as permanent (never expires)."""
        self.is_permanent = True
        self.expires_at = None
        self.update_timestamp()
    
    @property
    def is_expired(self) -> bool:
        """Check if artifact has expired."""
        if self.is_permanent or not self.expires_at:
            return False
        return datetime.now() > self.expires_at
    
    @property
    def is_processed(self) -> bool:
        """Check if artifact has been processed."""
        return self.status in [ArtifactStatus.READY, ArtifactStatus.ARCHIVED]
    
    @property
    def has_thumbnail(self) -> bool:
        """Check if artifact has a thumbnail."""
        return self.thumbnail_path is not None
    
    @property
    def has_compressed_version(self) -> bool:
        """Check if artifact has a compressed version."""
        return self.compressed_path is not None
    
    @property
    def size_mb(self) -> float:
        """Get size in megabytes."""
        return self.size_bytes / (1024 * 1024)
    
    @property
    def compression_ratio(self) -> Optional[float]:
        """Get compression ratio if available."""
        return self.metadata.get("compression_ratio")
    
    def get_display_name(self) -> str:
        """Generate a display name for the artifact."""
        return f"{self.type.capitalize()} - {self.original_name}"
    
    @classmethod
    def from_artifact_collector(cls, collector_artifact: Dict[str, Any], artifact_id: str = None) -> "Artifact":
        """
        Create Artifact from an artifact collector dictionary.
        
        Args:
            collector_artifact: Dictionary from the artifact collector.
            artifact_id: Optional artifact ID to use.
            
        Returns:
            Artifact: Converted document
        """
        # Extract relevant fields from collector artifact
        file_path = collector_artifact.get("file_path")
        if not file_path:
            raise ValueError("file_path is required for artifact creation")
        
        # Determine artifact type from MIME type or file extension if not provided
        artifact_type = collector_artifact.get("type")
        if not artifact_type:
            mime_type = collector_artifact.get("mime_type")
            if mime_type:
                # Basic mapping from MIME type to artifact type
                if "image" in mime_type:
                    artifact_type = ArtifactType.SCREENSHOT
                elif "video" in mime_type:
                    artifact_type = ArtifactType.VIDEO
                elif "text" in mime_type or "log" in mime_type:
                    artifact_type = ArtifactType.LOG
                else:
                    artifact_type = ArtifactType.CUSTOM
            else:
                artifact_type = ArtifactType.CUSTOM  # Default if cannot determine
        
        # Extract metadata and check for cloud storage fields
        metadata = collector_artifact.get("metadata", {})
        
        return cls(
            artifact_id=artifact_id or str(uuid.uuid4()),
            execution_id=collector_artifact.get("execution_id"),
            session_id=collector_artifact.get("session_id"),
            project_id=collector_artifact.get("project_id"),
            type=artifact_type,
            file_path=file_path,
            original_name=collector_artifact.get("original_name", file_path.split("/")[-1]),
            size_bytes=collector_artifact.get("size_bytes", 0),
            content_hash=collector_artifact.get("content_hash"),
            mime_type=collector_artifact.get("mime_type"),
            collected_at=collector_artifact.get("timestamp", datetime.now()),
            metadata=metadata,
            step_name=collector_artifact.get("step_name"),
            step_number=collector_artifact.get("step_number"),
            # Extract cloud storage fields from metadata to dedicated fields
            blob_url=metadata.get("blob_url"),
            blob_key=metadata.get("blob_key")
        )
    
    def to_artifact_collector(self) -> Dict[str, Any]:
        """
        Convert to ArtifactCollector format for backward compatibility.
        
        Returns:
            Dict: Legacy format for ArtifactCollector
        """
        return {
            "artifact_id": self.artifact_id,
            "execution_id": self.execution_id,
            "type": self.type.value,
            "status": self.status.value,
            "file_path": self.file_path,
            "original_name": self.original_name,
            "size_bytes": self.size_bytes,
            "content_hash": self.content_hash,
            "thumbnail_path": self.thumbnail_path,
            "compressed_path": self.compressed_path,
            "created_at": self.collected_at.isoformat() if self.collected_at else None,
            "tags": set(self.tags),
            "metadata": self.metadata,
        } 