"""
Environment Model for QAK Multi-Environment System

Defines the Environment model for managing different execution environments
(Development, QA, Staging, Production) within projects.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field, field_validator, ConfigDict
import uuid


class Environment(BaseModel):
    """Environment model for multi-environment test execution."""
    
    # Identifiers
    env_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique environment identifier")
    
    # Basic Information
    name: str = Field(..., description="Environment name (e.g., Development, QA, Staging, Production)")
    description: str = Field(default="", description="Environment description")
    
    # Configuration
    base_url: str = Field(..., description="Base URL for this environment")
    is_default: bool = Field(default=False, description="Whether this is the default environment")
    
    # Environment-specific configurations (structured for UI-friendly editing)
    config_overrides: Dict[str, Any] = Field(default_factory=dict, description="Browser config overrides for this environment")
    
    # Common configuration options as dedicated fields for better UX
    headless: Optional[bool] = Field(default=None, description="Run browser in headless mode")
    wait_time: Optional[float] = Field(default=None, description="Default wait time in seconds")
    timeout: Optional[int] = Field(default=None, description="Page timeout in milliseconds")
    viewport_width: Optional[int] = Field(default=None, description="Browser viewport width")
    viewport_height: Optional[int] = Field(default=None, description="Browser viewport height")
    user_agent: Optional[str] = Field(default=None, description="Custom user agent string")
    
    # Metadata
    tags: List[str] = Field(default_factory=list, description="Environment tags for categorization")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    
    model_config = ConfigDict(
        extra="ignore",
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        }
    )
    
    @field_validator('env_id')
    @classmethod
    def validate_env_id(cls, v):
        """Validate environment ID format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("env_id cannot be empty")
        return v.strip()
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate environment name."""
        if not v or len(v.strip()) == 0:
            raise ValueError("name cannot be empty")
        return v.strip()
    
    @field_validator('base_url')
    @classmethod
    def validate_base_url(cls, v):
        """Validate and normalize base URL."""
        if not v:
            # Allow empty base URL for cases where test cases use absolute URLs
            return ""
        
        # Remove trailing slash for consistency
        url = v.strip().rstrip('/')
        
        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = f'https://{url}'
        
        return url
    
    def construct_full_url(self, relative_path: str) -> str:
        """
        Construct full URL by combining base_url with relative path.
        
        Args:
            relative_path: Relative path to append to base URL
            
        Returns:
            str: Full URL
        """
        if not relative_path:
            return self.base_url
        
        # Handle absolute URLs
        if relative_path.startswith(('http://', 'https://')):
            return relative_path
        
        # Ensure relative path starts with /
        if not relative_path.startswith('/'):
            relative_path = f'/{relative_path}'
        
        return f"{self.base_url}{relative_path}"
    
    def merge_config_overrides(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge environment-specific config overrides with base configuration.
        Includes both dedicated fields and custom overrides.
        
        Args:
            base_config: Base configuration dictionary
            
        Returns:
            Dict: Merged configuration
        """
        merged_config = base_config.copy()
        
        # Add dedicated fields if they are set
        if self.headless is not None:
            merged_config['headless'] = self.headless
        if self.wait_time is not None:
            merged_config['wait_time'] = self.wait_time
        if self.timeout is not None:
            merged_config['timeout'] = self.timeout
        if self.viewport_width is not None and self.viewport_height is not None:
            merged_config['viewport'] = {
                'width': self.viewport_width,
                'height': self.viewport_height
            }
        if self.user_agent is not None:
            merged_config['user_agent'] = self.user_agent
        
        # Apply custom overrides (these take precedence)
        merged_config.update(self.config_overrides)
        
        return merged_config
    
    def update_timestamp(self):
        """Update the updated_at timestamp."""
        self.updated_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert environment to dictionary."""
        return {
            "env_id": self.env_id,
            "name": self.name,
            "description": self.description,
            "base_url": self.base_url,
            "is_default": self.is_default,
            "config_overrides": self.config_overrides,
            "tags": self.tags,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Environment":
        """Create Environment from dictionary."""
        # Convert datetime strings to datetime objects
        if data.get("created_at") and isinstance(data["created_at"], str):
            data["created_at"] = datetime.fromisoformat(data["created_at"].replace("Z", "+00:00"))
        if data.get("updated_at") and isinstance(data["updated_at"], str):
            data["updated_at"] = datetime.fromisoformat(data["updated_at"].replace("Z", "+00:00"))
        
        return cls(**data)
    
    @classmethod
    def create_default_environment(cls, base_url: str = "", name: str = "Default") -> "Environment":
        """
        Create a default environment for projects without explicit environments.
        
        Args:
            base_url: Base URL for the default environment
            name: Name for the default environment
            
        Returns:
            Environment: Default environment instance
        """
        return cls(
            name=name,
            description="Auto-generated default environment",
            base_url=base_url,
            is_default=True,
            config_overrides={},
            tags=["default", "auto-generated"]
        )
    
    def __str__(self) -> str:
        """String representation of the environment."""
        return f"Environment(name='{self.name}', base_url='{self.base_url}', is_default={self.is_default})"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"Environment(env_id='{self.env_id}', name='{self.name}', base_url='{self.base_url}', is_default={self.is_default})"