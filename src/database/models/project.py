"""
Project Model for QAK MongoDB Integration

Beanie ODM model for project entities with test suites and test cases.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from enum import Enum
from pydantic import Field, field_validator, BaseModel, ConfigDict
from .environment import Environment


class TestStatus(str, Enum):
    """Test execution status values."""
    PASSED = "Passed"
    FAILED = "Failed"
    PENDING = "Pending"
    RUNNING = "Running"
    SKIPPED = "Skipped"
    NOT_EXECUTED = "Not Executed"


class GitHubConfig(BaseModel):
    """GitHub configuration for project integration."""
    
    enabled: bool = Field(default=False, description="Whether GitHub integration is enabled")
    repo: Optional[str] = Field(default=None, description="GitHub repository URL")
    token: Optional[str] = Field(default=None, description="GitHub access token")
    
    model_config = ConfigDict(
        extra="ignore"
    )


class TestCase(BaseModel):
    """Test case model with all test-related information."""
    
    # Identifiers
    test_id: str = Field(..., description="Unique test case identifier")
    
    # Basic Information
    name: str = Field(..., description="Test case name")
    description: str = Field(default="", description="Test case description")
    
    # Test Details
    instrucciones: str = Field(default="", description="Test instructions")
    historia_de_usuario: str = Field(default="", description="User story")
    gherkin: str = Field(default="", description="Gherkin scenario")
    
    # Execution Details
    url: str = Field(default="", description="Target URL for testing")
    tags: List[str] = Field(default_factory=list, description="Test case tags")
    
    # Execution History
    history_files: List[str] = Field(default_factory=list, description="History file paths")
    execution_history: List[str] = Field(default_factory=list, description="MongoDB execution IDs history")
    status: TestStatus = Field(default=TestStatus.NOT_EXECUTED, description="Last execution status")
    last_execution: Optional[datetime] = Field(default=None, description="Last execution timestamp")
    
    # Code Generation
    code: str = Field(default="", description="Generated test code")
    framework: str = Field(default="", description="Test framework used")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")

    model_config = ConfigDict(
        extra="ignore"
    )
    
    @field_validator('test_id')
    @classmethod
    def validate_test_id(cls, v):
        """Validate test_id format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("test_id cannot be empty")
        return v.strip()


class TestSuite(BaseModel):
    """Test suite model containing multiple test cases."""
    
    # Identifiers
    suite_id: str = Field(..., description="Unique test suite identifier")
    
    # Basic Information
    name: str = Field(..., description="Test suite name")
    description: str = Field(default="", description="Test suite description")
    tags: List[str] = Field(default_factory=list, description="Test suite tags")
    
    # Test Cases (embedded documents)
    test_cases: Dict[str, TestCase] = Field(default_factory=dict, description="Test cases in this suite")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")

    model_config = ConfigDict(
        extra="ignore"
    )
    
    @field_validator('suite_id')
    @classmethod
    def validate_suite_id(cls, v):
        """Validate suite_id format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("suite_id cannot be empty")
        return v.strip()
    
    def add_test_case(self, test_case: TestCase):
        """Add a test case to this suite."""
        self.test_cases[test_case.test_id] = test_case
        self.updated_at = datetime.utcnow()
    
    def remove_test_case(self, test_id: str) -> bool:
        """Remove a test case from this suite."""
        if test_id in self.test_cases:
            del self.test_cases[test_id]
            self.updated_at = datetime.utcnow()
            return True
        return False
    
    def get_test_case(self, test_id: str) -> Optional[TestCase]:
        """Get a specific test case by ID."""
        return self.test_cases.get(test_id)
    
    def get_test_cases_by_status(self, status: TestStatus) -> List[TestCase]:
        """Get all test cases with a specific status."""
        return [tc for tc in self.test_cases.values() if tc.status == status]
    
    def get_test_cases_by_tag(self, tag: str) -> List[TestCase]:
        """Get all test cases with a specific tag."""
        return [tc for tc in self.test_cases.values() if tag in tc.tags]


class Project(BaseModel):
    """Project model containing test suites and configuration."""
    
    # MongoDB _id field
    id: Optional[str] = Field(default=None, description="MongoDB document ID")
    
    # Identifiers
    project_id: str = Field(..., description="Unique project identifier")
    
    # Basic Information
    name: str = Field(..., description="Project name")
    description: str = Field(default="", description="Project description")
    tags: List[str] = Field(default_factory=list, description="Project tags")
    
    # Test Suites (embedded documents)
    test_suites: Dict[str, TestSuite] = Field(default_factory=dict, description="Test suites in this project")
    
    # Environment Management
    environments: List[Environment] = Field(default_factory=list, description="Environments in this project")
    default_environment_id: Optional[str] = Field(default=None, description="Default environment ID")
    
    # GitHub Integration
    github_config: Optional[GitHubConfig] = Field(default=None, description="GitHub configuration")

    # Campos que estaban en BaseDocument
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    model_config = ConfigDict(
        use_enum_values=True,
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        },
        extra="ignore"
    )

    @field_validator('project_id')
    @classmethod
    def validate_project_id(cls, v):
        """Validate project_id format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("project_id cannot be empty")
        return v.strip()
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate project name."""
        if not v or len(v.strip()) == 0:
            raise ValueError("name cannot be empty")
        return v.strip()
    
    # Environment Management
    
    def add_environment(self, environment: Environment):
        """Add an environment to this project."""
        # If this environment is set as default, unset other defaults
        if environment.is_default:
            for env in self.environments:
                env.is_default = False
            self.default_environment_id = environment.env_id
        
        # Check if environment already exists, if so, update it
        existing_env = self.get_environment(environment.env_id)
        if existing_env:
            # Update existing environment
            for i, env in enumerate(self.environments):
                if env.env_id == environment.env_id:
                    self.environments[i] = environment
                    break
        else:
            # Add new environment
            self.environments.append(environment)
        
        self.updated_at = datetime.utcnow()
    
    def remove_environment(self, env_id: str) -> bool:
        """Remove an environment from this project."""
        env_to_remove = None
        for i, env in enumerate(self.environments):
            if env.env_id == env_id:
                env_to_remove = i
                break
        
        if env_to_remove is not None:
            removed_env = self.environments.pop(env_to_remove)
            
            # If we removed the default environment, set a new default
            if removed_env.is_default and self.environments:
                self.environments[0].is_default = True
                self.default_environment_id = self.environments[0].env_id
            elif removed_env.is_default:
                self.default_environment_id = None
            
            self.updated_at = datetime.utcnow()
            return True
        
        return False
    
    def get_environment(self, env_id: str) -> Optional[Environment]:
        """Get a specific environment by ID."""
        for env in self.environments:
            if env.env_id == env_id:
                return env
        return None
    
    def get_default_environment(self) -> Optional[Environment]:
        """Get the default environment."""
        if self.default_environment_id:
            return self.get_environment(self.default_environment_id)
        
        # Fallback: return first environment marked as default
        for env in self.environments:
            if env.is_default:
                return env
        
        return None
    
    def set_default_environment(self, env_id: str) -> bool:
        """Set an environment as the default."""
        target_env = self.get_environment(env_id)
        if not target_env:
            return False
        
        # Unset all other defaults
        for env in self.environments:
            env.is_default = False
        
        # Set the new default
        target_env.is_default = True
        self.default_environment_id = env_id
        self.updated_at = datetime.utcnow()
        
        return True
    
    def get_environments_by_tag(self, tag: str) -> List[Environment]:
        """Get all environments with a specific tag."""
        return [env for env in self.environments if tag in env.tags]
    
    def ensure_default_environment(self):
        """Ensure the project has a default environment."""
        if not self.environments:
            # Create a default environment based on existing test case URLs
            base_url = self._extract_base_url_from_test_cases()
            default_env = Environment.create_default_environment(base_url)
            self.add_environment(default_env)
    
    def _extract_base_url_from_test_cases(self) -> str:
        """Extract base URL from existing test case URLs."""
        from urllib.parse import urlparse
        
        urls = []
        for suite in self.test_suites.values():
            for test_case in suite.test_cases.values():
                if test_case.url and test_case.url.startswith(('http://', 'https://')):
                    parsed = urlparse(test_case.url)
                    base_url = f"{parsed.scheme}://{parsed.netloc}"
                    urls.append(base_url)
        
        # Return the most common base URL, or empty string if none found
        if urls:
            from collections import Counter
            return Counter(urls).most_common(1)[0][0]
        
        return ""
    
    # Test Suite Management
    
    def add_test_suite(self, test_suite: TestSuite):
        """Add a test suite to this project."""
        self.test_suites[test_suite.suite_id] = test_suite
        self.updated_at = datetime.utcnow()
    
    def remove_test_suite(self, suite_id: str) -> bool:
        """Remove a test suite from this project."""
        if suite_id in self.test_suites:
            del self.test_suites[suite_id]
            self.updated_at = datetime.utcnow()
            return True
        return False
    
    def get_test_suite(self, suite_id: str) -> Optional[TestSuite]:
        """Get a specific test suite by ID."""
        return self.test_suites.get(suite_id)
    
    def get_test_suites_by_tag(self, tag: str) -> List[TestSuite]:
        """Get all test suites with a specific tag."""
        return [ts for ts in self.test_suites.values() if tag in ts.tags]
    
    # Test Case Management (across all suites)
    
    def get_all_test_cases(self) -> List[TestCase]:
        """Get all test cases across all suites."""
        test_cases = []
        for suite in self.test_suites.values():
            test_cases.extend(suite.test_cases.values())
        return test_cases
    
    def get_test_case_by_id(self, test_id: str) -> Optional[TestCase]:
        """Find a test case by ID across all suites."""
        for suite in self.test_suites.values():
            test_case = suite.get_test_case(test_id)
            if test_case:
                return test_case
        return None
    
    def get_test_cases_by_status(self, status: TestStatus) -> List[TestCase]:
        """Get all test cases with a specific status across all suites."""
        test_cases = []
        for suite in self.test_suites.values():
            test_cases.extend(suite.get_test_cases_by_status(status))
        return test_cases
    
    def get_test_cases_by_tag(self, tag: str) -> List[TestCase]:
        """Get all test cases with a specific tag across all suites."""
        test_cases = []
        for suite in self.test_suites.values():
            test_cases.extend(suite.get_test_cases_by_tag(tag))
        return test_cases
    
    # Statistics
    
    def get_project_stats(self) -> Dict[str, Any]:
        """Get project statistics."""
        all_test_cases = self.get_all_test_cases()
        
        return {
            "total_suites": len(self.test_suites),
            "total_test_cases": len(all_test_cases),
            "status_breakdown": {
                status.value: len([tc for tc in all_test_cases if tc.status == status])
                for status in TestStatus
            },
            "tags": list(set(self.tags + [tag for suite in self.test_suites.values() for tag in suite.tags])),
            "last_updated": self.updated_at,
        }
    
    # Legacy Compatibility
    
    @classmethod
    def from_legacy_json(cls, json_data: Dict[str, Any]) -> "Project":
        """Create Project from legacy JSON format."""
        
        # Handle MongoDB ObjectId conversion
        if "_id" in json_data:
            json_data["id"] = str(json_data["_id"])
            del json_data["_id"]
        elif "id" in json_data and not isinstance(json_data["id"], str):
            json_data["id"] = str(json_data["id"])
        
        # Create GitHub config if present
        github_config = None
        if "github_config" in json_data and json_data["github_config"] is not None:
            github_config = GitHubConfig(**json_data["github_config"])
        
        # Create environments if present
        environments = []
        for env_data in json_data.get("environments", []):
            # Convert datetime strings if present
            if env_data.get("created_at") and isinstance(env_data["created_at"], str):
                env_data["created_at"] = datetime.fromisoformat(env_data["created_at"].replace("Z", "+00:00"))
            if env_data.get("updated_at") and isinstance(env_data["updated_at"], str):
                env_data["updated_at"] = datetime.fromisoformat(env_data["updated_at"].replace("Z", "+00:00"))
            
            environment = Environment(**env_data)
            environments.append(environment)
        
        # Create test suites
        test_suites = {}
        for suite_id, suite_data in json_data.get("test_suites", {}).items():
            
            # Create test cases for this suite
            test_cases = {}
            for test_id, test_data in suite_data.get("test_cases", {}).items():
                # Convert datetime strings to datetime objects
                if test_data.get("created_at") and isinstance(test_data["created_at"], str):
                    test_data["created_at"] = datetime.fromisoformat(test_data["created_at"].replace("Z", "+00:00"))
                if test_data.get("updated_at") and isinstance(test_data["updated_at"], str):
                    test_data["updated_at"] = datetime.fromisoformat(test_data["updated_at"].replace("Z", "+00:00"))
                if test_data.get("last_execution") and isinstance(test_data["last_execution"], str):
                    test_data["last_execution"] = datetime.fromisoformat(test_data["last_execution"].replace("Z", "+00:00"))
                
                test_case = TestCase(**test_data)
                test_cases[test_id] = test_case
            
            # Convert suite datetime strings
            if suite_data.get("created_at") and isinstance(suite_data["created_at"], str):
                suite_data["created_at"] = datetime.fromisoformat(suite_data["created_at"].replace("Z", "+00:00"))
            if suite_data.get("updated_at") and isinstance(suite_data["updated_at"], str):
                suite_data["updated_at"] = datetime.fromisoformat(suite_data["updated_at"].replace("Z", "+00:00"))
            
            # Create test suite with test cases
            suite_data_copy = suite_data.copy()
            suite_data_copy["test_cases"] = test_cases
            test_suite = TestSuite(**suite_data_copy)
            test_suites[suite_id] = test_suite
        
        # Convert project datetime strings
        if json_data.get("created_at") and isinstance(json_data["created_at"], str):
            json_data["created_at"] = datetime.fromisoformat(json_data["created_at"].replace("Z", "+00:00"))
        if json_data.get("updated_at") and isinstance(json_data["updated_at"], str):
            json_data["updated_at"] = datetime.fromisoformat(json_data["updated_at"].replace("Z", "+00:00"))
        
        # Create project
        project_data = json_data.copy()
        project_data["test_suites"] = test_suites
        project_data["github_config"] = github_config
        project_data["environments"] = environments
        
        project = cls(**project_data)
        
        # Ensure the project has a default environment if none exist
        if not project.environments:
            project.ensure_default_environment()
        
        return project
    
    def to_legacy_json(self) -> Dict[str, Any]:
        """Convert Project to legacy JSON format."""
        
        # Convert test suites
        legacy_suites = {}
        for suite_id, suite in self.test_suites.items():
            
            # Convert test cases
            legacy_test_cases = {}
            for test_id, test_case in suite.test_cases.items():
                test_case_dict = test_case.model_dump()
                
                # Convert datetime objects to strings
                if test_case_dict.get("created_at"):
                    test_case_dict["created_at"] = test_case_dict["created_at"].isoformat()
                if test_case_dict.get("updated_at"):
                    test_case_dict["updated_at"] = test_case_dict["updated_at"].isoformat()
                if test_case_dict.get("last_execution"):
                    test_case_dict["last_execution"] = test_case_dict["last_execution"].isoformat()
                
                legacy_test_cases[test_id] = test_case_dict
            
            # Convert suite
            suite_dict = suite.model_dump()
            suite_dict["test_cases"] = legacy_test_cases
            
            # Convert datetime objects to strings
            if suite_dict.get("created_at"):
                suite_dict["created_at"] = suite_dict["created_at"].isoformat()
            if suite_dict.get("updated_at"):
                suite_dict["updated_at"] = suite_dict["updated_at"].isoformat()
            
            legacy_suites[suite_id] = suite_dict
        
        # Convert environments
        legacy_environments = []
        for env in self.environments:
            env_dict = env.to_dict()
            legacy_environments.append(env_dict)
        
        # Convert project
        project_dict = self.model_dump()
        project_dict["test_suites"] = legacy_suites
        project_dict["environments"] = legacy_environments
        
        # Convert datetime objects to strings
        if project_dict.get("created_at"):
            project_dict["created_at"] = project_dict["created_at"].isoformat()
        if project_dict.get("updated_at"):
            project_dict["updated_at"] = project_dict["updated_at"].isoformat()
        
        # Convert GitHub config
        if self.github_config:
            project_dict["github_config"] = self.github_config.model_dump()
        else:
            project_dict["github_config"] = {
                "enabled": False,
                "repo": None,
                "token": None
            }
        
        return project_dict 