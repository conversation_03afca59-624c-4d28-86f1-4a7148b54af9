"""
Semantic Interaction Model for QAK MongoDB Implementation

ODM model for recent interactions from browser-use agents.
"""

from typing import Dict, Any, Optional
from datetime import datetime
from beanie import Document, Indexed
from pydantic import Field, BaseModel

class TargetElement(BaseModel):
    """Represents the target element of an interaction."""
    # This can be expanded with more detailed fields if needed
    # For now, we'll keep it simple as it's a dict in the JSON
    element_data: Dict[str, Any] = Field(default_factory=dict)

class SemanticInteraction(Document):
    """
    MongoDB document model for a recent agent interaction.
    """
    interaction_id: Indexed(str, unique=True)
    agent_id: Indexed(str)
    timestamp: Indexed(datetime)
    url: Optional[str] = None
    action_type: Indexed(str)
    target_element: TargetElement
    context: str
    success: bool
    result: str
    lesson_learned: Optional[str] = None

    class Settings:
        name = "semantic_interactions"
        indexes = [
            "agent_id",
            "timestamp",
            "action_type",
            "success",
        ] 