"""
Semantic Pattern Model for QAK MongoDB Implementation

ODM model for semantic patterns learned by browser-use agents.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from beanie import Document, Indexed
from pydantic import Field, BaseModel

class PatternContext(BaseModel):
    """Context for a semantic pattern."""
    common_keywords: List[str] = Field(default_factory=list)
    domain: Optional[str] = None
    interaction_count: int = 0
    success_rate: float = 0.0

class SemanticPattern(Document):
    """
    MongoDB document model for a semantic pattern.
    """
    pattern_id: Indexed(str, unique=True)
    pattern_type: Indexed(str)
    description: str
    context: PatternContext
    success_indicators: List[str] = Field(default_factory=list)
    failure_indicators: List[str] = Field(default_factory=list)
    confidence_score: float = 0.0
    usage_count: int = 0
    last_used: Optional[datetime] = None
    merged_from: int = 0
    
    class Settings:
        name = "semantic_patterns"
        indexes = [
            "pattern_type",
            "context.domain",
            "confidence_score",
            "usage_count",
            "last_used",
        ] 