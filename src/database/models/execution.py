"""
Execution Model for QAK MongoDB Implementation

ODM model for test execution results using Beanie.
Converts StandardResult Pydantic model to MongoDB document model.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum
from beanie import Document, Indexed
from pydantic import Field, field_validator, ConfigDict
from dataclasses import asdict
import uuid

# Import from StandardResult to maintain consistency
from src.models.standard_result import (
    ExecutionStatus, TestType, TestStep, TestSummary, Artifacts,
    ElementLocator, StandardResult
)


class Execution(Document):
    """
    MongoDB document model for test execution results.
    
    This maps directly to StandardResult but with MongoDB-specific features:
    - Document inheritance from Beanie
    - Indexed fields for performance
    - Collection settings
    - Migration methods for legacy data
    """
    
    # Execution identification - indexed for performance
    execution_id: Indexed(str, unique=True) = Field(default_factory=lambda: str(uuid.uuid4()))
    test_type: Indexed(str) = Field(...)
    test_id: Optional[Indexed(str)] = None
    suite_id: Optional[Indexed(str)] = None
    project_id: Optional[Indexed(str)] = None
    
    # Environment information - indexed for filtering
    environment_id: Optional[Indexed(str)] = None
    environment_name: Optional[str] = None
    full_url: Optional[str] = None  # Complete URL used for execution (base_url + relative_path)
    environment_config: Optional[Dict[str, Any]] = None  # Environment-specific config used
    
    # Application version being tested - indexed for version comparison
    application_version: Optional[Indexed(str)] = None  # Version of the application being tested (e.g., 1.0.1, 1.2.3)
    
    # Execution status - indexed for queries
    status: Indexed(str) = Field(...)
    started_at: Indexed(datetime) = Field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    duration_ms: Optional[int] = None
    
    # Results and data (embedded documents)
    summary: TestSummary = Field(default_factory=TestSummary)
    steps: List[TestStep] = Field(default_factory=list)
    errors: List[str] = Field(default_factory=list)
    
    # Artifacts (embedded document)
    artifacts: Artifacts = Field(default_factory=Artifacts)
    
    # Configuration used
    configuration: Dict[str, Any] = Field(default_factory=dict)
    
    # Legacy compatibility fields
    success: bool = True
    message: Optional[str] = None
    error: Optional[str] = None
    
    # Raw data for detailed analysis
    raw_data: Optional[Dict[str, Any]] = None
    raw_result: Optional[List[Any]] = None
    
    # Additional metadata
    metadata: Optional[Dict[str, Any]] = None
    
    # Codegen-specific fields (for codegen executions)
    session_id: Optional[Indexed(str)] = None  # Links to codegen session
    target_url: Optional[str] = None
    generated_code: Optional[str] = None
    browser_config: Optional[Dict[str, Any]] = None
    history: Optional[List[Dict[str, Any]]] = None
    
    # Enhanced analysis and context fields
    test_analysis: Optional[Dict[str, Any]] = None  # LLM analysis of test execution
    step_analysis: Optional[Dict[str, Any]] = None  # Analysis of individual steps
    performance_metrics: Optional[Dict[str, Any]] = None  # Performance metrics
    browser_context: Optional[Dict[str, Any]] = None  # Browser state and context
    user_story_context: Optional[str] = None  # Original user story/instructions
    gherkin_scenarios: Optional[List[str]] = None  # Associated Gherkin scenarios
    
    # AI insights and recommendations
    ai_insights: Optional[Dict[str, Any]] = None  # AI-generated insights
    ai_analysis: Optional[Dict[str, Any]] = None  # Complete AI analysis results from background jobs
    recommendations: Optional[List[str]] = None  # Improvement recommendations
    failure_analysis: Optional[Dict[str, Any]] = None  # Detailed failure analysis
    
    # Execution environment info
    execution_environment: Optional[Dict[str, Any]] = None  # Browser, OS, etc.
    execution_tags: Optional[List[str]] = None  # Tags for categorization
    
    # Generated automation scripts
    generated_scripts: Optional[Dict[str, Any]] = None  # Generated scripts by framework
    
    # Campos que estaban en BaseDocument
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    @field_validator('test_type', mode='before')
    @classmethod
    def validate_test_type(cls, v):
        """Validate and convert test_type to enum."""
        if isinstance(v, str):
            return TestType(v).value
        elif isinstance(v, TestType):
            return v.value
        return v
    
    @field_validator('status', mode='before')
    @classmethod
    def validate_status(cls, v):
        """Validate and convert status to enum."""
        if isinstance(v, str):
            return ExecutionStatus(v).value
        elif isinstance(v, ExecutionStatus):
            return v.value
        return v
    
    # MongoDB document settings
    class Settings:
        name = "executions"  # Collection name
        indexes = []
    
    model_config = ConfigDict(
        use_enum_values=True,
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None,
            TestSummary: lambda v: asdict(v) if hasattr(v, '__dataclass_fields__') else v,
            TestStep: lambda v: asdict(v) if hasattr(v, '__dataclass_fields__') else v,
            Artifacts: lambda v: asdict(v) if hasattr(v, '__dataclass_fields__') else v,
            ElementLocator: lambda v: asdict(v) if hasattr(v, '__dataclass_fields__') else v
        },
        extra="ignore"
    )
    
    @field_validator('duration_ms', mode='before')
    @classmethod
    def calculate_duration(cls, v, info):
        """Calculate duration if not provided and both timestamps exist."""
        if v is None and hasattr(info, 'data'):
            values = info.data
            if 'started_at' in values and values.get('completed_at'):
                start = values['started_at']
                end = values['completed_at']
                if isinstance(start, datetime) and isinstance(end, datetime):
                    return int((end - start).total_seconds() * 1000)
        return v
    
    @field_validator('success', mode='before')
    @classmethod
    def calculate_success(cls, v, info):
        """Calculate success based on status if not explicitly set."""
        if hasattr(info, 'data') and 'status' in info.data:
            return info.data['status'] == ExecutionStatus.SUCCESS
        return v
    
    def complete_execution(self, status: ExecutionStatus = ExecutionStatus.SUCCESS):
        """Mark execution as complete and calculate final metrics."""
        self.completed_at = datetime.now()
        self.status = status
        
        # Calculate duration
        if self.started_at and self.completed_at:
            self.duration_ms = int((self.completed_at - self.started_at).total_seconds() * 1000)
        
        # Update summary
        self.summary.total_steps = len(self.steps)
        self.summary.successful_steps = sum(1 for step in self.steps if step.success)
        self.summary.failed_steps = self.summary.total_steps - self.summary.successful_steps
        self.summary.calculate_success_rate()
        
        # Update success flag
        self.success = status == ExecutionStatus.SUCCESS
        
        # Set error message if failed
        if status == ExecutionStatus.FAILURE and self.errors:
            self.error = "; ".join(self.errors)
    
    def add_step(self, step: TestStep):
        """Add a test step to the execution."""
        self.steps.append(step)
        
        # Update real-time summary
        self.summary.total_steps = len(self.steps)
        self.summary.successful_steps = sum(1 for s in self.steps if s.success)
        self.summary.failed_steps = self.summary.total_steps - self.summary.successful_steps
        self.summary.calculate_success_rate()
    
    def add_error(self, error: str):
        """Add an error message."""
        self.errors.append(error)
        if not self.error:
            self.error = error
    
    def add_artifact(self, artifact_type: str, url: str):
        """Add an artifact URL."""
        if artifact_type == "screenshot":
            self.artifacts.screenshots.append(url)
        elif artifact_type == "video":
            self.artifacts.videos.append(url)
        elif artifact_type == "log":
            self.artifacts.logs.append(url)
        elif artifact_type == "history":
            self.artifacts.history_file = url
        elif artifact_type == "code":
            self.artifacts.generated_code = url
        elif artifact_type == "gherkin":
            self.artifacts.gherkin_scenarios.append(url)
    
    def set_environment_info(self, environment_id: str, environment_name: str, 
                           full_url: str, environment_config: Dict[str, Any] = None,
                           application_version: str = None):
        """Set environment information for this execution."""
        self.environment_id = environment_id
        self.environment_name = environment_name
        self.full_url = full_url
        self.environment_config = environment_config or {}
        self.application_version = application_version
    
    def to_standard_result(self) -> StandardResult:
        """
        Convert Execution to StandardResult for API compatibility.
        
        Returns:
            StandardResult: Pydantic model compatible with existing APIs
        """
        # Enrich metadata with analysis fields for frontend consumption
        enriched_metadata = self.metadata.copy() if self.metadata else {}
        enriched_metadata.update({
            'test_analysis': self.test_analysis,
            'step_analysis': self.step_analysis,
            'performance_metrics': self.performance_metrics,
            'browser_context': self.browser_context,
            'user_story_context': self.user_story_context,
            'gherkin_scenarios': self.gherkin_scenarios,
            'ai_insights': self.ai_insights,
            'ai_analysis': self.ai_analysis,  # Complete AI analysis from background jobs
            'recommendations': self.recommendations,
            'failure_analysis': self.failure_analysis,
            'execution_environment': self.execution_environment,
            'execution_tags': self.execution_tags,
            # Environment information
            'environment_id': self.environment_id,
            'environment_name': self.environment_name,
            'full_url': self.full_url,
            'environment_config': self.environment_config,
            # Application version being tested
            'application_version': self.application_version,
        })
        
        return StandardResult(
            execution_id=self.execution_id,
            test_type=self.test_type,
            test_id=self.test_id,
            suite_id=self.suite_id,
            project_id=self.project_id,
            status=self.status,
            started_at=self.started_at,
            completed_at=self.completed_at,
            duration_ms=self.duration_ms,
            summary=self.summary,
            steps=self.steps,
            errors=self.errors,
            artifacts=self.artifacts,
            configuration=self.configuration,
            success=self.success,
            message=self.message,
            error=self.error,
            raw_data=self.raw_data,
            raw_result=self.raw_result,
            metadata=enriched_metadata
        )
    
    @classmethod
    def from_standard_result(cls, result: StandardResult) -> "Execution":
        """
        Create Execution from StandardResult.
        
        Args:
            result: StandardResult instance
            
        Returns:
            Execution: MongoDB document ready for persistence
        """
        # Extract analysis fields from metadata if present
        metadata = result.metadata or {}
        
        return cls(
            execution_id=result.execution_id,
            test_type=result.test_type,
            test_id=result.test_id,
            suite_id=result.suite_id,
            project_id=result.project_id,
            status=result.status,
            started_at=result.started_at,
            completed_at=result.completed_at,
            duration_ms=result.duration_ms,
            summary=result.summary,
            steps=result.steps,
            errors=result.errors,
            artifacts=result.artifacts,
            configuration=result.configuration,
            success=result.success,
            message=result.message,
            error=result.error,
            raw_data=result.raw_data,
            raw_result=result.raw_result,
            metadata=result.metadata,
            
            # Extract enhanced analysis fields from metadata
            test_analysis=metadata.get('test_analysis'),
            step_analysis=metadata.get('step_analysis'),
            performance_metrics=metadata.get('performance_metrics'),
            browser_context=metadata.get('browser_context'),
            user_story_context=metadata.get('user_story_context'),
            gherkin_scenarios=metadata.get('gherkin_scenarios'),
            ai_insights=metadata.get('ai_insights'),
            ai_analysis=metadata.get('ai_analysis'),  # Complete AI analysis from background jobs
            recommendations=metadata.get('recommendations'),
            failure_analysis=metadata.get('failure_analysis'),
            execution_environment=metadata.get('execution_environment'),
            execution_tags=metadata.get('execution_tags'),
            
            # Extract environment information from metadata
            environment_id=metadata.get('environment_id'),
            environment_name=metadata.get('environment_name'),
            full_url=metadata.get('full_url'),
            environment_config=metadata.get('environment_config'),
            # Extract application version being tested
            application_version=metadata.get('application_version'),
        )
    
    @classmethod
    def from_legacy_json(cls, data: Dict[str, Any], execution_id: str = None) -> "Execution":
        """
        Create Execution from legacy JSON data.
        
        Args:
            data: Legacy JSON data from codegen_sessions/executions/
            execution_id: Override execution ID if provided
            
        Returns:
            Execution: Converted document
        """
        # Use provided execution_id or extract from data
        exec_id = execution_id or data.get("execution_id", str(uuid.uuid4()))
        
        # Determine test type based on data structure
        test_type = TestType.CODEGEN if data.get("session_id") else TestType.SMOKE
        
        # Map legacy status to ExecutionStatus
        status_mapping = {
            "running": ExecutionStatus.RUNNING,
            "completed": ExecutionStatus.SUCCESS,
            "failed": ExecutionStatus.FAILURE,
            "error": ExecutionStatus.ERROR
        }
        status = status_mapping.get(data.get("status", "error"), ExecutionStatus.ERROR)
        
        # Parse timestamps
        started_at = datetime.fromisoformat(data["created_at"]) if data.get("created_at") else datetime.now()
        completed_at = datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") and status != ExecutionStatus.RUNNING else None
        
        # Create document
        doc = cls(
            execution_id=exec_id,
            test_type=test_type,
            status=status,
            started_at=started_at,
            completed_at=completed_at,
            success=status == ExecutionStatus.SUCCESS,
            
            # Codegen-specific fields
            session_id=data.get("session_id"),
            target_url=data.get("target_url"),
            generated_code=data.get("generated_code"),
            browser_config=data.get("browser_config", {}),
            history=data.get("history", []),
            
            # Result and error fields
            raw_data=data,
            error=data.get("error"),
            raw_result=data.get("result")
        )
        
        # Calculate duration if completed
        if doc.completed_at and doc.started_at:
            doc.duration_ms = int((doc.completed_at - doc.started_at).total_seconds() * 1000)
        
        # Add artifacts if generated code exists
        if doc.generated_code:
            doc.add_artifact("code", doc.generated_code)
        
        return doc
    
    def add_test_analysis(self, analysis_data: Dict[str, Any]):
        """Add LLM-generated test analysis."""
        self.test_analysis = analysis_data
        
    def add_step_analysis(self, step_analysis: Dict[str, Any]):
        """Add detailed step-by-step analysis."""
        self.step_analysis = step_analysis
        
    def add_performance_metrics(self, metrics: Dict[str, Any]):
        """Add performance metrics like page load times, interaction delays."""
        self.performance_metrics = metrics
        
    def add_ai_insights(self, insights: Dict[str, Any]):
        """Add AI-generated insights about the test execution."""
        self.ai_insights = insights

    def add_ai_analysis(self, analysis_data: Dict[str, Any]):
        """Add complete AI analysis results from background jobs."""
        self.ai_analysis = analysis_data

    def add_recommendations(self, recommendations: List[str]):
        """Add improvement recommendations."""
        self.recommendations = recommendations or []
        
    def add_failure_analysis(self, failure_data: Dict[str, Any]):
        """Add detailed failure analysis for failed executions."""
        if self.status in [ExecutionStatus.FAILURE, ExecutionStatus.ERROR]:
            self.failure_analysis = failure_data
            
    def set_execution_context(self, user_story: str = None, gherkin: List[str] = None, 
                             environment: Dict[str, Any] = None, tags: List[str] = None):
        """Set execution context information."""
        if user_story:
            self.user_story_context = user_story
        if gherkin:
            self.gherkin_scenarios = gherkin
        if environment:
            self.execution_environment = environment
        if tags:
            self.execution_tags = tags
            
    def get_execution_summary_for_ui(self) -> Dict[str, Any]:
        """Get a summary optimized for UI display."""
        return {
            'execution_id': self.execution_id,
            'test_type': self.test_type,
            'status': self.status,
            'started_at': self.started_at,
            'completed_at': self.completed_at,
            'duration_ms': self.duration_ms,
            'success_rate': self.summary.success_rate if self.summary else 0.0,
            'total_steps': self.summary.total_steps if self.summary else 0,
            'successful_steps': self.summary.successful_steps if self.summary else 0,
            'failed_steps': self.summary.failed_steps if self.summary else 0,
            'error_count': len(self.errors),
            'first_error': self.errors[0] if self.errors else self.error,
            'has_analysis': bool(self.test_analysis),
            'has_recommendations': bool(self.recommendations),
            'tags': self.execution_tags or [],
            'target_url': self.target_url,
            # Environment information
            'environment_id': self.environment_id,
            'environment_name': self.environment_name,
            'full_url': self.full_url,
            'artifacts_count': {
                'screenshots': len(self.artifacts.screenshots) if self.artifacts else 0,
                'videos': len(self.artifacts.videos) if self.artifacts else 0,
                'logs': len(self.artifacts.logs) if self.artifacts else 0,
            }
        }
    
    def to_legacy_json(self) -> Dict[str, Any]:
        """
        Convert Execution to legacy JSON format for backward compatibility.
        
        Returns:
            Dict: Legacy JSON format matching current file structure
        """
        return {
            "execution_id": self.execution_id,
            "session_id": self.session_id,
            "status": "completed" if self.status == ExecutionStatus.SUCCESS else self.status.value,
            "created_at": self.started_at.isoformat() if self.started_at else None,
            "updated_at": self.completed_at.isoformat() if self.completed_at else self.started_at.isoformat(),
            "target_url": self.target_url,
            "generated_code": self.generated_code,
            "browser_config": self.browser_config or {},
            "history": self.history or [],
            "result": self.raw_result,
            "error": self.error
        }


# CodegenSessionDocument is now defined in codegen_session.py to avoid duplication
