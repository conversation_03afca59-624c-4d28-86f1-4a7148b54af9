"""
Beanie ODM Configuration for QAK MongoDB Integration

Sets up Beanie ODM with MongoDB connection and model registration.
"""

import logging
import os
from typing import List, Type, Optional, Dict, Any
from datetime import datetime
from beanie import init_beanie, Document
from motor.motor_asyncio import AsyncIOMotorDatabase
from pydantic import Field

from .connection import get_database
from .config import get_database_config, DatabaseEnvironment

logger = logging.getLogger(__name__)


class ODMManager:
    """Manages Beanie ODM initialization and model registration."""
    
    def __init__(self):
        self._database: Optional[AsyncIOMotorDatabase] = None
        self._models: List[Type[Document]] = []
        self._initialized = False
        
    def register_model(self, model: Type[Document]):
        """Register a model for initialization."""
        self._models.append(model)
        logger.info(f"📝 Registered ODM model: {model.__name__}")
    
    def register_models(self, models: List[Type[Document]]):
        """Register multiple models for initialization."""
        for model in models:
            self.register_model(model)
    
    async def initialize(self, database: Optional[AsyncIOMotorDatabase] = None) -> bool:
        """Initialize Beanie ODM with all registered models."""
        try:
            if self._initialized:
                logger.info("✅ ODM already initialized")
                return True
                
            # Get database connection
            if database is None:
                database = await get_database()
            
            self._database = database
            
            if not self._models:
                logger.warning("⚠️ No models registered for ODM initialization")
                return True
            
            logger.info(f"🚀 Initializing Beanie ODM with {len(self._models)} models...")
            
            # Clean up conflicting indexes before initialization
            await self._cleanup_conflicting_indexes(database)
            
            # Initialize Beanie with all registered models
            await init_beanie(
                database=database,
                document_models=self._models
            )
            
            self._initialized = True
            
            logger.info("✅ Beanie ODM initialization successful")
            
            # Log registered models
            for model in self._models:
                collection_name = model.get_collection_name()
                logger.info(f"  📝 {model.__name__} -> {collection_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Beanie ODM initialization failed: {e}")
            return False
    
    async def _cleanup_conflicting_indexes(self, database: AsyncIOMotorDatabase):
        """Clean up potentially conflicting indexes before Beanie initialization.

        Skips cleanup in production unless environment variable ODM_CLEANUP_INDEXES=true.
        """
        # Skip if not explicitly enabled in production to reduce startup time
        config = get_database_config()
        if config.environment == DatabaseEnvironment.PRODUCTION and os.getenv("ODM_CLEANUP_INDEXES", "false").lower() != "true":
            logger.info("🚀 Skipping index cleanup in production (set ODM_CLEANUP_INDEXES=true to enable)")
            return

        try:
            logger.info("🧹 Cleaning up potentially conflicting indexes...")
            
            # Collections and their potentially conflicting indexes
            collections_to_clean = {
                "projects": ["project_id_1", "created_at_-1"],
                "executions": ["execution_id_1", "project_id_1_started_at_-1", "test_type_1_status_1"],
                "codegen_sessions": ["session_id_1", "created_at_-1"],
                "artifacts": ["execution_id_1", "created_at_-1", "artifact_id_1"]
            }
            
            for collection_name, index_names in collections_to_clean.items():
                collection = database[collection_name]
                
                # Get existing indexes
                try:
                    existing_indexes = await collection.list_indexes().to_list(None)
                    existing_index_names = {idx.get("name") for idx in existing_indexes}
                    
                    # Drop conflicting indexes
                    for index_name in index_names:
                        if index_name in existing_index_names:
                            try:
                                await collection.drop_index(index_name)
                                logger.info(f"🗑️ Dropped conflicting index: {collection_name}.{index_name}")
                            except Exception as drop_error:
                                # Index might not exist or be in use, this is not critical
                                logger.debug(f"Could not drop index {index_name}: {drop_error}")
                                
                except Exception as list_error:
                    # Collection might not exist yet, this is fine
                    logger.debug(f"Could not list indexes for {collection_name}: {list_error}")
            
            logger.info("✅ Index cleanup completed")
            
        except Exception as e:
            logger.warning(f"⚠️ Index cleanup failed (non-critical): {e}")
    
    async def create_indexes(self) -> bool:
        """Create indexes for all registered models."""
        try:
            if not self._initialized:
                logger.error("❌ ODM not initialized, cannot create indexes")
                return False
            
            logger.info("📊 Creating indexes for ODM models...")
            
            for model in self._models:
                try:
                    # Beanie handles index creation based on model field definitions
                    collection_name = model.get_collection_name()
                    
                    # Get collection to check indexes
                    collection = self._database[collection_name]
                    indexes = await collection.list_indexes().to_list(length=None)
                    
                    logger.info(f"  📊 {model.__name__}: {len(indexes)} indexes")
                    
                except Exception as e:
                    logger.warning(f"⚠️ Failed to check indexes for {model.__name__}: {e}")
            
            logger.info("✅ Index creation completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Index creation failed: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about registered models."""
        models_info = []
        
        for model in self._models:
            model_info = {
                "name": model.__name__,
                "collection": model.get_collection_name(),
                "fields": list(model.__fields__.keys()),
                "initialized": self._initialized
            }
            models_info.append(model_info)
        
        return {
            "total_models": len(self._models),
            "initialized": self._initialized,
            "database": self._database.name if self._database else None,
            "models": models_info
        }
    
    @property
    def is_initialized(self) -> bool:
        """Check if ODM is initialized."""
        return self._initialized
    
    @property
    def models(self) -> List[Type[Document]]:
        """Get list of registered models."""
        return self._models.copy()


# Global ODM manager instance
_odm_manager: Optional[ODMManager] = None


def get_odm_manager() -> ODMManager:
    """Get the global ODM manager instance."""
    global _odm_manager
    
    if _odm_manager is None:
        _odm_manager = ODMManager()
        logger.info("🔧 ODM manager initialized")
        
    return _odm_manager


async def initialize_odm(models: Optional[List[Type[Document]]] = None) -> bool:
    """Initialize Beanie ODM with optional model list."""
    manager = get_odm_manager()
    
    if models:
        manager.register_models(models)
    
    return await manager.initialize()


def register_model(model: Type[Document]):
    """Register a model with the global ODM manager."""
    manager = get_odm_manager()
    manager.register_model(model)


def register_models(models: List[Type[Document]]):
    """Register multiple models with the global ODM manager."""
    manager = get_odm_manager()
    manager.register_models(models)


async def create_odm_indexes() -> bool:
    """Create indexes for all registered models."""
    manager = get_odm_manager()
    return await manager.create_indexes()


def get_odm_info() -> Dict[str, Any]:
    """Get ODM information."""
    manager = get_odm_manager()
    return manager.get_model_info()


def reset_odm_manager():
    """Reset the global ODM manager (mainly for testing)."""
    global _odm_manager
    _odm_manager = None 