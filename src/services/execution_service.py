"""
Execution Service for QAK MongoDB Integration

Provides business-logic operations for test executions backed by MongoDB.
Keeps backward compatibility with legacy JSON execution files via StandardResult.
"""

from __future__ import annotations

import logging
from pathlib import Path
from datetime import datetime
from typing import Any, Dict, List, Optional

from .base_service import (
    BaseService,
    DatabaseServiceMixin,
    ServiceException,
    ServiceResult,
    handle_service_exceptions,
    log_service_operation,
)

from ..database.repositories import ExecutionRepository
from ..database.models import Execution, Project
from src.models.standard_result import StandardResult, ExecutionStatus, TestType

logger = logging.getLogger(__name__)


class ExecutionService(BaseService, DatabaseServiceMixin):
    """Service layer that manages test executions via MongoDB."""

    LEGACY_EXEC_DIR = Path("executions")

    def __init__(self, legacy_exec_dir: str | Path = LEGACY_EXEC_DIR):
        super().__init__("executions")
        self.legacy_exec_dir = Path(legacy_exec_dir)
        self._execution_repo: Optional[ExecutionRepository] = None

    # ---------------------------------------------------------------------
    # Repository helper
    # ---------------------------------------------------------------------
    @property
    def execution_repo(self) -> ExecutionRepository:
        if self._execution_repo is None:
            self._execution_repo = self.get_repository("execution")
        return self._execution_repo

    # ---------------------------------------------------------------------
    # Health / Info
    # ---------------------------------------------------------------------
    async def health_check(self) -> ServiceResult[Dict[str, Any]]:
        info: Dict[str, Any] = {
            "service": self.service_name,
            "database_enabled": self.should_use_database(),
            "legacy_dir_exists": self.legacy_exec_dir.exists(),
        }
        if self.should_use_database():
            info["database"] = await self.check_database_health()
        return ServiceResult.success_result(info)

    async def get_service_info(self) -> ServiceResult[Dict[str, Any]]:
        stats = {}
        if self.should_use_database():
            try:
                stats["total_executions"] = await self.execution_repo.count({})
            except Exception as exc:  # pragma: no cover
                self.logger.warning(f"Cannot fetch execution stats: {exc}")
        else:
            stats["legacy_files"] = len(list(self.legacy_exec_dir.glob("*.json")))
        return ServiceResult.success_result(
            {
                "service": self.service_name,
                "features": {
                    "database_backend": self.should_use_database(),
                    "legacy_support": True,
                    "analytics": True,
                },
                "statistics": stats,
            }
        )

    # ---------------------------------------------------------------------
    # CRUD operations
    # ---------------------------------------------------------------------
    @handle_service_exceptions
    @log_service_operation
    async def create_execution(
        self,
        standard_result: StandardResult,
    ) -> ServiceResult[Execution]:
        """Persist a new execution from a StandardResult payload."""
        if not self.should_use_database():
            return ServiceResult.error_result("Database is not enabled.", "DB_DISABLED")

        exec_doc = Execution(**standard_result.model_dump())
        created = await self.execution_repo.create(exec_doc)
        return ServiceResult.success_result(created)

    @handle_service_exceptions
    @log_service_operation
    async def get_execution(self, execution_id: str) -> ServiceResult[Optional[Execution]]:
        if not self.should_use_database():
            return ServiceResult.error_result("Database is not enabled.", "DB_DISABLED")

        doc = await self.execution_repo.get_by_execution_id(execution_id)
        return ServiceResult.success_result(doc)

    # ------------------------------------------------------------------
    # Analytics
    # ------------------------------------------------------------------
    @handle_service_exceptions
    async def get_execution_statistics(self) -> ServiceResult[Dict[str, Any]]:
        if self.should_use_database():
            stats = await self.execution_repo.get_execution_statistics()
            return ServiceResult.success_result(stats)
        else:
            return ServiceResult.error_result("Analytics not available in legacy mode")

    # ------------------------------------------------------------------
    # Migration
    # ------------------------------------------------------------------
    @handle_service_exceptions
    async def migrate_legacy_executions(self, batch_size: int = 50) -> ServiceResult[Dict[str, Any]]:
        if not self.legacy_exec_dir.exists():
            return ServiceResult.error_result("Legacy execution directory not found")

        files = list(self.legacy_exec_dir.glob("execution_*.json"))
        migrated = 0
        for file in files[:batch_size]:
            try:
                sr = StandardResult.model_validate_json(file.read_text())
                exec_doc = Execution(**sr.model_dump())
                await self.execution_repo.create(exec_doc)
                migrated += 1
            except Exception as exc:
                self.logger.warning(f"Failed to migrate {file.name}: {exc}")
        return ServiceResult.success_result({"migrated": migrated, "total": len(files)})

    @handle_service_exceptions
    @log_service_operation
    async def clear_execution_history(self, execution_id: str) -> ServiceResult[bool]:
        """
        Clear the history of a specific execution.
        
        Args:
            execution_id: The ID of the execution to clear history for.
            
        Returns:
            ServiceResult containing success status.
        """
        self.validate_id_format(execution_id, "execution")
        
        if self.should_use_database():
            execution = await self.execution_repo.get_by_execution_id(execution_id)
            if not execution:
                return ServiceResult.error_result("Execution not found", "NOT_FOUND")
            execution.history = []
            await self.execution_repo.update_execution(execution)
            return ServiceResult.success_result(True)
        else:
            return ServiceResult.error_result("Operation not supported in legacy mode", "NOT_SUPPORTED")

    @handle_service_exceptions
    @log_service_operation
    async def export_execution(self, execution_id: str, file_path: str) -> ServiceResult[bool]:
        """
        Export an execution to a JSON file.
        
        Args:
            execution_id: The ID of the execution to export.
            file_path: The file path to save the exported JSON.
            
        Returns:
            ServiceResult containing success status.
        """
        self.validate_id_format(execution_id, "execution")
        
        if self.should_use_database():
            execution = await self.execution_repo.get_by_execution_id(execution_id)
            if not execution:
                return ServiceResult.error_result("Execution not found", "NOT_FOUND")
            import json
            from pathlib import Path
            execution_dict = execution.dict(by_alias=True)
            Path(file_path).write_text(json.dumps(execution_dict, indent=2))
            return ServiceResult.success_result(True)
        else:
            return ServiceResult.error_result("Operation not supported in legacy mode", "NOT_SUPPORTED")
    
    @handle_service_exceptions
    @log_service_operation
    async def import_execution(self, file_path: str) -> ServiceResult[Execution]:
        """
        Import an execution from a JSON file.
        
        Args:
            file_path: The file path of the JSON file to import.
            
        Returns:
            ServiceResult containing the imported execution.
        """
        if self.should_use_database():
            import json
            from pathlib import Path
            if not Path(file_path).exists():
                return ServiceResult.error_result("File not found", "NOT_FOUND")
            execution_data = json.loads(Path(file_path).read_text())
            execution = Execution(**execution_data)
            created_execution = await self.execution_repo.create_execution(execution)
            return ServiceResult.success_result(created_execution)
        else:
            return ServiceResult.error_result("Operation not supported in legacy mode", "NOT_SUPPORTED")

    @handle_service_exceptions
    @log_service_operation
    async def delete_execution(self, execution_id: str, delete_file: bool = False) -> ServiceResult[bool]:
        """Delete execution from DB and optionally remove legacy JSON file."""
        self.validate_id_format(execution_id, "execution")
        if self.should_use_database():
            success = await self.execution_repo.delete_by_execution_id(execution_id)
            if delete_file:
                from pathlib import Path
                legacy_path = Path(f"codegen_sessions/executions/execution_{execution_id}.json")
                if legacy_path.exists():
                    try:
                        legacy_path.unlink()
                    except Exception as e:
                        self.logger.warning(f"Failed to delete legacy file {legacy_path}: {e}")
            return ServiceResult.success_result(success)
        else:
            return ServiceResult.error_result("Operation not supported in legacy mode", "NOT_SUPPORTED")

    @handle_service_exceptions
    @log_service_operation
    async def delete_by_history_path(self, history_path: str) -> ServiceResult[bool]:
        """Delete execution document and file given a history file path."""
        if self.should_use_database():
            try:
                # Attempt to find execution where artifacts.history_file matches
                from beanie import PydanticObjectId
                from ..database.models.execution import Execution  # Avoid circular import issues

                exec_doc = await Execution.find_one({"artifacts.history_file": history_path})
                if exec_doc:
                    await self.execution_repo.delete_by_execution_id(exec_doc.execution_id)
            except Exception as exc:
                # Log but continue with file/legacy cleanup
                self.logger.warning(f"Error querying execution by history_path: {exc}")

        # Remove legacy file if it exists (regardless of DB mode)
        from pathlib import Path
        p = Path(history_path)
        if p.exists():
            try:
                p.unlink()
            except Exception as e:
                self.logger.warning(f"Cannot delete history file {p}: {e}")
        return ServiceResult.success_result(True) 