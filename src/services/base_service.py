"""
Base Service for QAK MongoDB Integration

Provides common functionality for all business logic services.
"""

import logging
from typing import Any, Dict, Optional, List, TypeVar, Generic
from abc import ABC, abstractmethod
from datetime import datetime, timezone

from ..database.exceptions import DocumentNotFoundError, DocumentExistsError, ValidationError

logger = logging.getLogger(__name__)

# Type variable for generic service responses
T = TypeVar('T')


class ServiceResult(Generic[T]):
    """Standard service operation result wrapper."""
    
    def __init__(
        self,
        success: bool,
        data: Optional[T] = None,
        error: Optional[str] = None,
        error_code: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.success = success
        self.data = data
        self.error = error
        self.error_code = error_code
        self.metadata = metadata or {}
        self.timestamp = datetime.now(timezone.utc)  # Use timezone-aware UTC datetime
    
    @classmethod
    def success_result(cls, data: T, metadata: Optional[Dict[str, Any]] = None) -> "ServiceResult[T]":
        """Create a successful result."""
        return cls(success=True, data=data, metadata=metadata)
    
    @classmethod
    def error_result(
        cls,
        error: str,
        error_code: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> "ServiceResult[T]":
        """Create an error result."""
        return cls(success=False, error=error, error_code=error_code, metadata=metadata)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary for API responses."""
        result = {
            "success": self.success,
            "timestamp": self.timestamp.isoformat(),
        }
        
        if self.success:
            result["data"] = self.data
        else:
            result["error"] = self.error
            if self.error_code:
                result["error_code"] = self.error_code
        
        if self.metadata:
            result["metadata"] = self.metadata
            
        return result


class ServiceException(Exception):
    """Base exception for service layer errors."""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}


class BaseService(ABC):
    """
    Base service class providing common functionality for all services.
    
    Features:
    - Standardized error handling and logging
    - Result wrapping for consistent responses
    - Validation helpers
    - Database exception mapping
    - Performance monitoring hooks
    """
    
    def __init__(self, service_name: str):
        """Initialize base service."""
        self.service_name = service_name
        self.logger = logging.getLogger(f"services.{service_name}")
        self._performance_metrics = {}
    
    # Error Handling & Result Wrapping
    
    def handle_service_error(self, error: Exception, operation: str) -> ServiceResult:
        """Handle and log service errors consistently."""
        error_msg = f"{self.service_name}.{operation} failed: {str(error)}"
        
        # Map database exceptions to service errors
        if isinstance(error, DocumentNotFoundError):
            self.logger.warning(f"Document not found in {operation}: {error}")
            return ServiceResult.error_result(
                error="Resource not found",
                error_code="NOT_FOUND",
                metadata={"operation": operation, "entity": error.collection}
            )
        
        elif isinstance(error, DocumentExistsError):
            self.logger.warning(f"Document already exists in {operation}: {error}")
            return ServiceResult.error_result(
                error="Resource already exists",
                error_code="ALREADY_EXISTS",
                metadata={"operation": operation, "entity": error.collection}
            )
        
        elif isinstance(error, ValidationError):
            self.logger.warning(f"Validation error in {operation}: {error}")
            return ServiceResult.error_result(
                error="Validation failed",
                error_code="VALIDATION_ERROR",
                metadata={"operation": operation, "details": str(error)}
            )
        
        elif isinstance(error, ServiceException):
            self.logger.error(f"Service error in {operation}: {error}")
            return ServiceResult.error_result(
                error=error.message,
                error_code=error.error_code,
                metadata={"operation": operation, **error.details}
            )
        
        else:
            # Unexpected error
            self.logger.error(f"Unexpected error in {operation}: {error}", exc_info=True)
            return ServiceResult.error_result(
                error="Internal service error",
                error_code="INTERNAL_ERROR",
                metadata={"operation": operation}
            )
    
    def log_operation(self, operation: str, details: Dict[str, Any] = None):
        """Log service operation with details."""
        log_details = {"service": self.service_name, "operation": operation}
        if details:
            log_details.update(details)
        
        self.logger.info(f"Service operation: {operation}", extra=log_details)
    
    # Validation Helpers
    
    def validate_required_fields(self, data: Dict[str, Any], required_fields: List[str]) -> None:
        """Validate that required fields are present and not empty."""
        missing_fields = []
        empty_fields = []
        
        for field in required_fields:
            if field not in data:
                missing_fields.append(field)
            elif not data[field] or (isinstance(data[field], str) and not data[field].strip()):
                empty_fields.append(field)
        
        if missing_fields or empty_fields:
            error_details = {}
            if missing_fields:
                error_details["missing"] = missing_fields
            if empty_fields:
                error_details["empty"] = empty_fields
            
            raise ServiceException(
                f"Validation failed: {', '.join(missing_fields + empty_fields)}",
                error_code="VALIDATION_ERROR",
                details=error_details
            )
    
    def validate_id_format(self, entity_id: str, entity_type: str = "entity") -> None:
        """Validate ID format."""
        if not entity_id or not isinstance(entity_id, str) or not entity_id.strip():
            raise ServiceException(
                f"Invalid {entity_type} ID format",
                error_code="INVALID_ID",
                details={"entity_type": entity_type, "provided_id": entity_id}
            )
    
    # Feature Flags & Migration Support
    
    def is_feature_enabled(self, feature_name: str) -> bool:
        """Check if a feature flag is enabled."""
        # TODO: Implement feature flag system (Redis, env vars, etc.)
        # For now, return True for database features
        database_features = [
            "database_projects",
            "database_executions", 
            "database_codegen",
            "database_artifacts"
        ]
        return feature_name in database_features
    
    def should_use_database(self) -> bool:
        """Check if service should use database or legacy file system."""
        return True  # Forzando el uso de MongoDB
    
    # Performance Monitoring
    
    def track_operation_time(self, operation: str, duration_ms: float):
        """Track operation performance."""
        if operation not in self._performance_metrics:
            self._performance_metrics[operation] = {
                "total_calls": 0,
                "total_time_ms": 0,
                "avg_time_ms": 0,
                "min_time_ms": float('inf'),
                "max_time_ms": 0
            }
        
        metrics = self._performance_metrics[operation]
        metrics["total_calls"] += 1
        metrics["total_time_ms"] += duration_ms
        metrics["avg_time_ms"] = metrics["total_time_ms"] / metrics["total_calls"]
        metrics["min_time_ms"] = min(metrics["min_time_ms"], duration_ms)
        metrics["max_time_ms"] = max(metrics["max_time_ms"], duration_ms)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get service performance metrics."""
        return {
            "service": self.service_name,
            "metrics": self._performance_metrics.copy(),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    # Abstract methods for subclasses
    
    @abstractmethod
    async def health_check(self) -> ServiceResult[Dict[str, Any]]:
        """Perform service health check."""
        pass
    
    @abstractmethod
    async def get_service_info(self) -> ServiceResult[Dict[str, Any]]:
        """Get service information and statistics."""
        pass


class DatabaseServiceMixin:
    """
    Mixin for services that use database repositories.
    
    Provides common database operations and connection management.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._repositories = {}
    
    def get_repository(self, repository_type: str):
        """Get a repository instance with caching."""
        if repository_type not in self._repositories:
            # Import repositories dynamically to avoid circular imports
            if repository_type == "project":
                from ..database.repositories import ProjectRepository
                self._repositories[repository_type] = ProjectRepository()
            elif repository_type == "execution":
                from ..database.repositories import ExecutionRepository
                self._repositories[repository_type] = ExecutionRepository()
            elif repository_type == "codegen" or repository_type == "codegen_session":
                from ..database.repositories import CodegenSessionRepository
                self._repositories[repository_type] = CodegenSessionRepository()
            elif repository_type == "artifact":
                from ..database.repositories import ArtifactRepository
                self._repositories[repository_type] = ArtifactRepository()
            else:
                raise ServiceException(
                    f"Unknown repository type: {repository_type}",
                    error_code="UNKNOWN_REPOSITORY"
                )
        
        return self._repositories[repository_type]
    
    async def check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity and health."""
        try:
            from ..database.connection import database_health_check
            return await database_health_check()
        except Exception as e:
            return {
                "database_connection": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }


# Decorators for common service patterns

def handle_service_exceptions(func):
    """Decorator to handle service exceptions consistently."""
    async def wrapper(self, *args, **kwargs):
        try:
            return await func(self, *args, **kwargs)
        except Exception as e:
            operation_name = func.__name__
            return self.handle_service_error(e, operation_name)
    
    return wrapper


def log_service_operation(func):
    """Decorator to log service operations."""
    async def wrapper(self, *args, **kwargs):
        operation_name = func.__name__
        self.log_operation(operation_name, {"args_count": len(args), "kwargs_keys": list(kwargs.keys())})
        return await func(self, *args, **kwargs)
    
    return wrapper 