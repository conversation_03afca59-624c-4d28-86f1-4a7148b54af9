"""
Artifact Service for QAK MongoDB Integration

Handles artifact metadata operations, storage management, and legacy file migration.
Now includes cloud storage support via EnhancedArtifactCollector.
"""

from __future__ import annotations

import logging
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from .base_service import (
    BaseService,
    DatabaseServiceMixin,
    ServiceResult,
    handle_service_exceptions,
    log_service_operation,
)

from ..core.enhanced_artifact_collector import EnhancedArtifactCollector, get_shared_enhanced_collector
from ..database.repositories import ArtifactRepository
from ..database.models.artifact import Artifact, ArtifactType, ArtifactStatus, StorageBackend

logger = logging.getLogger(__name__)


class ArtifactService(BaseService, DatabaseServiceMixin):
    """Service layer for managing test artifacts with cloud storage support."""

    LEGACY_ARTIFACT_DIR = Path("artifacts")

    def __init__(self, legacy_dir: str | Path = LEGACY_ARTIFACT_DIR, blob_storage_backend: Optional[str] = None):
        # Initialize both parent classes properly
        BaseService.__init__(self, "artifacts")
        DatabaseServiceMixin.__init__(self)
        
        self.legacy_dir = Path(legacy_dir)
        # Auto-detect storage backend from environment if not provided
        self.blob_storage_backend = blob_storage_backend or os.getenv("ARTIFACT_STORAGE_BACKEND", "local")
        self._artifact_repo: Optional[ArtifactRepository] = None
        self._enhanced_collector: Optional[EnhancedArtifactCollector] = None

    # ------------------------------------------------------------------
    @property
    def artifact_repo(self) -> ArtifactRepository:
        if self._artifact_repo is None:
            self._artifact_repo = self.get_repository("artifact")
        return self._artifact_repo

    @property
    async def enhanced_collector(self) -> EnhancedArtifactCollector:
        """Get or create enhanced artifact collector with cloud storage."""
        if self._enhanced_collector is None:
            # Use the shared global instance to ensure consistency
            # between orchestrator and API service
            self._enhanced_collector = await get_shared_enhanced_collector(
                base_storage_path=str(self.legacy_dir)
            )
        return self._enhanced_collector

    # ------------------------------------------------------------------
    async def health_check(self) -> ServiceResult[Dict[str, Any]]:
        info = {
            "service": self.service_name,
            "database_enabled": self.should_use_database(),
            "legacy_dir_exists": self.legacy_dir.exists(),
            "blob_storage_backend": self.blob_storage_backend,
        }
        if self.should_use_database():
            info["database"] = await self.check_database_health()
        
        # Test cloud storage if configured
        if self.blob_storage_backend != "local":
            try:
                collector = await self.enhanced_collector
                info["cloud_storage"] = "available"
                info["cloud_stats"] = collector.get_enhanced_stats()
            except Exception as e:
                info["cloud_storage"] = f"error: {str(e)}"
        
        return ServiceResult.success_result(info)

    async def get_service_info(self) -> ServiceResult[Dict[str, Any]]:
        stats: Dict[str, Any] = {}
        if self.should_use_database():
            stats["total_artifacts"] = await self.artifact_repo.count({})
        else:
            stats["legacy_files"] = len(list(self.legacy_dir.rglob("*.*")))
        
        # Add enhanced collector stats
        if self.blob_storage_backend != "local":
            try:
                collector = await self.enhanced_collector
                stats.update(collector.get_enhanced_stats())
            except Exception as e:
                stats["collector_error"] = str(e)
        
        return ServiceResult.success_result({"service": self.service_name, "statistics": stats})

    @handle_service_exceptions
    @log_service_operation
    async def collect_screenshot(
        self,
        execution_id: str,
        screenshot_data: bytes,
        step_name: str = "",
        upload_to_cloud: bool = None
    ) -> ServiceResult[Artifact]:
        """Collect screenshot with optional cloud upload."""
        try:
            collector = await self.enhanced_collector
            artifact = await collector.collect_screenshot(
                execution_id=execution_id,
                screenshot_data=screenshot_data,
                step_name=step_name,
                upload_to_cloud=upload_to_cloud
            )
            
            if artifact and self.should_use_database():
                # Save metadata to database
                db_artifact = Artifact(
                    artifact_id=artifact.artifact_id,
                    execution_id=artifact.execution_id,
                    type=artifact.type.value,
                    file_path=artifact.file_path,
                    original_name=artifact.original_name,
                    size_bytes=artifact.size_bytes,
                    status=artifact.status.value,
                    storage_backend=self.blob_storage_backend,
                    blob_url=artifact.metadata.get('blob_url'),
                    blob_key=artifact.metadata.get('blob_key'),
                    metadata=artifact.metadata,
                    step_name=step_name
                )
                db_artifact = await self.artifact_repo.create(db_artifact)
                return ServiceResult.success_result(db_artifact)
            
            return ServiceResult.success_result(artifact)
            
        except Exception as e:
            logger.error(f"Failed to collect screenshot: {e}")
            return ServiceResult.error_result(f"Screenshot collection failed: {str(e)}")

    @handle_service_exceptions
    @log_service_operation
    async def collect_error_report(
        self,
        execution_id: str,
        error_details: Dict[str, Any],
        stack_trace: str = "",
        upload_to_cloud: bool = None
    ) -> ServiceResult[Artifact]:
        """Collect error report with optional cloud upload."""
        try:
            collector = await self.enhanced_collector
            artifact = await collector.collect_error_report(
                execution_id=execution_id,
                error_details=error_details,
                stack_trace=stack_trace,
                upload_to_cloud=upload_to_cloud
            )
            
            if artifact and self.should_use_database():
                # Save metadata to database
                db_artifact = Artifact(
                    artifact_id=artifact.artifact_id,
                    execution_id=artifact.execution_id,
                    type=artifact.type.value,
                    file_path=artifact.file_path,
                    original_name=artifact.original_name,
                    size_bytes=artifact.size_bytes,
                    status=artifact.status.value,
                    storage_backend=self.blob_storage_backend,
                    blob_url=artifact.metadata.get('blob_url'),
                    blob_key=artifact.metadata.get('blob_key'),
                    metadata=artifact.metadata
                )
                db_artifact = await self.artifact_repo.create(db_artifact)
                return ServiceResult.success_result(db_artifact)
            
            return ServiceResult.success_result(artifact)
            
        except Exception as e:
            logger.error(f"Failed to collect error report: {e}")
            return ServiceResult.error_result(f"Error report collection failed: {str(e)}")

    @handle_service_exceptions
    async def get_artifact_content(self, artifact_id: str) -> ServiceResult[bytes]:
        """Get artifact content from local or cloud storage."""
        try:
            collector = await self.enhanced_collector
            content = await collector.get_artifact_content(artifact_id)
            
            if content is None:
                return ServiceResult.error_result(f"Artifact {artifact_id} not found")
            
            return ServiceResult.success_result(content)
            
        except Exception as e:
            logger.error(f"Failed to get artifact content: {e}")
            return ServiceResult.error_result(f"Failed to retrieve artifact: {str(e)}")

    @handle_service_exceptions
    async def migrate_artifacts_to_cloud(
        self,
        artifact_ids: Optional[List[str]] = None,
        remove_local: bool = True
    ) -> ServiceResult[Dict[str, Any]]:
        """Migrate artifacts to cloud storage."""
        if self.blob_storage_backend == "local":
            return ServiceResult.error_result("No cloud storage backend configured")
        
        try:
            collector = await self.enhanced_collector
            results = await collector.bulk_migrate_to_cloud(
                artifact_ids=artifact_ids,
                remove_local=remove_local
            )
            
            # Update database records if using database
            if self.should_use_database():
                for artifact_id, success in results.items():
                    if success:
                        db_artifact = await self.artifact_repo.get_by_artifact_id(artifact_id)
                        if db_artifact:
                            # Update with cloud storage info
                            collector_artifact = collector.artifacts.get(artifact_id)
                            if collector_artifact:
                                db_artifact.blob_url = collector_artifact.metadata.get('blob_url')
                                db_artifact.blob_key = collector_artifact.metadata.get('blob_key')
                                db_artifact.storage_backend = self.blob_storage_backend
                                if remove_local:
                                    db_artifact.metadata["local_file_removed"] = True
                                await db_artifact.save()
            
            return ServiceResult.success_result({
                "migration_results": results,
                "total_attempted": len(results),
                "successful": sum(1 for success in results.values() if success),
                "failed": sum(1 for success in results.values() if not success)
            })
            
        except Exception as e:
            logger.error(f"Failed to migrate artifacts: {e}")
            return ServiceResult.error_result(f"Migration failed: {str(e)}")

    @handle_service_exceptions
    async def cleanup_cloud_artifacts(self, dry_run: bool = True) -> ServiceResult[Dict[str, Any]]:
        """Clean up expired artifacts from cloud storage."""
        if self.blob_storage_backend == "local":
            return ServiceResult.error_result("No cloud storage backend configured")
        
        try:
            collector = await self.enhanced_collector
            cleanup_stats = await collector.cleanup_cloud_artifacts(dry_run=dry_run)
            return ServiceResult.success_result(cleanup_stats)
            
        except Exception as e:
            logger.error(f"Failed to cleanup cloud artifacts: {e}")
            return ServiceResult.error_result(f"Cleanup failed: {str(e)}")

    @handle_service_exceptions
    async def find_by_execution(self, execution_id: str) -> ServiceResult[List[Artifact]]:
        if self.should_use_database():
            arts = await self.artifact_repo.find_by_execution_id(execution_id)
            return ServiceResult.success_result(arts)
        else:
            return ServiceResult.error_result("Legacy search not supported")

    @handle_service_exceptions
    @log_service_operation
    async def get_artifact_by_id(self, artifact_id: str) -> ServiceResult[Optional[Artifact]]:
        """Get artifact by ID from database."""
        if not self.should_use_database():
            return ServiceResult.error_result("Database not enabled")
        
        try:
            artifact = await self.artifact_repo.get_by_artifact_id(artifact_id)
            return ServiceResult.success_result(artifact)
        except Exception as e:
            logger.error(f"Failed to get artifact {artifact_id}: {e}")
            return ServiceResult.error_result(f"Failed to get artifact: {str(e)}")

    async def shutdown(self):
        """Shutdown the artifact service."""
        if self._enhanced_collector:
            await self._enhanced_collector.shutdown()
        logger.info("ArtifactService shutdown complete")