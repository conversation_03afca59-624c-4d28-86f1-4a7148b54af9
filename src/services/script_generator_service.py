"""
Script Generator Service

Generates automation scripts (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>press) from execution history.
Uses captured element information and interaction patterns to create robust scripts.
"""

from typing import List, Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass, asdict
import logging
from datetime import datetime
from src.models.standard_result import TestStep, ElementLocator
from src.database.models.execution import Execution
from src.models.generated_script import (
    GeneratedScript, EditableStep, ElementSelector, ActionType,
    create_editable_step, create_element_selector
)


class ScriptFramework(str, Enum):
    """Supported automation frameworks."""
    PLAYWRIGHT_PYTHON = "playwright_python"
    PLAYWRIGHT_JS = "playwright_js"
    SELENIUM_PYTHON = "selenium_python"
    CYPRESS = "cypress"
    ROBOT_FRAMEWORK = "robot_framework"


@dataclass
class ScriptGenerationOptions:
    """Options for script generation."""
    framework: ScriptFramework
    include_assertions: bool = True
    include_waits: bool = True
    include_screenshots: bool = False
    add_error_handling: bool = True
    use_best_locators: bool = True  # Use most reliable locator strategy
    add_comments: bool = True
    page_object_pattern: bool = False


class ScriptGeneratorService:
    """Service for generating automation scripts from execution history."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.locator_priority = {
            ScriptFramework.PLAYWRIGHT_PYTHON: [
                'testid_selector',  # Most reliable
                'label_selector',
                'role_selector', 
                'text_selector',
                'css_selector',
                'xpath'  # Least reliable
            ],
            ScriptFramework.SELENIUM_PYTHON: [
                'testid_selector',
                'css_selector',
                'xpath',
                'text_selector'
            ]
        }
    
    async def generate_script_from_execution(
        self, 
        execution: Execution, 
        options: ScriptGenerationOptions,
        save_to_execution: bool = True
    ) -> Dict[str, Any]:
        """
        Generate automation script from execution history.
        
        Args:
            execution: Execution document with steps and element info
            options: Generation options and framework choice
            save_to_execution: Whether to save the generated script to the execution
            
        Returns:
            Dict with generated script and metadata
        """
        # Get steps from different possible locations
        steps = None
        if hasattr(execution, 'steps') and execution.steps:
            steps = execution.steps
            self.logger.debug(f"📍 Found steps in execution.steps: {len(steps)} steps")
        elif hasattr(execution, 'result') and execution.result and hasattr(execution.result, 'steps'):
            steps = execution.result.steps
            self.logger.debug(f"📍 Found steps in execution.result.steps: {len(steps)} steps")
        elif hasattr(execution, 'result') and execution.result and isinstance(execution.result, dict):
            steps = execution.result.get('steps', [])
            self.logger.debug(f"📍 Found steps in execution.result dict: {len(steps)} steps")
            
        if not steps:
            self.logger.error(f"❌ No steps found in execution. Available attributes: {dir(execution)}")
            raise ValueError("Execution has no steps to generate script from")
                
        # Convert execution steps to enhanced format if needed
        enhanced_steps = await self._enhance_steps_for_generation(steps)
        self.logger.debug(f"✨ Enhanced {len(enhanced_steps)} steps")
        
        # Convert to editable steps for the new system
        editable_steps = await self._convert_to_editable_steps(enhanced_steps)
        self.logger.debug(f"📝 Created {len(editable_steps)} editable steps")
        
        # Use placeholder for script content (will be generated later)
        preliminary_script = f"# Generated {options.framework.value} script\n# Placeholder content"
        
        # Create GeneratedScript object
        self.logger.debug(f"🏗️ Creating GeneratedScript with {len(editable_steps)} editable steps")
        generated_script = GeneratedScript(
            execution_id=execution.execution_id,
            framework=options.framework.value,
            target_url=getattr(execution, 'target_url', None),
            script_name=f"Test from execution {execution.execution_id[:8]}",
            description=f"Generated from execution on {getattr(execution, 'started_at', datetime.now()).strftime('%Y-%m-%d %H:%M')}",
            raw_script_content=preliminary_script,  # Provide required field
            editable_steps=editable_steps,
            include_screenshots=options.include_screenshots,
            include_waits=options.include_waits,
            include_validations=options.include_assertions
        )
        
        # Manually update statistics since Pydantic doesn't call it automatically
        generated_script._update_statistics()
        self.logger.info(f"📊 Updated script statistics: {generated_script.total_steps} total, {generated_script.enabled_steps} enabled")
        
        if generated_script.editable_steps:
            first_step = generated_script.editable_steps[0]
            self.logger.info(f"🔍 First editable step type: {type(first_step)}")
            if hasattr(first_step, '__dict__'):
                self.logger.info(f"🔍 First editable step dict: {first_step.__dict__}")
            elif hasattr(first_step, '__dataclass_fields__'):
                from dataclasses import asdict
                self.logger.info(f"🔍 First editable step dataclass: {asdict(first_step)}")
            else:
                self.logger.info(f"🔍 First editable step: {first_step}")
        else:
            self.logger.error(f"❌ No editable_steps found in generated_script!")
            # Additional debugging - check if the original list was empty
            self.logger.error(f"🔍 Original editable_steps variable: {len(editable_steps)} items")
            if editable_steps:
                self.logger.error(f"🔍 Original first step: {editable_steps[0]}")
        
        # Test model_dump to see what happens to editable_steps
        try:
            self.logger.info(f"🧪 Testing model_dump()...")
            dump_result = generated_script.model_dump()
            self.logger.info(f"🧪 model_dump editable_steps count: {len(dump_result.get('editable_steps', []))}")
            if dump_result.get('editable_steps'):
                self.logger.info(f"🧪 model_dump first step: {dump_result['editable_steps'][0]}")
        except Exception as dump_error:
            self.logger.error(f"❌ model_dump failed: {dump_error}")
        # Generate the actual script content using AI-powered generation
        try:
            script_result = await self._generate_intelligent_script(execution, enhanced_steps, options)
            generated_script.raw_script_content = script_result['script_content']
            # Store additional AI-generated metadata
            if 'human_description' in script_result:
                generated_script.description = script_result['human_description']
            if 'script_summary' in script_result:
                generated_script.script_summary = script_result.get('script_summary', '')
            
            # Replace editable_steps with steps extracted from actual generated code
            if 'code_steps' in script_result and script_result['code_steps']:
                code_based_editable_steps = await self._convert_code_steps_to_editable(script_result['code_steps'])
                generated_script.editable_steps = code_based_editable_steps
                editable_steps = code_based_editable_steps  # Update for response
                self.logger.info(f"✅ Updated to {len(editable_steps)} code-based editable steps")
        except Exception as e:
            # Fallback to basic script generation if AI generation fails
            self.logger.warning(f"Failed to generate script with AI, using fallback: {e}")
            script_content = self._generate_basic_script_fallback(editable_steps, options)
            generated_script.raw_script_content = script_content
        
        # Save script to execution
        if save_to_execution:
            try:
                await self._save_script_to_execution(execution, generated_script)
                self.logger.info(f"✅ Save operation completed successfully")
            except Exception as e:
                self.logger.error(f"Failed to save script to execution: {e}", exc_info=True)
                # Continue without saving
        else:
            self.logger.info(f"⏭️ Skipping save to execution (save_to_execution={save_to_execution})")
        
        # Ensure generated_at is always a string
        generated_at_str = datetime.now().isoformat()
        
        # Convert editable_steps to dictionaries properly
        self.logger.info(f"🔧 Converting {len(editable_steps)} editable steps to dictionaries for response")
        converted_editable_steps = []
        for i, step in enumerate(editable_steps):
            if hasattr(step, '__dataclass_fields__'):
                # It's a dataclass, use asdict
                from dataclasses import asdict
                step_dict = asdict(step)
                converted_editable_steps.append(step_dict)
            elif hasattr(step, '__dict__'):
                # It's an object with __dict__
                step_dict = step.__dict__
                converted_editable_steps.append(step_dict)
            else:
                # It's already a dict or primitive
                converted_editable_steps.append(step)
        
        self.logger.info(f"✅ Successfully converted {len(converted_editable_steps)} editable steps")

        result = {
            'script_content': generated_script.raw_script_content,  # Use the final script content
            'framework': options.framework.value,
            'execution_id': execution.execution_id,
            'generated_at': generated_at_str,  # Ensure this is never None
            'steps_count': len(enhanced_steps),
            'target_url': execution.target_url if hasattr(execution, 'target_url') else None,
            'success_rate': execution.summary.success_rate if hasattr(execution, 'summary') and execution.summary else 0.0,
            'generated_script': generated_script.model_dump(),
            # Add editable_steps to root level for frontend compatibility
            'editable_steps': converted_editable_steps,
            'script_name': generated_script.script_name,
            'description': generated_script.description,
            'script_summary': generated_script.script_summary,  # AI-generated summary
            'total_steps': generated_script.total_steps,
            'enabled_steps': generated_script.enabled_steps,
            'validations_count': generated_script.validations_count,
            'include_screenshots': generated_script.include_screenshots,
            'include_waits': generated_script.include_waits,
            'include_validations': generated_script.include_validations,
            'raw_script_content': generated_script.raw_script_content,
            'metadata': {
                'original_duration_ms': execution.duration_ms if hasattr(execution, 'duration_ms') else None,
                'options_used': options.__dict__,
                'element_strategies_used': self._get_used_strategies(enhanced_steps),
                'editable_steps_count': len(editable_steps),
                'validations_count': generated_script.validations_count
            }
        }
        
        return result
    
    async def _enhance_steps_for_generation(self, steps: List[TestStep]) -> List[TestStep]:
        """Enhance steps with better locator information for script generation."""
        enhanced_steps = []
        
        for step in steps:
            # Create enhanced step copy
            enhanced_step = TestStep(
                step_number=step.step_number,
                action_type=step.action_type,
                description=step.description,
                success=step.success,
                duration_ms=step.duration_ms,
                error_message=step.error_message,
                screenshot_url=step.screenshot_url,
                element_info=step.element_info,
                url=step.url,
                metadata=step.metadata
            )
            
            # Extract and enhance locator information
            if step.element_info:
                enhanced_step.element_locators = self._extract_locators_from_element_info(
                    step.element_info
                )
                enhanced_step.input_data = self._extract_input_data(step)
                enhanced_step.expected_result = self._extract_expected_result(step)
                enhanced_step.wait_conditions = self._extract_wait_conditions(step)
                enhanced_step.assertions = self._generate_assertions(step)
            
            enhanced_steps.append(enhanced_step)
        
        return enhanced_steps
    
    def _extract_locators_from_element_info(self, element_info: Dict[str, Any]) -> ElementLocator:
        """Extract multiple locator strategies from element_info."""
        attributes = element_info.get('attributes', {})
        
        # Build comprehensive locator information
        locators = ElementLocator()
        
        # CSS Selector (primary)
        locators.css_selector = element_info.get('css_selector')
        
        # XPath (fallback)
        locators.xpath = element_info.get('xpath')
        
        # Playwright-specific locators (more reliable)
        if 'data-testid' in attributes:
            locators.testid_selector = attributes['data-testid']
        elif 'data-test' in attributes:
            locators.testid_selector = attributes['data-test']
        
        if 'aria-label' in attributes:
            locators.label_selector = attributes['aria-label']
        elif 'label' in attributes:
            locators.label_selector = attributes['label']
        
        if 'placeholder' in attributes:
            locators.placeholder_selector = attributes['placeholder']
        
        if 'title' in attributes:
            locators.title_selector = attributes['title']
        
        # Text-based locator
        text_content = element_info.get('text', '').strip()
        if text_content and len(text_content) < 50:  # Reasonable text length
            locators.text_selector = text_content
        
        # Role-based locator
        if 'role' in attributes:
            locators.role_selector = attributes['role']
        elif element_info.get('tag_name') in ['button', 'link', 'textbox', 'checkbox']:
            locators.role_selector = element_info.get('tag_name')
        
        return locators
    
    def _extract_input_data(self, step: TestStep) -> Optional[str]:
        """Extract input data from step description or metadata."""
        if step.action_type in ['type', 'fill', 'select']:
            # Try to extract from description
            desc = step.description.lower()
            if 'type' in desc or 'fill' in desc:
                # Simple pattern matching - could be enhanced with regex
                parts = step.description.split('"')
                if len(parts) >= 2:
                    return parts[1]  # Text between quotes
        
        return step.metadata.get('input_value') if step.metadata else None
    
    def _extract_expected_result(self, step: TestStep) -> Optional[str]:
        """Extract expected result from step."""
        if step.action_type == 'click':
            return "Element should be clicked successfully"
        elif step.action_type in ['type', 'fill']:
            return "Text should be entered in the field"
        elif step.action_type == 'select':
            return "Option should be selected"
        
        return step.metadata.get('expected_result') if step.metadata else None
    
    def _extract_wait_conditions(self, step: TestStep) -> List[str]:
        """Extract wait conditions based on action type."""
        conditions = []
        
        if step.action_type == 'click':
            conditions.append('element_visible')
            conditions.append('element_enabled')
        elif step.action_type in ['type', 'fill']:
            conditions.append('element_visible')
            conditions.append('element_enabled')
        elif step.action_type == 'select':
            conditions.append('element_visible')
        
        return conditions
    
    def _generate_assertions(self, step: TestStep) -> List[Dict[str, Any]]:
        """Generate appropriate assertions for the step."""
        assertions = []
        
        if step.action_type == 'click':
            assertions.append({
                'type': 'element_clicked',
                'description': 'Verify element was clicked'
            })
        elif step.action_type in ['type', 'fill'] and step.metadata:
            input_value = step.metadata.get('input_value')
            if input_value:
                assertions.append({
                    'type': 'input_value',
                    'expected_value': input_value,
                    'description': f'Verify input contains "{input_value}"'
                })
        
        return assertions
    
    def _get_best_locator(self, locators: ElementLocator, framework: ScriptFramework) -> str:
        """Get the best locator strategy for the framework."""
        priority_list = self.locator_priority.get(framework, [])
        
        for strategy in priority_list:
            value = getattr(locators, strategy, None)
            if value:
                return self._format_locator_for_framework(strategy, value, framework)
        
        # Fallback to CSS selector
        if locators.css_selector:
            return self._format_locator_for_framework('css_selector', locators.css_selector, framework)
        
        raise ValueError("No suitable locator found for element")
    
    def _format_locator_for_framework(self, strategy: str, value: str, framework: ScriptFramework) -> str:
        """Format locator for specific framework syntax."""
        if framework == ScriptFramework.PLAYWRIGHT_PYTHON:
            if strategy == 'testid_selector':
                return f'page.get_by_test_id("{value}")'
            elif strategy == 'label_selector':
                return f'page.get_by_label("{value}")'
            elif strategy == 'role_selector':
                return f'page.get_by_role("{value}")'
            elif strategy == 'text_selector':
                return f'page.get_by_text("{value}")'
            elif strategy == 'placeholder_selector':
                return f'page.get_by_placeholder("{value}")'
            elif strategy == 'css_selector':
                return f'page.locator("{value}")'
            elif strategy == 'xpath':
                return f'page.locator("xpath={value}")'
        
        elif framework == ScriptFramework.SELENIUM_PYTHON:
            if strategy == 'testid_selector':
                return f'driver.find_element(By.CSS_SELECTOR, "[data-testid=\\"{value}\\"]")'
            elif strategy == 'css_selector':
                return f'driver.find_element(By.CSS_SELECTOR, "{value}")'
            elif strategy == 'xpath':
                return f'driver.find_element(By.XPATH, "{value}")'
            elif strategy == 'text_selector':
                return f'driver.find_element(By.PARTIAL_LINK_TEXT, "{value}")'
        
        return value  # Fallback
    
    def _generate_playwright_python(self, steps: List[TestStep], options: ScriptGenerationOptions) -> str:
        """Generate Playwright Python script."""
        script_lines = [
            "import pytest",
            "from playwright.sync_api import Page, expect",
            "",
            "def test_execution(page: Page):",
        ]
        
        # Add navigation if we have a URL
        first_step = steps[0] if steps else None
        if first_step and first_step.url:
            script_lines.append(f'    page.goto("{first_step.url}")')
            script_lines.append("")
        
        for step in steps:
            if options.add_comments:
                script_lines.append(f"    # Step {step.step_number}: {step.description}")
            
            if step.element_locators:
                try:
                    locator = self._get_best_locator(step.element_locators, ScriptFramework.PLAYWRIGHT_PYTHON)
                    
                    # Add waits if enabled
                    if options.include_waits and step.wait_conditions:
                        script_lines.append(f"    {locator}.wait_for()")
                    
                    # Generate action
                    if step.action_type == 'click':
                        script_lines.append(f"    {locator}.click()")
                    elif step.action_type in ['type', 'fill'] and step.input_data:
                        script_lines.append(f"    {locator}.fill(\"{step.input_data}\")")
                    elif step.action_type == 'select' and step.input_data:
                        script_lines.append(f"    {locator}.select_option(\"{step.input_data}\")")
                    
                    # Add assertions if enabled
                    if options.include_assertions and step.assertions:
                        for assertion in step.assertions:
                            if assertion['type'] == 'input_value':
                                script_lines.append(f"    expect({locator}).to_have_value(\"{assertion['expected_value']}\")")
                    
                    # Add screenshot if enabled
                    if options.include_screenshots:
                        script_lines.append(f"    page.screenshot(path=\"step_{step.step_number}.png\")")
                    
                    script_lines.append("")
                    
                except ValueError as e:
                    script_lines.append(f"    # WARNING: Could not generate locator for step {step.step_number}: {e}")
                    script_lines.append("")
        
        return "\n".join(script_lines)
    
    def _generate_selenium_python(self, steps: List[TestStep], options: ScriptGenerationOptions) -> str:
        """Generate Selenium Python script."""
        script_lines = [
            "from selenium import webdriver",
            "from selenium.webdriver.common.by import By",
            "from selenium.webdriver.support.ui import WebDriverWait",
            "from selenium.webdriver.support import expected_conditions as EC",
            "",
            "def test_execution():",
            "    driver = webdriver.Chrome()",
            "    wait = WebDriverWait(driver, 10)",
            "    ",
            "    try:",
        ]
        
        # Add navigation
        first_step = steps[0] if steps else None
        if first_step and first_step.url:
            script_lines.append(f'        driver.get("{first_step.url}")')
            script_lines.append("")
        
        for step in steps:
            if options.add_comments:
                script_lines.append(f"        # Step {step.step_number}: {step.description}")
            
            if step.element_locators:
                try:
                    locator = self._get_best_locator(step.element_locators, ScriptFramework.SELENIUM_PYTHON)
                    element_var = f"element_{step.step_number}"
                    
                    # Add waits
                    if options.include_waits:
                        # Extract locator parts to avoid f-string complexity
                        locator_method = locator.split('(')[1].split(',')[0] if '(' in locator else "By.ID"
                        locator_value = locator.split('"')[1] if '"' in locator else locator
                        script_lines.append(f"        {element_var} = wait.until(EC.element_to_be_clickable(({locator_method}, \"{locator_value}\")))")
                    else:
                        script_lines.append(f"        {element_var} = {locator}")
                    
                    # Generate actions
                    if step.action_type == 'click':
                        script_lines.append(f"        {element_var}.click()")
                    elif step.action_type in ['type', 'fill'] and step.input_data:
                        script_lines.append(f"        {element_var}.clear()")
                        script_lines.append(f"        {element_var}.send_keys(\"{step.input_data}\")")
                    
                    script_lines.append("")
                    
                except ValueError as e:
                    script_lines.append(f"        # WARNING: Could not generate locator for step {step.step_number}: {e}")
                    script_lines.append("")
        
        script_lines.extend([
            "    finally:",
            "        driver.quit()"
        ])
        
        return "\n".join(script_lines)
    
    def _generate_playwright_js(self, steps: List[TestStep], options: ScriptGenerationOptions) -> str:
        """Generate Playwright JavaScript script."""
        # Implementation for JS version
        return "// Playwright JS generation not implemented yet"
    
    def _generate_cypress(self, steps: List[TestStep], options: ScriptGenerationOptions) -> str:
        """Generate Cypress script."""
        # Implementation for Cypress
        return "// Cypress generation not implemented yet"
    
    def _get_used_strategies(self, steps: List[TestStep]) -> Dict[str, int]:
        """Get statistics on which locator strategies were used."""
        strategies = {}
        
        for step in steps:
            if step.element_locators:
                for attr in ['testid_selector', 'css_selector', 'xpath', 'text_selector', 'label_selector']:
                    if getattr(step.element_locators, attr, None):
                        strategies[attr] = strategies.get(attr, 0) + 1
        
        return strategies
    
    async def _convert_to_editable_steps(self, enhanced_steps: List[TestStep]) -> List[EditableStep]:
        """Convert enhanced TestSteps to user-friendly EditableSteps."""
        editable_steps = []
        
        
        for i, step in enumerate(enhanced_steps):
            self.logger.info(f"📋 Processing step {i+1}: {step.action_type} - {step.description}")
            
            # Map action types
            action_type = self._map_action_type(step.action_type)
            self.logger.info(f"📋 Mapped action type: {step.action_type} -> {action_type}")
            
            # Create element selector if available
            element_selector = None
            if step.element_locators:
                self.logger.info(f"📋 Creating element selector from locators")
                element_selector = self._create_element_selector_from_locators(step.element_locators)
            elif step.element_info:
                self.logger.info(f"📋 Creating element selector from element_info fallback")
                # Create element selector from element_info as fallback
                element_selector = self._create_element_selector_from_element_info(step.element_info)
            else:
                self.logger.info(f"📋 No element selector data available")
            
            # Create editable step
            editable_step = create_editable_step(
                step_number=step.step_number,
                action_type=action_type,
                description=self._make_description_user_friendly(step.description, action_type),
                element_selector=element_selector,
                input_data=step.input_data,
                url=step.url,
                enabled=step.success,  # Disable steps that failed in original execution
                original_step_number=step.step_number,
                success_in_original=step.success
            )
            
            self.logger.info(f"📋 Created editable step: {editable_step.id} - {editable_step.action_type}")
            
            # Add basic validations if the step was successful
            if step.success and step.assertions:
                for assertion in step.assertions:
                    # Convert assertion to validation (simplified)
                    # This could be enhanced with more sophisticated mapping
                    pass
            
            editable_steps.append(editable_step)
        
        self.logger.info(f"✅ Converted to {len(editable_steps)} editable steps")
        return editable_steps
    
    def _map_action_type(self, original_action: str) -> ActionType:
        """Map original action types to user-friendly ActionType enum."""
        action_mapping = {
            'click': ActionType.CLICK,
            'type': ActionType.TYPE_TEXT,
            'type_text': ActionType.TYPE_TEXT,
            'fill': ActionType.TYPE_TEXT,
            'select': ActionType.SELECT_OPTION,
            'select_option': ActionType.SELECT_OPTION,
            'navigate': ActionType.NAVIGATE,
            'goto': ActionType.NAVIGATE,
            'wait': ActionType.WAIT,
            'hover': ActionType.HOVER,
            'scroll': ActionType.SCROLL,
            'screenshot': ActionType.TAKE_SCREENSHOT,
            'take_screenshot': ActionType.TAKE_SCREENSHOT,
            'verify': ActionType.VERIFY,
            'locate': ActionType.CUSTOM  # For variable assignments
        }
        
        return action_mapping.get(original_action.lower(), ActionType.CUSTOM)
    
    def _create_element_selector_from_locators(self, locators: ElementLocator) -> ElementSelector:
        """Create user-friendly ElementSelector from ElementLocator."""
        # Prioritize the best locator method
        priority_order = ['testid_selector', 'label_selector', 'text_selector', 'css_selector', 'xpath']
        
        for method_name in priority_order:
            value = getattr(locators, method_name, None)
            if value:
                method = method_name.replace('_selector', '')
                description = self._generate_element_description(method, value)
                
                # Create backup selectors from other available methods
                backup_selectors = []
                for backup_method in priority_order:
                    if backup_method != method_name:
                        backup_value = getattr(locators, backup_method, None)
                        if backup_value:
                            backup_selectors.append({
                                'method': backup_method.replace('_selector', ''),
                                'value': backup_value
                            })
                
                return create_element_selector(
                    description=description,
                    method=method,
                    value=value
                )
        
        # Fallback
        return create_element_selector(
            description="Unknown element",
            method="css",
            value="unknown"
        )
    
    def _generate_element_description(self, method: str, value: str) -> str:
        """Generate user-friendly description for element selector."""
        descriptions = {
            'testid': f"Element with test ID '{value}'",
            'label': f"Field labeled '{value}'",
            'text': f"Element containing text '{value}'",
            'css': f"Element matching CSS '{value}'",
            'xpath': f"Element at XPath '{value[:50]}...'" if len(value) > 50 else f"Element at XPath '{value}'"
        }
        
        return descriptions.get(method, f"Element ({method}: {value})")
    
    def _make_description_user_friendly(self, original_description: str, action_type: ActionType) -> str:
        """Make step description more user-friendly."""
        if action_type == ActionType.CLICK:
            return original_description.replace("Click", "Click on").replace("click", "click on")
        elif action_type == ActionType.TYPE_TEXT:
            return original_description.replace("Type", "Enter text in").replace("type", "enter text in")
        elif action_type == ActionType.NAVIGATE:
            return original_description.replace("Navigate", "Go to").replace("navigate", "go to")
        
        return original_description
    
    async def _save_script_to_execution(self, execution: Execution, generated_script: GeneratedScript):
        """Save the generated script to the execution document."""
        try:
            
            if not execution.generated_scripts:
                execution.generated_scripts = {}
            
            # Convert to dict, handling any serialization issues
            self.logger.info(f"📝 Converting GeneratedScript to dict")
            script_dict = generated_script.model_dump()
            
            # Convert datetime to ISO format string
            if 'generated_at' in script_dict and hasattr(script_dict['generated_at'], 'isoformat'):
                script_dict['generated_at'] = script_dict['generated_at'].isoformat()
            
            # Convert editable_steps to serializable format
            if 'editable_steps' in script_dict:
                for step in script_dict['editable_steps']:
                    # Ensure all fields are JSON serializable
                    if isinstance(step, dict):
                        for key, value in step.items():
                            if hasattr(value, 'isoformat'):
                                step[key] = value.isoformat()
            
            # Handle TestSummary and other dataclass objects
            def make_serializable(obj):
                """Convert dataclass objects to dicts recursively."""
                if hasattr(obj, '__dataclass_fields__'):
                    # It's a dataclass, convert to dict
                    from dataclasses import asdict
                    return asdict(obj)
                elif isinstance(obj, dict):
                    return {k: make_serializable(v) for k, v in obj.items()}
                elif isinstance(obj, (list, tuple)):
                    return [make_serializable(item) for item in obj]
                elif hasattr(obj, 'isoformat'):
                    return obj.isoformat()
                else:
                    return obj
            
            # Make the entire script_dict serializable
            self.logger.info(f"🔧 Making script_dict serializable")
            script_dict = make_serializable(script_dict)
            
            self.logger.info(f"💾 Saving script to execution.generated_scripts")
            execution.generated_scripts[generated_script.framework] = script_dict
            
            # The execution model now handles dataclass serialization automatically
            
            # Try to save just the generated_scripts field by updating only that field
            try:
                self.logger.info(f"💿 Updating execution.generated_scripts in database")
                await execution.update({"$set": {"generated_scripts": execution.generated_scripts}})
                self.logger.info(f"✅ Successfully updated generated_scripts field")
            except Exception as update_error:
                self.logger.error(f"❌ Failed to update generated_scripts field: {update_error}")
                # Fallback to regular save with dataclass serialization
                try:
                    self.logger.info(f"💿 Falling back to regular save with dataclass serialization")
                    # Convert dataclass fields to dicts before saving
                    if hasattr(execution.summary, '__dataclass_fields__'):
                        execution.summary = asdict(execution.summary)
                    if execution.steps:
                        serialized_steps = []
                        for step in execution.steps:
                            if hasattr(step, '__dataclass_fields__'):
                                serialized_steps.append(asdict(step))
                            else:
                                serialized_steps.append(step)
                        execution.steps = serialized_steps
                    
                    await execution.save()
                    self.logger.info(f"✅ Fallback save successful after dataclass serialization")
                except Exception as fallback_error:
                    self.logger.error(f"❌ Fallback save also failed: {fallback_error}", exc_info=True)
                    raise update_error
            
            self.logger.info(f"✅ Successfully saved script to execution {execution.execution_id}")
            
            # Debug: Check if the script was actually saved
            self.logger.info(f"🔍 Checking saved scripts: {list(execution.generated_scripts.keys()) if execution.generated_scripts else 'No scripts'}")
            
            # Verify by reading back from database
            from src.database.repositories.execution_repository import ExecutionRepository
            execution_repo = ExecutionRepository()
            fresh_execution = await execution_repo.get_by_execution_id(execution.execution_id)
            if fresh_execution and fresh_execution.generated_scripts:
                self.logger.info(f"🔍 Fresh from DB - saved scripts: {list(fresh_execution.generated_scripts.keys())}")
            else:
                self.logger.warning(f"⚠️ Fresh from DB - no scripts found")
            
        except Exception as e:
            self.logger.error(f"❌ Error saving script to execution: {e}", exc_info=True)
            raise
    
    async def get_saved_scripts_for_execution(self, execution_id: str) -> Dict[str, GeneratedScript]:
        """Get all saved scripts for an execution."""
        from src.database.repositories.execution_repository import ExecutionRepository
        
        execution_repo = ExecutionRepository()
        execution = await execution_repo.get_by_execution_id(execution_id)
        
        if not execution or not execution.generated_scripts:
            return {}
        
        scripts = {}
        for framework, script_data in execution.generated_scripts.items():
            try:
                # Convert back to GeneratedScript object
                scripts[framework] = GeneratedScript(**script_data)
            except Exception as e:
                self.logger.error(f"Failed to deserialize script for framework {framework}: {e}")
                # Continue with other frameworks
                continue
        
        return scripts
    
    async def update_editable_script(
        self, 
        execution_id: str, 
        framework: str, 
        updated_script: GeneratedScript
    ) -> bool:
        """Update an existing editable script."""
        
        try:
            from src.database.repositories.execution_repository import ExecutionRepository
            
            execution_repo = ExecutionRepository()
            execution = await execution_repo.get_by_execution_id(execution_id)
            
            if not execution:
                self.logger.error(f"❌ Execution not found: {execution_id}")
                return False
            
            if not execution.generated_scripts:
                execution.generated_scripts = {}
                self.logger.info("📦 Initialized empty generated_scripts dict")
            
            # Update statistics before saving
            updated_script._update_statistics()
            self.logger.info(f"📊 Updated script statistics: {updated_script.total_steps} total steps")
            
            # Increment version
            if framework in execution.generated_scripts:
                current_version = execution.generated_scripts[framework].get('version', 1)
                updated_script.version = current_version + 1
                self.logger.info(f"📈 Incremented version to {updated_script.version}")
            else:
                updated_script.version = 1
                self.logger.info("📈 Set initial version to 1")
            
            # Convert to dict with error handling
            try:
                script_dict = updated_script.model_dump()
                self.logger.info(f"✅ model_dump successful with {len(script_dict.get('editable_steps', []))} editable steps")
            except Exception as dump_error:
                self.logger.error(f"❌ model_dump failed: {dump_error}", exc_info=True)
                raise
            
            # Update the script in execution
            execution.generated_scripts[framework] = script_dict
            self.logger.info(f"💾 Updated script in execution.generated_scripts")
            
            # Save to database - only update the generated_scripts field to avoid dataclass serialization issues
            try:
                # Use update query to only modify the generated_scripts field
                await execution.update({"$set": {"generated_scripts": execution.generated_scripts}})
                self.logger.info(f"✅ Successfully saved updated script to database")
                return True
            except Exception as save_error:
                self.logger.error(f"❌ Failed to save execution to database: {save_error}", exc_info=True)
                # Try to save the whole document as fallback
                try:
                    # Convert dataclass fields to dicts before saving
                    if hasattr(execution.summary, '__dataclass_fields__'):
                        execution.summary = asdict(execution.summary)
                    if execution.steps:
                        serialized_steps = []
                        for step in execution.steps:
                            if hasattr(step, '__dataclass_fields__'):
                                serialized_steps.append(asdict(step))
                            else:
                                serialized_steps.append(step)
                        execution.steps = serialized_steps
                    
                    await execution.save()
                    self.logger.info(f"✅ Fallback save successful after dataclass serialization")
                    return True
                except Exception as fallback_error:
                    self.logger.error(f"❌ Fallback save also failed: {fallback_error}", exc_info=True)
                    raise save_error
                
        except Exception as e:
            self.logger.error(f"❌ Error updating editable script: {e}", exc_info=True)
            raise
    
    def _generate_basic_script_fallback(self, editable_steps: List[EditableStep], options: ScriptGenerationOptions) -> str:
        """Fallback method to generate basic script content."""
        from datetime import datetime
        
        lines = [
            f"# Generated {options.framework.value} script",
            f"# Generated at: {datetime.now()}",
            "",
            "# Basic test script"
        ]
        
        if options.framework == ScriptFramework.PLAYWRIGHT_PYTHON:
            lines.extend([
                "import asyncio",
                "from playwright.async_api import async_playwright",
                "",
                "async def run():",
                "    async with async_playwright() as p:",
                "        browser = await p.chromium.launch()",
                "        page = await browser.new_page()",
                "        # Add your test steps here",
                "        await browser.close()",
                "",
                "if __name__ == '__main__':",
                "    asyncio.run(run())"
            ])
        else:
            lines.extend([
                "# Basic script template",
                "# TODO: Implement test steps"
            ])
        
        return "\n".join(lines)
    
    def _create_element_selector_from_element_info(self, element_info: Dict[str, Any]) -> ElementSelector:
        """Create ElementSelector from raw element_info as fallback."""
        # Extract basic information
        tag_name = element_info.get('tag_name', 'element')
        text_content = element_info.get('text', '').strip()
        attributes = element_info.get('attributes', {})
        
        # Determine the best selector method and value
        method = 'css'
        value = element_info.get('css_selector', 'unknown')
        description = f"Unknown {tag_name}"
        
        # Try to find better selectors
        if 'data-testid' in attributes:
            method = 'testid'
            value = attributes['data-testid']
            description = f"{tag_name.title()} with test ID '{value}'"
        elif 'id' in attributes:
            method = 'css'
            value = f"#{attributes['id']}"
            description = f"{tag_name.title()} with ID '{attributes['id']}'"
        elif 'aria-label' in attributes:
            method = 'label'
            value = attributes['aria-label']
            description = f"{tag_name.title()} labeled '{value}'"
        elif text_content and len(text_content) < 50:
            method = 'text'
            value = text_content
            description = f"{tag_name.title()} containing '{text_content}'"
        elif element_info.get('css_selector'):
            method = 'css'
            value = element_info['css_selector']
            description = f"{tag_name.title()} element"
        elif element_info.get('xpath'):
            method = 'xpath'
            value = element_info['xpath']
            description = f"{tag_name.title()} element"
        
        # Create backup selectors
        backup_selectors = []
        if element_info.get('css_selector') and method != 'css':
            backup_selectors.append({
                'method': 'css',
                'value': element_info['css_selector']
            })
        if element_info.get('xpath') and method != 'xpath':
            backup_selectors.append({
                'method': 'xpath',
                'value': element_info['xpath']
            })
        
        return create_element_selector(
            description=description,
            method=method,
            value=value,
            backup_selectors=backup_selectors
        )
    
    async def _generate_intelligent_script(
        self, 
        execution: Execution, 
        enhanced_steps: List[TestStep], 
        options: ScriptGenerationOptions
    ) -> Dict[str, str]:
        """
        Generate intelligent automation script using AI and the existing prompt infrastructure.
        Uses the same OpenRouter + prompt system as the rest of AgentQA.
        Returns both script content and human-readable summary.
        """
        self.logger.info(f"🤖 Generating intelligent {options.framework.value} script using AI")
        
        try:
            # Import the unified prompt service (same as used by test analysis)
            from src.services.llm.prompt_llm_service import get_prompt_llm_service
            
            prompt_service = get_prompt_llm_service()
            
            # Prepare rich execution context for the AI
            execution_context = self._prepare_execution_context(execution, enhanced_steps)
            
            # Determine the target framework prompt
            framework_mapping = {
                ScriptFramework.PLAYWRIGHT_PYTHON: "playwright-python",
                ScriptFramework.SELENIUM_PYTHON: "selenium-python", 
                ScriptFramework.PLAYWRIGHT_JS: "playwright-js",
                ScriptFramework.CYPRESS: "cypress"
            }
            
            framework_id = framework_mapping.get(options.framework, "playwright-python")
            
            # Use versioned prompt system for script generation
            result = prompt_service.execute_versioned_prompt(
                category="code-generation",
                prompt_id=f"script-generation-{framework_id}",
                variables={
                    "execution_context": execution_context,
                    "framework": options.framework.value,
                    "include_screenshots": options.include_screenshots,
                    "include_waits": options.include_waits,
                    "include_validations": options.include_assertions,
                    "add_error_handling": options.add_error_handling,
                    "use_best_locators": options.use_best_locators,
                    "add_comments": options.add_comments,
                    "page_object_pattern": options.page_object_pattern
                },
                use_case="script_generation",
                language="en",  # Scripts are always in English
                max_tokens=4096,  # Allow for longer scripts
                temperature=0.1   # Low temperature for consistent code generation
            )
            
            if result.get("success"):
                script_content = result["content"].strip()
                
                # Clean up any markdown formatting from the AI response
                script_content = self._clean_script_content(script_content)
                
                # Add metadata comment at the top
                metadata_header = self._generate_script_header(execution, options, result)
                final_script = f"{metadata_header}\n\n{script_content}"
                
                # Generate human-readable summary
                script_summary = await self._generate_script_summary(execution, enhanced_steps, prompt_service)
                human_description = await self._generate_human_description(execution, enhanced_steps, prompt_service)
                
                # Extract actual steps from generated code
                code_steps = self._extract_steps_from_generated_code(final_script, options.framework)
                
                self.logger.info(f"✅ AI generated {len(final_script)} chars of {options.framework.value} script")
                self.logger.info(f"🔍 Extracted {len(code_steps)} steps from generated code")
                self.logger.info(f"🤖 Model used: {result.get('model_used', 'unknown')}")
                
                return {
                    'script_content': final_script,
                    'script_summary': script_summary,
                    'human_description': human_description,
                    'code_steps': code_steps
                }
            else:
                raise Exception(f"AI script generation failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            self.logger.error(f"❌ Intelligent script generation failed: {e}")
            raise

    def _prepare_execution_context(self, execution: Execution, enhanced_steps: List[TestStep]) -> str:
        """
        Prepare rich context about the execution for AI script generation.
        """
        context_parts = []
        
        # Basic execution information
        context_parts.append("## Execution Overview")
        context_parts.append(f"- **Execution ID**: {execution.execution_id}")
        context_parts.append(f"- **Target URL**: {getattr(execution, 'target_url', 'Unknown')}")
        context_parts.append(f"- **Started At**: {getattr(execution, 'started_at', 'Unknown')}")
        context_parts.append(f"- **Duration**: {getattr(execution, 'duration_ms', 0)}ms")
        
        # Test context from metadata
        if hasattr(execution, 'metadata') and execution.metadata:
            context_parts.append("\n## Test Context")
            test_description = execution.metadata.get('test_description', '')
            gherkin_scenario = execution.metadata.get('gherkin_scenario', '')
            test_instructions = execution.metadata.get('test_instructions', '')
            
            if test_description:
                context_parts.append(f"- **Test Description**: {test_description}")
            if gherkin_scenario:
                context_parts.append(f"- **Gherkin Scenario**: {gherkin_scenario}")
            if test_instructions:
                context_parts.append(f"- **Test Instructions**: {test_instructions}")
        
        # Step-by-step execution details
        context_parts.append("\n## Execution Steps")
        
        for i, step in enumerate(enhanced_steps, 1):
            context_parts.append(f"\n### Step {i}: {step.description}")
            context_parts.append(f"- **Action**: {step.action_type}")
            context_parts.append(f"- **Success**: {'✅' if step.success else '❌'}")
            context_parts.append(f"- **Duration**: {step.duration_ms}ms")
            
            if step.url:
                context_parts.append(f"- **URL**: {step.url}")
            
            if step.element_info:
                context_parts.append(f"- **Element**: {step.element_info}")
            
            if step.element_locators:
                locators = []
                if step.element_locators.testid_selector:
                    locators.append(f"data-testid='{step.element_locators.testid_selector}'")
                if step.element_locators.css_selector:
                    locators.append(f"CSS: {step.element_locators.css_selector}")
                if step.element_locators.text_selector:
                    locators.append(f"Text: '{step.element_locators.text_selector}'")
                if locators:
                    context_parts.append(f"- **Locators**: {', '.join(locators)}")
            
            if step.input_data:
                context_parts.append(f"- **Input Data**: {step.input_data}")
            
            if step.error_message and not step.success:
                context_parts.append(f"- **Error**: {step.error_message}")
        
        # URLs visited during execution
        if hasattr(execution, 'metadata') and execution.metadata and execution.metadata.get('visited_urls'):
            context_parts.append("\n## URLs Visited")
            for url in execution.metadata['visited_urls']:
                context_parts.append(f"- {url}")
        
        # Summary statistics
        successful_steps = sum(1 for step in enhanced_steps if step.success)
        total_steps = len(enhanced_steps)
        success_rate = (successful_steps / total_steps * 100) if total_steps > 0 else 0
        
        context_parts.append(f"\n## Execution Summary")
        context_parts.append(f"- **Total Steps**: {total_steps}")
        context_parts.append(f"- **Successful Steps**: {successful_steps}")
        context_parts.append(f"- **Success Rate**: {success_rate:.1f}%")
        
        return "\n".join(context_parts)

    def _clean_script_content(self, content: str) -> str:
        """Clean up AI-generated script content by removing markdown formatting and explanations."""
        import re
        
        # Remove markdown code blocks
        content = re.sub(r'^```\w*\n', '', content, flags=re.MULTILINE)
        content = re.sub(r'\n```$', '', content, flags=re.MULTILINE)
        content = re.sub(r'^```$', '', content, flags=re.MULTILINE)
        
        # Split content by lines to process
        lines = content.split('\n')
        script_lines = []
        in_code_section = False
        
        for line in lines:
            # Start collecting code when we find import statements or function definitions
            if (line.strip().startswith('import ') or 
                line.strip().startswith('from ') or 
                line.strip().startswith('def ') or
                line.strip().startswith('with ') or
                in_code_section):
                in_code_section = True
                script_lines.append(line)
            # Stop collecting if we hit explanation text after code
            elif in_code_section and line.strip().startswith('**') and '**' in line:
                break
            elif in_code_section:
                script_lines.append(line)
        
        # If no clear code section found, try to extract everything that looks like code
        if not script_lines:
            for line in lines:
                # Skip explanatory text lines
                if (line.strip().startswith('Here') or 
                    line.strip().startswith('**') or
                    line.strip().startswith('This script') or
                    line.strip().startswith('The script') or
                    line.strip().startswith('Notes:') or
                    line.strip().startswith('Explanation:')):
                    continue
                # Keep lines that look like code
                if (line.strip().startswith('import ') or 
                    line.strip().startswith('from ') or
                    line.strip().startswith('def ') or
                    line.strip().startswith('with ') or
                    line.strip().startswith('    ') or  # Indented lines
                    line.strip().startswith('#') or    # Comments
                    line.strip() == ''):               # Empty lines
                    script_lines.append(line)
        
        # Join the lines and clean up
        cleaned_content = '\n'.join(script_lines).strip()
        
        # Remove any trailing explanation text
        cleaned_content = re.sub(r'\n\*\*.*$', '', cleaned_content, flags=re.DOTALL)
        
        return cleaned_content

    def _generate_script_header(self, execution: Execution, options: ScriptGenerationOptions, ai_result: Dict[str, Any]) -> str:
        """Generate informative header comment for the generated script."""
        from datetime import datetime
        
        header_lines = [
            f"# Generated {options.framework.value} Test Script",
            f"# Created by AgentQA AI Script Generator",
            f"# Generated at: {datetime.now().isoformat()}",
            f"# Source execution: {execution.execution_id}",
            f"# AI Model: {ai_result.get('model_used', 'unknown')}",
            f"# Provider: {ai_result.get('provider', 'unknown')}"
        ]
        
        if hasattr(execution, 'target_url') and execution.target_url:
            header_lines.append(f"# Target URL: {execution.target_url}")
        
        # Add generation options used
        header_lines.append("#")
        header_lines.append("# Generation Options:")
        header_lines.append(f"#   - Screenshots: {options.include_screenshots}")
        header_lines.append(f"#   - Waits: {options.include_waits}")
        header_lines.append(f"#   - Validations: {options.include_assertions}")
        header_lines.append(f"#   - Error Handling: {options.add_error_handling}")
        header_lines.append(f"#   - Best Locators: {options.use_best_locators}")
        
        return "\n".join(header_lines)

    async def _generate_script_summary(self, execution: Execution, enhanced_steps: List[TestStep], prompt_service) -> str:
        """Generate a human-readable summary of what the script does."""
        try:
            # Create a simple summary prompt
            summary_prompt = f"""
            Create a brief, human-readable summary of this test automation script in Spanish.
            
            **Test Execution Details:**
            - URL: {getattr(execution, 'target_url', 'Unknown')}
            - Total Steps: {len(enhanced_steps)}
            - Successful Steps: {sum(1 for s in enhanced_steps if s.success)}
            
            **Steps Summary:**
            {self._create_steps_summary(enhanced_steps)}
            
            Generate a 2-3 sentence summary in Spanish explaining what this test does in simple terms.
            Focus on the user actions and what the test validates.
            """
            
            result = prompt_service.execute_direct_prompt(
                prompt=summary_prompt,
                max_tokens=200,
                temperature=0.3
            )
            
            if result.get("success"):
                return result["content"].strip()
            else:
                return f"Script automatizado que ejecuta {len(enhanced_steps)} pasos de prueba en {getattr(execution, 'target_url', 'la aplicación')}"
                
        except Exception as e:
            self.logger.warning(f"Failed to generate script summary: {e}")
            return f"Script automatizado que ejecuta {len(enhanced_steps)} pasos de prueba"

    async def _generate_human_description(self, execution: Execution, enhanced_steps: List[TestStep], prompt_service) -> str:
        """Generate a detailed human-readable description."""
        try:
            description_prompt = f"""
            Create a detailed description in Spanish of this automated test, explaining what it accomplishes from a business/user perspective.
            
            **Test Context:**
            - Target URL: {getattr(execution, 'target_url', 'Unknown')}
            - Execution ID: {execution.execution_id}
            - Total Steps: {len(enhanced_steps)}
            
            **Detailed Steps:**
            {self._create_detailed_steps_description(enhanced_steps)}
            
            Write a comprehensive description in Spanish that explains:
            1. What user scenario this test covers
            2. The main workflow being tested
            3. Key validations being performed
            
            Keep it business-friendly and avoid technical jargon.
            """
            
            result = prompt_service.execute_direct_prompt(
                prompt=description_prompt,
                max_tokens=500,
                temperature=0.3
            )
            
            if result.get("success"):
                return result["content"].strip()
            else:
                return f"Prueba automatizada generada desde la ejecución {execution.execution_id}"
                
        except Exception as e:
            self.logger.warning(f"Failed to generate human description: {e}")
            return f"Prueba automatizada generada desde la ejecución {execution.execution_id}"

    def _create_steps_summary(self, enhanced_steps: List[TestStep]) -> str:
        """Create a brief summary of the steps."""
        steps_summary = []
        for i, step in enumerate(enhanced_steps[:5], 1):  # First 5 steps
            action = step.action_type.replace('_', ' ').title()
            steps_summary.append(f"{i}. {action}: {step.description}")
        
        if len(enhanced_steps) > 5:
            steps_summary.append(f"... and {len(enhanced_steps) - 5} more steps")
        
        return "\n".join(steps_summary)

    def _create_detailed_steps_description(self, enhanced_steps: List[TestStep]) -> str:
        """Create a detailed description of all steps."""
        steps_desc = []
        for i, step in enumerate(enhanced_steps, 1):
            status = "✅" if step.success else "❌"
            action = step.action_type.replace('_', ' ').title()
            steps_desc.append(f"{i}. {status} {action}: {step.description}")
            
            if step.element_info and 'text' in step.element_info:
                element_text = step.element_info['text'][:50]
                if element_text:
                    steps_desc.append(f"   Element: {element_text}")
        
        return "\n".join(steps_desc)

    def _extract_steps_from_generated_code(self, script_content: str, framework: ScriptFramework) -> List[Dict[str, Any]]:
        """Extract actual code steps from the generated script."""
        import re
        
        steps = []
        lines = script_content.split('\n')
        
        if framework == ScriptFramework.PLAYWRIGHT_PYTHON:
            steps = self._extract_playwright_python_steps(lines)
        elif framework == ScriptFramework.SELENIUM_PYTHON:
            steps = self._extract_selenium_python_steps(lines)
        
        return steps

    def _extract_playwright_python_steps(self, lines: List[str]) -> List[Dict[str, Any]]:
        """Extract steps from Playwright Python code."""
        import re
        
        steps = []
        step_number = 1
        
        for i, line in enumerate(lines):
            original_line = line
            line = line.strip()
            
            # Skip empty lines and comments (except step comments)
            if not line or (line.startswith('#') and 'Step' not in line):
                continue
            
            step_info = None
            
            # Navigation
            if re.search(r'page\.goto\(', line):
                url_match = re.search(r'page\.goto\(["\']([^"\']+)["\']', line)
                url = url_match.group(1) if url_match else ''
                step_info = {
                    'action_type': 'navigate',
                    'description': f'Navegar a {url}',
                    'url': url,
                    'code_line': original_line.strip()
                }
            
            # Variable assignments for locators (capture them for later use)
            elif re.search(r'(\w+)\s*=\s*page\.(?:locator|get_by_\w+)\(', line):
                var_match = re.search(r'(\w+)\s*=\s*page\.(?:locator|get_by_\w+)\((.+)\)', line)
                if var_match:
                    var_name = var_match.group(1)
                    selector_part = var_match.group(2).strip()
                    
                    # Clean quotes from selector
                    selector = selector_part.strip('\'"')
                    
                    if 'get_by_test_id' in line:
                        element_desc = f'elemento con test ID "{selector}"'
                    elif 'get_by_text' in line:
                        element_desc = f'elemento con texto "{selector}"'
                    elif 'get_by_label' in line:
                        element_desc = f'campo etiquetado "{selector}"'
                    elif 'input[name=' in selector:
                        field_name = re.search(r"input\[name=['\"]([^'\"]+)['\"]", selector)
                        element_desc = f'campo de entrada "{field_name.group(1)}"' if field_name else f'campo de entrada'
                    elif 'button:has-text' in selector:
                        button_text = re.search(r"button:has-text\(['\"]([^'\"]+)['\"]", selector)
                        element_desc = f'botón "{button_text.group(1)}"' if button_text else f'botón'
                    elif selector == 'h1':
                        element_desc = 'título principal de la página'
                    elif 'text=' in selector:
                        text_match = re.search(r'text=([^"\']+)', selector)
                        text_content = text_match.group(1) if text_match else selector
                        element_desc = f'elemento con texto "{text_content}"'
                    else:
                        element_desc = f'elemento "{selector}"'
                    
                    step_info = {
                        'action_type': 'locate',
                        'description': f'Localizar {element_desc}',
                        'selector': selector,
                        'variable_name': var_name,
                        'code_line': original_line.strip()
                    }
            
            # Click actions (both direct and variable-based)
            elif re.search(r'\.click\(\)', line):
                # Check if it's a variable click
                var_match = re.search(r'(\w+)\.click\(\)', line)
                if var_match:
                    var_name = var_match.group(1)
                    step_info = {
                        'action_type': 'click',
                        'description': f'Hacer clic en el {var_name}',
                        'variable_name': var_name,
                        'code_line': original_line.strip()
                    }
                else:
                    # Direct locator click
                    locator_match = re.search(r'page\.(?:get_by_\w+|locator)\(["\']([^"\']+)["\']', line)
                    if locator_match:
                        selector = locator_match.group(1)
                        element_desc = self._get_element_description_from_selector(selector, line)
                        step_info = {
                            'action_type': 'click',
                            'description': f'Hacer clic en {element_desc}',
                            'selector': selector,
                            'code_line': original_line.strip()
                        }
            
            # Fill/Type actions
            elif re.search(r'\.fill\(', line):
                value_match = re.search(r'\.fill\(["\']([^"\']+)["\']', line)
                var_match = re.search(r'(\w+)\.fill\(', line)
                
                if value_match:
                    value = value_match.group(1)
                    
                    if var_match:
                        var_name = var_match.group(1)
                        step_info = {
                            'action_type': 'type_text',
                            'description': f'Escribir "{value}" en el {var_name}',
                            'variable_name': var_name,
                            'input_data': value,
                            'code_line': original_line.strip()
                        }
                    else:
                        # Direct locator fill
                        locator_match = re.search(r'page\.(?:get_by_\w+|locator)\(["\']([^"\']+)["\']', line)
                        if locator_match:
                            selector = locator_match.group(1)
                            element_desc = self._get_element_description_from_selector(selector, line)
                            step_info = {
                                'action_type': 'type_text',
                                'description': f'Escribir "{value}" en {element_desc}',
                                'selector': selector,
                                'input_data': value,
                                'code_line': original_line.strip()
                            }
            
            # Wait actions
            elif re.search(r'\.wait_for\(', line) or re.search(r'page\.wait_for_timeout\(', line) or re.search(r'page\.wait_for_url\(', line):
                if 'wait_for_timeout' in line:
                    timeout_match = re.search(r'wait_for_timeout\((\d+)', line)
                    if timeout_match:
                        timeout_ms = int(timeout_match.group(1))
                        timeout_sec = timeout_ms / 1000
                        step_info = {
                            'action_type': 'wait',
                            'description': f'Esperar {timeout_sec} segundos',
                            'wait_time': timeout_sec,
                            'code_line': original_line.strip()
                        }
                elif 'wait_for_url' in line:
                    url_match = re.search(r'wait_for_url\(["\']([^"\']+)["\']', line)
                    url = url_match.group(1) if url_match else ''
                    step_info = {
                        'action_type': 'wait',
                        'description': f'Esperar a que se cargue la página {url}',
                        'url': url,
                        'code_line': original_line.strip()
                    }
                else:
                    var_match = re.search(r'(\w+)\.wait_for\(', line)
                    if var_match:
                        var_name = var_match.group(1)
                        step_info = {
                            'action_type': 'wait',
                            'description': f'Esperar a que el {var_name} esté listo',
                            'variable_name': var_name,
                            'code_line': original_line.strip()
                        }
            
            # Assertions/Validations
            elif re.search(r'expect\(', line) or re.search(r'assert\s+', line):
                if 'expect(' in line:
                    if 'to_be_visible' in line:
                        step_info = {
                            'action_type': 'verify',
                            'description': 'Verificar que el elemento esté visible',
                            'code_line': original_line.strip()
                        }
                    elif 'to_have_text' in line:
                        text_match = re.search(r'to_have_text\(["\']([^"\']+)["\']', line)
                        text = text_match.group(1) if text_match else ''
                        step_info = {
                            'action_type': 'verify',
                            'description': f'Verificar que el texto sea "{text}"',
                            'expected_value': text,
                            'code_line': original_line.strip()
                        }
                elif 'assert' in line:
                    # Handle assert statements
                    if 'is_visible()' in line:
                        text_match = re.search(r'"([^"]+)"', line)
                        error_msg = text_match.group(1) if text_match else 'elemento'
                        step_info = {
                            'action_type': 'verify',
                            'description': f'Verificar visibilidad: {error_msg}',
                            'code_line': original_line.strip()
                        }
            
            # Screenshots
            elif re.search(r'\.screenshot\(', line):
                step_info = {
                    'action_type': 'take_screenshot',
                    'description': 'Tomar captura de pantalla',
                    'code_line': original_line.strip()
                }
            
            # Add step if found
            if step_info:
                step_info['step_number'] = step_number
                step_info['line_number'] = i + 1
                steps.append(step_info)
                step_number += 1
        
        return steps

    def _get_element_description_from_selector(self, selector: str, line: str) -> str:
        """Get human-friendly element description from selector and line context."""
        if 'get_by_test_id' in line:
            return f'elemento con test ID "{selector}"'
        elif 'get_by_text' in line:
            return f'elemento con texto "{selector}"'
        elif 'get_by_label' in line:
            return f'campo etiquetado "{selector}"'
        elif 'input[name=' in selector:
            field_match = re.search(r"input\[name=['\"]([^'\"]+)['\"]", selector)
            field_name = field_match.group(1) if field_match else selector
            return f'campo de entrada "{field_name}"'
        elif 'button:has-text' in selector:
            button_match = re.search(r"button:has-text\(['\"]([^'\"]+)['\"]", selector)
            button_text = button_match.group(1) if button_match else selector
            return f'botón "{button_text}"'
        elif selector.startswith('#'):
            return f'elemento con ID "{selector[1:]}"'
        elif selector.startswith('.'):
            return f'elemento con clase "{selector[1:]}"'
        else:
            return f'elemento "{selector}"'

    def _extract_selenium_python_steps(self, lines: List[str]) -> List[Dict[str, Any]]:
        """Extract steps from Selenium Python code."""
        # Similar implementation for Selenium
        steps = []
        # TODO: Implement Selenium step extraction
        return steps

    async def _convert_code_steps_to_editable(self, code_steps: List[Dict[str, Any]]) -> List[EditableStep]:
        """Convert code-extracted steps to EditableStep objects with AI-enhanced descriptions."""
        editable_steps = []
        
        # Generate human descriptions with AI
        humanized_descriptions = await self._generate_human_step_descriptions(code_steps)
        
        for i, step in enumerate(code_steps):
            # Map action types
            action_type = self._map_action_type(step['action_type'])
            
            # Create element selector if available
            element_selector = None
            if step.get('selector'):
                # Determine method based on the selector pattern
                selector_value = step['selector']
                if 'data-testid' in step.get('code_line', ''):
                    method = 'testid'
                elif 'get_by_text' in step.get('code_line', ''):
                    method = 'text'
                elif 'get_by_label' in step.get('code_line', ''):
                    method = 'label'
                elif selector_value.startswith('#') or selector_value.startswith('.') or '[' in selector_value:
                    method = 'css'
                else:
                    method = 'text'
                
                element_selector = create_element_selector(
                    description=self._generate_element_description_from_code(step),
                    method=method,
                    value=selector_value
                )
            
            # Use AI-generated description if available, otherwise use original
            human_description = humanized_descriptions.get(i, step['description'])
            
            # Create the editable step
            editable_step = create_editable_step(
                step_number=step['step_number'],
                action_type=action_type,
                description=human_description,
                element_selector=element_selector,
                input_data=step.get('input_data'),
                url=step.get('url'),
                wait_time=int(step.get('wait_time', 0)) if step.get('wait_time') else None,
                enabled=True,  # Code-generated steps are always enabled
                success_in_original=True  # Code represents successful execution
            )
            
            # Add code line reference in notes
            if step.get('code_line'):
                editable_step.notes = f"Línea de código: {step['code_line']}"
            
            editable_steps.append(editable_step)
        
        return editable_steps

    def _generate_element_description_from_code(self, step: Dict[str, Any]) -> str:
        """Generate element description from code step information."""
        code_line = step.get('code_line', '')
        selector = step.get('selector', '')
        
        if 'get_by_test_id' in code_line:
            return f'Elemento con test ID "{selector}"'
        elif 'get_by_text' in code_line:
            return f'Elemento con texto "{selector}"'
        elif 'get_by_label' in code_line:
            return f'Campo etiquetado "{selector}"'
        elif selector.startswith('#'):
            return f'Elemento con ID "{selector[1:]}"'
        elif selector.startswith('.'):
            return f'Elemento con clase "{selector[1:]}"'
        elif '[' in selector:
            return f'Elemento con atributo "{selector}"'
        else:
            return f'Elemento "{selector}"'

    async def _generate_human_step_descriptions(self, code_steps: List[Dict[str, Any]]) -> Dict[int, str]:
        """Generate human-friendly descriptions for code steps using AI."""
        try:
            from src.services.llm.prompt_llm_service import get_prompt_llm_service
            
            prompt_service = get_prompt_llm_service()
            
            # Create context for AI
            steps_context = []
            for i, step in enumerate(code_steps):
                step_info = f"Paso {i+1}: {step['action_type']}"
                if step.get('selector'):
                    step_info += f" | Selector: {step['selector']}"
                if step.get('input_data'):
                    step_info += f" | Datos: {step['input_data']}"
                if step.get('url'):
                    step_info += f" | URL: {step['url']}"
                step_info += f" | Código: {step.get('code_line', '')}"
                steps_context.append(step_info)
            
            prompt = f"""
            Genera descripciones super humanas y naturales en español para estos pasos de automatización web.
            
            **Contexto:** Esto es un script de pruebas de login en una aplicación web.
            
            **Pasos técnicos:**
            {chr(10).join(steps_context)}
            
            **Instrucciones:**
            - Usa lenguaje súper natural como si le explicaras a tu abuela
            - En lugar de "Localizar campo de entrada username" di "Buscar la casilla donde escribir el nombre de usuario"
            - En lugar de "Hacer clic en login_button" di "Presionar el botón de iniciar sesión"
            - En lugar de "Escribir <EMAIL> en username_input" di "Escribir el email del administrador"
            - Usa palabras como: buscar, presionar, escribir, esperar, verificar, dirigirse
            - Evita palabras técnicas como: localizar, elemento, selector, input, button
            - Máximo 60 caracteres por descripción
            
            **Formato de respuesta:**
            Paso 1: [descripción natural]
            Paso 2: [descripción natural]
            ...
            """
            
            result = prompt_service.execute_direct_prompt(
                prompt=prompt,
                max_tokens=800,
                temperature=0.4
            )
            
            if result.get("success"):
                # Parse the AI response
                content = result["content"].strip()
                descriptions = {}
                
                for line in content.split('\n'):
                    if line.strip().startswith('Paso '):
                        try:
                            # Extract step number and description
                            parts = line.split(':', 1)
                            if len(parts) == 2:
                                step_num_str = parts[0].replace('Paso ', '').strip()
                                step_num = int(step_num_str) - 1  # Convert to 0-based index
                                description = parts[1].strip()
                                descriptions[step_num] = description
                        except (ValueError, IndexError):
                            continue
                
                self.logger.info(f"✅ AI generated {len(descriptions)} human descriptions")
                return descriptions
            else:
                self.logger.warning("Failed to generate human descriptions with AI")
                return {}
                
        except Exception as e:
            self.logger.warning(f"Failed to generate human step descriptions: {e}")
            return {}
        

# Factory function for easy access
def create_script_generator() -> ScriptGeneratorService:
    """Create a new script generator service instance."""
    return ScriptGeneratorService()