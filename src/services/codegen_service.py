"""
Codegen Session Service for QAK MongoDB Integration

Provides CRUD, analytics, and migration for code generation sessions stored in MongoDB.
"""

from __future__ import annotations

import logging
from pathlib import Path
from datetime import datetime
from typing import Any, Dict, List, Optional

from .base_service import (
    BaseService,
    DatabaseServiceMixin,
    ServiceResult,
    handle_service_exceptions,
    log_service_operation,
)

from ..database.repositories import CodegenSessionRepository
from ..database.models.codegen_session import CodegenSession, CodegenStatus, TargetLanguage

logger = logging.getLogger(__name__)


class CodegenService(BaseService, DatabaseServiceMixin):
    """Service layer for managing codegen sessions."""

    LEGACY_SESS_DIR = Path("codegen_sessions")

    def __init__(self, legacy_dir: str | Path = LEGACY_SESS_DIR):
        super().__init__("codegen")
        self.legacy_dir = Path(legacy_dir)
        self._session_repo: Optional[CodegenSessionRepository] = None

    # ------------------------------------------------------------------
    @property
    def session_repo(self) -> CodegenSessionRepository:
        if self._session_repo is None:
            self._session_repo = self.get_repository("codegen_session")
        return self._session_repo

    # ------------------------------------------------------------------
    async def health_check(self) -> ServiceResult[Dict[str, Any]]:
        info = {
            "service": self.service_name,
            "database_enabled": self.should_use_database(),
            "legacy_dir_exists": self.legacy_dir.exists(),
        }
        if self.should_use_database():
            info["database"] = await self.check_database_health()
        return ServiceResult.success_result(info)

    async def get_service_info(self) -> ServiceResult[Dict[str, Any]]:
        stats: Dict[str, Any] = {}
        if self.should_use_database():
            stats["total_sessions"] = await self.session_repo.count({})
        else:
            stats["legacy_files"] = len(list(self.legacy_dir.glob("*.json")))
        return ServiceResult.success_result({"service": self.service_name, "statistics": stats})

    # ------------------------------------------------------------------
    @handle_service_exceptions
    @log_service_operation
    async def create_session(
        self,
        target_language: TargetLanguage,
        url: Optional[str] = None,
        name: Optional[str] = None,
        description: str = "",
    ) -> ServiceResult[CodegenSession]:
        session = CodegenSession(
            target_language=target_language.value,
            url=url,
            name=name,
            description=description,
        )
        if self.should_use_database():
            created = await self.session_repo.create(session)
            return ServiceResult.success_result(created)
        else:
            file_path = self.legacy_dir / f"session_{session.session_id}.json"
            file_path.write_text(session.model_dump_json(indent=2))
            return ServiceResult.success_result(session)

    @handle_service_exceptions
    async def get_session(self, session_id: str) -> ServiceResult[Optional[CodegenSession]]:
        if self.should_use_database():
            sess = await self.session_repo.get_by_session_id(session_id)
            return ServiceResult.success_result(sess)
        else:
            file = self.legacy_dir / f"session_{session_id}.json"
            if file.exists():
                sess = CodegenSession.model_validate_json(file.read_text())
                return ServiceResult.success_result(sess)
            return ServiceResult.success_result(None)

    # Additional analytics
    @handle_service_exceptions
    async def get_language_stats(self) -> ServiceResult[List[Dict[str, Any]]]:
        if self.should_use_database():
            stats = await self.session_repo.get_language_statistics()
            return ServiceResult.success_result(stats)
        return ServiceResult.error_result("Analytics not available in legacy mode") 