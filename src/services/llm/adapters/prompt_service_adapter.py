"""
Prompt Service Adapter - Maintains compatibility with existing PromptService
Gradually migrates existing functionality to new LLM architecture
"""

from typing import Dict, Any, Optional
from ...core.prompt_service import PromptService as LegacyPromptService
from ..use_cases.gherkin_generator import GherkinGenerator
from ..use_cases.manual_test_case_generator import ManualTest<PERSON>aseGenerator
from ..use_cases.story_enhancer import StoryEnhancer
from ..use_cases.result_validator import ResultValidator
from ..use_cases.text_translator import TextTranslator
from ..llm_config import get_feature_flags


class PromptServiceAdapter:
    """Adapter that maintains PromptService API while using new LLM services."""
    
    def __init__(self):
        """Initialize the adapter with both legacy and new services."""
        # Keep legacy service for backward compatibility
        self.legacy_service = LegacyPromptService()
        
        # New LLM use cases
        self.gherkin_generator = GherkinGenerator()
        self.manual_test_generator = ManualTestCaseGenerator()
        self.story_enhancer = StoryEnhancer() 
        self.result_validator = ResultValidator()
        self.text_translator = TextTranslator()
        
        # Feature flags to gradually migrate
        flags = get_feature_flags()
        self.use_new_gherkin = flags.get("USE_OPENROUTER_FOR_GHERKIN", False)
        self.use_new_validation = flags.get("USE_OPENROUTER_FOR_VALIDATION", False)
        self.use_new_translation = flags.get("USE_OPENROUTER_FOR_TRANSLATION", False)
        self.use_new_enhancement = flags.get("USE_OPENROUTER_FOR_ENHANCEMENT", False)
    
    def generate_gherkin(self, test_cases: str, language: str = "en", **context) -> str:
        """Generate Gherkin scenarios - migrated to new service."""
        if self.use_new_gherkin:
            try:
                # Extract context
                instructions = context.get("instructions", test_cases)
                user_story = context.get("user_story", "")
                url = context.get("url", "")
                
                result = self.gherkin_generator.generate_from_instructions(
                    instructions=instructions,
                    user_story=user_story,
                    url=url,
                    language=language
                )
                
                if result["success"]:
                    return result["gherkin"]
                else:
                    # Fallback to legacy on failure
                    return self._fallback_to_legacy_gherkin(test_cases, language, **context)
                    
            except Exception as e:
                # Fallback to legacy on exception
                return self._fallback_to_legacy_gherkin(test_cases, language, **context)
        else:
            return self._fallback_to_legacy_gherkin(test_cases, language, **context)
    
    def enhance_user_story(self, user_story: str, language: str = "en") -> str:
        """Enhance user story - migrated to new service."""
        if self.use_new_enhancement:
            try:
                result = self.story_enhancer.enhance_user_story(
                    user_story=user_story,
                    language=language
                )
                
                if result["success"]:
                    return result["enhanced_story"]
                else:
                    # Fallback to legacy
                    return self.legacy_service.enhance_user_story(user_story, language)
                    
            except Exception:
                # Fallback to legacy on exception
                return self.legacy_service.enhance_user_story(user_story, language)
        else:
            return self.legacy_service.enhance_user_story(user_story, language)
    
    def generate_manual_test_cases(self, enhanced_story: str, language: str = "en") -> str:
        """Generate manual test cases - migrated to new service."""
        try:
            result = self.manual_test_generator.generate_from_story(
                enhanced_story=enhanced_story,
                language=language
            )
            
            if result["success"]:
                # If we got JSON, convert to string format for legacy compatibility
                test_cases = result["test_cases"]
                if isinstance(test_cases, list):
                    return str(test_cases)  # JSON array as string
                else:
                    return test_cases  # Already a string
            else:
                # Fallback to legacy
                return self.legacy_service.generate_manual_test_cases(enhanced_story, language)
                
        except Exception:
            # Fallback to legacy on exception
            return self.legacy_service.generate_manual_test_cases(enhanced_story, language)
    
    def translate_text(self, text: str, target_language: str, source_language: str = "auto") -> str:
        """Translate text - conditionally use new service."""
        if self.use_new_translation:
            try:
                result = self.text_translator.translate(
                    text=text,
                    target_language=target_language,
                    source_language=source_language
                )
                
                if result["success"]:
                    return result["translated_text"]
                else:
                    # Fallback to original text
                    return text
                    
            except Exception:
                # Fallback to original text
                return text
        else:
            # Use legacy method (if exists) or return original
            return text
    
    def execute_prompt(self, category: str, prompt_id: str, language: str = "en", **variables) -> str:
        """Execute prompt - delegates to legacy service."""
        return self.legacy_service.execute_prompt(category, prompt_id, language, **variables)
    
    def substitute_template(self, category: str, prompt_id: str, language: str = "en", **variables) -> str:
        """Substitute template - delegates to legacy service."""
        return self.legacy_service.substitute_template(category, prompt_id, language, **variables)
    
    def _fallback_to_legacy_gherkin(self, test_cases: str, language: str = "en", **context) -> str:
        """Fallback to legacy Gherkin generation."""
        try:
            return self.legacy_service.generate_gherkin(test_cases, language, **context)
        except Exception:
            # If even legacy fails, return a basic template
            return f"""Feature: Test Feature
  Scenario: Test Scenario
    Given the application is ready
    When the user performs an action
    Then the expected result should occur
    
# Generated from: {test_cases}
"""
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get status of all services."""
        return {
            "legacy_service": "available",
            "new_services": {
                "gherkin_generator": self.use_new_gherkin,
                "story_enhancer": self.use_new_enhancement,
                "text_translator": self.use_new_translation
            },
            "feature_flags": {
                "use_new_gherkin": self.use_new_gherkin,
                "use_new_enhancement": self.use_new_enhancement,
                "use_new_translation": self.use_new_translation
            }
        }
