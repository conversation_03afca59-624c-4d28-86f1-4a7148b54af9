# LLM Services Architecture - Clean & Organized

Esta es la nueva arquitectura limpia para todos los servicios LLM en QAK, sin romper la funcionalidad existente.

## 📁 Estructura Organizada

```
src/services/llm/
├── __init__.py                    # Exports principales
├── llm_config.py                  # Configuración centralizada
├── base_llm_service.py           # Interface base abstracta
├── llm_service_factory.py        # Factory con routing inteligente
│
├── providers/                     # Proveedores LLM individuales
│   ├── __init__.py
│   ├── gemini_service.py         # Wrapper para Gemini existente
│   └── openrouter_service.py     # Nueva integración OpenRouter
│
├── use_cases/                     # Lógica de negocio de alto nivel
│   ├── __init__.py
│   ├── gherkin_generator.py      # Generación de Gherkin
│   ├── result_validator.py       # Validación de resultados
│   ├── text_translator.py        # Traducción ES↔EN
│   └── story_enhancer.py         # Mejora de user stories
│
└── adapters/                      # Bridges con código existente
    ├── __init__.py
    ├── prompt_service_adapter.py  # Mantiene API de PromptService
    └── legacy_llm_adapter.py      # Para otras integraciones
```

## 🎯 Beneficios de esta Organización

### **1. Separación de Responsabilidades**
- **Providers**: Lógica específica de cada LLM (Gemini, OpenRouter)
- **Use Cases**: Lógica de negocio pura, independiente del proveedor
- **Adapters**: Compatibilidad con código existente
- **Factory**: Orchestración inteligente y fallbacks

### **2. Migración Gradual Sin Romper Nada**
```python
# El código existente sigue funcionando:
from src.core.prompt_service import PromptService
service = PromptService()  # ✅ Funciona igual

# Nuevo código puede usar la nueva arquitectura:
from src.services.llm import LLMServiceFactory
factory = LLMServiceFactory()  # ✅ Nueva funcionalidad
```

### **3. Configuración Flexible**
```bash
# Variables de entorno para control granular
OPENROUTER_API_KEY=your_key
USE_OPENROUTER_FOR_GHERKIN=true
USE_OPENROUTER_FOR_VALIDATION=true
USE_OPENROUTER_FOR_TRANSLATION=false  # Gradual migration
OPENROUTER_PREFER_FREE_MODELS=true
```

### **4. Routing Inteligente**
```python
# Automatico basado en use case
request = LLMRequest(use_case="gherkin", ...)
response = factory.make_request(request)
# → Intenta OpenRouter primero, fallback a Gemini

request = LLMRequest(use_case="browser_automation", ...)  
response = factory.make_request(request)
# → Intenta Gemini primero (optimizado para browser)
```

## 🚀 Cómo Usar la Nueva Arquitectura

### **Opción 1: High-Level Use Cases (Recomendado)**
```python
from src.services.llm.use_cases import GherkinGenerator

generator = GherkinGenerator()
result = generator.generate_from_instructions(
    instructions="Login with valid credentials",
    user_story="As a user I want to login...",
    language="es"
)
```

### **Opción 2: Factory Direct**
```python
from src.services.llm import LLMServiceFactory, LLMRequest

factory = LLMServiceFactory()
request = LLMRequest(
    messages=[{"role": "user", "content": "Generate Gherkin..."}],
    use_case="gherkin",
    language="es"
)
response = factory.make_request(request)
```

### **Opción 3: Adapter (Compatibilidad)**
```python
from src.services.llm.adapters import PromptServiceAdapter

# Drop-in replacement for PromptService
adapter = PromptServiceAdapter()
gherkin = adapter.generate_gherkin(test_cases, language="es")
# ✅ Usa la nueva arquitectura internamente
```

## 🔧 Próximos Pasos

1. **✅ COMPLETADO**: Estructura organizada sin romper existente
2. **⏭️ SIGUIENTE**: Instalar `openrouter-client-unofficial`
3. **⏭️ DESPUÉS**: Migrar gradualmente los endpoints de `/src/api/`
4. **⏭️ FUTURO**: Añadir más proveedores (Claude directo, etc.)

## 🧪 Testing de la Nueva Estructura

```python
# Verificar que todo funciona
from src.services.llm.llm_service_factory import get_llm_factory

factory = get_llm_factory()
status = factory.get_health_status()
print(status)  # Debe mostrar proveedores disponibles
```

Esta organización es **escalable**, **mantenible** y **no rompe nada existente**. 🎉
