"""
Base LLM Service - Abstract interface for all LLM providers
Ensures consistent API across different providers (Gemini, OpenRouter, etc.)
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass


@dataclass
class LLMResponse:
    """Standardized LLM response format."""
    content: str
    model_used: str
    usage: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    success: bool = True
    error: Optional[str] = None


@dataclass
class ImageContent:
    """Image content for multimodal requests."""
    data: str  # Base64 encoded image data
    mime_type: str = "image/png"  # MIME type of the image

@dataclass
class MessageContent:
    """Content that can be text or image."""
    text: Optional[str] = None
    image: Optional[ImageContent] = None

@dataclass 
class LLMRequest:
    """Standardized LLM request format with multimodal support."""
    messages: List[Dict[str, Union[str, List[MessageContent]]]]
    use_case: str
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    language: str = "en"
    metadata: Optional[Dict[str, Any]] = None
    has_images: bool = False  # Flag to indicate if request contains images


class BaseLLMService(ABC):
    """Abstract base class for all LLM service providers."""
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if this LLM service is available."""
        pass
    
    @abstractmethod
    def get_provider_name(self) -> str:
        """Get the name of this LLM provider."""
        pass
    
    @abstractmethod
    def make_request(self, request: LLMRequest) -> LLMResponse:
        """Make a request to the LLM provider."""
        pass
    
    @abstractmethod
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get current usage statistics."""
        pass
    
    @abstractmethod
    def get_supported_use_cases(self) -> List[str]:
        """Get list of supported use cases."""
        pass
    
    def validate_request(self, request: LLMRequest) -> bool:
        """Validate request format and requirements."""
        if not request.messages:
            return False
        if not request.use_case:
            return False
        if request.use_case not in self.get_supported_use_cases():
            return False
        return True
