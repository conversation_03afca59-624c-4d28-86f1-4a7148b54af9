"""
Story Enhancer Use Case - Improve user stories using versioned prompts
Uses optimized LLM routing for cost efficiency
"""

from typing import Dict, Any
from ..prompt_llm_service import get_prompt_llm_service


class StoryEnhancer:
    """High-level service for enhancing user stories using versioned prompts."""
    
    def __init__(self):
        """Initialize the story enhancer."""
        self.prompt_llm_service = get_prompt_llm_service()
    
    def enhance_story(self, story: str, language: str = "en") -> Dict[str, Any]:
        """Enhance a user story using the versioned enhance prompt.
        
        Args:
            story: Original user story text
            language: Response language ('en' or 'es')
            
        Returns:
            Dict with enhanced story and metadata
        """
        # Use the versioned user-story/enhance prompt
        result = self.prompt_llm_service.execute_versioned_prompt(
            category="user-story",
            prompt_id="enhance",
            variables={
                "user_story": story
            },
            language=language,
            use_case="enhancement",
            max_tokens=1024,
            temperature=0.2
        )
        
        if result["success"]:
            return {
                "enhanced_story": result["content"],
                "model_used": result["model_used"],
                "usage": result["usage"],
                "cost_estimate": result.get("cost_estimate"),
                "provider": result.get("provider"),
                "prompt_version": result["prompt_version"],
                "success": True
            }
        else:
            return {
                "enhanced_story": story,  # Return original on failure
                "error": result["error"],
                "success": False
            }

    def enhance_user_story(self, user_story: str, language: str = "en",
                          include_edge_cases: bool = True,
                          include_technical_notes: bool = True) -> Dict[str, Any]:
        """Legacy method for backward compatibility - redirects to enhance_story."""
        return self.enhance_story(user_story, language)
        
        sections.append("5. User experience improvements and accessibility considerations")
        
        prompt = f"""Enhance this user story with comprehensive details:

Original User Story:
{user_story}

Provide an enhanced version that includes:
{chr(10).join(sections)}

Structure your response clearly with headings for each section.
Keep the original intent but add depth and clarity.
Focus on testable, actionable requirements.

Respond in {"Spanish" if language == "es" else "English"}.

Enhanced User Story:
"""
        return prompt
