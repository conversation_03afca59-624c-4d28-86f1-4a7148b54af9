"""
Result Validator Use Case - Intelligent test result validation
Uses LLM to determine if test results match expected outcomes
"""

import json
from typing import Dict, Any
from ..llm_service_factory import get_llm_factory
from ..base_llm_service import LLMRequest


class ResultValidator:
    """High-level service for validating test execution results."""
    
    def __init__(self):
        """Initialize the result validator."""
        self.llm_factory = get_llm_factory()
    
    def validate_execution_result(self, result_data: Dict[str, Any], 
                                test_objective: str, 
                                gherkin_scenario: str = "") -> Dict[str, Any]:
        """Validate test execution result using LLM analysis.
        
        Args:
            result_data: Test execution result data
            test_objective: What the test should accomplish
            gherkin_scenario: Optional Gherkin scenario context
            
        Returns:
            Dict with validation result and reasoning
        """
        # Build validation prompt
        prompt = self._build_validation_prompt(result_data, test_objective, gherkin_scenario)
        
        # Create LLM request
        request = LLMRequest(
            messages=[
                {"role": "system", "content": "You are an expert test result analyst. Analyze carefully and respond only with valid JSON."},
                {"role": "user", "content": prompt}
            ],
            use_case="validation",
            max_tokens=512,
            temperature=0.0  # Deterministic for validation
        )
        
        # Make request through factory
        response = self.llm_factory.make_request(request)
        
        if response.success:
            try:
                # Try to parse JSON response
                validation_result = json.loads(response.content)
                validation_result["model_used"] = response.model_used
                validation_result["usage"] = response.usage
                return validation_result
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                return {
                    "validated_success": None,
                    "validation_confidence": 0.0,
                    "validation_reasoning": f"Failed to parse LLM response: {response.content}",
                    "should_override_result": False,
                    "model_used": response.model_used,
                    "error": "JSON parsing failed"
                }
        else:
            return {
                "validated_success": None,
                "validation_confidence": 0.0,
                "validation_reasoning": f"LLM request failed: {response.error}",
                "should_override_result": False,
                "error": response.error
            }
    
    def _build_validation_prompt(self, result_data: Dict[str, Any], 
                               test_objective: str, gherkin_scenario: str = "") -> str:
        """Build the prompt for result validation."""
        prompt = f"""Analyze this test execution result and determine if it truly succeeded:

Test Objective: {test_objective}
{f"Gherkin Scenario: {gherkin_scenario}" if gherkin_scenario else ""}

Result Data:
{json.dumps(result_data, indent=2)}

Consider:
1. Did the test achieve its stated objective?
2. Are there any signs of failure in the steps or final state?
3. Does the reported success status match the actual outcome?
4. Are there any error messages or failed actions?

Respond with ONLY valid JSON in this exact format:
{{
    "validated_success": true/false,
    "validation_confidence": 0.0-1.0,
    "validation_reasoning": "Brief explanation of your analysis",
    "should_override_result": true/false
}}
"""
        return prompt
