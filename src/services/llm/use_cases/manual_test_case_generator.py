"""
Manual Test Case Generator Use Case - Generate manual test cases using versioned prompts
Uses optimized LLM routing for cost efficiency
"""

from typing import Dict, Any, List
from ..prompt_llm_service import get_prompt_llm_service


class ManualTestCaseGenerator:
    """High-level service for generating manual test cases using versioned prompts."""
    
    def __init__(self):
        """Initialize the manual test case generator."""
        self.prompt_llm_service = get_prompt_llm_service()
    
    def generate_from_story(self, enhanced_story: str, language: str = "en") -> Dict[str, Any]:
        """Generate manual test cases from enhanced user story using versioned prompts.
        
        Args:
            enhanced_story: Enhanced user story with acceptance criteria
            language: Response language ('en' or 'es')
            
        Returns:
            Dict with generated test cases and metadata
        """
        # Use the versioned test-cases/manual-generation prompt
        result = self.prompt_llm_service.execute_versioned_prompt(
            category="test-cases",
            prompt_id="manual-generation",
            variables={
                "user_story": enhanced_story,
                "context": ""
            },
            language=language,
            use_case="test_generation",
            max_tokens=2048,
            temperature=0.1
        )
        
        if result["success"]:
            return {
                "test_cases": result["content"],
                "model_used": result["model_used"],
                "usage": result["usage"],
                "cost_estimate": result.get("cost_estimate"),
                "provider": result.get("provider"),
                "prompt_version": result["prompt_version"],
                "success": True
            }
        else:
            return {
                "test_cases": "",
                "error": result["error"],
                "success": False
            }
        
        # Create LLM request
        request = LLMRequest(
            messages=[
                {"role": "system", "content": "You are an expert QA engineer specializing in test case design and comprehensive test coverage."},
                {"role": "user", "content": prompt}
            ],
            use_case="manual_test_generation",
            language=language,
            max_tokens=3048,
            temperature=0.1
        )
        
        # Make request through factory
        response = self.llm_factory.make_request(request)
        
        if response.success:
            # Try to parse JSON response
            try:
                test_cases = json.loads(response.content.strip())
                return {
                    "test_cases": test_cases,
                    "model_used": response.model_used,
                    "usage": response.usage,
                    "success": True
                }
            except json.JSONDecodeError:
                # If not valid JSON, return as string
                return {
                    "test_cases": response.content,
                    "model_used": response.model_used,
                    "usage": response.usage,
                    "success": True,
                    "format": "string"
                }
        else:
            return {
                "test_cases": [],
                "error": response.error,
                "success": False
            }
    
    def _build_manual_test_prompt(self, enhanced_story: str, language: str = "en") -> str:
        """Build the prompt for manual test case generation using versioned prompts."""
        # Use the versioned prompt from /prompts/test-cases/manual-generation.md
        prompt = f"""Your task is to generate detailed manual test cases from the following improved user story.

Improved user story:
{enhanced_story}

Please generate complete manual test cases following these guidelines:

1. Generate at least 5-10 test cases covering all acceptance criteria and main flows
2. Include positive and negative test cases
3. Each test case must include:
   - Test Case ID (format TC-001, TC-002, etc.)
   - Test Case Title (brief description of the test objective)
   - Preconditions (necessary prerequisites)
   - Test Steps (detailed numbered steps to execute the case)
   - Expected Results (what should happen)
   - Priority (High, Medium, Low)

IMPORTANT: Return the response as a valid JSON array. Each test case should be a JSON object with the following structure:
{{
  "id": "TC-001",
  "title": "Test case title",
  "preconditions": "Prerequisites for the test",
  "instrucciones": "Detailed step-by-step instructions",
  "expected_results": "Expected outcomes",
  "priority": "High/Medium/Low",
  "historia_de_usuario": "Related user story context"
}}

Do not include any introductory text, explanations, or markdown formatting. Return only the clean JSON array."""
        return prompt
