"""
Gherkin Generator Use Case - Clean business logic for Gherkin generation
Uses versioned prompts and optimized LLM routing
"""

from typing import Dict, Any
from ..prompt_llm_service import get_prompt_llm_service


class GherkinGenerator:
    """High-level service for generating Gherkin scenarios using versioned prompts."""
    
    def __init__(self):
        """Initialize the Gherkin generator."""
        self.prompt_llm_service = get_prompt_llm_service()
    
    def generate_from_instructions(self, instructions: str, user_story: str = "", 
                                 url: str = "", language: str = "en") -> Dict[str, Any]:
        """Generate Gherkin scenario from instructions using versioned prompts.
        
        Args:
            instructions: Test instructions
            user_story: Optional user story context
            url: Optional URL context  
            language: Response language ('en' or 'es')
            
        Returns:
            Dict with generated Gherkin and metadata
        """
        # Format input for the versioned prompt
        manual_test_cases_markdown = self._format_manual_test_cases(instructions, user_story, url)
        
        # Use the versioned gherkin-conversion prompt
        result = self.prompt_llm_service.execute_versioned_prompt(
            category="test-cases",
            prompt_id="gherkin-conversion", 
            variables={
                "manual_test_cases_markdown": manual_test_cases_markdown
            },
            language=language,
            use_case="gherkin",
            max_tokens=2048,
            temperature=0.1
        )
        
        if result["success"]:
            return {
                "gherkin": result["content"],
                "model_used": result["model_used"],
                "usage": result["usage"],
                "cost_estimate": result.get("cost_estimate"),
                "provider": result.get("provider"),
                "prompt_version": result["prompt_version"],
                "success": True
            }
        else:
            return {
                "gherkin": "",
                "error": result["error"],
                "success": False
            }
    
    def _format_manual_test_cases(self, instructions: str, user_story: str = "", url: str = "") -> str:
        """Format instructions into the expected markdown format for the versioned prompt."""
        formatted = f"## Manual Test Cases\n\n{instructions}\n"
        
        if user_story:
            formatted += f"\n### Context\n**User Story:** {user_story}\n"
            
        if url:
            formatted += f"**URL:** {url}\n"
            
        return formatted
            
        return formatted
