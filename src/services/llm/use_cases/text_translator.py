"""
Text Translator Use Case - Intelligent translation service using versioned prompts
Handles ES↔EN translation for prompts and content with OpenRouter optimization
"""

from typing import Dict, Any
from ..prompt_llm_service import get_prompt_llm_service


class TextTranslator:
    """High-level service for text translation using versioned prompts."""
    
    def __init__(self):
        """Initialize the text translator."""
        self.prompt_llm_service = get_prompt_llm_service()
    
    def translate(self, text: str, target_language: str, 
                 source_language: str = "auto", 
                 preserve_technical_terms: bool = True) -> Dict[str, Any]:
        """Translate text between languages using versioned prompts.
        
        Args:
            text: Text to translate
            target_language: Target language ('en' or 'es')
            source_language: Source language ('en', 'es', or 'auto')
            preserve_technical_terms: Whether to preserve technical terms
            
        Returns:
            Dict with translated text and metadata
        """
        # Skip translation if already in target language
        if source_language == target_language and source_language != "auto":
            return {
                "translated_text": text,
                "source_language": source_language,
                "target_language": target_language,
                "model_used": "none",
                "skipped": True,
                "success": True
            }
        
        # Prepare variables for the versioned prompt
        lang_names = {"en": "English", "es": "Spanish", "auto": "automatically detected language"}
        source_name = lang_names.get(source_language, source_language)
        target_name = lang_names.get(target_language, target_language)
        
        preserve_instruction = ""
        if preserve_technical_terms:
            preserve_instruction = "- Preserve technical terms, API names, and code elements unchanged"
        
        # Use the versioned translation prompt
        result = self.prompt_llm_service.execute_versioned_prompt(
            category="translation",
            prompt_id="text-translation",
            variables={
                "text": text,
                "source_language": source_name,
                "target_language": target_name,
                "preserve_technical_terms_instruction": preserve_instruction
            },
            language=target_language,
            use_case="translation",
            max_tokens=1024,
            temperature=0.1
        )
        
        if result["success"]:
            return {
                "translated_text": result["content"].strip(),
                "source_language": source_language,
                "target_language": target_language,
                "model_used": result["model_used"],
                "usage": result["usage"],
                "cost_estimate": result.get("cost_estimate"),
                "provider": result.get("provider"),
                "prompt_version": result["prompt_version"],
                "success": True
            }
        else:
            return {
                "translated_text": text,  # Return original on failure
                "error": result["error"],
                "source_language": source_language,
                "target_language": target_language,
                "success": False
            }
