"""
Unified Prompt + LLM Service
Integrates the existing PromptService (versioned prompts) with the new LLM architecture (OpenRouter)
"""

import os
import logging
from typing import Dict, Any, Optional, List
from .llm_service_factory import get_llm_factory
from .base_llm_service import LLMRequest, MessageContent, ImageContent
from src.core.prompt_service import PromptService

logger = logging.getLogger(__name__)


class PromptLLMService:
    """
    Unified service that combines:
    - PromptService: Versioned prompts from markdown files
    - LLMServiceFactory: OpenRouter + Gemini routing and cost optimization
    """
    
    def __init__(self, prompts_dir: str = "prompts"):
        """Initialize the unified service."""
        self.prompt_service = PromptService(prompts_dir)
        self.llm_factory = get_llm_factory()
        
    def execute_versioned_prompt(
        self, 
        category: str, 
        prompt_id: str, 
        variables: Dict[str, Any],
        language: str = "en",
        max_tokens: int = 2048,
        temperature: float = 0.1,
        use_case: str = "general",
        images: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Execute a versioned prompt through the optimized LLM architecture.
        
        Args:
            category: Prompt category (e.g., 'user-story', 'test-cases')
            prompt_id: Prompt identifier (e.g., 'enhance', 'gherkin-conversion')
            variables: Variables to substitute in the prompt
            language: Language for response ('en' or 'es')
            max_tokens: Maximum tokens for response
            temperature: LLM temperature (0.0-1.0)
            use_case: Use case for cost optimization routing
            
        Returns:
            Dict with response content, model used, usage stats, etc.
        """
        try:
            # Load and render the versioned prompt using substitute_template
            prompt_content = self.prompt_service.substitute_template(category, prompt_id, language, **variables)
            
            # Create LLM request using new architecture with image support
            messages = [
                {
                    "role": "system", 
                    "content": self._get_system_prompt(category, prompt_id)
                }
            ]
            
            # Add user message with optional images
            if images:
                # Create multimodal message
                content_parts = [MessageContent(text=prompt_content)]
                for image_base64 in images:
                    content_parts.append(MessageContent(image=ImageContent(data=image_base64)))
                messages.append({
                    "role": "user",
                    "content": content_parts
                })
            else:
                # Text-only message
                messages.append({"role": "user", "content": prompt_content})
            
            request = LLMRequest(
                messages=messages,
                use_case=use_case,
                language=language,
                max_tokens=max_tokens,
                temperature=temperature,
                has_images=bool(images)
            )
            
            # Execute through LLM factory (OpenRouter + fallback)
            response = self.llm_factory.make_request(request)
            
            if response.success:
                return {
                    "content": response.content,
                    "model_used": response.model_used,
                    "usage": response.usage,
                    "cost_estimate": getattr(response, 'cost_estimate', None),
                    "provider": getattr(response, 'provider', 'unknown'),
                    "prompt_version": f"{category}/{prompt_id}",
                    "success": True
                }
            else:
                logger.error(f"LLM request failed: {response.error}")
                return {
                    "content": "",
                    "error": response.error,
                    "prompt_version": f"{category}/{prompt_id}",
                    "success": False
                }
                
        except Exception as e:
            logger.error(f"Error executing versioned prompt {category}/{prompt_id}: {e}")
            return {
                "content": "",
                "error": str(e),
                "prompt_version": f"{category}/{prompt_id}",
                "success": False
            }
    
    def _get_system_prompt(self, category: str, prompt_id: str) -> str:
        """Get appropriate system prompt based on category and prompt type."""
        system_prompts = {
            "test-cases": {
                "gherkin-conversion": "You are an expert QA engineer specializing in Gherkin test scenarios and BDD practices.",
                "manual-generation": "You are an expert QA engineer specializing in manual test case creation."
            },
            "user-story": {
                "enhance": "You are an expert Business Analyst specializing in user story development and requirements engineering."
            },
            "code-generation": {
                "playwright": "You are an expert automation engineer specializing in Playwright test automation.",
                "cypress": "You are an expert automation engineer specializing in Cypress test automation.",
                "selenium-pytest": "You are an expert automation engineer specializing in Selenium with PyTest.",
                "robot-framework": "You are an expert automation engineer specializing in Robot Framework.",
                "java-selenium": "You are an expert automation engineer specializing in Java Selenium automation."
            },
            "test-analysis": {
                "results-summary": "You are an expert QA analyst specializing in test result analysis and reporting.",
                "step-validation": "You are an expert QA analyst specializing in browser automation testing step validation.",
                "test-completion-analysis": "You are an experienced QA Director specializing in comprehensive test execution analysis."
            },
            "browser-automation": {
                "task-generation": "You are an expert in browser automation and task decomposition.",
                "captcha-handling": "You are an expert in web automation and captcha handling strategies."
            },
            "translation": {
                "text-translation": "You are an expert translator specializing in technical content and preserving formatting."
            }
        }
        
        return system_prompts.get(category, {}).get(prompt_id, 
            "You are an expert AI assistant specialized in software quality assurance and testing.")
    
    def execute_prompt(
        self,
        prompt_text: str,
        use_case: str = "general",
        language: str = "en",
        max_tokens: int = 2048,
        temperature: float = 0.1,
        context: Optional[Dict[str, Any]] = None,
        images: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Execute a direct prompt through the LLM architecture (without versioned prompts).
        
        Args:
            prompt_text: Direct prompt text to execute
            use_case: Use case for cost optimization routing
            language: Language for response ('en' or 'es')
            max_tokens: Maximum tokens for response
            temperature: LLM temperature (0.0-1.0)
            context: Optional context for the request
            
        Returns:
            Dict with response content, model used, usage stats, etc.
        """
        try:
            # Create LLM request using new architecture with image support
            messages = []
            
            if images:
                # Create multimodal message
                content_parts = [MessageContent(text=prompt_text)]
                for image_base64 in images:
                    content_parts.append(MessageContent(image=ImageContent(data=image_base64)))
                messages.append({
                    "role": "user",
                    "content": content_parts
                })
            else:
                # Text-only message
                messages.append({"role": "user", "content": prompt_text})
            
            request = LLMRequest(
                messages=messages,
                use_case=use_case,
                language=language,
                max_tokens=max_tokens,
                temperature=temperature,
                has_images=bool(images)
            )
            
            # Execute through LLM factory (OpenRouter + fallback)
            response = self.llm_factory.make_request(request)
            
            if response.success:
                return {
                    "content": response.content,
                    "model_used": response.model_used,
                    "usage": response.usage,
                    "cost_estimate": getattr(response, 'cost_estimate', None),
                    "provider": getattr(response, 'provider', 'unknown'),
                    "metadata": getattr(response, 'metadata', {}),
                    "prompt_type": "direct",
                    "success": True
                }
            else:
                logger.error(f"LLM request failed: {response.error}")
                return {
                    "content": "",
                    "error": response.error,
                    "prompt_type": "direct",
                    "success": False
                }
                
        except Exception as e:
            logger.error(f"Error executing direct prompt: {e}")
            return {
                "content": "",
                "error": str(e),
                "prompt_type": "direct",
                "success": False
            }


# Global instance for easy access
_prompt_llm_service = None

def get_prompt_llm_service() -> PromptLLMService:
    """Get the global PromptLLMService instance."""
    global _prompt_llm_service
    if _prompt_llm_service is None:
        _prompt_llm_service = PromptLLMService()
    return _prompt_llm_service
