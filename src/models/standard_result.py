"""
Standard Result Model for QAK Test Execution System

Unifies all response formats from different test execution types into a single,
consistent data structure with proper validation and serialization.
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, field, asdict
from pydantic import BaseModel, Field, field_validator, ConfigDict
import json
import uuid


class ExecutionStatus(str, Enum):
    """Standard execution status values."""
    SUCCESS = "success"
    FAILURE = "failure"
    ERROR = "error"
    RUNNING = "running"
    PENDING = "pending"
    PAUSED = "paused"
    CANCELLED = "cancelled"


class TestType(str, Enum):
    """Types of test execution."""
    SMOKE = "smoke"
    FULL = "full"
    CASE = "case"
    SUITE = "suite"
    CODEGEN = "codegen"


@dataclass 
class ElementLocator:
    """Element locator information for script generation."""
    css_selector: Optional[str] = None
    xpath: Optional[str] = None
    text_selector: Optional[str] = None  # For locator.getByText()
    role_selector: Optional[str] = None  # For locator.getByRole()
    label_selector: Optional[str] = None  # For locator.getByLabel()
    placeholder_selector: Optional[str] = None  # For locator.getByPlaceholder()
    testid_selector: Optional[str] = None  # For locator.getByTestId()
    title_selector: Optional[str] = None  # For locator.getByTitle()
    
@dataclass
class TestStep:
    """Individual test step data."""
    step_number: int
    action_type: str  # click, type, select, hover, etc.
    description: str
    success: bool
    duration_ms: Optional[int] = None
    error_message: Optional[str] = None
    screenshot_url: Optional[str] = None
    element_info: Optional[Dict[str, Any]] = None
    url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    # Enhanced fields for script generation
    element_locators: Optional[ElementLocator] = None  # Multiple locator strategies
    input_data: Optional[str] = None  # Data typed/selected
    expected_result: Optional[str] = None  # What should happen
    wait_conditions: Optional[List[str]] = None  # Conditions to wait for
    assertions: Optional[List[Dict[str, Any]]] = None  # Validations to add


@dataclass
class TestSummary:
    """Summary statistics for test execution."""
    total_steps: int = 0
    successful_steps: int = 0
    failed_steps: int = 0
    total_test_cases: int = 0
    passed_test_cases: int = 0
    failed_test_cases: int = 0
    success_rate: float = 0.0
    
    def calculate_success_rate(self):
        """Calculate and update success rate."""
        if self.total_steps > 0:
            self.success_rate = self.successful_steps / self.total_steps
        else:
            self.success_rate = 0.0


@dataclass
class Artifacts:
    """Test execution artifacts."""
    screenshots: List[str] = field(default_factory=list)
    videos: List[str] = field(default_factory=list)
    logs: List[str] = field(default_factory=list)
    generated_code: Optional[str] = None
    history_file: Optional[str] = None
    gherkin_scenarios: List[str] = field(default_factory=list)


class StandardResult(BaseModel):
    """
    Unified standard result format for all test execution types.
    
    This replaces multiple legacy formats:
    - TestExecutionResponse
    - SuiteExecutionResponse  
    - TestExecutionHistoryData
    - CodegenExecutionResponse
    """
    
    # Execution identification
    execution_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    test_type: TestType
    test_id: Optional[str] = None
    suite_id: Optional[str] = None
    project_id: Optional[str] = None
    
    # Execution status
    status: ExecutionStatus
    started_at: datetime = Field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    duration_ms: Optional[int] = None
    
    # Results and data
    summary: TestSummary = Field(default_factory=TestSummary)
    steps: List[TestStep] = Field(default_factory=list)
    errors: List[str] = Field(default_factory=list)
    
    # Artifacts
    artifacts: Artifacts = Field(default_factory=Artifacts)
    
    # Configuration used
    configuration: Dict[str, Any] = Field(default_factory=dict)
    
    # Legacy compatibility fields
    success: bool = True  # For backward compatibility
    message: Optional[str] = None
    error: Optional[str] = None
    
    # Raw data for detailed analysis (optional)
    raw_data: Optional[Dict[str, Any]] = None
    
    # Raw result data for processing (list of result items)
    raw_result: Optional[List[Any]] = None
    
    # Additional metadata
    metadata: Optional[Dict[str, Any]] = None
    
    model_config = ConfigDict(
        use_enum_values=True,
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        }
    )
    
    @field_validator('duration_ms', mode='before')
    @classmethod
    def calculate_duration(cls, v, info):
        """Calculate duration if not provided and both timestamps exist."""
        if v is None and hasattr(info, 'data'):
            values = info.data
            if 'started_at' in values and values.get('completed_at'):
                start = values['started_at']
                end = values['completed_at']
                if isinstance(start, datetime) and isinstance(end, datetime):
                    return int((end - start).total_seconds() * 1000)
        return v
    
    @field_validator('success', mode='before')
    @classmethod
    def calculate_success(cls, v, info):
        """Calculate success based on status if not explicitly set."""
        if hasattr(info, 'data') and 'status' in info.data:
            return info.data['status'] == ExecutionStatus.SUCCESS
        return v
    
    def complete_execution(self, status: ExecutionStatus = ExecutionStatus.SUCCESS):
        """Mark execution as complete and calculate final metrics."""
        self.completed_at = datetime.now()
        self.status = status
        
        # Calculate duration
        if self.started_at and self.completed_at:
            self.duration_ms = int((self.completed_at - self.started_at).total_seconds() * 1000)
        
        # Update summary
        self.summary.total_steps = len(self.steps)
        self.summary.successful_steps = sum(1 for step in self.steps if step.success)
        self.summary.failed_steps = self.summary.total_steps - self.summary.successful_steps
        self.summary.calculate_success_rate()
        
        # Update success flag
        self.success = status == ExecutionStatus.SUCCESS
        
        # Set error message if failed
        if status == ExecutionStatus.FAILURE and self.errors:
            self.error = "; ".join(self.errors)
    
    def add_step(self, step: TestStep):
        """Add a test step to the execution."""
        self.steps.append(step)
        
        # Update real-time summary
        self.summary.total_steps = len(self.steps)
        self.summary.successful_steps = sum(1 for s in self.steps if s.success)
        self.summary.failed_steps = self.summary.total_steps - self.summary.successful_steps
        self.summary.calculate_success_rate()
    
    def add_error(self, error: str):
        """Add an error message."""
        self.errors.append(error)
        if not self.error:
            self.error = error
    
    def add_artifact(self, artifact_type: str, url: str):
        """Add an artifact URL."""
        if artifact_type == "screenshot":
            self.artifacts.screenshots.append(url)
        elif artifact_type == "video":
            self.artifacts.videos.append(url)
        elif artifact_type == "log":
            self.artifacts.logs.append(url)
        elif artifact_type == "history":
            self.artifacts.history_file = url
        elif artifact_type == "code":
            self.artifacts.generated_code = url
        elif artifact_type == "gherkin":
            self.artifacts.gherkin_scenarios.append(url)
    
    def to_legacy_format(self, format_type: str) -> Dict[str, Any]:
        """
        Convert to legacy format for backward compatibility.
        
        Args:
            format_type: Legacy format type (execution, suite, history, codegen)
            
        Returns:
            Dict in legacy format
        """
        base_data = {
            "success": self.success,
            "execution_time": f"{self.duration_ms}ms" if self.duration_ms else None,
            "error": self.error
        }
        
        if format_type == "execution":
            return {
                **base_data,
                "test_id": self.test_id,
                "result": self.to_dict()
            }
        
        elif format_type == "suite":
            return {
                **base_data,
                "suite_id": self.suite_id,
                "total_tests": self.summary.total_test_cases,
                "passed": self.summary.passed_test_cases,
                "failed": self.summary.failed_test_cases,
                "results": [self.to_dict()]  # Single result for now
            }
        
        elif format_type == "history":
            return {
                "actions": [
                    {
                        "step": step.step_number,
                        "type": step.action_type,
                        "details": step.description
                    } for step in self.steps
                ],
                "results": [
                    {
                        "step": step.step_number,
                        "content": step.description,
                        "success": step.success
                    } for step in self.steps
                ],
                "elements": [
                    {
                        "step": step.step_number,
                        **step.element_info
                    } for step in self.steps if step.element_info
                ],
                "urls": [
                    {
                        "step": step.step_number,
                        "url": step.url
                    } for step in self.steps if step.url
                ],
                "errors": self.errors,
                "screenshots": self.artifacts.screenshots,
                "metadata": {
                    "start_time": self.started_at.isoformat() if self.started_at else None,
                    "end_time": self.completed_at.isoformat() if self.completed_at else None,
                    "total_steps": self.summary.total_steps,
                    "success": self.success
                }
            }
        
        elif format_type == "codegen":
            return {
                "execution_id": self.execution_id,
                "status": self.status.value if hasattr(self.status, 'value') else str(self.status),
                "message": self.message or "",
                "result": self.to_dict()
            }
        
        else:
            raise ValueError(f"Unknown legacy format type: {format_type}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with proper serialization."""
        start_time_iso = self.started_at.isoformat() if self.started_at else None
        end_time_iso = self.completed_at.isoformat() if self.completed_at else None
        
        return {
            "execution_id": self.execution_id,
            "test_type": self.test_type.value if hasattr(self.test_type, 'value') else str(self.test_type),
            "test_id": self.test_id,
            "suite_id": self.suite_id,
            "project_id": self.project_id,
            "status": self.status.value if hasattr(self.status, 'value') else str(self.status),
            "started_at": start_time_iso,
            "completed_at": end_time_iso,
            # Frontend compatibility fields
            "start_time": start_time_iso,
            "end_time": end_time_iso,
            "duration_ms": self.duration_ms,
            "summary": asdict(self.summary),
            "steps": [asdict(step) for step in self.steps],
            "errors": self.errors,
            "artifacts": asdict(self.artifacts),
            "configuration": self.configuration,
            "success": self.success,
            "message": self.message,
            "error": self.error,
            "raw_data": self.raw_data,
            "raw_result": self.raw_result,  # Include raw result for processing
            "metadata": self.metadata  # Include metadata field for frontend
        }
    
    def to_dict_optimized(self) -> Dict[str, Any]:
        """Convert to dictionary with optimized serialization (excludes base64 screenshots)."""
        start_time_iso = self.started_at.isoformat() if self.started_at else None
        end_time_iso = self.completed_at.isoformat() if self.completed_at else None
        
        # Create optimized steps without base64 screenshot data
        optimized_steps = []
        for step in self.steps:
            step_dict = asdict(step)
            # Remove screenshot_url if it contains base64 data
            if 'screenshot_url' in step_dict and step_dict['screenshot_url']:
                if step_dict['screenshot_url'].startswith('data:image/'):
                    step_dict['screenshot_url'] = '[base64_removed_for_performance]'
            optimized_steps.append(step_dict)
        
        return {
            "execution_id": self.execution_id,
            "test_type": self.test_type.value if hasattr(self.test_type, 'value') else str(self.test_type),
            "test_id": self.test_id,
            "suite_id": self.suite_id,
            "project_id": self.project_id,
            "status": self.status.value if hasattr(self.status, 'value') else str(self.status),
            "started_at": start_time_iso,
            "completed_at": end_time_iso,
            # Frontend compatibility fields
            "start_time": start_time_iso,
            "end_time": end_time_iso,
            "duration_ms": self.duration_ms,
            "summary": asdict(self.summary),
            "steps": optimized_steps,  # Use optimized steps without base64
            "errors": self.errors,
            "artifacts": asdict(self.artifacts),
            "configuration": self.configuration,
            "success": self.success,
            "message": self.message,
            "error": self.error,
            "raw_data": self.raw_data,
            "raw_result": self.raw_result,  # Include raw result for processing
            "metadata": self.metadata  # Include metadata field for frontend
        }
    
    @classmethod
    def from_legacy_data(cls, data: Dict[str, Any], test_type: TestType) -> 'StandardResult':
        """
        Create StandardResult from legacy data formats.
        
        Args:
            data: Legacy format data
            test_type: Type of test execution
            
        Returns:
            StandardResult instance
        """
        result = cls(test_type=test_type)
        
        # Map common fields
        if "test_id" in data:
            result.test_id = data["test_id"]
        if "suite_id" in data:
            result.suite_id = data["suite_id"]
        if "success" in data:
            result.success = data["success"]
            result.status = ExecutionStatus.SUCCESS if data["success"] else ExecutionStatus.FAILURE
        if "error" in data and data["error"]:
            result.add_error(data["error"])
        if "message" in data:
            result.message = data["message"]
        
        # Handle legacy history data format
        if "actions" in data and "results" in data:
            actions = data.get("actions", [])
            results = data.get("results", [])
            elements = data.get("elements", [])
            urls = data.get("urls", [])
            
            # Create steps from legacy data
            for i, action in enumerate(actions):
                step_result = next((r for r in results if r.get("step") == action.get("step")), {})
                step_element = next((e for e in elements if e.get("step") == action.get("step")), {})
                step_url = next((u for u in urls if u.get("step") == action.get("step")), {})
                
                step = TestStep(
                    step_number=action.get("step", i + 1),
                    action_type=action.get("type", "unknown"),
                    description=action.get("details", ""),
                    success=step_result.get("success", True),
                    element_info=step_element if step_element else None,
                    url=step_url.get("url") if step_url else None
                )
                result.add_step(step)
            
            # Add artifacts
            if "screenshots" in data:
                result.artifacts.screenshots = data["screenshots"]
            if "errors" in data:
                result.errors.extend(data["errors"])
        
        # Store raw data for debugging
        result.raw_data = data
        
        return result


def create_success_result(
    test_type: TestType,
    test_id: Optional[str] = None,
    suite_id: Optional[str] = None,
    message: str = "Test executed successfully"
) -> StandardResult:
    """Create a successful test result."""
    result = StandardResult(
        test_type=test_type,
        test_id=test_id,
        suite_id=suite_id,
        status=ExecutionStatus.SUCCESS,
        message=message
    )
    result.complete_execution(ExecutionStatus.SUCCESS)
    return result


def create_error_result(
    test_type: TestType,
    error_message: str,
    test_id: Optional[str] = None,
    suite_id: Optional[str] = None
) -> StandardResult:
    """Create an error test result."""
    result = StandardResult(
        test_type=test_type,
        test_id=test_id,
        suite_id=suite_id,
        status=ExecutionStatus.ERROR,
        message=error_message
    )
    result.add_error(error_message)
    result.complete_execution(ExecutionStatus.ERROR)
    return result 