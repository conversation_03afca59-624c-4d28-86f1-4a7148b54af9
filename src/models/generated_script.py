"""
Generated Script Models

Models for storing and managing generated automation scripts with visual editing capabilities.
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, field, asdict
from pydantic import BaseModel, Field, field_validator


class ActionType(str, Enum):
    """User-friendly action types."""
    NAVIGATE = "navigate"
    CLICK = "click"
    TYPE_TEXT = "type_text"
    SELECT_OPTION = "select_option"
    WAIT = "wait"
    VERIFY = "verify"
    SCROLL = "scroll"
    HOVER = "hover"
    TAKE_SCREENSHOT = "take_screenshot"
    CUSTOM = "custom"


class ValidationType(str, Enum):
    """Types of validations that can be added."""
    TEXT_CONTAINS = "text_contains"
    TEXT_EQUALS = "text_equals"
    ELEMENT_VISIBLE = "element_visible"
    ELEMENT_ENABLED = "element_enabled"
    URL_CONTAINS = "url_contains"
    VALUE_EQUALS = "value_equals"
    COUNT_EQUALS = "count_equals"
    CUSTOM_ASSERTION = "custom_assertion"


@dataclass
class ElementSelector:
    """User-friendly element selector."""
    description: str  # Human-readable description like "Login button"
    method: str  # "testid", "text", "label", "css", "xpath"
    value: str  # The actual selector value
    backup_selectors: List[Dict[str, str]] = field(default_factory=list)


@dataclass
class Validation:
    """User-friendly validation."""
    id: str
    type: ValidationType
    description: str  # Human-readable description
    expected_value: Optional[str] = None
    element_selector: Optional[ElementSelector] = None
    enabled: bool = True


@dataclass
class EditableStep:
    """User-friendly editable test step."""
    id: str
    step_number: int
    action_type: ActionType
    description: str  # Human-readable description
    
    # Action-specific data
    element_selector: Optional[ElementSelector] = None
    input_data: Optional[str] = None
    url: Optional[str] = None
    wait_time: Optional[int] = None  # in seconds
    
    # Validations for this step
    validations: List[Validation] = field(default_factory=list)
    
    # Metadata
    notes: Optional[str] = None
    enabled: bool = True
    screenshot_after: bool = False
    
    # Original step info for reference
    original_step_number: Optional[int] = None
    success_in_original: bool = True


class GeneratedScript(BaseModel):
    """Model for generated automation script with editing capabilities."""
    
    # Script identification
    script_id: str = Field(default_factory=lambda: f"script_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    execution_id: str
    framework: str  # "playwright_python", "selenium_python", etc.
    
    # Generation info
    generated_at: datetime = Field(default_factory=datetime.now)
    generated_by: str = "QAK Script Generator"
    version: int = 1
    
    # Script content
    raw_script_content: str  # Original generated script
    editable_steps: List[EditableStep] = Field(default_factory=list)
    
    # Script metadata
    script_name: str = "Generated Test Script"
    description: str = ""
    script_summary: str = ""  # AI-generated human-readable summary
    target_url: Optional[str] = None
    estimated_duration_seconds: Optional[int] = None
    
    # Configuration
    include_screenshots: bool = False
    include_waits: bool = True
    include_validations: bool = True
    error_handling: str = "continue"  # "stop", "continue", "retry"
    
    # Export options
    export_format: str = "pytest"  # "pytest", "unittest", "raw"
    include_page_objects: bool = False
    
    # Statistics
    total_steps: int = 0
    enabled_steps: int = 0
    validations_count: int = 0
    
    model_config = {"arbitrary_types_allowed": True}
    
    @field_validator('editable_steps', mode='before')
    @classmethod
    def convert_editable_steps(cls, v):
        """Convert EditableStep dataclasses to dicts if needed."""
        import logging
        logger = logging.getLogger(__name__)
        
        try:
            if v and isinstance(v, list):
                result = []
                for i, step in enumerate(v):
                    try:
                        if hasattr(step, '__dataclass_fields__'):
                            # It's a dataclass, convert to dict
                            step_dict = asdict(step)
                            # Ensure all required fields are present
                            if 'id' not in step_dict:
                                step_dict['id'] = f"step_{i}"
                            if 'enabled' not in step_dict:
                                step_dict['enabled'] = True
                            if 'validations' not in step_dict:
                                step_dict['validations'] = []
                            if 'success_in_original' not in step_dict:
                                step_dict['success_in_original'] = True
                            result.append(step_dict)
                        elif isinstance(step, dict):
                            # Already a dict, ensure required fields
                            step_copy = step.copy()
                            if 'id' not in step_copy:
                                step_copy['id'] = f"step_{i}"
                            if 'enabled' not in step_copy:
                                step_copy['enabled'] = True
                            if 'validations' not in step_copy:
                                step_copy['validations'] = []
                            if 'success_in_original' not in step_copy:
                                step_copy['success_in_original'] = True
                            result.append(step_copy)
                        else:
                            logger.warning(f"⚠️ Unknown step type {type(step)}: {step}")
                            continue
                    except Exception as e:
                        logger.error(f"❌ Error converting step {i}: {e}")
                        continue
                
                logger.info(f"✅ convert_editable_steps processed {len(result)} steps")
                return result
            
            logger.info(f"⚠️ convert_editable_steps: received non-list or empty: {type(v)}")
            return v if v is not None else []
            
        except Exception as e:
            logger.error(f"❌ Error in convert_editable_steps: {e}", exc_info=True)
            return []
    
    def model_dump(self, **kwargs) -> dict:
        """Override model_dump to properly serialize dataclasses."""
        import logging
        logger = logging.getLogger(__name__)
        
        try:
            # Update statistics before dumping
            self._update_statistics()
            
            # Call parent model_dump with error handling
            data = super().model_dump(**kwargs)
            
            # Ensure editable_steps is always a list
            if 'editable_steps' not in data:
                data['editable_steps'] = []
                logger.info("🔧 Added empty editable_steps list")
            elif not isinstance(data['editable_steps'], list):
                data['editable_steps'] = []
                logger.warning("🔧 Reset invalid editable_steps to empty list")
            
            # Ensure generated_at is a string
            if 'generated_at' in data and hasattr(data['generated_at'], 'isoformat'):
                data['generated_at'] = data['generated_at'].isoformat()
            
            logger.info(f"✅ model_dump successful with {len(data.get('editable_steps', []))} steps")
            return data
            
        except Exception as e:
            logger.error(f"❌ model_dump failed: {e}", exc_info=True)
            # Return a minimal valid dict
            try:
                generated_at_str = self.generated_at.isoformat() if hasattr(self.generated_at, 'isoformat') else str(self.generated_at)
            except:
                generated_at_str = datetime.now().isoformat()
                
            return {
                'script_id': getattr(self, 'script_id', 'unknown'),
                'execution_id': getattr(self, 'execution_id', 'unknown'),
                'framework': getattr(self, 'framework', 'unknown'),
                'generated_at': generated_at_str,
                'generated_by': getattr(self, 'generated_by', 'QAK Script Generator'),
                'version': getattr(self, 'version', 1),
                'script_name': getattr(self, 'script_name', 'Generated Script'),
                'description': getattr(self, 'description', ''),
                'script_summary': getattr(self, 'script_summary', ''),
                'target_url': getattr(self, 'target_url', None),
                'raw_script_content': getattr(self, 'raw_script_content', ''),
                'editable_steps': [],
                'total_steps': 0,
                'enabled_steps': 0,
                'validations_count': 0,
                'include_screenshots': getattr(self, 'include_screenshots', False),
                'include_waits': getattr(self, 'include_waits', True),
                'include_validations': getattr(self, 'include_validations', True),
                'error_handling': getattr(self, 'error_handling', 'continue'),
                'export_format': getattr(self, 'export_format', 'pytest'),
                'include_page_objects': getattr(self, 'include_page_objects', False),
                'estimated_duration_seconds': getattr(self, 'estimated_duration_seconds', None)
            }
    
    def add_step(self, step: EditableStep):
        """Add a new step to the script."""
        self.editable_steps.append(step)
        self._update_statistics()
    
    def remove_step(self, step_id: str):
        """Remove a step from the script."""
        self.editable_steps = [s for s in self.editable_steps if s.id != step_id]
        self._renumber_steps()
        self._update_statistics()
    
    def move_step(self, step_id: str, new_position: int):
        """Move a step to a new position."""
        step = next((s for s in self.editable_steps if s.id == step_id), None)
        if step:
            self.editable_steps.remove(step)
            self.editable_steps.insert(new_position - 1, step)
            self._renumber_steps()
    
    def duplicate_step(self, step_id: str):
        """Duplicate an existing step."""
        step = next((s for s in self.editable_steps if s.id == step_id), None)
        if step:
            new_step = EditableStep(
                id=f"{step.id}_copy_{len(self.editable_steps)}",
                step_number=step.step_number + 1,
                action_type=step.action_type,
                description=f"{step.description} (Copy)",
                element_selector=step.element_selector,
                input_data=step.input_data,
                url=step.url,
                wait_time=step.wait_time,
                validations=step.validations.copy(),
                notes=step.notes,
                enabled=step.enabled,
                screenshot_after=step.screenshot_after
            )
            self.add_step(new_step)
    
    def add_validation_to_step(self, step_id: str, validation: Validation):
        """Add a validation to a specific step."""
        step = next((s for s in self.editable_steps if s.id == step_id), None)
        if step:
            step.validations.append(validation)
            self._update_statistics()
    
    def _renumber_steps(self):
        """Renumber all steps sequentially."""
        for i, step in enumerate(self.editable_steps, 1):
            step.step_number = i
    
    def _update_statistics(self):
        """Update script statistics."""
        self.total_steps = len(self.editable_steps)
        self.enabled_steps = sum(1 for s in self.editable_steps if s.enabled)
        self.validations_count = sum(len(s.validations) for s in self.editable_steps)
    
    def to_executable_script(self) -> str:
        """Generate executable script from editable steps."""
        # This will be implemented based on the framework
        return self._generate_script_content()
    
    def _generate_script_content(self) -> str:
        """Generate the actual script content from editable steps."""
        if self.framework == "playwright_python":
            return self._generate_playwright_python()
        elif self.framework == "selenium_python":
            return self._generate_selenium_python()
        else:
            return self.raw_script_content
    
    def _generate_playwright_python(self) -> str:
        """Generate Playwright Python script from editable steps."""
        lines = [
            "import pytest",
            "from playwright.sync_api import Page, expect",
            "",
            f"def test_{self.script_id.replace('-', '_')}(page: Page):",
            f'    """',
            f'    {self.description or "Generated test script"}',
            f'    """'
        ]
        
        if self.target_url:
            lines.append(f'    page.goto("{self.target_url}")')
            lines.append("")
        
        for step in self.editable_steps:
            if not step.enabled:
                lines.append(f"    # DISABLED: {step.description}")
                continue
            
            lines.append(f"    # Step {step.step_number}: {step.description}")
            
            if step.notes:
                lines.append(f"    # Note: {step.notes}")
            
            # Generate action
            action_code = self._generate_playwright_action(step)
            if action_code:
                lines.extend(f"    {line}" for line in action_code)
            
            # Add validations
            for validation in step.validations:
                if validation.enabled:
                    validation_code = self._generate_playwright_validation(validation)
                    if validation_code:
                        lines.append(f"    # Validation: {validation.description}")
                        lines.extend(f"    {line}" for line in validation_code)
            
            # Screenshot if requested
            if step.screenshot_after:
                lines.append(f"    page.screenshot(path='step_{step.step_number}.png')")
            
            lines.append("")
        
        return "\n".join(lines)
    
    def _generate_playwright_action(self, step: EditableStep) -> List[str]:
        """Generate Playwright action code for a step."""
        lines = []
        
        if step.action_type == ActionType.NAVIGATE and step.url:
            lines.append(f'page.goto("{step.url}")')
        
        elif step.action_type == ActionType.CLICK and step.element_selector:
            locator = self._get_playwright_locator(step.element_selector)
            if self.include_waits:
                lines.append(f"{locator}.wait_for()")
            lines.append(f"{locator}.click()")
        
        elif step.action_type == ActionType.TYPE_TEXT and step.element_selector and step.input_data:
            locator = self._get_playwright_locator(step.element_selector)
            if self.include_waits:
                lines.append(f"{locator}.wait_for()")
            lines.append(f'{locator}.fill("{step.input_data}")')
        
        elif step.action_type == ActionType.SELECT_OPTION and step.element_selector and step.input_data:
            locator = self._get_playwright_locator(step.element_selector)
            lines.append(f'{locator}.select_option("{step.input_data}")')
        
        elif step.action_type == ActionType.WAIT and step.wait_time:
            lines.append(f"page.wait_for_timeout({step.wait_time * 1000})")
        
        elif step.action_type == ActionType.TAKE_SCREENSHOT:
            filename = step.input_data or f"step_{step.step_number}.png"
            lines.append(f'page.screenshot(path="{filename}")')
        
        return lines
    
    def _get_playwright_locator(self, selector: ElementSelector) -> str:
        """Convert ElementSelector to Playwright locator."""
        if selector.method == "testid":
            return f'page.get_by_test_id("{selector.value}")'
        elif selector.method == "text":
            return f'page.get_by_text("{selector.value}")'
        elif selector.method == "label":
            return f'page.get_by_label("{selector.value}")'
        elif selector.method == "css":
            return f'page.locator("{selector.value}")'
        elif selector.method == "xpath":
            return f'page.locator("xpath={selector.value}")'
        else:
            return f'page.locator("{selector.value}")'
    
    def _generate_playwright_validation(self, validation: Validation) -> List[str]:
        """Generate Playwright validation code."""
        lines = []
        
        if validation.type == ValidationType.TEXT_CONTAINS and validation.element_selector:
            locator = self._get_playwright_locator(validation.element_selector)
            lines.append(f'expect({locator}).to_contain_text("{validation.expected_value}")')
        
        elif validation.type == ValidationType.TEXT_EQUALS and validation.element_selector:
            locator = self._get_playwright_locator(validation.element_selector)
            lines.append(f'expect({locator}).to_have_text("{validation.expected_value}")')
        
        elif validation.type == ValidationType.ELEMENT_VISIBLE and validation.element_selector:
            locator = self._get_playwright_locator(validation.element_selector)
            lines.append(f'expect({locator}).to_be_visible()')
        
        elif validation.type == ValidationType.VALUE_EQUALS and validation.element_selector:
            locator = self._get_playwright_locator(validation.element_selector)
            lines.append(f'expect({locator}).to_have_value("{validation.expected_value}")')
        
        elif validation.type == ValidationType.URL_CONTAINS:
            lines.append(f'expect(page).to_have_url("*{validation.expected_value}*")')
        
        return lines
    
    def _generate_selenium_python(self) -> str:
        """Generate Selenium Python script from editable steps."""
        # Similar implementation for Selenium
        return "# Selenium generation not implemented yet"


# Factory functions
def create_editable_step(
    step_number: int,
    action_type: ActionType,
    description: str,
    **kwargs
) -> EditableStep:
    """Create a new editable step."""
    return EditableStep(
        id=f"step_{step_number}_{action_type.value}",
        step_number=step_number,
        action_type=action_type,
        description=description,
        **kwargs
    )


def create_element_selector(
    description: str,
    method: str,
    value: str
) -> ElementSelector:
    """Create a new element selector."""
    return ElementSelector(
        description=description,
        method=method,
        value=value
    )


def create_validation(
    validation_type: ValidationType,
    description: str,
    expected_value: Optional[str] = None,
    element_selector: Optional[ElementSelector] = None
) -> Validation:
    """Create a new validation."""
    return Validation(
        id=f"validation_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}",
        type=validation_type,
        description=description,
        expected_value=expected_value,
        element_selector=element_selector
    )