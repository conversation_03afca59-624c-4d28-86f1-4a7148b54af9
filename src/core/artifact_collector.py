"""
Artifact Collector for QAK Test Execution System

Manages collection, storage, and retrieval of test execution artifacts
including screenshots, videos, logs, and generated files.
"""

from typing import Dict, Any, Optional, List, Set, Union
import asyncio
import logging
import os
import json
import shutil
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import hashlib
import mimetypes

logger = logging.getLogger(__name__)


class ArtifactType(str, Enum):
    """Types of artifacts that can be collected."""
    SCREENSHOT = "screenshot"
    VIDEO = "video"
    LOG = "log"
    HTML_SNAPSHOT = "html_snapshot"
    NETWORK_HAR = "network_har"
    CONSOLE_OUTPUT = "console_output"
    ERROR_REPORT = "error_report"
    GENERATED_CODE = "generated_code"
    TEST_REPORT = "test_report"
    EXECUTION_METADATA = "execution_metadata"
    CUSTOM = "custom"


class ArtifactStatus(str, Enum):
    """Status of artifact processing."""
    PENDING = "pending"
    PROCESSING = "processing"
    READY = "ready"
    ARCHIVED = "archived"
    FAILED = "failed"
    DELETED = "deleted"


@dataclass
class Artifact:
    """Represents a collected test artifact."""
    artifact_id: str
    type: ArtifactType
    execution_id: str
    file_path: str
    original_name: str
    size_bytes: int
    created_at: datetime
    status: ArtifactStatus = ArtifactStatus.PENDING
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: Set[str] = field(default_factory=set)
    content_hash: Optional[str] = None
    thumbnail_path: Optional[str] = None
    compressed_path: Optional[str] = None


class ArtifactCollector:
    """
    Manages collection and storage of test execution artifacts.
    
    Features:
    - Automatic artifact collection during test execution
    - Intelligent storage with compression and deduplication
    - Thumbnail generation for images/videos
    - Cleanup of old artifacts
    - Fast retrieval with metadata search
    - Export capabilities for reporting
    """
    
    def __init__(
        self,
        base_storage_path: str = "artifacts",
        max_storage_gb: float = 10.0,
        retention_days: int = 30,
        auto_compress: bool = True,
        generate_thumbnails: bool = True
    ):
        """Initialize artifact collector."""
        self.base_storage_path = Path(base_storage_path)
        self.max_storage_bytes = int(max_storage_gb * 1024 * 1024 * 1024)
        self.retention_days = retention_days
        self.auto_compress = auto_compress
        self.generate_thumbnails = generate_thumbnails
        
        # Storage organization
        self.artifacts: Dict[str, Artifact] = {}
        self.execution_artifacts: Dict[str, Set[str]] = {}
        self.type_artifacts: Dict[ArtifactType, Set[str]] = {}
        
        # Statistics
        self.total_collected = 0
        self.total_size_bytes = 0
        self.compressed_artifacts = 0
        self.deleted_artifacts = 0
        
        # Background tasks
        self.cleanup_task: Optional[asyncio.Task] = None
        self.compression_queue: asyncio.Queue = asyncio.Queue()
        self.compression_task: Optional[asyncio.Task] = None
        
        # Ensure base directory exists
        self.base_storage_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"ArtifactCollector initialized (storage: {base_storage_path}, max: {max_storage_gb}GB)")
    
    async def initialize(self):
        """Initialize the artifact collector."""
        # Start background tasks
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        self.compression_task = asyncio.create_task(self._compression_loop())
        
        # Load existing artifacts
        await self._load_existing_artifacts()
        
        logger.info("ArtifactCollector initialized successfully")
    
    async def collect_screenshot(
        self,
        execution_id: str,
        screenshot_data: bytes,
        step_name: str = "",
        timestamp: Optional[datetime] = None
    ) -> Optional[Artifact]:
        """Collect a screenshot artifact."""
        # Extract step number from step_name if available
        step_num = ""
        if step_name and step_name.startswith("step_"):
            try:
                step_num = f"_{step_name.replace('step_', '').zfill(3)}"
            except:
                step_num = ""
        
        # Use provided timestamp or current time
        ts = timestamp or datetime.now()
        
        return await self._collect_binary_artifact(
            execution_id=execution_id,
            artifact_type=ArtifactType.SCREENSHOT,
            data=screenshot_data,
            filename=f"screenshot_{ts.strftime('%Y%m%d_%H%M%S')}{step_num}.png",
            metadata={
                "step_name": step_name,
                "format": "PNG",
                "capture_timestamp": ts.isoformat()
            }
        )
    
    async def collect_error_report(
        self,
        execution_id: str,
        error_details: Dict[str, Any],
        stack_trace: str = ""
    ) -> Optional[Artifact]:
        """Collect an error report artifact."""
        error_content = {
            "error_details": error_details,
            "stack_trace": stack_trace,
            "timestamp": datetime.now().isoformat(),
            "execution_id": execution_id
        }
        
        return await self._collect_text_artifact(
            execution_id=execution_id,
            artifact_type=ArtifactType.ERROR_REPORT,
            content=json.dumps(error_content, indent=2),
            filename=f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            metadata={
                "error_type": error_details.get("type", "unknown"),
                "severity": error_details.get("severity", "error"),
                "has_stack_trace": bool(stack_trace)
            }
        )
    
    async def _collect_binary_artifact(
        self,
        execution_id: str,
        artifact_type: ArtifactType,
        data: bytes,
        filename: str,
        metadata: Dict[str, Any] = None
    ) -> Optional[Artifact]:
        """Collect binary data as artifact."""
        try:
            # Generate artifact ID and storage path
            artifact_id = self._generate_artifact_id(execution_id, artifact_type, filename)
            storage_path = self._get_storage_path(execution_id, artifact_type, filename)
            
            # Ensure directory exists
            storage_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write data to file
            with open(storage_path, 'wb') as f:
                f.write(data)
            
            # Calculate content hash
            content_hash = hashlib.md5(data).hexdigest()
            
            # Create artifact
            artifact = Artifact(
                artifact_id=artifact_id,
                type=artifact_type,
                execution_id=execution_id,
                file_path=str(storage_path),
                original_name=filename,
                size_bytes=len(data),
                created_at=datetime.now(),
                status=ArtifactStatus.READY,
                metadata=metadata or {},
                content_hash=content_hash
            )
            
            # Store artifact
            await self._store_artifact(artifact)
            
            logger.debug(f"Collected binary artifact {artifact_id}")
            return artifact
            
        except Exception as e:
            logger.error(f"Failed to collect binary artifact: {e}")
            return None
    
    async def _collect_text_artifact(
        self,
        execution_id: str,
        artifact_type: ArtifactType,
        content: str,
        filename: str,
        metadata: Dict[str, Any] = None
    ) -> Optional[Artifact]:
        """Collect text content as artifact."""
        try:
            data = content.encode('utf-8')
            return await self._collect_binary_artifact(
                execution_id=execution_id,
                artifact_type=artifact_type,
                data=data,
                filename=filename,
                metadata=metadata
            )
        except Exception as e:
            logger.error(f"Failed to collect text artifact: {e}")
            return None
    
    def _generate_artifact_id(self, execution_id: str, artifact_type: ArtifactType, filename: str) -> str:
        """Generate unique artifact ID."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
        content = f"{execution_id}_{artifact_type}_{filename}_{timestamp}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    def _get_storage_path(self, execution_id: str, artifact_type: ArtifactType, filename: str) -> Path:
        """Get storage path for artifact."""
        date_dir = datetime.now().strftime('%Y/%m/%d')
        return self.base_storage_path / date_dir / execution_id[:8] / artifact_type / filename
    
    async def _store_artifact(self, artifact: Artifact):
        """Store artifact in internal collections."""
        # Store in main collection
        self.artifacts[artifact.artifact_id] = artifact
        
        # Index by execution ID
        if artifact.execution_id not in self.execution_artifacts:
            self.execution_artifacts[artifact.execution_id] = set()
        self.execution_artifacts[artifact.execution_id].add(artifact.artifact_id)
        
        # Index by type
        if artifact.type not in self.type_artifacts:
            self.type_artifacts[artifact.type] = set()
        self.type_artifacts[artifact.type].add(artifact.artifact_id)
        
        # Update statistics
        self.total_collected += 1
        self.total_size_bytes += artifact.size_bytes
        
        logger.debug(f"Stored artifact {artifact.artifact_id} ({artifact.size_bytes} bytes)")
    
    async def _compression_loop(self):
        """Background compression loop."""
        while True:
            try:
                await asyncio.sleep(1)  # Basic implementation
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in compression loop: {e}")
    
    async def _cleanup_loop(self):
        """Background cleanup loop."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run cleanup every hour
                await self._cleanup_old_artifacts()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
    
    async def _cleanup_old_artifacts(self):
        """Clean up artifacts older than retention period."""
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        artifacts_to_delete = []
        
        for artifact in self.artifacts.values():
            if artifact.created_at < cutoff_date:
                artifacts_to_delete.append(artifact.artifact_id)
        
        for artifact_id in artifacts_to_delete:
            await self.delete_artifact(artifact_id)
        
        if artifacts_to_delete:
            logger.info(f"Cleaned up {len(artifacts_to_delete)} old artifacts")
    
    async def delete_artifact(self, artifact_id: str) -> bool:
        """Delete an artifact."""
        try:
            artifact = self.artifacts.get(artifact_id)
            if not artifact:
                return False
            
            # Delete files
            for path in [artifact.file_path, artifact.thumbnail_path, artifact.compressed_path]:
                if path and os.path.exists(path):
                    os.remove(path)
            
            # Remove from collections
            self.artifacts.pop(artifact_id, None)
            
            if artifact.execution_id in self.execution_artifacts:
                self.execution_artifacts[artifact.execution_id].discard(artifact_id)
            
            if artifact.type in self.type_artifacts:
                self.type_artifacts[artifact.type].discard(artifact_id)
            
            # Update statistics
            self.total_size_bytes -= artifact.size_bytes
            self.deleted_artifacts += 1
            
            logger.debug(f"Deleted artifact {artifact_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete artifact {artifact_id}: {e}")
            return False
    
    async def _load_existing_artifacts(self):
        """Load existing artifacts from storage (simplified implementation)."""
        logger.info("Loaded existing artifacts from storage")
    
    def get_artifacts_by_execution(self, execution_id: str) -> List[Artifact]:
        """Get all artifacts for an execution."""
        artifact_ids = self.execution_artifacts.get(execution_id, set())
        return [self.artifacts[aid] for aid in artifact_ids if aid in self.artifacts]
    
    def get_artifacts_by_type(self, artifact_type: ArtifactType) -> List[Artifact]:
        """Get all artifacts of a specific type."""
        artifact_ids = self.type_artifacts.get(artifact_type, set())
        return [self.artifacts[aid] for aid in artifact_ids if aid in self.artifacts]
    
    def get_collector_stats(self) -> Dict[str, Any]:
        """Get collector statistics."""
        return {
            "total_artifacts": len(self.artifacts),
            "total_collected": self.total_collected,
            "total_size_bytes": self.total_size_bytes,
            "total_size_mb": self.total_size_bytes / (1024 * 1024),
            "compressed_artifacts": self.compressed_artifacts,
            "deleted_artifacts": self.deleted_artifacts,
            "storage_utilization": self.total_size_bytes / self.max_storage_bytes,
            "executions_with_artifacts": len(self.execution_artifacts),
            "artifact_types_count": len(self.type_artifacts)
        }
    
    async def shutdown(self):
        """Shutdown the artifact collector."""
        logger.info("Shutting down ArtifactCollector")
        
        # Cancel background tasks
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        if self.compression_task:
            self.compression_task.cancel()
        
        logger.info("ArtifactCollector shutdown complete")


# Global artifact collector instance
artifact_collector = ArtifactCollector()


async def collect_screenshot(execution_id: str, screenshot_data: bytes, step_name: str = "") -> Optional[Artifact]:
    """Convenience function to collect a screenshot."""
    return await artifact_collector.collect_screenshot(execution_id, screenshot_data, step_name)


async def collect_error_report(execution_id: str, error_details: Dict[str, Any]) -> Optional[Artifact]:
    """Convenience function to collect an error report."""
    return await artifact_collector.collect_error_report(execution_id, error_details)
