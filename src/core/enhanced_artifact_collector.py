"""
Enhanced Artifact Collector with Cloud Storage Support

Extends the original ArtifactCollector with cloud blob storage capabilities.
Supports local filesystem fallback and migration utilities.
"""

import asyncio
import logging
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List, Set, Union, Tuple
import hashlib
import mimetypes
import tempfile
import json
import shutil

from .artifact_collector import ArtifactCollector, Artifact, ArtifactType, ArtifactStatus
from .storage_backends.blob_storage_backend import create_blob_storage_backend, BlobStorageBackend
from ..database.models.artifact import Artifact as ArtifactModel, StorageBackend

logger = logging.getLogger(__name__)


def get_artifact_storage_backend() -> Optional[BlobStorageBackend]:
    """Create blob storage backend based on environment configuration."""
    storage_backend = os.getenv('ARTIFACT_STORAGE_BACKEND', 'local')
    
    if storage_backend == 'local':
        logger.info("Using local filesystem storage for artifacts")
        return None
    
    try:
        backend = create_blob_storage_backend(storage_backend)
        logger.info(f"✅ Initialized {storage_backend} storage backend for artifacts")
        return backend
    except Exception as e:
        logger.error(f"Failed to initialize {storage_backend} storage backend: {e}")
        logger.info("Falling back to local filesystem storage")
        return None


# Global singleton instance
_global_enhanced_collector: Optional['EnhancedArtifactCollector'] = None
_collector_lock = asyncio.Lock()


async def get_shared_enhanced_collector(base_storage_path: str = "artifacts") -> 'EnhancedArtifactCollector':
    """
    Get or create the shared global EnhancedArtifactCollector instance.
    
    This ensures that the orchestrator and API service use the same collector instance,
    solving the issue where artifacts created by the orchestrator were not visible
    to the API endpoints.
    """
    global _global_enhanced_collector
    
    async with _collector_lock:
        if _global_enhanced_collector is None:
            logger.info("🔄 Creating shared global EnhancedArtifactCollector instance")
            _global_enhanced_collector = EnhancedArtifactCollector(
                base_storage_path=base_storage_path
            )
            await _global_enhanced_collector.initialize()
            logger.info("✅ Shared global EnhancedArtifactCollector instance initialized")
        
        return _global_enhanced_collector


class EnhancedArtifactCollector(ArtifactCollector):
    """
    Enhanced Artifact Collector with cloud storage support.
    
    Features:
    - All original ArtifactCollector features
    - Cloud blob storage (R2, S3, Azure, GCS)
    - Automatic migration from local to cloud
    - Hybrid storage (metadata + cloud blobs)
    - Cost optimization with lifecycle policies
    - Singleton pattern support for shared instance across services
    """
    
    def __init__(self, blob_storage_backend: Optional[BlobStorageBackend] = None, base_storage_path: str = "artifacts"):
        """Initializes the artifact collector with a storage backend."""
        
        # Use provided backend or get from environment configuration
        if blob_storage_backend is None:
            self.blob_storage_backend = get_artifact_storage_backend()
        else:
            self.blob_storage_backend = blob_storage_backend
        
        # R2-only mode configuration based on environment
        self.r2_only_mode = os.getenv("ARTIFACT_R2_ONLY_MODE", "false").lower() == "true"
        self.skip_local_storage = os.getenv("ARTIFACT_SKIP_LOCAL_STORAGE", "false").lower() == "true"
        
        # Initialize parent with local storage path for base functionality
        # But in R2-only mode, we'll bypass most local operations
        super().__init__(base_storage_path=base_storage_path)
        
        if self.blob_storage_backend:
            backend_name = type(self.blob_storage_backend).__name__
            mode_msg = " (R2-ONLY MODE)" if self.r2_only_mode else ""
            logger.info(f"EnhancedArtifactCollector initialized with {backend_name} cloud storage backend{mode_msg}")
            # Initialize upload queue for future use
            self.upload_queue = asyncio.Queue()
            self.worker_task = None  # Can be started when needed
        else:
            logger.info("EnhancedArtifactCollector initialized with local storage only")
            self.upload_queue = None
            self.worker_task = None
        
        # Migration tracking (disabled in R2-only mode)
        if not self.r2_only_mode:
            self.migration_queue: asyncio.Queue = asyncio.Queue()
            self.migration_task: Optional[asyncio.Task] = None
        else:
            self.migration_queue = None
            self.migration_task = None
        
        # Statistics
        self.cloud_uploads = 0
        self.cloud_downloads = 0
        self.migration_count = 0
        self.failed_migrations = 0
    
    async def initialize(self):
        """Initialize the enhanced artifact collector."""
        # Initialize parent
        await super().initialize()
        
        # Start migration task if cloud storage is enabled and not in R2-only mode
        if self.blob_storage_backend and not self.r2_only_mode and self.migration_queue:
            self.migration_task = asyncio.create_task(self._migration_loop())
            logger.info("🔄 Started automatic cloud migration task")
        elif self.r2_only_mode:
            logger.info("🔄 Migration task disabled in R2-only mode - artifacts created directly in cloud")
    
    async def collect_screenshot(
        self,
        execution_id: str,
        screenshot_data: bytes,
        step_name: str = "",
        timestamp: Optional[datetime] = None,
        upload_to_cloud: bool = None
    ) -> Optional[Artifact]:
        """Collect a screenshot artifact with optional cloud upload."""
        
        # In R2-only mode, create artifact directly in cloud storage
        if self.r2_only_mode and self.blob_storage_backend:
            # Extract step number from step_name if available (same logic as original collector)
            step_num = ""
            if step_name and step_name.startswith("step_"):
                try:
                    step_num = f"_{step_name.replace('step_', '').zfill(3)}"
                except:
                    step_num = ""
            
            # Use provided timestamp or current time
            ts = timestamp or datetime.now()
            
            # Create filename matching original collector format
            filename = f"screenshot_{ts.strftime('%Y%m%d_%H%M%S')}{step_num}.png"
            
            return await self._create_cloud_only_artifact(
                execution_id=execution_id,
                content=screenshot_data,
                artifact_type=ArtifactType.SCREENSHOT,
                step_name=step_name,
                timestamp=timestamp,
                original_name=filename
            )
        
        # Legacy path: collect locally first, then optionally upload
        artifact = await super().collect_screenshot(
            execution_id=execution_id,
            screenshot_data=screenshot_data,
            step_name=step_name,
            timestamp=timestamp
        )
        
        if not artifact:
            return None
        
        # Determine if should upload to cloud automatically based on backend availability
        # If R2-only mode is enabled OR auto_migrate is enabled, upload immediately
        auto_migrate = os.getenv("ARTIFACT_AUTO_MIGRATE", "false").lower() == "true"
        should_upload = upload_to_cloud if upload_to_cloud is not None else (self.r2_only_mode or auto_migrate)
        
        if should_upload and self.blob_storage_backend:
            await self._upload_to_cloud(artifact, screenshot_data)
        elif self.blob_storage_backend and self.migration_queue:
            # Queue for later migration (only if not in R2-only mode)
            await self.migration_queue.put((artifact.artifact_id, datetime.now()))
        
        return artifact
    
    async def collect_error_report(
        self,
        execution_id: str,
        error_details: Dict[str, Any],
        stack_trace: str = "",
        upload_to_cloud: bool = None
    ) -> Optional[Artifact]:
        """Collect an error report artifact with optional cloud upload."""
        
        # In R2-only mode, create artifact directly in cloud storage
        if self.r2_only_mode and self.blob_storage_backend:
            # Convert error report to JSON bytes
            error_content = {
                "error_details": error_details,
                "stack_trace": stack_trace,
                "timestamp": datetime.now().isoformat(),
                "execution_id": execution_id
            }
            content_bytes = json.dumps(error_content, indent=2).encode('utf-8')
            
            return await self._create_cloud_only_artifact(
                execution_id=execution_id,
                content=content_bytes,
                artifact_type=ArtifactType.ERROR_REPORT,
                timestamp=datetime.now(),
                original_name="error_report.json",
                metadata={"error_type": error_details.get("type", "unknown_error")}
            )
        
        # Legacy path: collect locally first, then optionally upload
        artifact = await super().collect_error_report(
            execution_id=execution_id,
            error_details=error_details,
            stack_trace=stack_trace
        )
        
        if not artifact:
            return None
        
        # Read the file content for cloud upload
        file_content = None
        if artifact.file_path and os.path.exists(artifact.file_path):
            with open(artifact.file_path, 'rb') as f:
                file_content = f.read()
        
        # Determine if should upload to cloud
        should_upload = upload_to_cloud if upload_to_cloud is not None else False
        
        if should_upload and file_content and self.blob_storage_backend:
            await self._upload_to_cloud(artifact, file_content)
        elif self.blob_storage_backend and self.migration_queue:
            # Queue for later migration (only if not in R2-only mode)
            await self.migration_queue.put((artifact.artifact_id, datetime.now()))
        
        return artifact
    
    async def _create_cloud_only_artifact(
        self,
        execution_id: str,
        content: bytes,
        artifact_type: ArtifactType,
        step_name: str = "",
        timestamp: Optional[datetime] = None,
        original_name: str = "",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Artifact]:
        """Create an artifact directly in cloud storage without local file."""
        
        if not self.blob_storage_backend:
            logger.error("Cannot create cloud-only artifact without blob storage backend")
            return None
        
        try:
            # Generate artifact ID and timestamp
            artifact_id = hashlib.md5(f"{execution_id}_{datetime.now().isoformat()}_{len(content)}".encode()).hexdigest()
            created_at = timestamp or datetime.now()
            
            # Create virtual file path (not actually created)
            virtual_file_path = f"r2-only://{execution_id}/{artifact_id}_{original_name}"
            
            # Prepare artifact metadata (all values must be strings for R2)
            artifact_metadata = {
                'execution_id': str(execution_id),
                'artifact_type': str(artifact_type.value),
                'original_name': str(original_name),
                'created_at': str(created_at.isoformat()),
                'artifact_id': str(artifact_id),
                'step_name': str(step_name),
                'r2_only_mode': 'true',
                'content_size': str(len(content))
            }
            
            if metadata:
                # Convert all metadata values to strings
                for key, value in metadata.items():
                    artifact_metadata[key] = str(value)
            
            # Determine content type
            content_type = mimetypes.guess_type(original_name)[0] or "application/octet-stream"
            
            # Upload directly to cloud
            blob_url, blob_key = await self.blob_storage_backend.upload_artifact(
                file_path=virtual_file_path,  # Virtual path
                content=content,
                content_type=content_type,
                metadata=artifact_metadata
            )
            
            # Attempt to generate a presigned URL valid for 1 hour
            presigned_url: Optional[str] = None
            presigned_expires: Optional[datetime] = None
            try:
                client = await self.blob_storage_backend.s3_client
                presigned_url_tmp = client.generate_presigned_url(
                    "get_object",
                    Params={
                        "Bucket": self.blob_storage_backend.bucket_name,
                        "Key": blob_key,
                    },
                    ExpiresIn=3600,
                )
                # Handle potential coroutine from aiobotocore
                if hasattr(presigned_url_tmp, '__await__'):
                    presigned_url = await presigned_url_tmp
                else:
                    presigned_url = presigned_url_tmp
                presigned_expires = datetime.utcnow() + timedelta(hours=1)
            except Exception as e:
                logger.warning(f"Could not generate presigned URL for {blob_key}: {e}")
            
            # Create artifact object with cloud information
            artifact = Artifact(
                artifact_id=artifact_id,
                execution_id=execution_id,
                type=artifact_type,
                file_path=virtual_file_path,
                original_name=original_name,
                size_bytes=len(content),
                status=ArtifactStatus.READY,
                created_at=created_at,
                metadata={
                    **artifact_metadata,
                    'blob_url': blob_url,
                    'blob_key': blob_key,
                    'storage_backend': type(self.blob_storage_backend).__name__,
                    'uploaded_to_cloud': datetime.now().isoformat(),
                    'presigned_url': presigned_url,
                    'presigned_url_expires': presigned_expires.isoformat() if presigned_expires else None,
                    'r2_only_artifact': True
                }
            )
            
            # Store in memory (no local file created)
            self.artifacts[artifact_id] = artifact
            
            # Register in execution mapping
            if execution_id not in self.execution_artifacts:
                self.execution_artifacts[execution_id] = set()
            self.execution_artifacts[execution_id].add(artifact_id)
            
            # Register in type mapping  
            if artifact_type not in self.type_artifacts:
                self.type_artifacts[artifact_type] = set()
            self.type_artifacts[artifact_type].add(artifact_id)
            
            # Save to database if enabled
            try:
                from src.database.models.artifact import Artifact as ArtifactModel, ArtifactStatus as ArtifactStatusModel, StorageBackend as StorageBackendModel
                from src.database.repositories.artifact_repository import ArtifactRepository
                artifact_repo = ArtifactRepository()
                
                # VALIDACIÓN: Verificar si ya existe antes de crear
                existing_artifact = await ArtifactModel.find_one({"artifact_id": artifact_id})
                
                if existing_artifact:
                    logger.info(f"Artifact {artifact_id} already exists in database, skipping save")
                    # Opcional: actualizar metadata si es necesario
                    return artifact
                
                logger.debug(f"Creating artifact {artifact_id} in database with type {artifact_type}")
                
                # Crear modelo Pydantic para guardar en MongoDB
                artifact_model = ArtifactModel(
                    artifact_id=artifact_id,
                    execution_id=execution_id,
                    type=artifact_type.value if hasattr(artifact_type, 'value') else str(artifact_type),
                    file_path=virtual_file_path,
                    original_name=original_name,
                    size_bytes=len(content),
                    status=ArtifactStatusModel.READY.value,
                    storage_backend=StorageBackendModel.R2.value,
                    collected_at=created_at,
                    metadata={
                        **artifact_metadata,
                        'blob_url': blob_url,
                        'blob_key': blob_key,
                        'storage_backend': type(self.blob_storage_backend).__name__,
                        'uploaded_to_cloud': datetime.now().isoformat(),
                        'presigned_url': presigned_url,
                        'presigned_url_expires': presigned_expires.isoformat() if presigned_expires else None,
                        'r2_only_artifact': True
                    }
                )
                await artifact_repo.create(artifact_model)
                logger.info(f"💾 Saved R2-only artifact {artifact_id} to database (size: {len(content)} bytes)")
                
            except Exception as e:
                logger.warning(f"Failed to save artifact {artifact_id} to database: {e}")
                # Continue anyway - artifact is still created in memory and R2
            
            self.cloud_uploads += 1
            
            logger.info(f"☁️ Created R2-only artifact {artifact_id} ({len(content)} bytes) with original_name: {original_name}")
            return artifact
            
        except Exception as e:
            logger.error(f"Failed to create cloud-only artifact: {e}")
            return None

    async def _upload_to_cloud(
        self,
        artifact: Artifact,
        content: bytes,
        remove_local: bool = False
    ) -> bool:
        """Upload artifact to cloud storage."""
        if not self.blob_storage_backend:
            return False
        
        try:
            # Determine content type
            content_type = mimetypes.guess_type(artifact.original_name)[0] or "application/octet-stream"
            
            # Prepare metadata
            metadata = {
                'execution_id': artifact.execution_id,
                'artifact_type': artifact.type.value,
                'original_name': artifact.original_name,
                'created_at': artifact.created_at.isoformat(),
                'artifact_id': artifact.artifact_id
            }
            metadata.update(artifact.metadata)
            
            # Upload to cloud
            blob_url, blob_key = await self.blob_storage_backend.upload_artifact(
                file_path=artifact.file_path,
                content=content,
                content_type=content_type,
                metadata=metadata
            )
            
            # Update artifact with cloud information
            artifact.metadata['blob_url'] = blob_url
            artifact.metadata['blob_key'] = blob_key
            artifact.metadata['storage_backend'] = type(self.blob_storage_backend).__name__
            artifact.metadata['uploaded_to_cloud'] = datetime.now().isoformat()
            
            # Remove local file if requested
            if remove_local and os.path.exists(artifact.file_path):
                os.remove(artifact.file_path)
                artifact.metadata['local_file_removed'] = True
            
            self.cloud_uploads += 1
            logger.info(f"☁️ Uploaded artifact {artifact.artifact_id} to {type(self.blob_storage_backend).__name__}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to upload artifact {artifact.artifact_id} to cloud: {e}")
            return False
    
    async def download_from_cloud(self, artifact_id: str) -> Optional[bytes]:
        """Download artifact content from cloud storage."""
        if not self.blob_storage_backend:
            return None
        
        artifact = self.artifacts.get(artifact_id)
        if not artifact:
            return None
        
        blob_key = artifact.metadata.get('blob_key')
        if not blob_key:
            return None
        
        try:
            content = await self.blob_storage_backend.download_artifact(blob_key)
            self.cloud_downloads += 1
            logger.debug(f"☁️ Downloaded artifact {artifact_id} from {type(self.blob_storage_backend).__name__}")
            return content
        except Exception as e:
            logger.error(f"Failed to download artifact {artifact_id} from cloud: {e}")
            return None
    
    async def get_artifact_content(self, artifact_id: str) -> Optional[bytes]:
        """Get artifact content from local storage or cloud."""
        artifact = self.artifacts.get(artifact_id)
        if not artifact:
            return None
        
        # In R2-only mode, skip local file check and go directly to cloud
        if self.r2_only_mode or artifact.metadata.get('r2_only_artifact'):
            return await self.download_from_cloud(artifact_id)
        
        # Try local file first (legacy mode)
        if os.path.exists(artifact.file_path):
            try:
                with open(artifact.file_path, 'rb') as f:
                    return f.read()
            except Exception as e:
                logger.warning(f"Failed to read local file for {artifact_id}: {e}")
        
        # Try cloud storage as fallback
        return await self.download_from_cloud(artifact_id)
    
    async def migrate_artifact_to_cloud(self, artifact_id: str, remove_local: bool = True) -> bool:
        """Manually migrate a specific artifact to cloud storage."""
        if not self.blob_storage_backend:
            logger.warning("No cloud storage backend configured")
            return False
        
        artifact = self.artifacts.get(artifact_id)
        if not artifact:
            logger.warning(f"Artifact {artifact_id} not found")
            return False
        
        # Skip if already in cloud
        if artifact.metadata.get('blob_key'):
            logger.debug(f"Artifact {artifact_id} already in cloud storage")
            return True
        
        # Read local file
        if not os.path.exists(artifact.file_path):
            logger.warning(f"Local file not found for artifact {artifact_id}")
            return False
        
        try:
            with open(artifact.file_path, 'rb') as f:
                content = f.read()
            
            success = await self._upload_to_cloud(artifact, content, remove_local)
            if success:
                self.migration_count += 1
            else:
                self.failed_migrations += 1
            
            return success
        except Exception as e:
            logger.error(f"Failed to migrate artifact {artifact_id}: {e}")
            self.failed_migrations += 1
            return False
    
    async def _migration_loop(self):
        """Background migration loop."""
        while True:
            try:
                # Wait for artifacts to migrate
                artifact_id, queued_at = await self.migration_queue.get()
                
                # Check if enough time has passed
                time_diff = datetime.now() - queued_at
                if time_diff.total_seconds() < (60 * 60):  # Migrate to cloud after 1 hour
                    # Re-queue for later
                    await asyncio.sleep(60)  # Wait 1 minute before checking again
                    await self.migration_queue.put((artifact_id, queued_at))
                    continue
                
                # Migrate the artifact
                await self.migrate_artifact_to_cloud(artifact_id, remove_local=True)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in migration loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def bulk_migrate_to_cloud(
        self,
        artifact_ids: Optional[List[str]] = None,
        remove_local: bool = True,
        max_concurrent: int = 5
    ) -> Dict[str, bool]:
        """Bulk migrate artifacts to cloud storage."""
        if not self.blob_storage_backend:
            logger.warning("No cloud storage backend configured")
            return {}
        
        # Use all artifacts if none specified
        if artifact_ids is None:
            artifact_ids = list(self.artifacts.keys())
        
        # Filter out artifacts already in cloud
        to_migrate = []
        for artifact_id in artifact_ids:
            artifact = self.artifacts.get(artifact_id)
            if artifact and not artifact.metadata.get('blob_key'):
                to_migrate.append(artifact_id)
        
        logger.info(f"🔄 Starting bulk migration of {len(to_migrate)} artifacts to {type(self.blob_storage_backend).__name__}")
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def migrate_one(artifact_id: str) -> Tuple[str, bool]:
            async with semaphore:
                result = await self.migrate_artifact_to_cloud(artifact_id, remove_local)
                return artifact_id, result
        
        # Execute migrations concurrently
        tasks = [migrate_one(artifact_id) for artifact_id in to_migrate]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        migration_results = {}
        successful = 0
        failed = 0
        
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Migration task failed: {result}")
                failed += 1
            else:
                artifact_id, success = result
                migration_results[artifact_id] = success
                if success:
                    successful += 1
                else:
                    failed += 1
        
        logger.info(f"✅ Bulk migration completed: {successful} successful, {failed} failed")
        return migration_results
    
    def get_enhanced_stats(self) -> Dict[str, Any]:
        """Get enhanced collector statistics including cloud storage metrics."""
        stats = super().get_collector_stats()
        
        # Add cloud storage stats
        cloud_backend_name = type(self.blob_storage_backend).__name__ if self.blob_storage_backend else "None"
        stats.update({
            "blob_storage_backend": cloud_backend_name,
            "cloud_uploads": self.cloud_uploads,
            "cloud_downloads": self.cloud_downloads,
            "migration_count": self.migration_count,
            "failed_migrations": self.failed_migrations,
            "auto_migrate_enabled": self.blob_storage_backend is not None,
            "migration_delay_minutes": 60,  # Migrate to cloud after 1 hour
        })
        
        # Count artifacts by storage location
        local_only = 0
        cloud_only = 0
        hybrid = 0
        
        for artifact in self.artifacts.values():
            has_local = os.path.exists(artifact.file_path)
            has_cloud = bool(artifact.metadata.get('blob_key'))
            
            if has_local and has_cloud:
                hybrid += 1
            elif has_local:
                local_only += 1
            elif has_cloud:
                cloud_only += 1
        
        stats.update({
            "artifacts_local_only": local_only,
            "artifacts_cloud_only": cloud_only,
            "artifacts_hybrid": hybrid,
        })
        
        return stats
    
    async def cleanup_cloud_artifacts(self, dry_run: bool = True) -> Dict[str, Any]:
        """Clean up expired artifacts from cloud storage."""
        if not self.blob_storage_backend:
            return {"error": "No cloud storage backend configured"}
        
        cleanup_stats = {
            "dry_run": dry_run,
            "artifacts_processed": 0,
            "artifacts_deleted": 0,
            "errors": 0,
            "size_freed_bytes": 0
        }
        
        cutoff_date = datetime.now() - timedelta(days=30)
        
        for artifact in self.artifacts.values():
            if artifact.created_at < cutoff_date and artifact.metadata.get('blob_key'):
                cleanup_stats["artifacts_processed"] += 1
                
                try:
                    if not dry_run:
                        success = await self.blob_storage_backend.delete_artifact(
                            artifact.metadata['blob_key']
                        )
                        if success:
                            cleanup_stats["artifacts_deleted"] += 1
                            cleanup_stats["size_freed_bytes"] += artifact.size_bytes
                            # Remove blob metadata from artifact
                            artifact.metadata.pop('blob_key', None)
                            artifact.metadata.pop('blob_url', None)
                    else:
                        cleanup_stats["artifacts_deleted"] += 1
                        cleanup_stats["size_freed_bytes"] += artifact.size_bytes
                        
                except Exception as e:
                    logger.error(f"Failed to delete cloud artifact {artifact.artifact_id}: {e}")
                    cleanup_stats["errors"] += 1
        
        cleanup_stats["size_freed_mb"] = cleanup_stats["size_freed_bytes"] / (1024 * 1024)
        cleanup_stats["size_freed_gb"] = cleanup_stats["size_freed_bytes"] / (1024 * 1024 * 1024)
        
        if not dry_run:
            logger.info(f"🧹 Cleaned up {cleanup_stats['artifacts_deleted']} cloud artifacts")
        
        return cleanup_stats
    
    async def shutdown(self):
        """Shutdown the enhanced artifact collector."""
        logger.info("Shutting down EnhancedArtifactCollector")
        
        # Cancel migration task
        if self.migration_task:
            self.migration_task.cancel()
            try:
                await self.migration_task
            except asyncio.CancelledError:
                pass
        
        # Shutdown parent
        await super().shutdown()
        
        logger.info("EnhancedArtifactCollector shutdown complete")

    async def _artifact_exists_in_db(self, artifact_id: str) -> bool:
        """Verificar si un artifact ya existe en la base de datos."""
        try:
            from src.database.models.artifact import Artifact as ArtifactModel
            existing = await ArtifactModel.find_one({"artifact_id": artifact_id})
            return existing is not None
        except Exception as e:
            logger.warning(f"Error checking artifact existence: {e}")
            return False


# Convenience functions for backward compatibility
async def collect_screenshot_enhanced(
    execution_id: str,
    screenshot_data: bytes,
    step_name: str = "",
    blob_storage_backend: str = "local"
) -> Optional[Artifact]:
    """Enhanced screenshot collection with cloud storage support."""
    collector = EnhancedArtifactCollector(blob_storage_backend=blob_storage_backend)
    await collector.initialize()
    
    try:
        artifact = await collector.collect_screenshot(
            execution_id=execution_id,
            screenshot_data=screenshot_data,
            step_name=step_name,
            upload_to_cloud=True
        )
        return artifact
    finally:
        await collector.shutdown()


async def collect_error_report_enhanced(
    execution_id: str,
    error_details: Dict[str, Any],
    blob_storage_backend: str = "local"
) -> Optional[Artifact]:
    """Enhanced error report collection with cloud storage support."""
    collector = EnhancedArtifactCollector(blob_storage_backend=blob_storage_backend)
    await collector.initialize()
    
    try:
        artifact = await collector.collect_error_report(
            execution_id=execution_id,
            error_details=error_details,
            upload_to_cloud=True
        )
        return artifact
    finally:
        await collector.shutdown()