"""
TestService Environment Management Mixin

Extends TestService with environment management capabilities for multi-environment
test execution support.
"""

from typing import List, Optional, Dict, Any
from src.database.models.environment import Environment


class TestServiceEnvironmentMixin:
    """Mixin for TestService to handle environment management operations."""
    
    async def add_environment_to_project(self, project_id: str, environment: Environment) -> bool:
        """Add an environment to a project."""
        try:
            project = await self.project_manager.get_project(project_id)
            if not project:
                return False
            
            project.add_environment(environment)
            await self.project_manager.save_project(project)
            return True
            
        except Exception as e:
            print(f"Error adding environment to project: {e}")
            return False
    
    async def get_project_environments(self, project_id: str) -> List[Environment]:
        """Get all environments for a project."""
        try:
            project = await self.project_manager.get_project(project_id)
            if not project:
                print(f"❌ Project {project_id} not found")
                return []
            
            print(f"✅ Project found: {project.name}")
            print(f"📝 Project type: {type(project)}")
            print(f"📝 Has environments attr: {hasattr(project, 'environments')}")
            
            if hasattr(project, 'environments'):
                print(f"📝 Environments count: {len(project.environments)}")
                return project.environments
            else:
                print(f"❌ Project does not have environments attribute")
                return []
            
        except Exception as e:
            print(f"Error getting project environments: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    async def get_project_environment(self, project_id: str, env_id: str) -> Optional[Environment]:
        """Get a specific environment from a project."""
        try:
            project = await self.project_manager.get_project(project_id)
            if not project:
                return None
            
            return project.get_environment(env_id)
            
        except Exception as e:
            print(f"Error getting project environment: {e}")
            return None
    
    async def update_project_environment(self, project_id: str, environment: Environment) -> bool:
        """Update an environment in a project."""
        try:
            project = await self.project_manager.get_project(project_id)
            if not project:
                return False
            
            # The add_environment method handles updates if the environment already exists
            project.add_environment(environment)
            await self.project_manager.save_project(project)
            return True
            
        except Exception as e:
            print(f"Error updating project environment: {e}")
            return False
    
    async def remove_environment_from_project(self, project_id: str, env_id: str) -> bool:
        """Remove an environment from a project."""
        try:
            project = await self.project_manager.get_project(project_id)
            if not project:
                return False
            
            success = project.remove_environment(env_id)
            if success:
                await self.project_manager.save_project(project)
            
            return success
            
        except Exception as e:
            print(f"Error removing environment from project: {e}")
            return False
    
    async def set_default_environment(self, project_id: str, env_id: str) -> bool:
        """Set an environment as the default for a project."""
        try:
            project = await self.project_manager.get_project(project_id)
            if not project:
                return False
            
            success = project.set_default_environment(env_id)
            if success:
                await self.project_manager.save_project(project)
            
            return success
            
        except Exception as e:
            print(f"Error setting default environment: {e}")
            return False
    
    async def get_default_environment(self, project_id: str) -> Optional[Environment]:
        """Get the default environment for a project."""
        try:
            project = await self.project_manager.get_project(project_id)
            if not project:
                return None
            
            return project.get_default_environment()
            
        except Exception as e:
            print(f"Error getting default environment: {e}")
            return None
    
    async def get_environments_by_tag(self, project_id: str, tag: str) -> List[Environment]:
        """Get all environments with a specific tag."""
        try:
            project = await self.project_manager.get_project(project_id)
            if not project:
                return []
            
            return project.get_environments_by_tag(tag)
            
        except Exception as e:
            print(f"Error getting environments by tag: {e}")
            return []
    
    async def ensure_project_has_default_environment(self, project_id: str) -> bool:
        """Ensure a project has a default environment, creating one if necessary."""
        try:
            project = await self.project_manager.get_project(project_id)
            if not project:
                return False
            
            if not project.get_default_environment():
                project.ensure_default_environment()
                await self.project_manager.save_project(project)
            
            return True
            
        except Exception as e:
            print(f"Error ensuring default environment: {e}")
            return False
    
    async def get_executions_by_environment(self, project_id: str, env_id: str, 
                                          limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """Get execution history for a specific environment."""
        try:
            # This would need to be implemented in the ExecutionRepository
            # For now, return empty list as this requires database query implementation
            from src.database.repositories.execution_repository import ExecutionRepository
            
            repo = ExecutionRepository()
            executions = await repo.get_executions_by_environment(
                project_id=project_id,
                environment_id=env_id,
                limit=limit,
                offset=offset
            )
            
            return [execution.get_execution_summary_for_ui() for execution in executions]
            
        except Exception as e:
            print(f"Error getting executions by environment: {e}")
            return []
    
    async def construct_environment_url(self, project_id: str, env_id: str, relative_path: str) -> Optional[str]:
        """Construct a full URL for a given environment and relative path."""
        try:
            environment = await self.get_project_environment(project_id, env_id)
            if not environment:
                return None
            
            return environment.construct_full_url(relative_path)
            
        except Exception as e:
            print(f"Error constructing environment URL: {e}")
            return None