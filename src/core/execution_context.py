"""
Execution Context for QAK Test Execution System

This module defines the ExecutionContext class, which maintains shared state
throughout a test execution.
"""
from typing import Optional, Any
from datetime import datetime
import logging
import asyncio

from src.models.standard_result import TestType, ExecutionStatus
from src.core.browser_pool import BrowserInstance
from src.core.artifact_collector import ArtifactCollector
from src.core.configuration_manager import BrowserConfig, get_config

# Forward reference for Orchestrator to avoid circular import
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from src.core.execution_orchestrator import ExecutionOrchestrator


logger = logging.getLogger(__name__)


class ExecutionContext:
    """
    Shared execution context that maintains state throughout test execution.
    Provides centralized access to configuration, resources, and metadata.
    """
    
    def __init__(
        self,
        execution_id: str,
        test_type: TestType,
        config_profile: str,
        environment: str,
        config_overrides: dict,
        metadata: dict
    ):
        self.execution_id = execution_id
        self.test_type = test_type
        self.config_profile = config_profile
        self.environment = environment
        self.config_overrides = config_overrides
        self.metadata = metadata
        
        # Execution state
        self.started_at = datetime.now()
        self.completed_at: Optional[datetime] = None
        self.status = ExecutionStatus.PENDING
        self.pause_event = asyncio.Event()
        self.pause_event.set()
        
        # Resources
        self.browser: Optional[BrowserInstance] = None
        self.artifact_collector: Optional[ArtifactCollector] = None
        self.orchestrator: Optional['ExecutionOrchestrator'] = None
        self.artifacts: dict = {}
        self.errors: list = []
        
        # Environment information
        self.resolved_environment: Optional[Any] = None  # Will be set by orchestrator
        self.resolved_url: Optional[str] = None  # Full URL after environment resolution
        self.environment_config: dict = {}  # Merged config with environment overrides
        
        logger.info(f"Created execution context {execution_id} for {test_type}")

    @property
    def config(self) -> BrowserConfig:
        """Get the resolved browser configuration for this execution."""
        return get_config(
            config_id=self.config_profile,
            environment=self.environment,
            overrides=self.config_overrides
        )

    def set_browser(self, browser: BrowserInstance):
        self.browser = browser

    def set_artifact_collector(self, collector: ArtifactCollector):
        self.artifact_collector = collector
    
    def set_orchestrator(self, orchestrator: 'ExecutionOrchestrator'):
        self.orchestrator = orchestrator

    def set_artifacts(self, artifacts: dict):
        self.artifacts = artifacts
    
    def update_status(self, status: ExecutionStatus):
        """Update execution status with logging."""
        self.status = status
        if status in [ExecutionStatus.SUCCESS, ExecutionStatus.FAILURE, ExecutionStatus.ERROR]:
            self.completed_at = datetime.now()
        
        logger.info(f"Execution {self.execution_id} status: {status}")
    
    def add_error(self, error: str):
        """Add an error to the context."""
        self.errors.append(error)
        logger.error(f"Execution {self.execution_id} error: {error}")
    
    def get_duration_ms(self) -> Optional[int]:
        """Get execution duration in milliseconds."""
        if self.started_at and self.completed_at:
            return int((self.completed_at - self.started_at).total_seconds() * 1000)
        return None

    def pause(self):
        """Pause the execution."""
        if self.status == ExecutionStatus.RUNNING:
            self.pause_event.clear()
            self.update_status(ExecutionStatus.PAUSED)
            logger.info(f"Execution {self.execution_id} paused.")

    def resume(self):
        """Resume the execution."""
        if self.status == ExecutionStatus.PAUSED:
            self.pause_event.set()
            self.update_status(ExecutionStatus.RUNNING)
            logger.info(f"Execution {self.execution_id} resumed.")

    def cancel(self):
        """Cancel the execution."""
        if self.status in [ExecutionStatus.RUNNING, ExecutionStatus.PAUSED, ExecutionStatus.PENDING]:
            self.update_status(ExecutionStatus.CANCELLED)
            # If paused, we need to set the event so the task can wake up and see it's cancelled.
            self.pause_event.set()
            logger.info(f"Execution {self.execution_id} cancelled.")
    
    def set_environment_info(self, environment: Any, resolved_url: str, environment_config: dict):
        """Set environment information resolved by the orchestrator."""
        self.resolved_environment = environment
        self.resolved_url = resolved_url
        self.environment_config = environment_config
        
        # Also update metadata with environment information for result storage
        self.metadata.update({
            'environment_id': getattr(environment, 'env_id', None),
            'environment_name': getattr(environment, 'name', None),
            'full_url': resolved_url,
            'environment_config': environment_config
        })
        
        env_name = getattr(environment, 'name', 'Unknown')
        logger.info(f"Environment info set for execution {self.execution_id}: {env_name} -> {resolved_url}")
    
    def set_application_version(self, version: str):
        """Set the version of the application being tested."""
        self.metadata['application_version'] = version
        logger.info(f"Application version set for execution {self.execution_id}: v{version}")