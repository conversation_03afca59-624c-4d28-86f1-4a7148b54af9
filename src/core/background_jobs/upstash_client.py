"""
Upstash Redis Client for Background Jobs

Provides a Redis-compatible interface using Upstash REST API.
"""

import os
import json
import logging
from typing import Optional, Any
import requests
from redis import Redis

logger = logging.getLogger(__name__)


class UpstashRedisClient:
    """Redis-compatible client using Upstash REST API."""
    
    def __init__(self, rest_url: str, rest_token: str):
        """Initialize Upstash client."""
        self.rest_url = rest_url.rstrip('/')
        self.rest_token = rest_token
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {rest_token}',
            'Content-Type': 'application/json'
        })
    
    def _execute(self, command: list) -> Any:
        """Execute a Redis command via REST API."""
        try:
            response = self.session.post(
                f"{self.rest_url}/",
                json=command,
                timeout=10
            )
            response.raise_for_status()
            
            data = response.json()
            if 'error' in data:
                raise Exception(data['error'])
            
            return data.get('result')
            
        except Exception as e:
            logger.error(f"Upstash command failed: {command}, error: {e}")
            raise
    
    def ping(self) -> bool:
        """Test connection."""
        result = self._execute(['PING'])
        return result == 'PONG'
    
    def set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """Set a key-value pair."""
        command = ['SET', key, value]
        if ex:
            command.extend(['EX', str(ex)])
        
        result = self._execute(command)
        return result == 'OK'
    
    def get(self, key: str) -> Optional[str]:
        """Get value by key."""
        return self._execute(['GET', key])
    
    def delete(self, key: str) -> int:
        """Delete a key."""
        return self._execute(['DEL', key])
    
    def setex(self, key: str, time: int, value: str) -> bool:
        """Set key with expiration."""
        result = self._execute(['SETEX', key, str(time), value])
        return result == 'OK'
    
    def keys(self, pattern: str = '*') -> list:
        """Get keys matching pattern."""
        return self._execute(['KEYS', pattern]) or []


def get_redis_client() -> Redis:
    """Get Redis client (Upstash or local)."""
    upstash_url = os.getenv("UPSTASH_REDIS_REST_URL")
    upstash_token = os.getenv("UPSTASH_REDIS_REST_TOKEN")
    
    if upstash_url and upstash_token:
        logger.info("Using Upstash Redis via REST API")
        # For now, let's try the traditional Redis client with proper SSL
        try:
            import urllib.parse
            host = urllib.parse.urlparse(upstash_url).netloc
            
            # Try different connection formats
            connection_formats = [
                f"rediss://default:{upstash_token}@{host}:6380",
                f"redis://default:{upstash_token}@{host}:6379",
                f"rediss://:{upstash_token}@{host}:6380"
            ]
            
            for redis_url in connection_formats:
                try:
                    redis_client = Redis.from_url(
                        redis_url, 
                        decode_responses=True,
                        ssl_cert_reqs=None,
                        socket_connect_timeout=5
                    )
                    redis_client.ping()
                    logger.info(f"✅ Connected to Upstash using: {redis_url.split('@')[0]}@{host}")
                    return redis_client
                except Exception as e:
                    logger.debug(f"Connection format failed: {redis_url.split('@')[0]}@{host}: {e}")
                    continue
            
            # If all traditional formats fail, return a wrapper for REST API
            logger.warning("Traditional Redis connection failed, this may cause issues with Celery")
            raise Exception("Unable to connect with traditional Redis client")
            
        except Exception as e:
            logger.error(f"Upstash connection failed: {e}")
            # Fall back to local Redis
            pass
    
    # Use local Redis as fallback
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    logger.info(f"Using local Redis: {redis_url}")
    return Redis.from_url(redis_url, decode_responses=True)