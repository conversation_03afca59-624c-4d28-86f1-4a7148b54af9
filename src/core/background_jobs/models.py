"""
Background Job Models

Defines data structures for job tracking and status management.
"""

from enum import Enum
from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field


class JobStatus(str, Enum):
    """Job execution status."""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class AnalysisJob(BaseModel):
    """Analysis job tracking model."""
    
    job_id: str = Field(..., description="Unique job identifier")
    execution_id: str = Field(..., description="Test execution ID being analyzed")
    status: JobStatus = Field(default=JobStatus.PENDING, description="Current job status")
    progress: int = Field(default=0, description="Progress percentage (0-100)")
    message: str = Field(default="", description="Current status message")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Job creation timestamp")
    started_at: Optional[datetime] = Field(None, description="Job start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Job completion timestamp")
    result: Optional[Dict[str, Any]] = Field(None, description="Analysis result")
    error: Optional[str] = Field(None, description="Error message if failed")
    
    class Config:
        use_enum_values = True


class JobProgress(BaseModel):
    """Job progress update model."""
    
    job_id: str
    progress: int = Field(ge=0, le=100, description="Progress percentage")
    message: str = Field(default="", description="Progress message")
    current_step: Optional[str] = Field(None, description="Current step being processed")
    total_steps: Optional[int] = Field(None, description="Total number of steps")
    
    
class JobResult(BaseModel):
    """Job result model."""
    
    job_id: str
    status: JobStatus
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time_ms: Optional[int] = None
    
    class Config:
        use_enum_values = True