"""
Background Jobs System for QAK

Provides asynchronous job processing for CPU-intensive tasks like AI analysis.
"""

try:
    from .celery_app import celery_app
    from .job_manager import JobManager
    from .models import JobStatus, AnalysisJob
    
    __all__ = ["celery_app", "JobManager", "JobStatus", "AnalysisJob"]
    BACKGROUND_JOBS_AVAILABLE = True
    
except ImportError as e:
    # Background jobs dependencies not installed
    celery_app = None
    JobManager = None
    JobStatus = None
    AnalysisJob = None
    
    __all__ = []
    BACKGROUND_JOBS_AVAILABLE = False
    
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"Background jobs not available: {e}. Install with: pip install celery[redis] redis")