"""Servicio para gestión de sesiones de Playwright Codegen."""

import os
import asyncio
import tempfile
import shutil
import subprocess
import json
import uuid
import sys
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import logging
from pathlib import Path

from src.api.models import (
    PlaywrightCodegenRequest,
    CodegenSessionInfo,
    CodegenTestCaseRequest,
    CodegenStatsResponse
)
from src.utilities.project_manager_service import ProjectManagerService
from src.database.repositories.codegen_repository import CodegenSessionRepository

logger = logging.getLogger(__name__)

class PlaywrightCodegenService:
    """Servicio para gestionar sesiones de Playwright Codegen."""
    
    def __init__(self, codegen_repo: Optional[CodegenSessionRepository] = None):
        """Inicializa el servicio de Playwright Codegen."""
        self.active_sessions: Dict[str, CodegenSessionInfo] = {}
        self.session_processes: Dict[str, asyncio.subprocess.Process] = {}
        self.codegen_repo = codegen_repo
        
        # Directorio temporal para los artefactos de sesión
        self.base_temp_dir = os.path.join(tempfile.gettempdir(), "qak_codegen_sessions")
        os.makedirs(self.base_temp_dir, exist_ok=True)
        
        # Cargar sesiones activas desde la base de datos
        asyncio.create_task(self._load_active_sessions_from_db())
        
        # Cleanup automático de sesiones antiguas
        asyncio.create_task(self._cleanup_old_sessions_from_db())
    
    async def _load_active_sessions_from_db(self):
        """Carga sesiones que no se completaron desde la base de datos al iniciar."""
        if not self.codegen_repo:
            logger.warning("Codegen repository not available, cannot load sessions from DB.")
            return

        try:
            logger.info("Loading active/stale sessions from database...")
            # Busca sesiones que se quedaron en estado 'starting' o 'running'
            stale_sessions = await self.codegen_repo.find_active_sessions()
            
            for session_doc in stale_sessions:
                # Marcar estas sesiones como 'stopped' o 'failed' ya que el servidor se reinició
                session_doc.status = "stopped"
                session_doc.error_message = "Session stopped due to server restart."
                session_doc.completed_at = datetime.now()
                await self.codegen_repo.update_session(session_doc)
                logger.info(f"Marked stale session {session_doc.session_id} as stopped.")
                
            logger.info(f"Processed {len(stale_sessions)} stale sessions from database.")
        except Exception as e:
            logger.error(f"Error loading stale sessions from database: {e}")

    async def start_session(self, request: PlaywrightCodegenRequest) -> CodegenSessionInfo:
        """Inicia una nueva sesión de Playwright Codegen."""

        try:
            # Verificar que Playwright esté disponible
            await self._verify_playwright_installation()

            # Generar ID único
            session_id = str(uuid.uuid4())

            # Crear directorio temporal para la sesión
            session_dir = tempfile.mkdtemp(dir=self.base_temp_dir)

            # Construir comando
            cmd = self._build_command(request, session_dir)

            # Crear información de sesión
            session_info = CodegenSessionInfo(
                session_id=session_id,
                status="starting",
                target_language=request.target_language,
                url=request.url,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                artifacts_path=session_dir,
                command_used=" ".join(cmd),
                project_integration=self._build_project_integration(request)
            )

            # Almacenar sesión
            self.active_sessions[session_id] = session_info

            # Ejecutar comando en background
            # Nota: Playwright CodeGen SIEMPRE debe ejecutarse en modo headed (interfaz visible)
            # ya que es una herramienta interactiva donde el usuario graba acciones manualmente
            asyncio.create_task(self._run_session(session_id, cmd, session_dir, headless=False, force_vnc=request.force_vnc))

            logger.info(f"Sesión de Playwright Codegen iniciada: {session_id}")
            return session_info

        except Exception as e:
            logger.error(f"Error iniciando sesión de codegen: {str(e)}")
            raise
    
    async def get_session(self, session_id: str) -> Optional[CodegenSessionInfo]:
        """Obtiene información de una sesión."""
        # Buscar en sesiones activas primero
        session = self.active_sessions.get(session_id)
        if session:
            return session
        
        # Si no está en activas, buscar en la base de datos
        if self.codegen_repo:
            session_doc = await self.codegen_repo.get_by_session_id(session_id)
            if session_doc:
                return CodegenSessionInfo(**session_doc.model_dump())

        return None
    
    async def stop_session(self, session_id: str) -> bool:
        """Detiene una sesión activa."""
        
        logger.debug(f"Intentando detener sesión: {session_id}")
        
        if session_id not in self.active_sessions:
            logger.warning(f"Sesión {session_id} no encontrada en active_sessions")
            return False
        
        try:
            # Actualizar estado primero para evitar race conditions
            logger.debug(f"Actualizando estado de sesión {session_id} a 'stopping'")
            session = self.active_sessions[session_id]
            session.status = "stopping"
            session.updated_at = datetime.now()
            
            # Terminar proceso si existe
            if session_id in self.session_processes:
                logger.debug(f"Terminando proceso para sesión {session_id}")
                process = self.session_processes[session_id]
                
                # Primero intentar terminar amablemente
                try:
                    logger.debug(f"Enviando SIGTERM al proceso de sesión {session_id}")
                    process.terminate()
                    # Esperar un poco para que termine amablemente
                    await asyncio.wait_for(process.wait(), timeout=3.0)
                    logger.debug(f"Proceso de sesión {session_id} terminado con SIGTERM")
                except asyncio.TimeoutError:
                    # Si no termina amablemente, forzar terminación
                    logger.debug(f"Timeout con SIGTERM, enviando SIGKILL a sesión {session_id}")
                    process.kill()
                    try:
                        await asyncio.wait_for(process.wait(), timeout=2.0)
                        logger.debug(f"Proceso de sesión {session_id} terminado con SIGKILL")
                    except asyncio.TimeoutError:
                        logger.warning(f"No se pudo terminar el proceso para sesión {session_id}")
                
                # Limpiar referencia al proceso
                logger.debug(f"Limpiando referencia al proceso para sesión {session_id}")
                if session_id in self.session_processes:
                    del self.session_processes[session_id]
                else:
                    logger.debug(f"Proceso para sesión {session_id} ya había sido limpiado")
            else:
                logger.debug(f"No hay proceso activo para sesión {session_id}")
            
            # Actualizar estado final
            logger.debug(f"Actualizando estado final de sesión {session_id} a 'stopped'")
            session.status = "stopped"
            session.completed_at = datetime.now()
            session.updated_at = datetime.now()
            
            # Intentar obtener código generado antes de persistir
            await self.get_generated_code(session_id)
            
            # Persistir sesión
            await self._persist_session(session)
            
            logger.info(f"Sesión de Playwright Codegen detenida: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deteniendo sesión {session_id}: {type(e).__name__}: {str(e)}")
            logger.error(f"Traceback completo:", exc_info=True)
            # Asegurarse de que la sesión está marcada como failed en caso de error
            if session_id in self.active_sessions:
                self.active_sessions[session_id].status = "failed"
                self.active_sessions[session_id].error_message = str(e)
                self.active_sessions[session_id].updated_at = datetime.now()
            return False
    
    async def get_generated_code(self, session_id: str) -> Optional[str]:
        """Obtiene el código generado de una sesión."""
        
        # Buscar en sesiones activas primero
        session = self.active_sessions.get(session_id)
        
        # Si no está en activas, buscar en la base de datos
        if not session and self.codegen_repo:
            session_doc = await self.codegen_repo.get_by_session_id(session_id)
            if session_doc:
                session = CodegenSessionInfo(**session_doc.model_dump())
        
        if not session:
            logger.warning(f"Sesión {session_id} no encontrada para obtener código.")
            return None
        
        # Si ya tenemos el código, devolverlo
        if session.generated_code:
            logger.debug(f"Código ya cacheado para sesión {session_id}")
            return session.generated_code
        
        # Intentar leer desde archivos de output
        if session.artifacts_path:
            try:
                artifacts_dir = Path(session.artifacts_path)
                logger.debug(f"Buscando código generado en: {artifacts_dir}")
                
                # Verificar que el directorio existe
                if not artifacts_dir.exists():
                    logger.warning(f"Directorio de artefactos no existe: {artifacts_dir}")
                    return None
                
                # Listar todos los archivos en el directorio
                all_files = list(artifacts_dir.iterdir())
                logger.debug(f"Archivos encontrados en {artifacts_dir}: {[f.name for f in all_files]}")
                
                # Patrones de archivos según el lenguaje
                patterns = []
                if session.target_language == "python":
                    patterns = ["*.py", "test_*.py", "*_test.py"]
                elif session.target_language == "javascript":
                    patterns = ["*.spec.js", "*.test.js", "*.js"]
                elif session.target_language == "typescript":
                    patterns = ["*.spec.ts", "*.test.ts", "*.ts"]
                elif session.target_language == "java":
                    patterns = ["*.java"]
                elif session.target_language == "csharp":
                    patterns = ["*.cs"]
                else:
                    # Fallback: buscar cualquier archivo de código
                    patterns = ["*.py", "*.js", "*.ts", "*.java", "*.cs"]
                
                logger.debug(f"Patrones de búsqueda para {session.target_language}: {patterns}")
                
                # Buscar archivos usando los patrones
                output_files = []
                for pattern in patterns:
                    found_files = list(artifacts_dir.glob(pattern))
                    logger.debug(f"Patrón {pattern} encontró: {[f.name for f in found_files]}")
                    output_files.extend(found_files)
                
                # Intentar leer el primer archivo encontrado
                if output_files:
                    # Preferir archivos que contengan "test" o "spec" en el nombre
                    test_files = [f for f in output_files if any(keyword in f.name.lower() 
                                 for keyword in ['test', 'spec', 'codegen'])]
                    
                    target_file = test_files[0] if test_files else output_files[0]
                    logger.info(f"Leyendo código generado desde: {target_file}")
                    
                    with open(target_file, 'r', encoding='utf-8') as f:
                        generated_code = f.read()
                        
                        # Validar que el código no esté vacío
                        if not generated_code.strip():
                            logger.warning(f"Archivo {target_file} está vacío")
                            return None
                            
                        session.generated_code = generated_code
                        session.updated_at = datetime.now()
                        logger.info(f"✅ Código generado cargado exitosamente para sesión {session_id} ({len(generated_code)} caracteres)")
                        return generated_code
                        
                # Si no encontramos archivos, pero la sesión está "completed", 
                # buscar en el directorio actual cualquier archivo reciente
                if session.status in ["completed", "stopped"]:
                    logger.debug(f"Buscando archivos recientes desde {session.created_at}")
                    import time
                    cutoff_time = session.created_at.timestamp()
                    recent_files = [
                        f for f in artifacts_dir.iterdir() 
                        if f.is_file() and f.stat().st_mtime > cutoff_time and not f.name.startswith('.')
                    ]
                    
                    logger.debug(f"Archivos recientes encontrados: {[f.name for f in recent_files]}")
                    
                    if recent_files:
                        # Tomar el archivo más reciente
                        newest_file = max(recent_files, key=lambda f: f.stat().st_mtime)
                        logger.info(f"Intentando leer archivo más reciente: {newest_file}")
                        try:
                            with open(newest_file, 'r', encoding='utf-8') as f:
                                generated_code = f.read()
                                
                                # Validar que el código no esté vacío
                                if not generated_code.strip():
                                    logger.warning(f"Archivo más reciente {newest_file} está vacío")
                                    return None
                                    
                                session.generated_code = generated_code
                                session.updated_at = datetime.now()
                                logger.info(f"✅ Código generado cargado desde archivo reciente para sesión {session_id}")
                                return generated_code
                        except UnicodeDecodeError:
                            logger.warning(f"No se pudo decodificar archivo {newest_file} (posiblemente binario)")
                            pass
                        except Exception as e:
                            logger.error(f"Error leyendo archivo reciente {newest_file}: {str(e)}")
                
                logger.warning(f"❌ No se encontró código generado válido para sesión {session_id}")
                return None
                
            except Exception as e:
                logger.warning(f"Error leyendo código generado para sesión {session_id}: {str(e)}")
        
        return None
    
    async def convert_to_testcase(self, request: CodegenTestCaseRequest) -> Dict[str, Any]:
        """Convierte código generado en un caso de prueba QAK."""
        
        session = self.active_sessions.get(request.session_id)
        if not session:
            raise ValueError(f"Sesión no encontrada: {request.session_id}")
        
        generated_code = await self.get_generated_code(request.session_id)
        if not generated_code:
            raise ValueError("No hay código generado disponible")
        
        try:
            # Procesar y adaptar el código para QAK
            adapted_code = self._adapt_code_for_qak(
                generated_code,
                session.target_language,
                request.framework,
                request.include_assertions,
                request.add_error_handling
            )
            
            # Crear estructura de caso de prueba QAK
            testcase_data = {
                "name": request.test_name,
                "description": request.test_description or f"Test generado con Playwright Codegen",
                "framework": request.framework,
                "language": session.target_language,
                "code": adapted_code,
                "metadata": {
                    "generated_from_codegen": True,
                    "codegen_session_id": request.session_id,
                    "original_url": session.url,
                    "generated_at": datetime.now().isoformat(),
                    "user_story": request.user_story
                }
            }
            
            # Si se especifica proyecto y suite, guardarlo
            if request.project_id and request.test_suite:
                success = await self._save_to_project(
                    request.project_id,
                    request.test_suite,
                    testcase_data
                )
                testcase_data["saved_to_project"] = success
            
            logger.info(f"Código convertido a caso de prueba QAK para sesión {request.session_id}")
            return testcase_data
            
        except Exception as e:
            logger.error(f"Error convirtiendo código a caso de prueba: {str(e)}")
            raise
    
    async def cleanup_session(self, session_id: str) -> bool:
        """Limpia recursos de una sesión."""
        
        if session_id not in self.active_sessions:
            return False
        
        try:
            # Detener proceso si está corriendo
            await self.stop_session(session_id)
            
            # Limpiar directorio
            session = self.active_sessions[session_id]
            if session.artifacts_path and os.path.exists(session.artifacts_path):
                shutil.rmtree(session.artifacts_path)
            
            # Remover de sesiones activas
            del self.active_sessions[session_id]
            
            logger.info(f"Recursos de sesión {session_id} limpiados")
            return True
            
        except Exception as e:
            logger.error(f"Error limpiando sesión {session_id}: {str(e)}")
            return False
    
    async def get_stats(self) -> CodegenStatsResponse:
        """Obtiene estadísticas de uso del servicio."""
        
        total_sessions = len(self.active_sessions)
        active_sessions = len([s for s in self.active_sessions.values() if s.status in ["starting", "running"]])
        completed_sessions = len([s for s in self.active_sessions.values() if s.status == "completed"])
        failed_sessions = len([s for s in self.active_sessions.values() if s.status == "failed"])
        
        # Estadísticas por lenguaje
        sessions_by_language = {}
        for session in self.active_sessions.values():
            lang = session.target_language
            sessions_by_language[lang] = sessions_by_language.get(lang, 0) + 1
        
        # Última sesión
        last_session_at = None
        if self.active_sessions:
            last_session_at = max(s.created_at for s in self.active_sessions.values())
        
        return CodegenStatsResponse(
            total_sessions=total_sessions,
            active_sessions=active_sessions,
            completed_sessions=completed_sessions,
            failed_sessions=failed_sessions,
            sessions_by_language=sessions_by_language,
            last_session_at=last_session_at
        )
    
    def _build_command(self, request: PlaywrightCodegenRequest, session_dir: str) -> List[str]:
        """Construye el comando de Playwright Codegen."""

        # COMANDO BASE: Usar siempre el playwright instalado en el entorno Python
        # No usar npx ya que puede intentar instalar una versión diferente
        cmd = ["playwright", "codegen"]

        # IMPORTANTE: Playwright CodeGen NO soporta modo headless
        # Es una herramienta interactiva que requiere que el usuario vea el navegador
        # y realice acciones manualmente para generar el código
        # El parámetro headless en la API se ignora para esta funcionalidad

        # Configuraciones del navegador
        if request.device:
            # Validar y mapear dispositivos conocidos
            device_name = self._validate_device_name(request.device)
            if device_name:
                cmd.extend(["--device", device_name])

        if request.viewport_size:
            cmd.extend(["--viewport-size", request.viewport_size])

        # Configuraciones del entorno
        if request.timezone:
            cmd.extend(["--timezone", request.timezone])

        if request.geolocation:
            cmd.extend(["--geolocation", request.geolocation])

        if request.language:
            cmd.extend(["--lang", request.language])

        if request.color_scheme:
            cmd.extend(["--color-scheme", request.color_scheme])

        # Gestión de estado
        if request.load_storage:
            storage_path = os.path.join(session_dir, "auth_input.json")
            cmd.extend(["--load-storage", storage_path])

        if request.save_storage:
            storage_path = os.path.join(session_dir, "auth_output.json")
            cmd.extend(["--save-storage", storage_path])

        # Lenguaje objetivo y archivo de salida
        if request.target_language != "javascript":
            cmd.extend(["--target", request.target_language])

        # Especificar archivo de salida explícitamente
        output_file = self._get_output_filename(request.target_language, session_dir)
        cmd.extend(["-o", output_file])

        # URL inicial
        if request.url:
            cmd.append(request.url)

        return cmd

    def _validate_device_name(self, device: str) -> Optional[str]:
        """Valida y mapea nombres de dispositivos para Playwright."""

        # Mapeo de nombres comunes a dispositivos válidos de Playwright
        device_mapping = {
            "desktop chrome": None,  # No usar --device para desktop
            "desktop": None,
            "chrome": None,
            "desktop firefox": None,
            "firefox": None,
            "desktop safari": None,
            "safari": None,
            "iphone 13": "iPhone 13",
            "iphone 12": "iPhone 12",
            "iphone 11": "iPhone 11",
            "iphone se": "iPhone SE",
            "ipad": "iPad Pro",
            "ipad pro": "iPad Pro",
            "pixel 5": "Pixel 5",
            "pixel 4": "Pixel 4",
            "galaxy s21": "Galaxy S21",
            "galaxy s20": "Galaxy S20",
            "galaxy note 20": "Galaxy Note 20",
            "galaxy tab s7": "Galaxy Tab S7"
        }

        device_lower = device.lower().strip()

        # Si está en el mapeo, usar el valor mapeado
        if device_lower in device_mapping:
            mapped_device = device_mapping[device_lower]
            if mapped_device is None:
                logger.info(f"Dispositivo '{device}' mapeado a desktop (sin --device)")
            else:
                logger.info(f"Dispositivo '{device}' mapeado a '{mapped_device}'")
            return mapped_device

        # Si no está en el mapeo pero parece un dispositivo válido, usarlo tal como está
        if any(keyword in device_lower for keyword in ['iphone', 'ipad', 'pixel', 'galaxy', 'samsung']):
            logger.info(f"Usando dispositivo '{device}' tal como está")
            return device

        # Si no es reconocido, no usar --device (comportamiento desktop por defecto)
        logger.warning(f"Dispositivo '{device}' no reconocido, usando comportamiento desktop por defecto")
        return None

    def _get_output_filename(self, language: str, session_dir: str) -> str:
        """Genera el nombre del archivo de salida según el lenguaje."""
        
        if language == "python":
            return os.path.join(session_dir, "test_codegen.py")
        elif language == "java":
            return os.path.join(session_dir, "TestCodegen.java")
        elif language == "csharp":
            return os.path.join(session_dir, "TestCodegen.cs")
        elif language == "typescript":
            return os.path.join(session_dir, "test.spec.ts")
        else:  # javascript
            return os.path.join(session_dir, "test.spec.js")
    
    def _build_project_integration(self, request: PlaywrightCodegenRequest) -> Optional[Dict[str, Any]]:
        """Construye datos de integración con proyecto QAK."""

        if not request.project_id:
            return None

        return {
            "project_id": request.project_id,
            "test_suite": request.test_suite,
            "user_story": request.user_story,
            "integration_timestamp": datetime.now().isoformat()
        }

    async def _verify_playwright_installation(self):
        """Verifica que Playwright esté correctamente instalado y configurado."""

        try:
            # Preparar entorno para ejecutar playwright
            env = os.environ.copy()

            # Verificar que el comando playwright esté disponible
            process = await asyncio.create_subprocess_exec(
                "playwright", "--version",
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "Comando playwright no encontrado"
                raise Exception(f"Playwright no está disponible: {error_msg}")

            version = stdout.decode('utf-8', errors='ignore').strip()
            logger.info(f"Playwright verificado: {version}")

            # Verificar que los navegadores estén instalados
            process = await asyncio.create_subprocess_exec(
                "playwright", "install", "--dry-run",
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "Error verificando navegadores"
                logger.warning(f"Advertencia verificando navegadores: {error_msg}")
            else:
                logger.info("Navegadores de Playwright verificados")

            # Verificar que el comando codegen funciona
            test_cmd = ["playwright", "codegen", "--help"]
            process = await asyncio.create_subprocess_exec(
                *test_cmd,
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "Error con comando codegen"
                raise Exception(f"Comando 'playwright codegen' no funciona: {error_msg}")

            logger.info("Comando 'playwright codegen' verificado correctamente")

        except FileNotFoundError:
            raise Exception("Playwright no está instalado. Ejecute 'pip install playwright' y 'playwright install'")
        except Exception as e:
            logger.error(f"Error verificando instalación de Playwright: {str(e)}")
            raise
    
    async def _run_session(self, session_id: str, cmd: List[str], session_dir: str, headless: bool = False, force_vnc: bool = False):
        """Ejecuta una sesión de codegen en background.
        
        Nota: Playwright CodeGen siempre requiere interfaz gráfica (headless=False)
        para que el usuario pueda interactuar con el navegador y grabar acciones.
        
        Si detecta un entorno sin GUI (servidor), automáticamente usa VNC.
        """

        session = self.active_sessions[session_id]

        try:
            session.status = "running"
            session.updated_at = datetime.now()

            # Detectar si necesitamos VNC (entorno sin GUI o forzado por usuario)
            needs_vnc = force_vnc or await self._detect_headless_environment()
            
            if needs_vnc:
                if force_vnc:
                    logger.info(f"Modo VNC forzado por usuario para sesión {session_id}")
                else:
                    logger.info(f"Entorno sin GUI detectado - iniciando sesión VNC para {session_id}")
                
                try:
                    vnc_info = await self._start_session_with_vnc(session_id, cmd, session_dir)
                    
                    # Actualizar información de la sesión con datos VNC
                    session.vnc_info = vnc_info
                    session.web_vnc_url = vnc_info["web_vnc_url"]
                    session.vnc_port = vnc_info["vnc_port"]
                    
                    logger.info(f"Sesión VNC iniciada para {session_id}")
                    logger.info(f"Acceso web: {vnc_info['web_vnc_url']}")
                    
                    # El monitoreo se maneja en RemoteCodegenService
                    return
                    
                except Exception as e:
                    logger.error(f"Error con VNC, intentando modo normal: {str(e)}")
                    # Si VNC falla, continuar con modo normal
                    needs_vnc = False

            # Modo normal (local con GUI)
            logger.info(f"Usando modo local con GUI para sesión {session_id}")
            
            # Preparar variables de entorno
            env = os.environ.copy()

            # Asegurar que el comando playwright esté disponible en el PATH
            # Buscar el directorio del entorno virtual Python donde está instalado Playwright
            python_executable = sys.executable
            venv_bin_dir = os.path.dirname(python_executable)
            
            # Agregar el directorio bin del venv al PATH si no está presente
            current_path = env.get("PATH", "")
            if venv_bin_dir not in current_path:
                env["PATH"] = f"{venv_bin_dir}:{current_path}"
                logger.info(f"Agregando {venv_bin_dir} al PATH para acceso a playwright")

            # Playwright CodeGen SIEMPRE necesita interfaz gráfica
            # Asegurar que el entorno permite mostrar ventanas GUI
            if "DISPLAY" not in env and hasattr(os, 'fork'):  # Unix-like systems
                env["DISPLAY"] = ":0"
                
            # En macOS, asegurar que el proceso puede acceder a la interfaz gráfica
            import platform
            if platform.system() == "Darwin":  # macOS
                # No manipular DISPLAY en macOS - usar configuración nativa
                env.pop("DISPLAY", None)
                
            logger.info(f"Sesión {session_id} configurada para modo headed (interfaz visible)")

            # Log del comando que se va a ejecutar
            logger.info(f"Ejecutando comando para sesión {session_id}: {' '.join(cmd)}")
            logger.info(f"Directorio de trabajo: {session_dir}")

            # Ejecutar comando para Playwright Codegen
            # Capturar stderr para errores, pero permitir que stdout sea interactivo
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=session_dir,
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            self.session_processes[session_id] = process

            # No esperamos a que termine porque es interactivo
            # El usuario lo detendrá manualmente desde la UI
            logger.info(f"Proceso Playwright Codegen iniciado para sesión {session_id}, PID: {process.pid}")

            # Monitorear el proceso en background
            asyncio.create_task(self._monitor_session(session_id, process))

        except Exception as e:
            session.status = "failed"
            session.error_message = str(e)
            logger.error(f"Error ejecutando sesión {session_id}: {str(e)}")
            logger.error(f"Comando que falló: {' '.join(cmd)}")
            session.updated_at = datetime.now()
            if session_id in self.session_processes:
                del self.session_processes[session_id]
    
    async def _monitor_session(self, session_id: str, process: asyncio.subprocess.Process):
        """Monitorea una sesión de codegen en background."""

        session = self.active_sessions.get(session_id)
        if not session:
            return

        try:
            # Leer stderr en background para capturar errores inmediatos
            async def read_stderr():
                try:
                    stderr_data = await process.stderr.read()
                    if stderr_data:
                        error_msg = stderr_data.decode('utf-8', errors='ignore')
                        if error_msg.strip():
                            logger.warning(f"Stderr de sesión {session_id}: {error_msg}")
                            # Si hay errores críticos, actualizar la sesión
                            if any(keyword in error_msg.lower() for keyword in ['error', 'failed', 'cannot', 'not found']):
                                session.error_message = error_msg
                except Exception as e:
                    logger.debug(f"Error leyendo stderr de sesión {session_id}: {str(e)}")

            # Iniciar lectura de stderr en background
            asyncio.create_task(read_stderr())

            # Esperar un poco para que el proceso se inicie completamente
            await asyncio.sleep(2)

            # Verificar si el proceso sigue corriendo después del inicio
            if process.returncode is not None:
                logger.warning(f"Proceso de sesión {session_id} terminó inmediatamente con código {process.returncode}")
                if process.returncode != 0:
                    session.status = "failed"
                    session.error_message = f"El proceso terminó inmediatamente con código {process.returncode}"
                    session.completed_at = datetime.now()
                    await self._persist_session(session)
                    return

            # Esperar a que el proceso termine (por acción del usuario o stop_session)
            returncode = await process.wait()

            # Verificar si la sesión ya está siendo manejada por stop_session
            if session.status == "stopping":
                # La sesión está siendo detenida manualmente, no hacer nada más
                logger.debug(f"Sesión {session_id} ya está siendo detenida manualmente")
                return

            # Actualizar estado basado en cómo terminó
            if returncode == 0:
                session.status = "completed"
                session.completed_at = datetime.now()
                logger.info(f"Sesión {session_id} completada exitosamente")
            elif returncode == -15 or returncode == -9:  # SIGTERM or SIGKILL
                session.status = "stopped"
                session.completed_at = datetime.now()
                logger.info(f"Sesión {session_id} detenida por el usuario")
            else:
                session.status = "failed"
                session.completed_at = datetime.now()
                # Intentar leer error si está disponible
                try:
                    _, stderr = await asyncio.wait_for(process.communicate(), timeout=1.0)
                    if stderr:
                        error_msg = stderr.decode('utf-8', errors='ignore')
                        if not session.error_message:  # Solo si no hay error previo
                            session.error_message = error_msg
                except:
                    if not session.error_message:
                        session.error_message = f"Proceso terminó con código {returncode}"
                logger.warning(f"Sesión {session_id} falló con código {returncode}: {session.error_message}")

            # CRÍTICO: Intentar obtener código generado ANTES de persistir
            try:
                generated_code = await self.get_generated_code(session_id)
                if generated_code:
                    logger.info(f"Código generado capturado para sesión {session_id} ({len(generated_code)} caracteres)")
                else:
                    logger.warning(f"No se pudo obtener código generado para sesión {session_id}")
            except Exception as e:
                logger.error(f"Error capturando código generado para sesión {session_id}: {str(e)}")

            # Persistir la sesión completada
            await self._persist_session(session)

        except Exception as e:
            # Solo actualizar si la sesión todavía existe y no está siendo manejada manualmente
            if session_id in self.active_sessions and session.status != "stopping":
                session.status = "failed"
                session.error_message = str(e)
                logger.error(f"Error monitoreando sesión {session_id}: {str(e)}")

        finally:
            # Limpiar directorio temporal de la sesión
            if session.artifacts_path and os.path.exists(session.artifacts_path):
                try:
                    shutil.rmtree(session.artifacts_path)
                    logger.info(f"Directorio temporal de sesión {session_id} limpiado: {session.artifacts_path}")
                except Exception as e:
                    logger.error(f"Error limpiando directorio temporal {session.artifacts_path}: {e}")

            # Solo limpiar si la sesión todavía existe y no está siendo manejada manualmente
            if session_id in self.active_sessions and session.status != "stopping":
                session.updated_at = datetime.now()

            # Limpiar referencia al proceso solo si todavía existe
            if session_id in self.session_processes:
                del self.session_processes[session_id]
    
    def _adapt_code_for_qak(
        self,
        generated_code: str,
        language: str,
        framework: str,
        include_assertions: bool,
        add_error_handling: bool
    ) -> str:
        """Adapta el código generado para QAK."""
        
        # Plantillas de adaptación por lenguaje
        if language == "python":
            return self._adapt_python_code(generated_code, framework, include_assertions, add_error_handling)
        elif language == "javascript":
            return self._adapt_javascript_code(generated_code, framework, include_assertions, add_error_handling)
        else:
            # Para otros lenguajes, devolver tal como está por ahora
            return generated_code
    
    def _adapt_python_code(self, code: str, framework: str, include_assertions: bool, add_error_handling: bool) -> str:
        """Adapta código Python para QAK."""
        
        adapted = []
        adapted.append("# Código generado con Playwright Codegen y adaptado para QAK")
        adapted.append("import pytest")
        adapted.append("from playwright.sync_api import Page, expect")
        adapted.append("")
        
        if add_error_handling:
            adapted.append("@pytest.fixture(scope='function')")
            adapted.append("def setup_teardown():")
            adapted.append("    # Setup")
            adapted.append("    yield")
            adapted.append("    # Teardown")
            adapted.append("")
        
        # Agregar el código original con modificaciones
        lines = code.split('\n')
        for line in lines:
            if line.strip():
                adapted.append(line)
        
        if include_assertions:
            adapted.append("")
            adapted.append("    # Verificaciones adicionales agregadas por QAK")
            adapted.append("    expect(page).to_have_url(page.url)")
        
        return '\n'.join(adapted)
    
    def _adapt_javascript_code(self, code: str, framework: str, include_assertions: bool, add_error_handling: bool) -> str:
        """Adapta código JavaScript para QAK."""
        
        adapted = []
        adapted.append("// Código generado con Playwright Codegen y adaptado para QAK")
        adapted.append("import { test, expect } from '@playwright/test';")
        adapted.append("")
        
        # Agregar el código original
        adapted.append(code)
        
        if include_assertions:
            # Buscar la última línea del test e insertar verificaciones adicionales
            lines = adapted[-1].split('\n') if adapted else []
            if lines and '});' in lines[-1]:
                lines.insert(-1, "  // Verificaciones adicionales agregadas por QAK")
                lines.insert(-1, "  await expect(page).toHaveURL(page.url());")
                adapted[-1] = '\n'.join(lines)
        
        return '\n'.join(adapted)
    
    async def _save_to_project(self, project_id: str, test_suite: str, testcase_data: Dict[str, Any]) -> bool:
        """Guarda el caso de prueba en un proyecto QAK."""
        
        try:
            # Usar el ProjectManager existente para guardar
            success = self.project_manager.add_test_case(
                project_id,
                test_suite,
                testcase_data["name"],
                testcase_data
            )
            return success
            
        except Exception as e:
            logger.error(f"Error guardando caso de prueba en proyecto {project_id}: {str(e)}")
            return False
    
    async def _persist_session(self, session: CodegenSessionInfo):
        """Persiste una sesión completada en la base de datos."""
        if not self.codegen_repo:
            logger.error(f"Cannot persist session {session.session_id}: repository not available.")
            return

        try:
            # Convertir a un modelo de documento y guardar/actualizar
            from src.database.models.codegen_session import CodegenSession
            
            # Verificar si la sesión ya existe
            existing_session = await self.codegen_repo.get_by_session_id(session.session_id)
            
            session_doc = CodegenSession(**session.model_dump())
            
            if existing_session:
                # Actualizar la sesión existente
                await self.codegen_repo.update_session(session_doc)
                logger.debug(f"Sesión {session.session_id} actualizada en la base de datos.")
            else:
                # Crear una nueva sesión
                await self.codegen_repo.create_session(session_doc)
                logger.debug(f"Sesión {session.session_id} creada en la base de datos.")

        except Exception as e:
            logger.error(f"Error persistiendo sesión {session.session_id} en la base de datos: {str(e)}")

    async def get_sessions_history_async(self, limit: int = 50, skip: int = 0) -> List[CodegenSessionInfo]:
        """Obtiene el historial de sesiones de forma asíncrona desde la BD."""
        if not self.codegen_repo:
            logger.error("Cannot get session history: repository not available.")
            return []

        try:
            # Usar find() en lugar de get_all() con los parámetros correctos
            from pymongo import DESCENDING
            sort = [("created_at", DESCENDING)]
            sessions_docs = await self.codegen_repo.find(
                query={}, 
                sort=sort, 
                limit=limit, 
                skip=skip
            )
            return [CodegenSessionInfo(**doc.model_dump()) for doc in sessions_docs]
        except Exception as e:
            logger.error(f"Error getting session history from DB: {e}")
            return []

    async def _cleanup_old_sessions_from_db(self):
        """Tarea de limpieza automática de sesiones antiguas en la BD."""
        if not self.codegen_repo:
            return

        try:
            from src.database.models.codegen_session import CodegenStatus
            
            logger.info("Cleaning up old, unfinished sessions from database...")
            
            # Find sessions older than 1 day that are still pending or running
            cutoff_date = datetime.now() - timedelta(days=1)
            
            pending_sessions = await self.codegen_repo.find_by_status(CodegenStatus.PENDING)
            running_sessions = await self.codegen_repo.find_by_status(CodegenStatus.RUNNING)
            
            old_sessions = [
                s for s in pending_sessions + running_sessions 
                if s.created_at < cutoff_date
            ]
            
            if not old_sessions:
                logger.info("No old, unfinished sessions to clean up.")
                return

            for session in old_sessions:
                logger.warning(f"Marking old session {session.session_id} (created at {session.created_at}) as failed.")
                session.status = "failed"
                session.error_message = "Session timed out and was cleaned up by the system."
                session.completed_at = datetime.now()
                await self.codegen_repo.update(session)
            
            logger.info(f"Cleaned up {len(old_sessions)} old sessions.")
            
        except Exception as e:
            logger.error(f"Error during old session cleanup: {e}", exc_info=True)

    async def _detect_headless_environment(self) -> bool:
        """Determina si se está ejecutando en un entorno sin cabeza (headless)."""
        try:
            import platform
            
            # En macOS y Windows, asumir que hay interfaz gráfica
            if platform.system() in ["Darwin", "Windows"]:
                return False
            
            # En Linux, verificar si hay DISPLAY disponible
            if "DISPLAY" in os.environ:
                # Verificar si el display realmente funciona
                try:
                    process = await asyncio.create_subprocess_exec(
                        "xdpyinfo",
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    stdout, stderr = await process.communicate()
                    
                    if process.returncode == 0:
                        logger.info("Interfaz gráfica detectada - usando modo normal")
                        return False
                    else:
                        logger.info("DISPLAY configurado pero no funcional - usando modo VNC")
                        return True
                        
                except FileNotFoundError:
                    # xdpyinfo no disponible, asumir que no hay GUI
                    logger.info("xdpyinfo no disponible - asumiendo entorno sin GUI")
                    return True
            else:
                # No hay DISPLAY, definitivamente sin GUI
                logger.info("No hay DISPLAY configurado - usando modo VNC")
                return True
                
        except Exception as e:
            logger.warning(f"Error detectando entorno gráfico: {str(e)} - asumiendo VNC")
            return True
    
    async def _start_session_with_vnc(
        self, 
        session_id: str, 
        cmd: List[str], 
        session_dir: str
    ) -> Dict[str, Any]:
        """Inicia una sesión de CodeGen usando VNC para entornos sin GUI."""
        try:
            from src.core.remote_codegen_service import RemoteCodegenService
            
            if not hasattr(self, '_vnc_service'):
                self._vnc_service = RemoteCodegenService()
            
            # Verificar dependencias VNC
            deps_check = await self._vnc_service.check_vnc_dependencies()
            if not deps_check["all_dependencies_available"]:
                missing_deps = [
                    name for name, info in deps_check["dependencies"].items() 
                    if not info["available"]
                ]
                raise Exception(
                    f"Dependencias VNC faltantes: {missing_deps}. "
                    f"Instalar con: {deps_check['installation_commands']['ubuntu/debian']}"
                )
            
            # Iniciar sesión VNC
            vnc_info = await self._vnc_service.start_remote_codegen_session(
                session_id=session_id,
                cmd=cmd,
                session_dir=session_dir
            )
            
            return vnc_info
            
        except Exception as e:
            logger.error(f"Error iniciando sesión VNC: {str(e)}")
            raise
