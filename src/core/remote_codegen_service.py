"""Servicio para ejecutar Playwright CodeGen en servidores remotos con VNC."""

import os
import asyncio
import tempfile
import json
import uuid
import subprocess
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

class RemoteCodegenService:
    """Servicio para ejecutar Playwright CodeGen en entornos sin interfaz gráfica usando VNC."""
    
    def __init__(self):
        """Inicializa el servicio de CodeGen remoto."""
        self.active_vnc_sessions: Dict[str, Dict[str, Any]] = {}
        self.vnc_base_port = 5900  # Puerto base para VNC
        self.novnc_base_port = 6080  # Puerto base para noVNC (web)
        
    async def start_remote_codegen_session(
        self, 
        session_id: str,
        cmd: list,
        session_dir: str,
        vnc_port: Optional[int] = None,
        novnc_port: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Inicia una sesión de Playwright CodeGen con VNC para acceso remoto.
        
        Args:
            session_id: ID de la sesión de CodeGen
            cmd: Comando de Playwright CodeGen a ejecutar
            session_dir: Directorio de trabajo
            vnc_port: Puerto VNC específico (opcional)
            novnc_port: Puerto noVNC específico (opcional)
            
        Returns:
            Información de la sesión VNC con URLs de acceso
        """
        try:
            # Asignar puertos si no se especifican
            if vnc_port is None:
                vnc_port = await self._find_available_port(self.vnc_base_port)
            if novnc_port is None:
                novnc_port = await self._find_available_port(self.novnc_base_port)
            
            display_number = vnc_port - self.vnc_base_port
            
            # Configurar entorno para VNC
            import platform
            env = os.environ.copy()
            env["DISPLAY"] = f":{display_number}"
            
            processes = {}
            
            # 1. Configurar display según el sistema operativo
            if platform.system() == "Darwin":  # macOS
                logger.info("Sistema macOS detectado - configurando VNC nativo")
                # En macOS, usar el display principal (:0) que ya existe
                env["DISPLAY"] = ":0"
                display_number = 0
                
                # Verificar si Screen Sharing está habilitado
                try:
                    sharing_check = await asyncio.create_subprocess_exec(
                        "launchctl", "print", "system/com.apple.screensharing",
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    await sharing_check.communicate()
                    
                    if sharing_check.returncode != 0:
                        logger.warning("Screen Sharing no está habilitado en macOS. Intentando con x11vnc...")
                except Exception:
                    logger.warning("No se pudo verificar Screen Sharing. Usando x11vnc...")
                
            else:  # Linux
                env["XVFB_WHD"] = "1920x1080x24"
                
                xvfb_cmd = [
                    "Xvfb", 
                    f":{display_number}",
                    "-screen", "0", "1920x1080x24",
                    "-ac", "+extension", "GLX"
                ]
                
                logger.info(f"Iniciando Xvfb para display :{display_number}")
                xvfb_process = await asyncio.create_subprocess_exec(
                    *xvfb_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    env=env
                )
                processes["xvfb"] = xvfb_process.pid
                
                # Esperar a que Xvfb se inicie
                await asyncio.sleep(2)
            
            # 2. Iniciar VNC server
            if platform.system() == "Darwin":  # macOS
                # En macOS, usar x11vnc con modo rawfb para capturar la pantalla principal
                logger.info("Configurando VNC para macOS con modo rawfb")
                vnc_cmd = [
                    "x11vnc",
                    "-rfbport", str(vnc_port),
                    "-nopw",
                    "-forever", 
                    "-shared",
                    "-rawfb", "macosx:0",  # Usar captura nativa de macOS
                    "-desktop", f"QAK-Codegen-{session_id[:8]}",
                    "-cursor", "arrow",
                    "-reflect", "localhost:0"  # Reflejar el display principal
                ]
            else:  # Linux
                vnc_cmd = [
                    "x11vnc",
                    "-display", f":{display_number}",
                    "-rfbport", str(vnc_port),
                    "-nopw",
                    "-forever",
                    "-shared",
                    "-bg"
                ]
            
            logger.info(f"Iniciando VNC server en puerto {vnc_port}")
            logger.info(f"Comando VNC: {' '.join(vnc_cmd)}")
            vnc_process = await asyncio.create_subprocess_exec(
                *vnc_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env
            )
            processes["vnc"] = vnc_process.pid
            
            # Esperar más tiempo para que x11vnc se establezca
            await asyncio.sleep(5)
            
            # Verificar que el proceso VNC sigue ejecutándose
            if vnc_process.returncode is not None:
                vnc_stdout, vnc_stderr = await vnc_process.communicate()
                logger.error(f"VNC falló al iniciar: stdout={vnc_stdout.decode()}, stderr={vnc_stderr.decode()}")
                raise Exception(f"VNC server falló: {vnc_stderr.decode()}")
            
            # Verificar que el puerto VNC esté escuchando
            import socket
            max_retries = 10
            for i in range(max_retries):
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.settimeout(1)
                        result = s.connect_ex(('localhost', vnc_port))
                        if result == 0:
                            logger.info(f"Puerto VNC {vnc_port} está escuchando correctamente")
                            break
                except Exception:
                    pass
                
                if i == max_retries - 1:
                    logger.error(f"Puerto VNC {vnc_port} no está escuchando después de {max_retries} intentos")
                    raise Exception(f"Puerto VNC {vnc_port} no está escuchando")
                
                await asyncio.sleep(1)
            
            await asyncio.sleep(2)
            
            # 3. Iniciar noVNC (VNC web client)
            # Usar noVNC local incluido en el proyecto
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            novnc_path = os.path.join(project_root, "static", "novnc")
            
            novnc_cmd = [
                "websockify",
                "--web", novnc_path,
                str(novnc_port),
                f"localhost:{vnc_port}"
            ]
            
            logger.info(f"Iniciando noVNC en puerto {novnc_port} con path: {novnc_path}")
            novnc_process = await asyncio.create_subprocess_exec(
                *novnc_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env
            )
            processes["novnc"] = novnc_process.pid
            
            await asyncio.sleep(2)
            
            # 4. Iniciar window manager ligero (dwm en macOS, fluxbox en Linux)
            import platform
            if platform.system() == "Darwin":  # macOS
                wm_cmd = ["dwm"]
            else:  # Linux y otros
                wm_cmd = ["fluxbox"]
            
            logger.info(f"Iniciando window manager: {wm_cmd[0]}")
            wm_process = await asyncio.create_subprocess_exec(
                *wm_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env
            )
            processes["wm"] = wm_process.pid
            
            await asyncio.sleep(1)
            
            # 5. Finalmente, ejecutar Playwright CodeGen
            logger.info(f"Ejecutando Playwright CodeGen: {' '.join(cmd)}")
            playwright_process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=session_dir,
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            processes["playwright"] = playwright_process.pid
            
            # Guardar información de la sesión VNC
            vnc_session_info = {
                "session_id": session_id,
                "vnc_port": vnc_port,
                "novnc_port": novnc_port,
                "display_number": display_number,
                "vnc_url": f"vnc://localhost:{vnc_port}",
                "web_vnc_url": f"http://localhost:{novnc_port}/vnc.html?host=localhost&port={novnc_port}",
                "created_at": datetime.now(),
                "processes": processes,
                "status": "running"
            }
            
            self.active_vnc_sessions[session_id] = vnc_session_info
            
            # Monitorear procesos en background
            asyncio.create_task(self._monitor_vnc_session(session_id))
            
            logger.info(f"Sesión VNC iniciada para CodeGen {session_id}")
            logger.info(f"Acceso web: {vnc_session_info['web_vnc_url']}")
            
            return vnc_session_info
            
        except Exception as e:
            logger.error(f"Error iniciando sesión VNC para CodeGen: {str(e)}")
            # Limpiar procesos en caso de error
            await self._cleanup_vnc_session(session_id)
            raise
    
    async def _find_available_port(self, start_port: int) -> int:
        """Encuentra un puerto disponible empezando desde start_port."""
        import socket
        
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                continue
        
        raise Exception(f"No se pudo encontrar puerto disponible desde {start_port}")
    
    async def _monitor_vnc_session(self, session_id: str):
        """Monitorea una sesión VNC en background."""
        try:
            session_info = self.active_vnc_sessions.get(session_id)
            if not session_info:
                return
            
            # Monitorear el proceso de Playwright (principal)
            playwright_pid = session_info["processes"]["playwright"]
            
            # Esperar a que termine Playwright
            while True:
                try:
                    # Verificar si el proceso sigue corriendo
                    os.kill(playwright_pid, 0)  # No mata, solo verifica
                    await asyncio.sleep(5)
                except OSError:
                    # El proceso terminó
                    break
            
            logger.info(f"Proceso Playwright terminó para sesión VNC {session_id}")
            
            # Actualizar estado
            session_info["status"] = "completed"
            session_info["completed_at"] = datetime.now()
            
            # Limpiar después de un delay para permitir ver resultados
            await asyncio.sleep(30)  # 30 segundos para revisar resultados
            await self._cleanup_vnc_session(session_id)
            
        except Exception as e:
            logger.error(f"Error monitoreando sesión VNC {session_id}: {str(e)}")
            await self._cleanup_vnc_session(session_id)
    
    async def _cleanup_vnc_session(self, session_id: str):
        """Limpia todos los procesos de una sesión VNC."""
        try:
            session_info = self.active_vnc_sessions.get(session_id)
            if not session_info:
                return
            
            logger.info(f"Limpiando sesión VNC {session_id}")
            
            # Terminar todos los procesos
            for process_name, pid in session_info["processes"].items():
                try:
                    os.kill(pid, 15)  # SIGTERM
                    logger.debug(f"Terminado proceso {process_name} (PID: {pid})")
                except OSError:
                    logger.debug(f"Proceso {process_name} (PID: {pid}) ya terminado")
            
            # Esperar un poco y forzar si es necesario
            await asyncio.sleep(2)
            
            for process_name, pid in session_info["processes"].items():
                try:
                    os.kill(pid, 9)  # SIGKILL
                except OSError:
                    pass
            
            # Remover de sesiones activas
            if session_id in self.active_vnc_sessions:
                del self.active_vnc_sessions[session_id]
            
            logger.info(f"Sesión VNC {session_id} limpiada")
            
        except Exception as e:
            logger.error(f"Error limpiando sesión VNC {session_id}: {str(e)}")
    
    async def stop_vnc_session(self, session_id: str) -> bool:
        """Detiene una sesión VNC específica."""
        if session_id not in self.active_vnc_sessions:
            return False
        
        await self._cleanup_vnc_session(session_id)
        return True
    
    def get_vnc_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene información de una sesión VNC."""
        session_info = self.active_vnc_sessions.get(session_id)
        if session_info:
            # Convertir datetime a string para JSON
            info_copy = session_info.copy()
            info_copy["created_at"] = info_copy["created_at"].isoformat()
            if "completed_at" in info_copy:
                info_copy["completed_at"] = info_copy["completed_at"].isoformat()
            return info_copy
        return None
    
    def list_active_vnc_sessions(self) -> list:
        """Lista todas las sesiones VNC activas."""
        sessions = []
        for session_id, session_info in self.active_vnc_sessions.items():
            info_copy = session_info.copy()
            info_copy["created_at"] = info_copy["created_at"].isoformat()
            if "completed_at" in info_copy:
                info_copy["completed_at"] = info_copy["completed_at"].isoformat()
            sessions.append(info_copy)
        return sessions
    
    async def check_vnc_dependencies(self) -> Dict[str, Any]:
        """Verifica que todas las dependencias de VNC estén instaladas."""
        import platform
        
        # Dependencias básicas para todos los sistemas
        base_dependencies = {
            "x11vnc": "x11vnc", 
            "websockify": "websockify"
        }
        
        # Dependencias específicas por sistema operativo
        if platform.system() == "Darwin":  # macOS
            system_dependencies = {
                "dwm": "dwm"  # Window manager para macOS
            }
            install_commands = {
                "macos": [
                    "brew install --cask xquartz",
                    "brew install x11vnc dwm",
                    "npm install -g websockify"
                ]
            }
        else:  # Linux
            system_dependencies = {
                "xvfb": "Xvfb",
                "fluxbox": "fluxbox"
            }
            install_commands = {
                "ubuntu/debian": [
                    "sudo apt-get update",
                    "sudo apt-get install -y xvfb x11vnc fluxbox",
                    "sudo npm install -g websockify"
                ],
                "centos/rhel": [
                    "sudo yum install -y xorg-x11-server-Xvfb x11vnc fluxbox",
                    "sudo npm install -g websockify"
                ],
                "alpine": [
                    "apk add xvfb x11vnc fluxbox",
                    "npm install -g websockify"
                ]
            }
        
        # Combinar todas las dependencias
        dependencies = {**base_dependencies, **system_dependencies}
        
        results = {}
        all_available = True
        
        for dep_name, command in dependencies.items():
            try:
                process = await asyncio.create_subprocess_exec(
                    "which", command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()
                
                if process.returncode == 0:
                    results[dep_name] = {
                        "available": True,
                        "path": stdout.decode().strip()
                    }
                else:
                    results[dep_name] = {
                        "available": False,
                        "error": "Command not found"
                    }
                    all_available = False
                    
            except Exception as e:
                results[dep_name] = {
                    "available": False,
                    "error": str(e)
                }
                all_available = False
        
        # Verificación adicional para x11vnc en macOS
        if platform.system() == "Darwin" and results.get("x11vnc", {}).get("available"):
            try:
                # Verificar si x11vnc tiene soporte X11
                x11vnc_test = await asyncio.create_subprocess_exec(
                    "x11vnc", "-help",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await x11vnc_test.communicate()
                output = stdout.decode() + stderr.decode()
                
                if "without X11 support" in output or "rawfb only" in output:
                    results["x11vnc"]["warning"] = "x11vnc compiled without X11 support - may have limited functionality"
                    results["x11vnc"]["recommendation"] = "Consider installing a version with X11 support or use alternative VNC server"
                
            except Exception as e:
                results["x11vnc"]["warning"] = f"Could not verify x11vnc capabilities: {str(e)}"
        
        return {
            "all_dependencies_available": all_available,
            "system": platform.system(),
            "dependencies": results,
            "installation_commands": install_commands
        }
