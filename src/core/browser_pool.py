"""
Browser Pool for QAK Test Execution System

Manages a pool of reusable browser instances for optimal performance
with intelligent warm-up, cleanup, and resource management.
"""

from typing import Dict, Any, Optional, List, Set
import asyncio
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import hashlib

from src.core.configuration_manager import BrowserConfig

logger = logging.getLogger(__name__)


class BrowserState(str, Enum):
    """Browser instance states."""
    INITIALIZING = "initializing"
    AVAILABLE = "available"
    IN_USE = "in_use"
    CLEANING = "cleaning"
    CONTAMINATED = "contaminated"
    DISPOSED = "disposed"


@dataclass
class BrowserInstance:
    """Represents a managed browser instance."""
    instance_id: str
    browser_object: Any  # Will be actual browser instance
    config_hash: str
    state: BrowserState
    created_at: datetime
    last_used: datetime
    usage_count: int = 0
    session_data: Dict[str, Any] = None
    is_contaminated: bool = False
    cleanup_required: bool = False
    
    def __post_init__(self):
        if self.session_data is None:
            self.session_data = {}
    
    def mark_used(self):
        """Mark browser as used and update timestamps."""
        self.last_used = datetime.now()
        self.usage_count += 1
        self.state = BrowserState.IN_USE
    
    def mark_available(self):
        """Mark browser as available for reuse."""
        self.state = BrowserState.AVAILABLE
        self.cleanup_required = False
    
    def mark_contaminated(self, reason: str = ""):
        """Mark browser as contaminated and requiring disposal."""
        self.is_contaminated = True
        self.state = BrowserState.CONTAMINATED
        logger.warning(f"Browser {self.instance_id} contaminated: {reason}")
    
    def needs_disposal(self, max_age_hours: int = 1, max_usage: int = 50) -> bool:
        """Check if browser needs disposal based on age and usage."""
        age_hours = (datetime.now() - self.created_at).total_seconds() / 3600
        return (
            self.is_contaminated or
            age_hours > max_age_hours or
            self.usage_count > max_usage or
            self.state == BrowserState.CONTAMINATED
        )


class BrowserPool:
    """
    Manages a pool of browser instances for efficient reuse.
    
    Features:
    - Intelligent browser reuse based on configuration compatibility
    - Automatic warm-up of browsers
    - Contamination detection and cleanup
    - Resource limits and auto-disposal
    - Session isolation between executions
    """
    
    def __init__(
        self,
        min_pool_size: int = 2,
        max_pool_size: int = 10,
        warmup_count: int = 1,
        max_browser_age_hours: int = 1,
        max_browser_usage: int = 50
    ):
        """
        Initialize browser pool.
        
        Args:
            min_pool_size: Minimum browsers to maintain
            max_pool_size: Maximum browsers allowed
            warmup_count: Number of browsers to warm up per config
            max_browser_age_hours: Max hours before browser disposal
            max_browser_usage: Max uses before browser disposal
        """
        self.min_pool_size = min_pool_size
        self.max_pool_size = max_pool_size
        self.warmup_count = warmup_count
        self.max_browser_age_hours = max_browser_age_hours
        self.max_browser_usage = max_browser_usage
        
        # Pool storage
        self.browsers: Dict[str, BrowserInstance] = {}
        self.config_pools: Dict[str, Set[str]] = {}  # config_hash -> set of browser_ids
        self.active_browsers: Set[str] = set()
        
        # Pool state
        self.is_initialized = False
        self.total_created = 0
        self.total_disposed = 0
        
        # Background tasks
        self.cleanup_task: Optional[asyncio.Task] = None
        self.warmup_tasks: Dict[str, asyncio.Task] = {}
        
        logger.info(f"BrowserPool initialized (min: {min_pool_size}, max: {max_pool_size})")
    
    async def initialize(self):
        """Initialize the browser pool."""
        if self.is_initialized:
            return
        
        # Start background cleanup task
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        self.is_initialized = True
        logger.info("BrowserPool initialized successfully")
    
    async def acquire_browser(
        self, 
        config: BrowserConfig, 
        execution_id: str,
        allow_shared: bool = True
    ) -> Optional[BrowserInstance]:
        """
        Acquire a browser instance for use.
        
        Args:
            config: Browser configuration
            execution_id: ID of execution requesting browser
            allow_shared: Whether to allow sharing browsers between executions
            
        Returns:
            BrowserInstance or None if unavailable
        """
        await self.initialize()
        
        config_hash = self._hash_config(config)
        
        try:
            # Try to get existing browser from pool
            if allow_shared:
                browser = await self._get_available_browser(config_hash)
                if browser:
                    browser.mark_used()
                    self.active_browsers.add(browser.instance_id)
                    logger.info(f"Acquired existing browser {browser.instance_id} for {execution_id}")
                    return browser
            
            # Create new browser if pool has capacity
            if len(self.browsers) < self.max_pool_size:
                browser = await self._create_browser(config, config_hash)
                if browser:
                    browser.mark_used()
                    self.active_browsers.add(browser.instance_id)
                    logger.info(f"Created new browser {browser.instance_id} for {execution_id}")
                    return browser
            
            # Pool is full, try to dispose old browsers and create new one
            disposed_count = await self._dispose_old_browsers()
            if disposed_count > 0 and len(self.browsers) < self.max_pool_size:
                browser = await self._create_browser(config, config_hash)
                if browser:
                    browser.mark_used()
                    self.active_browsers.add(browser.instance_id)
                    logger.info(f"Created browser {browser.instance_id} after cleanup for {execution_id}")
                    return browser
            
            logger.warning(f"No browser available for {execution_id} (pool full)")
            return None
            
        except Exception as e:
            logger.error(f"Failed to acquire browser for {execution_id}: {e}")
            return None
    
    async def release_browser(
        self, 
        browser: BrowserInstance, 
        contaminated: bool = False,
        contamination_reason: str = ""
    ):
        """
        Release a browser back to the pool.
        
        Args:
            browser: Browser instance to release
            contaminated: Whether browser is contaminated
            contamination_reason: Reason for contamination
        """
        try:
            self.active_browsers.discard(browser.instance_id)
            
            if contaminated or browser.needs_disposal(self.max_browser_age_hours, self.max_browser_usage):
                if contaminated:
                    browser.mark_contaminated(contamination_reason)
                
                await self._dispose_browser(browser)
                logger.info(f"Disposed browser {browser.instance_id}")
            else:
                # Clean browser for reuse
                await self._clean_browser(browser)
                browser.mark_available()
                logger.debug(f"Released browser {browser.instance_id} back to pool")
            
        except Exception as e:
            logger.error(f"Failed to release browser {browser.instance_id}: {e}")
            # Force disposal on error
            await self._dispose_browser(browser)
    
    async def _get_available_browser(self, config_hash: str) -> Optional[BrowserInstance]:
        """Get an available browser with compatible configuration."""
        compatible_browsers = self.config_pools.get(config_hash, set())
        
        for browser_id in compatible_browsers:
            browser = self.browsers.get(browser_id)
            if browser and browser.state == BrowserState.AVAILABLE:
                return browser
        
        return None
    
    async def _create_browser(self, config: BrowserConfig, config_hash: str) -> Optional[BrowserInstance]:
        """Create a new browser instance."""
        try:
            import uuid
            instance_id = str(uuid.uuid4())
            
            # Create browser object (mock for now - will integrate with actual browser-use)
            browser_object = await self._create_browser_object(config)
            
            browser = BrowserInstance(
                instance_id=instance_id,
                browser_object=browser_object,
                config_hash=config_hash,
                state=BrowserState.INITIALIZING,
                created_at=datetime.now(),
                last_used=datetime.now()
            )
            
            # Store in pool
            self.browsers[instance_id] = browser
            if config_hash not in self.config_pools:
                self.config_pools[config_hash] = set()
            self.config_pools[config_hash].add(instance_id)
            
            self.total_created += 1
            
            # Warm up browser
            await self._warmup_browser(browser)
            
            logger.info(f"Created browser {instance_id} with config hash {config_hash[:8]}")
            return browser
            
        except Exception as e:
            logger.error(f"Failed to create browser: {e}")
            return None
    
    async def _create_browser_object(self, config: BrowserConfig) -> Any:
        """Create the actual browser object using browser-use."""
        try:
            # Import browser-use components
            import sys
            sys.path.append('/Users/<USER>/Proyectos/qak/libs')
            import configure_browser_use_local
            from browser_use import BrowserSession, BrowserProfile
            
            # Create BrowserProfile directly from BrowserConfig
            # Map BrowserConfig attributes to BrowserProfile parameters
            profile_args = {
                "headless": config.headless,
                "user_data_dir": None,  # Use ephemeral profiles to avoid conflicts
                "allowed_domains": config.allowed_domains,
                "disable_security": config.disable_security,
                "highlight_elements": config.highlight_elements,
                "minimum_wait_page_load_time": config.minimum_wait_page_load_time,
                "wait_between_actions": config.wait_between_actions,
                "keep_alive": config.keep_alive,
                "viewport": {"width": 1280, "height": 720},  # Default viewport
                "deterministic_rendering": False,  # Default
                "viewport_expansion": 500,  # Default
                "wait_for_network_idle_page_load_time": 1.0,  # Default
                "maximum_wait_page_load_time": 10.0,  # Default
            }
            
            # Remove None values except for user_data_dir which should be None for ephemeral profiles
            profile_args = {k: v for k, v in profile_args.items() if v is not None or k == "user_data_dir"}
            
            # Create browser profile
            browser_profile = BrowserProfile(**profile_args)
            
            # Create and return browser session
            browser_session = BrowserSession(
                browser_profile=browser_profile,
                keep_alive=config.keep_alive
            )
            
            logger.info(f"Created real browser session with headless={config.headless}")
            return browser_session
            
        except Exception as e:
            logger.error(f"Failed to create browser session: {e}")
            # Fallback to mock for now
            await asyncio.sleep(0.1)  # Simulate browser creation time
            
            return {
                "headless": config.headless,
                "config": config.__dict__,
                "mock": True,
                "created_at": datetime.now().isoformat()
            }
    
    async def _warmup_browser(self, browser: BrowserInstance):
        """Warm up browser for optimal performance."""
        try:
            browser.state = BrowserState.INITIALIZING
            
            # Warm-up activities (mock implementation)
            await asyncio.sleep(0.05)  # Simulate warmup time
            
            browser.state = BrowserState.AVAILABLE
            logger.debug(f"Warmed up browser {browser.instance_id}")
            
        except Exception as e:
            logger.error(f"Failed to warm up browser {browser.instance_id}: {e}")
            browser.mark_contaminated(f"Warmup failed: {e}")
    
    async def _clean_browser(self, browser: BrowserInstance):
        """Clean browser session data for reuse."""
        try:
            browser.state = BrowserState.CLEANING
            
            # Clean session data (mock implementation)
            browser.session_data.clear()
            browser.cleanup_required = False
            
            # Simulate cleanup time
            await asyncio.sleep(0.02)
            
            logger.debug(f"Cleaned browser {browser.instance_id}")
            
        except Exception as e:
            logger.error(f"Failed to clean browser {browser.instance_id}: {e}")
            browser.mark_contaminated(f"Cleanup failed: {e}")
    
    async def _dispose_browser(self, browser: BrowserInstance):
        """Dispose of a browser instance."""
        try:
            browser.state = BrowserState.DISPOSED
            
            # Remove from pools
            self.browsers.pop(browser.instance_id, None)
            if browser.config_hash in self.config_pools:
                self.config_pools[browser.config_hash].discard(browser.instance_id)
                if not self.config_pools[browser.config_hash]:
                    del self.config_pools[browser.config_hash]
            
            self.active_browsers.discard(browser.instance_id)
            
            # Close browser object (mock implementation)
            if browser.browser_object:
                # Will implement actual browser closure
                pass
            
            self.total_disposed += 1
            logger.debug(f"Disposed browser {browser.instance_id}")
            
        except Exception as e:
            logger.error(f"Failed to dispose browser {browser.instance_id}: {e}")
    
    async def _dispose_old_browsers(self) -> int:
        """Dispose of old or overused browsers."""
        disposed_count = 0
        browsers_to_dispose = []
        
        for browser in self.browsers.values():
            if browser.state != BrowserState.IN_USE and browser.needs_disposal(
                self.max_browser_age_hours, 
                self.max_browser_usage
            ):
                browsers_to_dispose.append(browser)
        
        for browser in browsers_to_dispose:
            await self._dispose_browser(browser)
            disposed_count += 1
        
        if disposed_count > 0:
            logger.info(f"Disposed {disposed_count} old browsers")
        
        return disposed_count
    
    async def _cleanup_loop(self):
        """Background cleanup loop."""
        while True:
            try:
                await asyncio.sleep(30)  # Run cleanup every 30 seconds
                
                # Dispose old browsers
                await self._dispose_old_browsers()
                
                # Ensure minimum pool size with warmup
                await self._ensure_minimum_pool()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
    
    async def _ensure_minimum_pool(self):
        """Ensure minimum pool size is maintained."""
        available_count = sum(
            1 for browser in self.browsers.values() 
            if browser.state == BrowserState.AVAILABLE
        )
        
        if available_count < self.min_pool_size:
            # Create browsers with default configuration for warmup
            from src.core.configuration_manager import get_config, ConfigProfile
            default_config = get_config(ConfigProfile.BALANCED)
            
            needed = self.min_pool_size - available_count
            for _ in range(min(needed, self.max_pool_size - len(self.browsers))):
                config_hash = self._hash_config(default_config)
                await self._create_browser(default_config, config_hash)
    
    def _hash_config(self, config: BrowserConfig) -> str:
        """Create hash for browser configuration."""
        # Create a hash based on significant configuration parameters
        config_str = f"{config.headless}_{config.use_vision}_{config.stealth}_{config.user_agent}"
        return hashlib.md5(config_str.encode()).hexdigest()
    
    async def warmup_for_config(self, config: BrowserConfig, count: int = None):
        """Warm up browsers for specific configuration."""
        if count is None:
            count = self.warmup_count
        
        config_hash = self._hash_config(config)
        
        # Cancel existing warmup for this config
        if config_hash in self.warmup_tasks:
            self.warmup_tasks[config_hash].cancel()
        
        # Start new warmup task
        self.warmup_tasks[config_hash] = asyncio.create_task(
            self._warmup_for_config_task(config, config_hash, count)
        )
    
    async def _warmup_for_config_task(self, config: BrowserConfig, config_hash: str, count: int):
        """Background task to warm up browsers for configuration."""
        try:
            existing_count = len(self.config_pools.get(config_hash, set()))
            needed = max(0, count - existing_count)
            
            for _ in range(min(needed, self.max_pool_size - len(self.browsers))):
                browser = await self._create_browser(config, config_hash)
                if browser:
                    browser.mark_available()
            
            logger.info(f"Warmed up {needed} browsers for config {config_hash[:8]}")
            
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Failed to warm up browsers for config {config_hash[:8]}: {e}")
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """Get current pool statistics."""
        stats = {
            "total_browsers": len(self.browsers),
            "active_browsers": len(self.active_browsers),
            "available_browsers": sum(
                1 for b in self.browsers.values() 
                if b.state == BrowserState.AVAILABLE
            ),
            "contaminated_browsers": sum(
                1 for b in self.browsers.values() 
                if b.state == BrowserState.CONTAMINATED
            ),
            "total_created": self.total_created,
            "total_disposed": self.total_disposed,
            "config_pools": len(self.config_pools),
            "pool_utilization": len(self.browsers) / self.max_pool_size
        }
        
        return stats
    
    async def shutdown(self):
        """Shutdown the browser pool."""
        logger.info("Shutting down BrowserPool")
        
        # Cancel background tasks
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        for task in self.warmup_tasks.values():
            task.cancel()
        
        # Dispose all browsers
        browsers_to_dispose = list(self.browsers.values())
        for browser in browsers_to_dispose:
            await self._dispose_browser(browser)
        
        logger.info("BrowserPool shutdown complete")


# Global browser pool instance
browser_pool = BrowserPool()


async def get_browser(config: BrowserConfig, execution_id: str) -> Optional[BrowserInstance]:
    """
    Convenience function to acquire a browser.
    
    Args:
        config: Browser configuration
        execution_id: Execution ID requesting browser
        
    Returns:
        BrowserInstance or None
    """
    return await browser_pool.acquire_browser(config, execution_id)


async def release_browser(browser: BrowserInstance, contaminated: bool = False):
    """
    Convenience function to release a browser.
    
    Args:
        browser: Browser to release
        contaminated: Whether browser is contaminated
    """
    await browser_pool.release_browser(browser, contaminated) 