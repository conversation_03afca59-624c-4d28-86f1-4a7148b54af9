"""
OpenRouter LLM Service for QAK
Optimized for /src requests: Gherkin generation, validation, translation
FREE MODELS PRIORITIZED - Zero cost operation with MOST POPULAR models
"""

import os
import logging
from typing import Dict, Any, List, Optional
from enum import Enum

logger = logging.getLogger(__name__)

# Try to import OpenRouter client with fallback to requests
try:
    from openrouter_client import OpenRouterClient
    from openrouter_client.exceptions import AuthenticationError, RateLimitError, ValidationError
    OPENROUTER_AVAILABLE = True
    USE_REQUESTS_FALLBACK = False
except ImportError:
    import requests
    OPENROUTER_AVAILABLE = True
    USE_REQUESTS_FALLBACK = True
    logger.warning("OpenRouter client not available, using requests fallback")


class ModelTier(Enum):
    """Model tier classification for cost control."""
    FREE = "free"
    CHEAP = "cheap"
    PREMIUM = "premium"


class OpenRouterService:
    """Service for OpenRouter LLM operations optimized for QAK /src use cases.
    
    Features:
    - TOP FREE models by weekly usage prioritized
    - Zero-cost operation mode
    - Automatic fallback with cost control
    - Vision support with most popular free models
    """
    
    def __init__(self, api_key: Optional[str] = None, 
                 max_monthly_spend: float = 0.0,
                 prefer_free_models: bool = True):
        """Initialize OpenRouter service with aggressive cost optimization.
        
        Args:
            api_key: OpenRouter API key (defaults to env OPENROUTER_API_KEY)
            max_monthly_spend: Maximum monthly spend (0.0 = FREE ONLY)
            prefer_free_models: Always try free models first
        """
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        if not self.api_key:
            raise ValueError("OpenRouter API key required. Set OPENROUTER_API_KEY env var.")
        
        self.max_monthly_spend = max_monthly_spend
        self.prefer_free_models = prefer_free_models
        
        # Initialize client with cost-optimized settings
        if USE_REQUESTS_FALLBACK:
            self.client = self._create_requests_client()
        else:
            self.client = OpenRouterClient(
                api_key=self.api_key,
                max_retries=3,  # Reduced retries to avoid costs
                rate_limit_buffer=0.5,  # More conservative buffer
                timeout=30.0  # Shorter timeout
            )
        
        logger.info(f"OpenRouter initialized - FREE ONLY MODE: {max_monthly_spend == 0.0}")
        
        # Model configurations PRIORITIZING MOST POPULAR FREE MODELS by weekly usage
        self.model_configs = {
            "gherkin": {
                "models": [
                    # TOP FREE models by weekly usage - these cost $0.00
                    ("deepseek/deepseek-chat-v3-0324:free", ModelTier.FREE),  # #1 for coding tasks
                    ("deepseek/deepseek-r1:free", ModelTier.FREE),  # #1 reasoning model
                    ("meta-llama/llama-3.3-70b-instruct:free", ModelTier.FREE),  # Latest 70B Llama
                    ("deepseek/deepseek-r1-distill-llama-70b:free", ModelTier.FREE),  # Popular distill
                    ("google/gemini-2.5-pro-exp-03-25:free", ModelTier.FREE),  # Experimental Google
                    ("deepseek/deepseek-r1-zero:free", ModelTier.FREE),  # Zero-shot specialist
                    ("qwen/qwq-32b:free", ModelTier.FREE),  # Strong math reasoning
                    # Cheap fallbacks (only if budget allows)
                    ("openai/gpt-3.5-turbo", ModelTier.CHEAP),
                    ("anthropic/claude-3-haiku", ModelTier.CHEAP),
                ],
                "temperature": 0.1,
                "max_tokens": 1024,
                "cost_priority": "free_only"
            },
            "validation": {
                "models": [
                    ("deepseek/deepseek-r1:free", ModelTier.FREE),  # Best reasoning for validation
                    ("deepseek/deepseek-chat-v3-0324:free", ModelTier.FREE),  # Reliable performer
                    ("meta-llama/llama-3.3-70b-instruct:free", ModelTier.FREE),  # Strong baseline
                    ("deepseek/deepseek-r1-zero:free", ModelTier.FREE),  # Zero-shot specialist
                    ("google/gemini-2.5-pro-exp-03-25:free", ModelTier.FREE),  # Google reasoning
                    ("openai/gpt-3.5-turbo", ModelTier.CHEAP),
                ],
                "temperature": 0.0,
                "max_tokens": 512,  # Very conservative for validation
                "cost_priority": "free_only"
            },
            "translation": {
                "models": [
                    ("google/gemini-2.5-pro-exp-03-25:free", ModelTier.FREE),  # Google excels at translation
                    ("deepseek/deepseek-chat-v3-0324:free", ModelTier.FREE),  # Multilingual capable
                    ("meta-llama/llama-3.3-70b-instruct:free", ModelTier.FREE),  # Good multilingual
                    ("qwen/qwq-32b:free", ModelTier.FREE),  # Strong in multiple languages
                    ("deepseek/deepseek-r1:free", ModelTier.FREE),  # Advanced reasoning
                    ("openai/gpt-3.5-turbo", ModelTier.CHEAP),
                ],
                "temperature": 0.1,
                "max_tokens": 512,
                "cost_priority": "free_only"
            },
            "enhancement": {
                "models": [
                    ("deepseek/deepseek-r1:free", ModelTier.FREE),  # Best for complex reasoning
                    ("deepseek/deepseek-chat-v3-0324:free", ModelTier.FREE),  # Great for enhancement
                    ("meta-llama/llama-3.3-70b-instruct:free", ModelTier.FREE),  # Strong general model
                    ("google/gemini-2.5-pro-exp-03-25:free", ModelTier.FREE),  # Creative enhancement
                    ("deepseek/deepseek-r1-distill-llama-70b:free", ModelTier.FREE),  # Distilled power
                    ("qwen/qwq-32b:free", ModelTier.FREE),  # Mathematical reasoning
                    ("openai/gpt-4o-mini", ModelTier.CHEAP),
                    ("anthropic/claude-3-haiku", ModelTier.CHEAP),
                ],
                "temperature": 0.2,
                "max_tokens": 1024,
                "cost_priority": "balance"
            },
            "vision_analysis": {
                "models": [
                    # TOP FREE vision models by popularity - ZERO COST!
                    ("meta-llama/llama-4-maverick:free", ModelTier.FREE),  # 400B MoE, most popular
                    ("mistralai/mistral-small-3.1-24b-instruct:free", ModelTier.FREE),  # 24B, reliable
                    ("moonshotai/kimi-vl-a3b-thinking:free", ModelTier.FREE),  # Visual reasoning specialist
                    ("qwen/qwen2.5-vl-3b-instruct:free", ModelTier.FREE),  # Compact but effective
                    # Paid fallbacks (only if budget allows)
                    ("openai/gpt-4o-mini", ModelTier.CHEAP),
                    ("google/gemini-1.5-flash", ModelTier.CHEAP),
                ],
                "temperature": 0.1,
                "max_tokens": 1024,
                "cost_priority": "free_only"
            }
        }

    def _create_requests_client(self):
        """Create requests-based client for API calls."""
        return {
            "api_key": self.api_key,
            "base_url": "https://openrouter.ai/api/v1",
            "headers": {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://github.com/nahuelcioffi/qak",
                "X-Title": "QAK Agent - TOP FREE MODELS",
            }
        }

    def _check_spending_limit(self) -> bool:
        """Check if we're within spending limits."""
        if self.max_monthly_spend == 0.0:
            logger.info("FREE-ONLY MODE: Using only $0.00 models")
            return True
        
        try:
            if not USE_REQUESTS_FALLBACK and hasattr(self.client, 'credits'):
                credits = self.client.credits.get()
                monthly_usage = getattr(credits.data, 'usage', 0)
                
                if monthly_usage >= self.max_monthly_spend:
                    logger.warning(f"Monthly spend limit reached: ${monthly_usage:.4f}")
                    return False
        except Exception as e:
            logger.warning(f"Could not check spending limit: {e}")
        
        return True

    def _filter_models_by_budget(self, models: List[tuple], has_images: bool = False) -> List[str]:
        """Filter models based on budget constraints."""
        if not self._check_spending_limit():
            logger.error("Over budget - no models available")
            return []
        
        filtered_models = []
        
        # FREE ONLY MODE - only use $0.00 models
        if self.max_monthly_spend == 0.0:
            for model, tier in models:
                if tier == ModelTier.FREE:
                    filtered_models.append(model)
            logger.info(f"FREE ONLY: Selected {len(filtered_models)} popular free models")
        else:
            # Budget mode - prioritize free, allow paid
            if self.prefer_free_models:
                # Add free models first
                for model, tier in models:
                    if tier == ModelTier.FREE:
                        filtered_models.append(model)
                # Then add paid models
                for model, tier in models:
                    if tier != ModelTier.FREE and model not in filtered_models:
                        filtered_models.append(model)
            else:
                filtered_models = [model for model, tier in models]
        
        # Handle vision requirements
        if has_images:
            vision_models = []
            # TOP FREE vision models by popularity
            top_free_vision = [
                "meta-llama/llama-4-maverick:free",  # Most popular 400B MoE
                "mistralai/mistral-small-3.1-24b-instruct:free",  # Reliable 24B
                "moonshotai/kimi-vl-a3b-thinking:free",  # Visual reasoning specialist
                "qwen/qwen2.5-vl-3b-instruct:free"  # Compact but effective
            ]
            
            for model in filtered_models:
                if model in top_free_vision or (":free" not in model and self.max_monthly_spend > 0.0):
                    vision_models.append(model)
            
            if not vision_models and self.max_monthly_spend == 0.0:
                logger.error("No popular free vision models available in FREE-ONLY mode")
                return []
            
            return vision_models
        
        return filtered_models

    def _make_requests_call(self, request_params: Dict[str, Any]) -> Dict[str, Any]:
        """Make API call using requests with cost logging."""
        import requests
        
        model = request_params.get("model", "unknown")
        is_free = ":free" in model
        
        # Log model popularity info
        popular_models = [
            "deepseek/deepseek-r1:free",
            "deepseek/deepseek-chat-v3-0324:free", 
            "meta-llama/llama-3.3-70b-instruct:free",
            "meta-llama/llama-4-maverick:free"
        ]
        popularity = "🔥 TOP POPULAR" if model in popular_models else "📊 POPULAR" if is_free else "💰 PAID"
        
        logger.info(f"API CALL: {popularity} {'FREE $0.00' if is_free else 'PAID'} model: {model}")
        
        url = f"{self.client['base_url']}/chat/completions"
        
        response = requests.post(
            url,
            headers=self.client["headers"],
            json=request_params,
            timeout=30
        )
        
        if response.status_code == 401:
            raise Exception("Authentication error - check OPENROUTER_API_KEY")
        elif response.status_code == 429:
            raise Exception("Rate limit exceeded")
        elif response.status_code != 200:
            raise Exception(f"HTTP {response.status_code}: {response.text}")
        
        result = response.json()
        
        # Log usage for cost tracking
        if "usage" in result:
            usage = result["usage"]
            cost_info = "FREE $0.00" if is_free else "PAID"
            logger.info(f"✅ {model} - Tokens: {usage.get('total_tokens', 0)} ({cost_info})")
        
        return result

    def _make_request_with_fallback(self, messages: List[Dict[str, Any]], 
                                  use_case: str, has_images: bool = False, **kwargs) -> Dict[str, Any]:
        """Make request with automatic model fallback and cost optimization.
        
        Args:
            messages: Chat messages
            use_case: Use case type for model selection
            has_images: Whether request contains images
            **kwargs: Additional parameters for chat.create
            
        Returns:
            Response content and metadata
        """
        config = self.model_configs.get(use_case, self.model_configs["gherkin"])
        
        # Get available models within budget
        available_models = self._filter_models_by_budget(config["models"], has_images)
        
        if not available_models:
            error_msg = "No models available within budget constraints"
            if self.max_monthly_spend == 0.0:
                error_msg += " (FREE-ONLY mode active)"
            logger.error(error_msg)
            return {
                "content": "",
                "model_used": "none",
                "success": False,
                "error": error_msg
            }
        
        last_error = None
        
        for model in available_models:
            try:
                is_free = ":free" in model
                
                # Enhanced logging with popularity info
                popular_models = [
                    "deepseek/deepseek-r1:free",
                    "deepseek/deepseek-chat-v3-0324:free", 
                    "meta-llama/llama-3.3-70b-instruct:free",
                    "meta-llama/llama-4-maverick:free"
                ]
                popularity = "🔥 TOP" if model in popular_models else "📊 POPULAR" if is_free else "💰 PAID"
                cost_info = "FREE $0.00" if is_free else "PAID"
                
                logger.info(f"🚀 Trying {popularity} {cost_info} model: {model}")
                
                # Prepare request with conservative limits
                request_params = {
                    "model": model,
                    "messages": messages,
                    "temperature": kwargs.get("temperature", config.get("temperature", 0.1)),
                    "max_tokens": min(
                        kwargs.get("max_tokens", config.get("max_tokens", 1024)),
                        config.get("max_tokens", 1024)
                    )
                }
                
                # Make API call
                if USE_REQUESTS_FALLBACK:
                    response_data = self._make_requests_call(request_params)
                    content = response_data["choices"][0]["message"]["content"]
                    usage = response_data.get("usage")
                else:
                    response = self.client.chat.create(**request_params)
                    content = response.choices[0].message.content
                    usage = response.usage.dict() if response.usage else None
                
                logger.info(f"✅ SUCCESS with {popularity} {cost_info} model: {model}")
                
                return {
                    "content": content,
                    "model_used": model,
                    "usage": usage,
                    "metadata": {
                        "cost_tier": "free" if is_free else "paid",
                        "use_case": use_case,
                        "free_model": is_free,
                        "popular_model": model in popular_models
                    },
                    "success": True
                }
                
            except Exception as e:
                error_str = str(e)
                logger.warning(f"❌ Error with model {model}: {error_str}")
                last_error = error_str
                continue
        
        # All models failed
        error_msg = f"All available models failed for {use_case}. Last error: {last_error}"
        logger.error(error_msg)
        
        return {
            "content": "",
            "model_used": "failed",
            "success": False,
            "error": error_msg
        }

    def generate_gherkin(self, instructions: str, user_story: str = "", 
                        url: str = "", language: str = "en") -> Dict[str, Any]:
        """Generate Gherkin scenario using TOP FREE models.
        
        Args:
            instructions: Test instructions
            user_story: Optional user story context
            url: Optional URL context
            language: Response language ('en' or 'es')
            
        Returns:
            Dict with generated Gherkin and metadata
        """
        prompt = f"""Generate a Gherkin scenario for the following:

Instructions: {instructions}
{f"User Story: {user_story}" if user_story else ""}
{f"URL: {url}" if url else ""}

Create a clear, well-structured Gherkin scenario with Given-When-Then format.
Respond in {"Spanish" if language == "es" else "English"}.
"""
        
        messages = [
            {"role": "system", "content": "You are an expert QA engineer specializing in Gherkin test scenarios."},
            {"role": "user", "content": prompt}
        ]
        
        logger.info("🎯 Generating Gherkin with TOP FREE models")
        return self._make_request_with_fallback(messages, "gherkin", max_tokens=1024)

    def validate_result(self, result_data: Dict[str, Any], 
                       test_objective: str, gherkin_scenario: str = "") -> Dict[str, Any]:
        """Validate test execution result using TOP FREE models.
        
        Args:
            result_data: Test execution result data
            test_objective: What the test should accomplish
            gherkin_scenario: Optional Gherkin scenario context
            
        Returns:
            Dict with validation result and reasoning
        """
        prompt = f"""Analyze this test execution result:

Test Objective: {test_objective}
{f"Gherkin Scenario: {gherkin_scenario}" if gherkin_scenario else ""}

Result Data: {result_data}

Determine if the test actually succeeded in achieving its objective, regardless of the 
reported success status. Consider the steps taken and final state.

Respond with JSON:
{{
    "validated_success": true/false,
    "validation_confidence": 0.0-1.0,
    "validation_reasoning": "explanation",
    "should_override_result": true/false
}}
"""
        
        messages = [
            {"role": "system", "content": "You are an expert test result analyst. Analyze carefully and respond only with valid JSON."},
            {"role": "user", "content": prompt}
        ]
        
        logger.info("🔍 Validating result with TOP FREE models")
        return self._make_request_with_fallback(messages, "validation", max_tokens=512)

    def analyze_screenshot(self, image_data: str, mime_type: str,
                          test_objective: str, instructions: str = "") -> Dict[str, Any]:
        """Analyze test screenshot using TOP FREE vision models.
        
        Args:
            image_data: Base64 encoded image data
            mime_type: Image MIME type (e.g., 'image/png')
            test_objective: What the test should accomplish
            instructions: Additional analysis instructions
            
        Returns:
            Dict with analysis results
        """
        messages = [
            {
                "role": "system",
                "content": "You are an expert test analyst. Analyze screenshots to determine if test objectives are met."
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": f"""Test Objective: {test_objective}
{f"Additional Instructions: {instructions}" if instructions else ""}

Analyze this screenshot and determine:
1. What is visible on the screen
2. Whether the test objective appears to be met
3. Any issues or concerns you notice
4. Confidence level in your assessment (0.0-1.0)

Provide detailed analysis."""
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:{mime_type};base64,{image_data}"}
                    }
                ]
            }
        ]
        
        logger.info("📸 Analyzing screenshot with TOP FREE vision models")
        return self._make_request_with_fallback(messages, "vision_analysis", has_images=True, max_tokens=1024)

    def translate_text(self, text: str, target_language: str, 
                      source_language: str = "auto") -> Dict[str, Any]:
        """Translate text using TOP FREE models.
        
        Args:
            text: Text to translate
            target_language: Target language ('en' or 'es')
            source_language: Source language ('en', 'es', or 'auto')
            
        Returns:
            Dict with translated text and metadata
        """
        lang_names = {"en": "English", "es": "Spanish"}
        target_name = lang_names.get(target_language, target_language)
        
        prompt = f"""Translate the following text to {target_name}:

{text}

Provide only the translation, maintaining the original formatting and technical terms where appropriate.
"""
        
        messages = [
            {"role": "system", "content": f"You are an expert translator specializing in technical content. Translate accurately to {target_name}."},
            {"role": "user", "content": prompt}
        ]
        
        logger.info(f"🌐 Translating to {target_name} with TOP FREE models")
        return self._make_request_with_fallback(messages, "translation", max_tokens=512)

    def enhance_user_story(self, user_story: str, language: str = "en") -> Dict[str, Any]:
        """Enhance user story using TOP FREE models.
        
        Args:
            user_story: Original user story
            language: Response language ('en' or 'es')
            
        Returns:
            Dict with enhanced user story and metadata
        """
        prompt = f"""Enhance this user story with additional context, acceptance criteria, and edge cases:

Original User Story:
{user_story}

Provide an enhanced version that includes:
1. Clear acceptance criteria
2. Edge cases to consider
3. Technical considerations
4. User experience improvements

Respond in {"Spanish" if language == "es" else "English"}.
"""
        
        messages = [
            {"role": "system", "content": "You are an expert product analyst and QA engineer."},
            {"role": "user", "content": prompt}
        ]
        
        logger.info("📝 Enhancing user story with TOP FREE models")
        return self._make_request_with_fallback(messages, "enhancement")

    def get_cost_summary(self) -> Dict[str, Any]:
        """Get cost optimization summary."""
        total_free = sum(1 for config in self.model_configs.values() 
                        for _, tier in config["models"] if tier == ModelTier.FREE)
        total_paid = sum(1 for config in self.model_configs.values() 
                        for _, tier in config["models"] if tier != ModelTier.FREE)
        
        # Count top popular models
        top_models = [
            "deepseek/deepseek-r1:free",
            "deepseek/deepseek-chat-v3-0324:free",
            "meta-llama/llama-3.3-70b-instruct:free",
            "meta-llama/llama-4-maverick:free"
        ]
        
        return {
            "service": "openrouter",
            "mode": "FREE_ONLY" if self.max_monthly_spend == 0.0 else "BUDGET_CONTROLLED",
            "settings": {
                "max_monthly_spend": self.max_monthly_spend,
                "prefer_free_models": self.prefer_free_models,
                "free_only_mode": self.max_monthly_spend == 0.0
            },
            "available_models": {
                "total_free_models": total_free,
                "total_paid_models": total_paid,
                "vision_free_models": 4,  # llama-4-maverick, mistral-small-3.1, kimi-vl, qwen2.5-vl
                "top_popular_models": len(top_models)
            },
            "capabilities": {
                "text_generation": "FREE - TOP MODELS",
                "code_generation": "FREE - DEEPSEEK SPECIALIZED", 
                "translation": "FREE - GEMINI OPTIMIZED",
                "validation": "FREE - REASONING MODELS",
                "vision_analysis": "FREE - 400B MOE MODELS",
                "gherkin_generation": "FREE - CODING SPECIALISTS"
            },
            "top_models": top_models
        }

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get current usage statistics with cost tracking.
        
        Returns:
            Dict with credits, rate limits, and usage info
        """
        try:
            base_stats = {
                "service": "openrouter",
                "status": "available",
                "cost_optimization": self.get_cost_summary()
            }
            
            if not USE_REQUESTS_FALLBACK and hasattr(self.client, 'credits'):
                credits = self.client.credits.get()
                rate_limits = self.client.calculate_rate_limits()
                
                base_stats.update({
                    "credits": {
                        "balance": credits.data.credits,
                        "usage": credits.data.usage
                    },
                    "rate_limits": rate_limits
                })
            else:
                base_stats.update({
                    "credits": {"note": "Using requests fallback - limited stats"},
                    "rate_limits": None
                })
            
            return base_stats
            
        except Exception as e:
            logger.error(f"Error getting usage stats: {e}")
            return {
                "service": "openrouter",
                "status": f"error: {e}",
                "cost_optimization": self.get_cost_summary()
            }


# Singleton instance with FREE-ONLY default
_openrouter_service = None

def get_openrouter_service(max_monthly_spend: float = 0.0, 
                          prefer_free_models: bool = True) -> OpenRouterService:
    """Get singleton OpenRouter service instance optimized for zero cost with TOP models.
    
    Args:
        max_monthly_spend: Maximum monthly spend (DEFAULT: 0.0 = FREE ONLY)
        prefer_free_models: Always try free models first (DEFAULT: True)
        
    Returns:
        OpenRouterService instance configured for cost optimization with most popular models
    """
    global _openrouter_service
    if _openrouter_service is None:
        _openrouter_service = OpenRouterService(
            max_monthly_spend=max_monthly_spend,
            prefer_free_models=prefer_free_models
        )
        logger.info(f"🎯 QAK OpenRouter Service initialized with TOP FREE models - Mode: {'FREE ONLY' if max_monthly_spend == 0.0 else 'BUDGET CONTROLLED'}")
    return _openrouter_service