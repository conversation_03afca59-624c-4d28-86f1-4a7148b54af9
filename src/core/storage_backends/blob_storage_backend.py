"""
Blob Storage Backends for QAK Artifact System

Extends the artifact storage system with cloud blob storage support.
Supports S3, Azure Blob Storage, Google Cloud Storage, and Cloudflare R2.
"""

import asyncio
import os
import hashlib
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


class BlobStorageBackend(ABC):
    """Abstract base class for blob storage backends"""
    
    def __init__(self, base_path: str = "artifacts"):
        self.base_path = base_path
        self.logger = logger
    
    @abstractmethod
    async def upload_artifact(
        self,
        file_path: str,
        content: bytes,
        content_type: str = "application/octet-stream",
        metadata: Optional[Dict[str, str]] = None
    ) -> Tuple[str, str]:  # Returns (blob_url, blob_key)
        """Upload artifact to blob storage"""
        pass
    
    @abstractmethod
    async def download_artifact(self, blob_key: str) -> bytes:
        """Download artifact from blob storage"""
        pass
    
    @abstractmethod
    async def delete_artifact(self, blob_key: str) -> bool:
        """Delete artifact from blob storage"""
        pass
    
    @abstractmethod
    async def get_artifact_metadata(self, blob_key: str) -> Optional[Dict[str, Any]]:
        """Get artifact metadata from blob storage"""
        pass
    
    @abstractmethod
    async def list_artifacts(self, prefix: str = "") -> List[Dict[str, Any]]:
        """List artifacts in storage with metadata"""
        pass
    
    def generate_blob_key(self, execution_id: str, artifact_type: str, filename: str) -> str:
        """Generate consistent blob key for artifact"""
        date_part = datetime.now().strftime('%Y/%m/%d')
        execution_part = execution_id[:8] if execution_id else "unknown"
        return f"{self.base_path}/{date_part}/{execution_part}/{artifact_type}/{filename}"


class CloudflareR2StorageBackend(BlobStorageBackend):
    """Cloudflare R2 storage backend for artifacts (S3-compatible)"""
    
    def __init__(self, base_path: str = "artifacts"):
        super().__init__(base_path)
        
        # R2 configuration from environment
        self.bucket_name = os.getenv('R2_BUCKET_NAME')
        self.account_id = os.getenv('R2_ACCOUNT_ID')
        self.access_key = os.getenv('R2_ACCESS_KEY_ID')
        self.secret_key = os.getenv('R2_SECRET_ACCESS_KEY')
        self.public_url = os.getenv('R2_PUBLIC_URL')  # Optional: for public URLs
        
        if not all([self.bucket_name, self.account_id, self.access_key, self.secret_key]):
            raise ValueError(
                "Missing R2 credentials. Set R2_BUCKET_NAME, R2_ACCOUNT_ID, "
                "R2_ACCESS_KEY_ID, R2_SECRET_ACCESS_KEY"
            )
        
        # R2 endpoint URL
        self.endpoint_url = f"https://{self.account_id}.r2.cloudflarestorage.com"
        
        self._s3_client = None
    
    @property
    async def s3_client(self):
        """Lazy initialization of S3-compatible client for R2"""
        if self._s3_client is None:
            try:
                import aioboto3
                session = aioboto3.Session()
                self._s3_client = await session.client(
                    's3',
                    endpoint_url=self.endpoint_url,
                    aws_access_key_id=self.access_key,
                    aws_secret_access_key=self.secret_key,
                    region_name='auto'  # R2 uses 'auto' as region
                ).__aenter__()
                logger.info(f"✅ Connected to Cloudflare R2 bucket: {self.bucket_name}")
            except ImportError:
                raise ImportError("aioboto3 required for R2 backend. Install with: pip install aioboto3")
        return self._s3_client
    
    async def upload_artifact(
        self,
        file_path: str,
        content: bytes,
        content_type: str = "application/octet-stream",
        metadata: Optional[Dict[str, str]] = None
    ) -> Tuple[str, str]:
        """Upload artifact to Cloudflare R2"""
        try:
            client = await self.s3_client
            blob_key = file_path if file_path.startswith(self.base_path) else self.generate_blob_key(
                metadata.get('execution_id', ''), 
                metadata.get('artifact_type', 'unknown'),
                Path(file_path).name
            )
            
            # Prepare metadata for R2
            r2_metadata = metadata or {}
            r2_metadata.update({
                'upload_timestamp': datetime.now().isoformat(),
                'content_hash': hashlib.md5(content).hexdigest(),
                'size_bytes': str(len(content)),
                'storage_backend': 'cloudflare_r2'
            })
            
            await client.put_object(
                Bucket=self.bucket_name,
                Key=blob_key,
                Body=content,
                ContentType=content_type,
                Metadata=r2_metadata
            )
            
            # Generate public URL if configured
            if self.public_url:
                blob_url = f"{self.public_url.rstrip('/')}/{blob_key}"
            else:
                blob_url = f"{self.endpoint_url}/{self.bucket_name}/{blob_key}"
            
            logger.info(f"📦 Uploaded to Cloudflare R2: {blob_key}")
            return blob_url, blob_key
            
        except Exception as e:
            logger.error(f"Failed to upload to Cloudflare R2: {e}")
            raise
    
    async def download_artifact(self, blob_key: str) -> bytes:
        """Download artifact from Cloudflare R2"""
        try:
            client = await self.s3_client
            response = await client.get_object(Bucket=self.bucket_name, Key=blob_key)
            content = await response['Body'].read()
            logger.debug(f"📥 Downloaded from Cloudflare R2: {blob_key}")
            return content
        except Exception as e:
            logger.error(f"Failed to download from Cloudflare R2: {e}")
            raise
    
    async def delete_artifact(self, blob_key: str) -> bool:
        """Delete artifact from Cloudflare R2"""
        try:
            client = await self.s3_client
            await client.delete_object(Bucket=self.bucket_name, Key=blob_key)
            logger.info(f"🗑️ Deleted from Cloudflare R2: {blob_key}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete from Cloudflare R2: {e}")
            return False
    
    async def get_artifact_metadata(self, blob_key: str) -> Optional[Dict[str, Any]]:
        """Get artifact metadata from Cloudflare R2"""
        try:
            client = await self.s3_client
            response = await client.head_object(Bucket=self.bucket_name, Key=blob_key)
            return {
                'size': response.get('ContentLength'),
                'last_modified': response.get('LastModified'),
                'content_type': response.get('ContentType'),
                'metadata': response.get('Metadata', {}),
                'etag': response.get('ETag')
            }
        except Exception as e:
            logger.error(f"Failed to get Cloudflare R2 metadata: {e}")
            return None
    
    async def list_artifacts(self, prefix: str = "") -> List[Dict[str, Any]]:
        """List artifacts in Cloudflare R2"""
        try:
            client = await self.s3_client
            search_prefix = f"{self.base_path}/{prefix}" if prefix else self.base_path
            
            response = await client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=search_prefix
            )
            
            artifacts = []
            for obj in response.get('Contents', []):
                artifacts.append({
                    'key': obj['Key'],
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'],
                    'etag': obj['ETag'],
                    'storage_class': obj.get('StorageClass', 'STANDARD')
                })
            
            return artifacts
        except Exception as e:
            logger.error(f"Failed to list Cloudflare R2 artifacts: {e}")
            return []
    
    async def get_presigned_url(self, blob_key: str, expires_in: int = 3600) -> str:
        """Generate presigned URL for direct access (R2 specific feature)"""
        try:
            client = await self.s3_client
            url = await client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': blob_key},
                ExpiresIn=expires_in
            )
            logger.debug(f"🔗 Generated presigned URL for: {blob_key}")
            return url
        except Exception as e:
            logger.error(f"Failed to generate presigned URL: {e}")
            raise

    async def shutdown(self):
        """Close the S3 client connection."""
        if self._s3_client:
            logger.info("🔌 Closing Cloudflare R2 client connection...")
            try:
                await self._s3_client.close()
                self._s3_client = None
                logger.info("✅ R2 client connection closed successfully.")
            except Exception as e:
                logger.error(f"❌ Error closing R2 client connection: {e}")


class S3StorageBackend(BlobStorageBackend):
    """AWS S3 storage backend for artifacts"""
    
    def __init__(self, base_path: str = "artifacts"):
        super().__init__(base_path)
        
        # S3 configuration from environment
        self.bucket_name = os.getenv('AWS_S3_BUCKET')
        self.region = os.getenv('AWS_DEFAULT_REGION', 'us-east-1')
        self.access_key = os.getenv('AWS_ACCESS_KEY_ID')
        self.secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        
        if not all([self.bucket_name, self.access_key, self.secret_key]):
            raise ValueError("Missing S3 credentials. Set AWS_S3_BUCKET, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY")
        
        self._s3_client = None
    
    @property
    async def s3_client(self):
        """Lazy initialization of S3 client"""
        if self._s3_client is None:
            try:
                import aioboto3
                session = aioboto3.Session()
                self._s3_client = await session.client(
                    's3',
                    region_name=self.region,
                    aws_access_key_id=self.access_key,
                    aws_secret_access_key=self.secret_key
                ).__aenter__()
                logger.info(f"✅ Connected to S3 bucket: {self.bucket_name}")
            except ImportError:
                raise ImportError("aioboto3 required for S3 backend. Install with: pip install aioboto3")
        return self._s3_client
    
    async def upload_artifact(
        self,
        file_path: str,
        content: bytes,
        content_type: str = "application/octet-stream",
        metadata: Optional[Dict[str, str]] = None
    ) -> Tuple[str, str]:
        """Upload artifact to S3"""
        try:
            client = await self.s3_client
            blob_key = file_path if file_path.startswith(self.base_path) else self.generate_blob_key(
                metadata.get('execution_id', ''), 
                metadata.get('artifact_type', 'unknown'),
                Path(file_path).name
            )
            
            # Prepare metadata for S3
            s3_metadata = metadata or {}
            s3_metadata.update({
                'upload_timestamp': datetime.now().isoformat(),
                'content_hash': hashlib.md5(content).hexdigest(),
                'size_bytes': str(len(content))
            })
            
            await client.put_object(
                Bucket=self.bucket_name,
                Key=blob_key,
                Body=content,
                ContentType=content_type,
                Metadata=s3_metadata
            )
            
            blob_url = f"https://{self.bucket_name}.s3.{self.region}.amazonaws.com/{blob_key}"
            logger.info(f"📦 Uploaded to S3: {blob_key}")
            return blob_url, blob_key
            
        except Exception as e:
            logger.error(f"Failed to upload to S3: {e}")
            raise
    
    async def download_artifact(self, blob_key: str) -> bytes:
        """Download artifact from S3"""
        try:
            client = await self.s3_client
            response = await client.get_object(Bucket=self.bucket_name, Key=blob_key)
            content = await response['Body'].read()
            logger.debug(f"📥 Downloaded from S3: {blob_key}")
            return content
        except Exception as e:
            logger.error(f"Failed to download from S3: {e}")
            raise
    
    async def delete_artifact(self, blob_key: str) -> bool:
        """Delete artifact from S3"""
        try:
            client = await self.s3_client
            await client.delete_object(Bucket=self.bucket_name, Key=blob_key)
            logger.info(f"🗑️ Deleted from S3: {blob_key}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete from S3: {e}")
            return False
    
    async def get_artifact_metadata(self, blob_key: str) -> Optional[Dict[str, Any]]:
        """Get artifact metadata from S3"""
        try:
            client = await self.s3_client
            response = await client.head_object(Bucket=self.bucket_name, Key=blob_key)
            return {
                'size': response.get('ContentLength'),
                'last_modified': response.get('LastModified'),
                'content_type': response.get('ContentType'),
                'metadata': response.get('Metadata', {})
            }
        except Exception as e:
            logger.error(f"Failed to get S3 metadata: {e}")
            return None
    
    async def list_artifacts(self, prefix: str = "") -> List[Dict[str, Any]]:
        """List artifacts in S3"""
        try:
            client = await self.s3_client
            search_prefix = f"{self.base_path}/{prefix}" if prefix else self.base_path
            
            response = await client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=search_prefix
            )
            
            artifacts = []
            for obj in response.get('Contents', []):
                artifacts.append({
                    'key': obj['Key'],
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'],
                    'etag': obj['ETag']
                })
            
            return artifacts
        except Exception as e:
            logger.error(f"Failed to list S3 artifacts: {e}")
            return []


class AzureBlobStorageBackend(BlobStorageBackend):
    """Azure Blob Storage backend for artifacts"""
    
    def __init__(self, base_path: str = "artifacts"):
        super().__init__(base_path)
        
        # Azure configuration from environment
        self.account_name = os.getenv('AZURE_STORAGE_ACCOUNT_NAME')
        self.account_key = os.getenv('AZURE_STORAGE_ACCOUNT_KEY')
        self.container_name = os.getenv('AZURE_STORAGE_CONTAINER', 'artifacts')
        
        if not all([self.account_name, self.account_key]):
            raise ValueError("Missing Azure credentials. Set AZURE_STORAGE_ACCOUNT_NAME, AZURE_STORAGE_ACCOUNT_KEY")
        
        self._blob_service_client = None
    
    @property
    async def blob_service_client(self):
        """Lazy initialization of Azure Blob client"""
        if self._blob_service_client is None:
            try:
                from azure.storage.blob.aio import BlobServiceClient
                account_url = f"https://{self.account_name}.blob.core.windows.net"
                self._blob_service_client = BlobServiceClient(
                    account_url=account_url,
                    credential=self.account_key
                )
                logger.info(f"✅ Connected to Azure Blob: {self.container_name}")
            except ImportError:
                raise ImportError("azure-storage-blob required for Azure backend. Install with: pip install azure-storage-blob")
        return self._blob_service_client
    
    async def upload_artifact(
        self,
        file_path: str,
        content: bytes,
        content_type: str = "application/octet-stream",
        metadata: Optional[Dict[str, str]] = None
    ) -> Tuple[str, str]:
        """Upload artifact to Azure Blob Storage"""
        try:
            client = await self.blob_service_client
            blob_key = file_path if file_path.startswith(self.base_path) else self.generate_blob_key(
                metadata.get('execution_id', ''), 
                metadata.get('artifact_type', 'unknown'),
                Path(file_path).name
            )
            
            # Prepare metadata for Azure
            azure_metadata = metadata or {}
            azure_metadata.update({
                'upload_timestamp': datetime.now().isoformat(),
                'content_hash': hashlib.md5(content).hexdigest(),
                'size_bytes': str(len(content))
            })
            
            blob_client = client.get_blob_client(
                container=self.container_name,
                blob=blob_key
            )
            
            await blob_client.upload_blob(
                data=content,
                content_type=content_type,
                metadata=azure_metadata,
                overwrite=True
            )
            
            blob_url = f"https://{self.account_name}.blob.core.windows.net/{self.container_name}/{blob_key}"
            logger.info(f"📦 Uploaded to Azure: {blob_key}")
            return blob_url, blob_key
            
        except Exception as e:
            logger.error(f"Failed to upload to Azure: {e}")
            raise
    
    async def download_artifact(self, blob_key: str) -> bytes:
        """Download artifact from Azure Blob Storage"""
        try:
            client = await self.blob_service_client
            blob_client = client.get_blob_client(
                container=self.container_name,
                blob=blob_key
            )
            content = await blob_client.download_blob()
            data = await content.readall()
            logger.debug(f"📥 Downloaded from Azure: {blob_key}")
            return data
        except Exception as e:
            logger.error(f"Failed to download from Azure: {e}")
            raise
    
    async def delete_artifact(self, blob_key: str) -> bool:
        """Delete artifact from Azure Blob Storage"""
        try:
            client = await self.blob_service_client
            blob_client = client.get_blob_client(
                container=self.container_name,
                blob=blob_key
            )
            await blob_client.delete_blob()
            logger.info(f"🗑️ Deleted from Azure: {blob_key}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete from Azure: {e}")
            return False
    
    async def get_artifact_metadata(self, blob_key: str) -> Optional[Dict[str, Any]]:
        """Get artifact metadata from Azure Blob Storage"""
        try:
            client = await self.blob_service_client
            blob_client = client.get_blob_client(
                container=self.container_name,
                blob=blob_key
            )
            properties = await blob_client.get_blob_properties()
            return {
                'size': properties.size,
                'last_modified': properties.last_modified,
                'content_type': properties.content_type,
                'metadata': properties.metadata
            }
        except Exception as e:
            logger.error(f"Failed to get Azure metadata: {e}")
            return None
    
    async def list_artifacts(self, prefix: str = "") -> List[Dict[str, Any]]:
        """List artifacts in Azure Blob Storage"""
        try:
            client = await self.blob_service_client
            container_client = client.get_container_client(self.container_name)
            search_prefix = f"{self.base_path}/{prefix}" if prefix else self.base_path
            
            artifacts = []
            async for blob in container_client.list_blobs(name_starts_with=search_prefix):
                artifacts.append({
                    'key': blob.name,
                    'size': blob.size,
                    'last_modified': blob.last_modified,
                    'content_type': blob.content_type
                })
            
            return artifacts
        except Exception as e:
            logger.error(f"Failed to list Azure artifacts: {e}")
            return []


class GCSStorageBackend(BlobStorageBackend):
    """Google Cloud Storage backend for artifacts"""
    
    def __init__(self, base_path: str = "artifacts"):
        super().__init__(base_path)
        
        # GCS configuration from environment
        self.bucket_name = os.getenv('GCS_BUCKET_NAME')
        self.project_id = os.getenv('GCP_PROJECT_ID')
        self.credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        
        if not self.bucket_name:
            raise ValueError("Missing GCS bucket name. Set GCS_BUCKET_NAME")
        
        self._storage_client = None
    
    @property
    async def storage_client(self):
        """Lazy initialization of GCS client"""
        if self._storage_client is None:
            try:
                from google.cloud import storage
                if self.credentials_path:
                    self._storage_client = storage.Client.from_service_account_json(self.credentials_path)
                else:
                    self._storage_client = storage.Client(project=self.project_id)
                logger.info(f"✅ Connected to GCS bucket: {self.bucket_name}")
            except ImportError:
                raise ImportError("google-cloud-storage required for GCS backend. Install with: pip install google-cloud-storage")
        return self._storage_client
    
    async def upload_artifact(
        self,
        file_path: str,
        content: bytes,
        content_type: str = "application/octet-stream",
        metadata: Optional[Dict[str, str]] = None
    ) -> Tuple[str, str]:
        """Upload artifact to Google Cloud Storage"""
        try:
            client = await self.storage_client
            bucket = client.bucket(self.bucket_name)
            
            blob_key = file_path if file_path.startswith(self.base_path) else self.generate_blob_key(
                metadata.get('execution_id', ''), 
                metadata.get('artifact_type', 'unknown'),
                Path(file_path).name
            )
            
            blob = bucket.blob(blob_key)
            blob.content_type = content_type
            
            # Set metadata
            if metadata:
                blob.metadata = metadata.copy()
                blob.metadata.update({
                    'upload_timestamp': datetime.now().isoformat(),
                    'content_hash': hashlib.md5(content).hexdigest(),
                    'size_bytes': str(len(content))
                })
            
            # Upload in a thread to avoid blocking
            await asyncio.get_event_loop().run_in_executor(
                None, blob.upload_from_string, content
            )
            
            blob_url = f"https://storage.googleapis.com/{self.bucket_name}/{blob_key}"
            logger.info(f"📦 Uploaded to GCS: {blob_key}")
            return blob_url, blob_key
            
        except Exception as e:
            logger.error(f"Failed to upload to GCS: {e}")
            raise
    
    async def download_artifact(self, blob_key: str) -> bytes:
        """Download artifact from Google Cloud Storage"""
        try:
            client = await self.storage_client
            bucket = client.bucket(self.bucket_name)
            blob = bucket.blob(blob_key)
            
            content = await asyncio.get_event_loop().run_in_executor(
                None, blob.download_as_bytes
            )
            logger.debug(f"📥 Downloaded from GCS: {blob_key}")
            return content
        except Exception as e:
            logger.error(f"Failed to download from GCS: {e}")
            raise
    
    async def delete_artifact(self, blob_key: str) -> bool:
        """Delete artifact from Google Cloud Storage"""
        try:
            client = await self.storage_client
            bucket = client.bucket(self.bucket_name)
            blob = bucket.blob(blob_key)
            
            await asyncio.get_event_loop().run_in_executor(None, blob.delete)
            logger.info(f"🗑️ Deleted from GCS: {blob_key}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete from GCS: {e}")
            return False
    
    async def get_artifact_metadata(self, blob_key: str) -> Optional[Dict[str, Any]]:
        """Get artifact metadata from Google Cloud Storage"""
        try:
            client = await self.storage_client
            bucket = client.bucket(self.bucket_name)
            blob = bucket.blob(blob_key)
            
            await asyncio.get_event_loop().run_in_executor(None, blob.reload)
            
            return {
                'size': blob.size,
                'last_modified': blob.updated,
                'content_type': blob.content_type,
                'metadata': blob.metadata or {}
            }
        except Exception as e:
            logger.error(f"Failed to get GCS metadata: {e}")
            return None
    
    async def list_artifacts(self, prefix: str = "") -> List[Dict[str, Any]]:
        """List artifacts in Google Cloud Storage"""
        try:
            client = await self.storage_client
            bucket = client.bucket(self.bucket_name)
            search_prefix = f"{self.base_path}/{prefix}" if prefix else self.base_path
            
            blobs = await asyncio.get_event_loop().run_in_executor(
                None, lambda: list(bucket.list_blobs(prefix=search_prefix))
            )
            
            artifacts = []
            for blob in blobs:
                artifacts.append({
                    'key': blob.name,
                    'size': blob.size,
                    'last_modified': blob.updated,
                    'content_type': blob.content_type
                })
            
            return artifacts
        except Exception as e:
            logger.error(f"Failed to list GCS artifacts: {e}")
            return []


# Factory function for creating blob storage backends
def create_blob_storage_backend(backend_type: str = "local", base_path: str = "artifacts") -> Optional[BlobStorageBackend]:
    """
    Factory function to create blob storage backend based on configuration.
    
    Args:
        backend_type: Type of backend ('r2', 's3', 'azure', 'gcs', 'local')
        base_path: Base path for artifact storage
    
    Returns:
        BlobStorageBackend instance or None for local storage
    """
    backend_type = backend_type.lower()
    
    try:
        if backend_type == 'r2' or backend_type == 'cloudflare':
            return CloudflareR2StorageBackend(base_path)
        elif backend_type == 's3':
            return S3StorageBackend(base_path)
        elif backend_type == 'azure':
            return AzureBlobStorageBackend(base_path)
        elif backend_type == 'gcs':
            return GCSStorageBackend(base_path)
        elif backend_type == 'local':
            logger.info("Using local filesystem for artifact storage")
            return None
        else:
            logger.warning(f"Unknown backend type '{backend_type}', falling back to local storage")
            return None
    except Exception as e:
        logger.error(f"Failed to initialize {backend_type} backend: {e}")
        logger.info("Falling back to local filesystem storage")
        return None 