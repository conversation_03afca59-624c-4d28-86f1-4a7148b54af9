"""
Markdown Prompt Loader for AgentQA
Loads prompts from Markdown files with caching and validation
"""

import os
import json
import re
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class MarkdownPromptLoader:
    """Loads and manages prompts from Markdown files."""
    
    # Language code to section name mapping
    LANGUAGE_SECTION_MAP = {
        "en": "English Prompt",
        "es": "Spanish Prompt"
    }
    
    def __init__(self, prompts_dir: str = "prompts"):
        """Initialize the prompt loader.
        
        Args:
            prompts_dir: Directory containing prompt markdown files
        """
        self.prompts_dir = Path(prompts_dir)
        self._cache: Dict[str, Dict] = {}
        self._metadata_cache: Dict[str, Dict] = {}
        
        if not self.prompts_dir.exists():
            raise FileNotFoundError(f"Prompts directory not found: {prompts_dir}")
    
    def load_prompt(self, category: str, prompt_id: str, language: str = "en") -> str:
        """Load a prompt from markdown file.
        
        Args:
            category: Prompt category (e.g., 'user-story', 'test-cases')
            prompt_id: Prompt identifier (e.g., 'enhance', 'generate')
            language: Language code ('en' or 'es')
            
        Returns:
            str: The prompt text with placeholders for variables
            
        Raises:
            FileNotFoundError: If prompt file doesn't exist
            ValueError: If prompt structure is invalid
        """
        cache_key = f"{category}:{prompt_id}:{language}"
        
        # Return from cache if available
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        # Load metadata to find the correct file
        metadata = self.load_metadata(category)
        prompt_info = None
        
        for prompt in metadata.get("prompts", []):
            if prompt["id"] == prompt_id:
                prompt_info = prompt
                break
        
        if not prompt_info:
            raise ValueError(f"Prompt '{prompt_id}' not found in category '{category}'")
        
        # Check if language is supported
        if language not in prompt_info.get("languages", ["en"]):
            logger.warning(f"Language '{language}' not supported for {category}:{prompt_id}, falling back to English")
            language = "en"
        
        # Load the markdown file
        file_path = self.prompts_dir / category / prompt_info["file"]
        if not file_path.exists():
            raise FileNotFoundError(f"Prompt file not found: {file_path}")
        
        # Parse the markdown file
        from src.core.prompt_markdown_parser import PromptMarkdownParser
        parser = PromptMarkdownParser()
        prompt_data = parser.parse_prompt_file(str(file_path))
        
        # Get the prompt in the requested language
        section_key = self.LANGUAGE_SECTION_MAP.get(language, f"{language.title()} Prompt")
        if section_key not in prompt_data:
            section_key = self.LANGUAGE_SECTION_MAP["en"]  # Fallback to English
        
        if section_key not in prompt_data:
            raise ValueError(f"No prompt found for language '{language}' in {file_path}")
        
        prompt_text = prompt_data[section_key].strip()
        
        # Cache the result
        self._cache[cache_key] = prompt_text
        
        return prompt_text
    
    def load_metadata(self, category: str) -> Dict[str, Any]:
        """Load metadata for a prompt category.
        
        Args:
            category: Prompt category name
            
        Returns:
            Dict containing metadata information
        """
        if category in self._metadata_cache:
            return self._metadata_cache[category]
        
        metadata_file = self.prompts_dir / category / "metadata.json"
        if not metadata_file.exists():
            raise FileNotFoundError(f"Metadata file not found: {metadata_file}")
        
        with open(metadata_file, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        self._metadata_cache[category] = metadata
        return metadata
    
    def get_available_prompts(self) -> Dict[str, List[str]]:
        """Get all available prompts organized by category.
        
        Returns:
            Dict mapping category names to lists of prompt IDs
        """
        available_prompts = {}
        
        for category_dir in self.prompts_dir.iterdir():
            if category_dir.is_dir() and category_dir.name != "shared":
                try:
                    metadata = self.load_metadata(category_dir.name)
                    prompt_ids = [p["id"] for p in metadata.get("prompts", [])]
                    available_prompts[category_dir.name] = prompt_ids
                except Exception as e:
                    logger.warning(f"Could not load metadata for category {category_dir.name}: {e}")
        
        return available_prompts
    
    def validate_prompt(self, category: str, prompt_id: str) -> bool:
        """Validate that a prompt exists and is properly structured.
        
        Args:
            category: Prompt category
            prompt_id: Prompt identifier
            
        Returns:
            bool: True if prompt is valid, False otherwise
        """
        try:
            # Try to load the prompt in both languages
            self.load_prompt(category, prompt_id, "en")
            self.load_prompt(category, prompt_id, "es")
            return True
        except Exception as e:
            logger.error(f"Validation failed for {category}:{prompt_id}: {e}")
            return False
    
    def clear_cache(self) -> None:
        """Clear the prompt cache to force reload on next access."""
        self._cache.clear()
        self._metadata_cache.clear()
    
    def get_prompt_variables(self, category: str, prompt_id: str) -> List[str]:
        """Extract variable names from a prompt.
        
        Args:
            category: Prompt category
            prompt_id: Prompt identifier
            
        Returns:
            List of variable names found in the prompt
        """
        try:
            prompt_text = self.load_prompt(category, prompt_id, "en")
            from src.core.prompt_markdown_parser import PromptMarkdownParser
            parser = PromptMarkdownParser()
            return parser.extract_variables(prompt_text)
        except Exception as e:
            logger.error(f"Could not extract variables from {category}:{prompt_id}: {e}")
            return []
