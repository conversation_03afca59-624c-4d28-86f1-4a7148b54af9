import base64
"""
Result Transformer for QAK Test Execution System

Transforms multiple result formats (Raw → Analyzed → Frontend → JSON-safe)
into a single, standardized format that handles all result types efficiently.
"""

from typing import Dict, Any, List, Optional, Union, Type
import json
import logging
from datetime import datetime

# Import the unified models
from src.models.standard_result import (
    StandardResult, TestStep, TestSummary, Artifacts,
    ExecutionStatus, TestType, create_success_result, create_error_result
)

# Import response transformers for R2-only artifact support
from src.utilities.response_transformers import convert_screenshot_paths_to_urls

logger = logging.getLogger(__name__)


class ResultTransformer:
    """
    Transforms test results from various formats into StandardResult format.
    
    Eliminates the need for multiple transformation layers by providing
    direct conversion from raw execution data to StandardResult format.
    """
    
    def __init__(self):
        """Initialize the result transformer."""
        self.processors = {
            "browser_history": self._process_browser_history,
            "suite_execution": self._process_suite_execution,
            # Legacy processors for potential backward compatibility
            "test_execution": self._process_test_execution,
            "codegen_execution": self._process_codegen_execution,
        }
    
    async def process(self, raw_result: Any, result_type: str, **context) -> StandardResult:
        """
        Main processing method that converts raw results to StandardResult.
        
        Args:
            raw_result: Raw result data from execution
            result_type: Type of result to process
            **context: Additional context (test_id, suite_id, config, etc.)
            
        Returns:
            StandardResult: Unified result format
            
        Raises:
            ValueError: If result_type is not supported
        """
        # Forcing the use of browser_history for all agent-based tests.
        # The suite logic is different as it orchestrates multiple runs.
        if result_type not in ["suite_execution", "codegen_execution"]:
            # browser_history processor is now async
            result = await self._process_browser_history(raw_result, **context)
        elif result_type in self.processors:
            processor = self.processors[result_type]
            result = processor(raw_result, **context)
        else:
            raise ValueError(f"Unsupported result type: {result_type}")
        
        # Ensure result is properly completed and summary is calculated
        if result.status == ExecutionStatus.RUNNING:
            result.complete_execution(
                ExecutionStatus.SUCCESS if result.success else ExecutionStatus.FAILURE
            )
        else:
            # Even if not running, ensure summary is calculated
            result.summary.total_steps = len(result.steps)
            result.summary.successful_steps = sum(1 for step in result.steps if step.success)
            result.summary.failed_steps = result.summary.total_steps - result.summary.successful_steps
            result.summary.calculate_success_rate()
        
        logger.info(f"Processed {result_type} result: {result.execution_id}")
        return result
    
    async def _process_browser_history(self, raw_result: Any, **context) -> StandardResult:
        """
        Process browser-use library history data.
        
        Args:
            raw_result: Browser history object
            **context: Additional context
            
        Returns:
            StandardResult: Processed result
        """
        test_type = TestType(context.get("test_type", "smoke"))
        result = StandardResult(
            test_type=test_type,
            test_id=context.get("test_id") or context.get("execution_id"),  # Prefer test_id from context
            execution_id=context.get("execution_id"),
            suite_id=context.get("suite_id"),
            project_id=context.get("project_id"),
            status=ExecutionStatus.RUNNING,
            configuration=context.get("configuration", {})
        )
        
        # PRESERVE CONTEXT METADATA: Add all relevant metadata from execution context
        # This ensures important fields like test_id, test_case_id, etc. are preserved
        context_metadata = {}
        for key in ["test_id", "test_case_id", "project_id", "suite_id", "test_name", 
                   "test_description", "test_instructions", "gherkin_scenario", "url",
                   "environment_id", "environment_name", "full_url", "environment_config", 
                   "application_version"]:
            if key in context and context[key]:
                context_metadata[key] = context[key]
        
        if context_metadata:
            result.metadata = context_metadata
          
        # The raw_result is the history object from browser-use
        history = raw_result
        
        # Track all steps found from different sources
        step_counter = 0

        # Process history steps - NEW APPROACH: Extract from AgentHistoryList
        if hasattr(history, 'history') and isinstance(history.history, list):
            logger.info(f"Processing {len(history.history)} history items from history.history")
            for i, agent_history in enumerate(history.history):
                logger.info(f"[STEP EXTRACT] Processing step {i+1}: type={type(agent_history)}")
                step_counter += 1
                step = self._extract_step_from_agent_history(agent_history, step_counter)
                if step:
                    result.add_step(step)
                    logger.info(f"[STEP EXTRACT] Successfully processed step {i+1}: action_type={step.action_type}, success={step.success}")
                else:
                    logger.warning(f"[STEP EXTRACT] Step {i+1} returned None")
                import traceback
                logger.error(f"[STEP EXTRACT] Traceback: {traceback.format_exc()}")
                logger.debug(f"Processed history item {i+1}: action_type={step.action_type if step else 'None'}, success={step.success if step else 'None'}")
        # Fallback to old model_actions method if no history attribute
        elif hasattr(history, 'model_actions') and history.model_actions():
            logger.info("Falling back to model_actions approach")
            for i, action in enumerate(history.model_actions()):
                step_counter += 1
                step = self._extract_step_from_model_action(action, step_counter)
                result.add_step(step)
        
        # Check if we have additional history data outside of the main history list
        # Sometimes the browser-use library stores additional info elsewhere
        total_expected_steps = step_counter
        if hasattr(history, '__dict__'):
            for attr_name, attr_value in history.__dict__.items():
                if 'step' in attr_name.lower() or 'action' in attr_name.lower():
                    logger.debug(f"Found potential history attribute: {attr_name} = {type(attr_value)}")
        
        # Alternative approach: Check if the history object has step tracking
        if hasattr(history, 'step_count'):
            total_expected_steps = history.step_count
            logger.info(f"History indicates {total_expected_steps} total steps, but we processed {len(result.steps)}")
        
        # ADDITIONAL CHECK: Look for done action in the last history item or final action
        # Sometimes the done action might be stored separately
        if hasattr(history, 'history') and isinstance(history.history, list) and history.history:
            last_history_item = history.history[-1]
            # Check if the last item contains a done action that wasn't captured
            if hasattr(last_history_item, 'model_output') and last_history_item.model_output:
                model_output = last_history_item.model_output
                if hasattr(model_output, 'actions') and model_output.actions:
                    for action in model_output.actions:
                        if hasattr(action, 'type') and action.type == "done":
                            # Create a step for the done action if not already captured
                            if not any(step.action_type == "done" for step in result.steps):
                                step_counter += 1
                                done_step = self._extract_step_from_agent_history(last_history_item, step_counter)
                                result.add_step(done_step)
                                logger.info(f"Added missing done action as step {step_counter}")
                            break
        
        # ANOTHER CHECK: Look for done action in final model actions
        if hasattr(history, 'model_actions') and callable(history.model_actions):
            final_actions = history.model_actions()
            if final_actions:
                logger.info(f"Checking {len(final_actions)} model actions for done action")
                for action in final_actions:
                    if hasattr(action, 'action_type') and action.action_type == "done":
                        if not any(step.action_type == "done" for step in result.steps):
                            step_counter += 1
                            step = self._extract_step_from_model_action(action, step_counter)
                            result.add_step(step)
                            logger.info(f"Added done action from model_actions as step {step_counter}")
                        break
        
        # Final check: Look for any indication of success in the history object itself
        if hasattr(history, 'status') and history.status:
            logger.info(f"History status: {history.status}")
        if hasattr(history, 'completed') and history.completed:
            logger.info(f"History completed: {history.completed}")
        if hasattr(history, 'success') and history.success is not None:
            logger.info(f"History success: {history.success}")
        
        logger.info(f"Final step count: {len(result.steps)} steps processed")
        
        # Extract screenshots directly from the history object first
        if hasattr(history, 'artifact_collector') and history.artifact_collector:
            logger.info("Extracting screenshots from artifact_collector in history...")
            screenshots = history.artifact_collector.get_screenshots()
            for screenshot_path in screenshots:
                result.add_artifact("screenshot", screenshot_path)
            logger.info(f"Added {len(screenshots)} screenshots from artifact_collector.")
        else:
            # Fallback to older method if artifact_collector is not available
            await self._extract_screenshots_from_history(history, result)
        
        # Extract URLs and elements from agent history
        self._extract_rich_data_from_history(history, result)

        # Extract final result message
        if hasattr(history, 'final_result') and history.final_result():
            result.message = history.final_result()

        # The code below is now mostly redundant if the above works, but we keep it as a fallback.
        # Extract screenshots from artifacts already collected in the context
        artifacts_from_context = context.get('artifacts', [])
        screenshot_artifacts = []
        logger.debug(f"Processing {len(artifacts_from_context)} artifacts from context as a fallback.")
        if artifacts_from_context:
            # Get paths already added from the collector to avoid duplicates
            existing_paths = {artifact['path'] for artifact in result.artifacts.screenshots}
            
            for artifact in artifacts_from_context:
                file_path = getattr(artifact, 'file_path', None)
                if file_path and file_path not in existing_paths:
                    if hasattr(artifact, 'type') and (
                        str(getattr(artifact.type, 'value', artifact.type)) == "screenshot"
                    ):
                        screenshot_artifacts.append(artifact)
                        result.add_artifact("screenshot", file_path)
                        logger.info(f"Added fallback screenshot artifact from context: {file_path}")
        
        # Update step screenshot URLs with actual artifact paths
        self._update_step_screenshots_with_artifacts(result, screenshot_artifacts)
        
        # Extract errors if available, but filter out "None" errors and LLM parsing errors that don't affect execution
        if hasattr(history, 'errors') and history.errors():
            for error in history.errors():
                error_str = str(error)
                # Skip "None" errors, LLM parsing errors, and other non-critical errors
                if (error_str and error_str.strip() and error_str.strip() != "None" and
                    "Could not parse response" not in error_str and
                    "MALFORMED_FUNCTION_CALL" not in error_str and
                    "Failed to parse model output" not in error_str and
                    "Failed to extract step info: cannot access local variable 'model_output'" not in error_str):
                    result.add_error(error_str)
        
        # Determine success - Check for done action with success=True first
        success = False
        done_action_success = None
        
        # First, check if the history object indicates overall success
        agent_completed_successfully = False
        if hasattr(history, 'final_result') and history.final_result():
            final_result_str = str(history.final_result()).lower()
            if "successfully" in final_result_str or "completed" in final_result_str:
                agent_completed_successfully = True
                logger.info(f"Agent completed successfully based on final_result: {history.final_result()}")
        
        # Also check if the history object has any completion status
        if hasattr(history, 'is_completed') and callable(history.is_completed):
            if history.is_completed():
                agent_completed_successfully = True
                logger.info("Agent completed successfully based on is_completed() method")
        
        # CAPTURE RAW RESULT DATA for frontend processing
        if hasattr(history, 'history') and history.history:
            # Extract raw results from each step
            raw_results = []
            for step_history in history.history:
                if hasattr(step_history, 'result') and step_history.result:
                    for result_item in step_history.result:
                        # Format result item in a human-readable way
                        formatted_result = self._format_step_result(result_item)
                        raw_results.append(formatted_result)
            
            if raw_results:
                result.raw_result = raw_results
                logger.info(f"Captured {len(raw_results)} raw result items for frontend processing")

        # Look for done action in the steps to check its success flag
        last_step_success = None
        last_successful_step = None
        
        if result.steps:
            logger.info(f"Processing {len(result.steps)} steps to find done action")
            
            for i, step in enumerate(result.steps):
                logger.debug(f"Step {i+1} action_type: {step.action_type}, success: {step.success}")
                
                # Track the last step and last successful step
                if i == len(result.steps) - 1:  # Last step
                    last_step_success = step.success
                    logger.info(f"Last step (#{i+1}) success: {last_step_success}, action_type: {step.action_type}")
                
                if step.success:
                    last_successful_step = step
                
                # Check if this is a done action by action_type
                if step.action_type == "done":
                    done_action_success = step.success
                    logger.info(f"Found done action in step {i+1} with success: {done_action_success}")
                    break
                
                # Also check metadata for additional done action info
                if step.metadata and isinstance(step.metadata, dict):
                    logger.debug(f"Step {i+1} metadata keys: {list(step.metadata.keys())}")
                    
                    # Check if there are multiple actions (actions array format)
                    if "actions" in step.metadata:
                        actions = step.metadata["actions"]
                        if isinstance(actions, list):
                            for action_data in actions:
                                action_type = action_data.get("type", "").lower()
                                if action_type == "done":
                                    # Look for success in the action data
                                    action_success = action_data.get("success")
                                    if action_success is not None:
                                        done_action_success = action_success
                                        logger.info(f"Found done action in actions array with success: {done_action_success}")
                                        break
                            if done_action_success is not None:
                                break
                    
                    # Check primary_action for done action
                    if "primary_action" in step.metadata:
                        primary_action = step.metadata["primary_action"]
                        if isinstance(primary_action, dict):
                            action_type = primary_action.get("type", "").lower()
                            if action_type == "done":
                                action_success = primary_action.get("success")
                                if action_success is not None:
                                    done_action_success = action_success
                                    logger.info(f"Found done action in primary_action with success: {done_action_success}")
                                    break
        
            # New: Check for success indicators in the raw_result formatting
            if last_successful_step and result.raw_result:
                for raw_item in result.raw_result:
                    if isinstance(raw_item, str):
                        raw_lower = raw_item.lower()
                        if any(phrase in raw_lower for phrase in ["task completed", "login successful", "test completed", "successfully"]):
                            logger.info(f"Found success indicator in formatted raw result: {raw_item[:100]}...")
                            # If we found success indicators and the last step was successful, override
                            if last_step_success is True:
                                done_action_success = True
                                logger.info("SUCCESS INDICATOR: Last step successful with completion message - marking as successful")
                                break
        
        # Enhanced success determination logic
        # Priority 1: Explicit done action with success flag (most important)
        if done_action_success is not None:
            success = done_action_success
            logger.info(f"SUCCESS DETERMINATION: Using done action success: {success}")
            
            # If done action is successful, the overall test is successful regardless of intermediate step failures
            if success:
                logger.info("SUCCESS DETERMINATION: Done action successful - overriding any intermediate step failures")
            
        # Priority 2: Last step success (if no explicit done action found)
        elif last_step_success is True:
            success = True
            logger.info("SUCCESS DETERMINATION: Last step was successful - considering test successful")
            
        # Priority 3: Agent completion status (if available)
        elif agent_completed_successfully:
            success = True
            logger.info(f"SUCCESS DETERMINATION: Using agent completion status: {success}")
        # Priority 4: Check for "Task completed successfully" in final result or logs
        elif hasattr(history, 'final_result') and history.final_result():
            final_result_text = str(history.final_result())
            if "task completed successfully" in final_result_text.lower() or "successfully" in final_result_text.lower():
                success = True
                logger.info(f"SUCCESS DETERMINATION: Found success indicator in final_result: {final_result_text}")
            else:
                success = False
                logger.info(f"SUCCESS DETERMINATION: No success indicator in final_result: {final_result_text}")
        # Priority 5: More lenient fallback logic
        else:
            # Filter out non-critical errors for success determination
            critical_errors = []
            for error in result.errors:
                error_str = str(error).lower()
                # Skip validation warnings, LLM parsing errors, and other non-critical errors
                if not any(skip_phrase in error_str for skip_phrase in [
                    "verification failed", "post-action verification", 
                    "none", "warning", "deprecated", "could not parse response",
                    "malformed_function_call", "failed to parse model output",
                    "failed to extract step info: cannot access local variable 'model_output'"
                ]):
                    critical_errors.append(error)
            
            # Be more lenient - if there are no critical errors, consider it successful
            if len(critical_errors) == 0:
                success = True
                logger.info(f"SUCCESS DETERMINATION: No critical errors found - test successful")
            elif result.steps:
                # Allow test to succeed if the majority of steps passed and there's no critical error
                successful_steps = [s.success for s in result.steps if s.success is not None]
                if successful_steps:
                    success_rate = sum(successful_steps) / len(successful_steps)
                    # More lenient threshold: 40% success rate is acceptable if no critical errors
                    success = success_rate >= 0.4
                    logger.info(f"SUCCESS DETERMINATION: Using step success rate: {success_rate:.2%} - {'Success' if success else 'Failure'}")
                else:
                    success = False
                    logger.info("SUCCESS DETERMINATION: No step success data available - defaulting to failure")
            else:
                success = False
                logger.info("SUCCESS DETERMINATION: Critical errors found - test failed")
            
            logger.info(f"SUCCESS DETERMINATION: Fallback logic - success: {success}, critical_errors: {len(critical_errors)}")

        # Final decision logging
        logger.info(f"FINAL SUCCESS DETERMINATION: success={success}, done_action_success={done_action_success}, last_step_success={last_step_success}, agent_completed_successfully={agent_completed_successfully}")

        # Add iteration information if available
        execution_times = context.get("execution_times", 1)
        current_iteration = context.get("current_iteration", 1)
        
        # Add iteration metadata while preserving existing metadata
        if not hasattr(result, 'metadata') or result.metadata is None:
            result.metadata = {}
        
        # Preserve existing metadata and add iteration info
        result.metadata.update({
            "execution_times": execution_times,
            "current_iteration": current_iteration,
            "is_multi_iteration": execution_times > 1
        })
        
      
        # ENHANCED: LLM-based result validation
        # Apply intelligent validation to improve accuracy of success determination
        llm_validation = self._apply_llm_validation(result, success, context)
        if llm_validation:
            # Update success status based on LLM analysis
            validated_success = llm_validation.get("validated_success", success)
            logger.info(f"LLM VALIDATION: Original success={success}, LLM validated success={validated_success}")
            
            # Add validation metadata to result while preserving existing metadata
            if not hasattr(result, 'metadata') or result.metadata is None:
                result.metadata = {}
            result.metadata.update({
                "llm_validation": llm_validation,
                "original_success_determination": success
            })
            
          
            # Use LLM validation if confidence is high enough
            validation_confidence = llm_validation.get("validation_confidence", 0.0)
            if validation_confidence >= 0.95:  # Increased threshold to be more conservative
                success = validated_success
                logger.info(f"Applied LLM validation with confidence {validation_confidence:.2f}")
            else:
                logger.info(f"LLM validation confidence too low ({validation_confidence:.2f}), keeping original determination")

        result.status = ExecutionStatus.SUCCESS if success else ExecutionStatus.FAILURE
        result.success = success
        
        # 🤖 AI-POWERED ANALYSIS: Perform intelligent analysis of the test execution
        try:
            logger.info("🤖 Starting AI-powered test analysis...")
            
            # Extract screenshot data for AI analysis (needed for both sync and async analysis)
            screenshot_data = []
            
            # PRIORITY 1: Try to extract screenshots directly from history object BEFORE checking artifacts
            logger.info(f"🔍 PRIORITY EXTRACTION: Attempting to extract screenshots directly from history")
            if hasattr(history, 'artifact_collector') and history.artifact_collector:
                try:
                    collector_screenshots = history.artifact_collector.get_screenshots()
                    logger.info(f"🔍 COLLECTOR: Found {len(collector_screenshots)} screenshots in collector")
                    
                    # Read screenshots directly from collector paths
                    import base64
                    for i, screenshot_path in enumerate(collector_screenshots):
                        try:
                            logger.info(f"🔍 COLLECTOR: Reading screenshot {i+1}: {screenshot_path}")
                            with open(screenshot_path, 'rb') as f:
                                screenshot_bytes = f.read()
                                original_size = len(screenshot_bytes)
                                
                                # 💰 COST OPTIMIZATION: Compress collector screenshots too
                                if original_size > 200000:  # > 200KB
                                    try:
                                        from PIL import Image
                                        import io
                                        
                                        image = Image.open(io.BytesIO(screenshot_bytes))
                                        max_size = (800, 600)
                                        if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
                                            image.thumbnail(max_size, Image.Resampling.LANCZOS)
                                        
                                        output = io.BytesIO()
                                        if image.mode == 'RGBA':
                                            rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                                            rgb_image.paste(image, mask=image.split()[-1])
                                            image = rgb_image
                                        
                                        image.save(output, format='JPEG', quality=70, optimize=True)
                                        compressed_bytes = output.getvalue()
                                        screenshot_base64 = base64.b64encode(compressed_bytes).decode('utf-8')
                                        
                                        compression_ratio = len(compressed_bytes) / original_size
                                        logger.info(f"💰 COLLECTOR: Compressed screenshot {i+1}: {original_size:,} → {len(compressed_bytes):,} bytes ({compression_ratio:.1%})")
                                    except Exception as compress_error:
                                        logger.warning(f"⚠️ COLLECTOR: Compression failed, using original: {compress_error}")
                                        screenshot_base64 = base64.b64encode(screenshot_bytes).decode('utf-8')
                                else:
                                    screenshot_base64 = base64.b64encode(screenshot_bytes).decode('utf-8')
                                
                                screenshot_data.append(screenshot_base64)
                                logger.info(f"✅ COLLECTOR: Successfully read screenshot {i+1} ({len(screenshot_base64)} chars)")
                        except Exception as e:
                            logger.warning(f"⚠️ COLLECTOR: Could not read screenshot {i+1} from {screenshot_path}: {e}")
                    
                    if screenshot_data:
                        logger.info(f"🖼️ COLLECTOR: Successfully extracted {len(screenshot_data)} screenshots for AI analysis")
                except Exception as e:
                    logger.warning(f"⚠️ COLLECTOR: Error extracting from artifact_collector: {e}")
            
            # Continue with additional screenshot extraction methods...
            # (Keep the rest of the screenshot extraction logic here)
            
            # PRIORITY 2: Try to extract from history.history (state.screenshot)
            if not screenshot_data and hasattr(history, 'history') and isinstance(history.history, list):
                logger.info(f"🔍 HISTORY: Attempting to extract screenshots from history.history")
                import base64
                for i, agent_history in enumerate(history.history):
                    try:
                        if hasattr(agent_history, 'state') and agent_history.state:
                            state = agent_history.state
                            if hasattr(state, 'screenshot') and state.screenshot:
                                screenshot_b64 = state.screenshot
                                # Remove data URL prefix if present
                                if screenshot_b64.startswith('data:image/'):
                                    screenshot_b64 = screenshot_b64.split(',', 1)[1]
                                
                                # Validate and optimize base64
                                try:
                                    # Decode to validate
                                    screenshot_bytes = base64.b64decode(screenshot_b64)
                                    original_size = len(screenshot_bytes)
                                    
                                    # 💰 COST OPTIMIZATION: Compress large screenshots
                                    if original_size > 200000:  # > 200KB
                                        try:
                                            from PIL import Image
                                            import io
                                            
                                            # Open image
                                            image = Image.open(io.BytesIO(screenshot_bytes))
                                            
                                            # Resize if too large (maintain aspect ratio)
                                            max_size = (800, 600)  # Reduce resolution for AI analysis
                                            if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
                                                image.thumbnail(max_size, Image.Resampling.LANCZOS)
                                                logger.info(f"📐 Resized screenshot {i+1} from {image.size} for cost optimization")
                                            
                                            # Compress to JPEG with reasonable quality
                                            output = io.BytesIO()
                                            if image.mode == 'RGBA':
                                                # Convert RGBA to RGB for JPEG
                                                rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                                                rgb_image.paste(image, mask=image.split()[-1])
                                                image = rgb_image
                                            
                                            image.save(output, format='JPEG', quality=70, optimize=True)
                                            compressed_bytes = output.getvalue()
                                            compressed_b64 = base64.b64encode(compressed_bytes).decode('utf-8')
                                            
                                            compression_ratio = len(compressed_bytes) / original_size
                                            logger.info(f"💰 Compressed screenshot {i+1}: {original_size:,} → {len(compressed_bytes):,} bytes ({compression_ratio:.1%})")
                                            
                                            screenshot_data.append(compressed_b64)
                                        except Exception as compress_error:
                                            logger.warning(f"⚠️ Compression failed for screenshot {i+1}, using original: {compress_error}")
                                            screenshot_data.append(screenshot_b64)
                                    else:
                                        screenshot_data.append(screenshot_b64)
                                    
                                    logger.info(f"✅ HISTORY: Extracted screenshot {i+1} from state ({len(screenshot_data[-1])} chars)")
                                except Exception as e:
                                    logger.warning(f"⚠️ HISTORY: Invalid base64 screenshot {i+1}: {e}")
                    except Exception as e:
                        logger.warning(f"⚠️ HISTORY: Error extracting screenshot {i+1}: {e}")
                
                if screenshot_data:
                    logger.info(f"🖼️ HISTORY: Successfully extracted {len(screenshot_data)} screenshots for AI analysis")
            
            # Debug: Check what artifacts we have (for debugging purposes)
            logger.info(f"🔍 ARTIFACTS DEBUG: result.artifacts = {result.artifacts}")
            if result.artifacts:
                logger.info(f"🔍 ARTIFACTS DEBUG: artifacts.screenshots = {result.artifacts.screenshots}")
                if hasattr(result.artifacts, '__dict__'):
                    logger.info(f"🔍 ARTIFACTS DEBUG: artifacts.__dict__ = {result.artifacts.__dict__}")
            else:
                logger.warning(f"🔍 ARTIFACTS DEBUG: No artifacts found in result")
            
            # Use extracted screenshots for both sync and async analysis
            logger.info(f"📸 AI Analysis: Prepared {len(screenshot_data)} screenshots for analysis")
            
            # Check if background jobs are available for async processing
            from src.core.background_jobs import BACKGROUND_JOBS_AVAILABLE
            import os
            
            background_jobs_env = os.getenv("USE_BACKGROUND_JOBS", "true").lower()
            use_background_jobs = (
                BACKGROUND_JOBS_AVAILABLE and 
                background_jobs_env in ["true", "1", "yes"]
            )
            
            logger.info(f"🔍 Background jobs check: BACKGROUND_JOBS_AVAILABLE={BACKGROUND_JOBS_AVAILABLE}, USE_BACKGROUND_JOBS={background_jobs_env}, use_background_jobs={use_background_jobs}")
            
            if use_background_jobs:
                logger.info("🔄 Background jobs available - will process AI analysis asynchronously")
                try:
                    from src.core.background_jobs.tasks.analysis_tasks import analyze_test_background
                    from src.core.background_jobs.job_manager import get_job_manager
                    
                    # Get execution ID for the job
                    execution_id = result.execution_id or result.metadata.get("test_id", "unknown")
                    job_manager = get_job_manager()
                    
                    # Create job entry and schedule analysis task
                    job_id = job_manager.create_job(execution_id, "ai_analysis")
                    analyze_test_background.delay(job_id, result.to_dict(), screenshot_data)
                    
                    # Store job_id in metadata for future retrieval
                    result.metadata["ai_analysis_job_id"] = job_id
                    result.metadata["ai_analysis_status"] = "processing"
                    
                    logger.info(f"🚀 AI analysis job created: {job_id}")
                    
                    # Skip synchronous analysis since it will be done in background
                    return result
                    
                except Exception as bg_error:
                    logger.error(f"⚠️ Background job creation failed, falling back to synchronous analysis: {bg_error}")
                    import traceback
                    logger.error(f"🔍 Background job error traceback: {traceback.format_exc()}")
                    # Fall through to synchronous analysis
            
            # Synchronous analysis (fallback or when background jobs not available)
            logger.info("🔄 Processing AI analysis synchronously...")
            from src.services.test_analysis_service import get_test_analysis_service
            analysis_service = get_test_analysis_service()
            
            # Screenshots were already extracted above - use them directly
            logger.info(f"� Using {len(screenshot_data)} pre-extracted screenshots for synchronous analysis")
            
            # Perform full AI analysis with screenshots
            ai_analysis = await analysis_service.analyze_full_test(result, screenshot_data)
            
            # Add AI analysis to metadata
            result.metadata["ai_analysis"] = ai_analysis
            
            # Update success determination based on AI analysis if confidence is high
            ai_verdict = ai_analysis.get("completion_analysis", {}).get("final_verdict")
            ai_confidence = ai_analysis.get("completion_analysis", {}).get("confidence_level", 0.0)
            
            if ai_confidence >= 0.9 and ai_verdict in ["PASS", "FAIL"]:
                ai_success = ai_verdict == "PASS"
                if ai_success != success:
                    logger.warning(f"🤖 AI analysis differs from basic determination: AI={ai_verdict}, Basic={'PASS' if success else 'FAIL'}")
                    # Use AI determination if confidence is very high
                    if ai_confidence >= 0.95:
                        result.success = ai_success
                        result.status = ExecutionStatus.SUCCESS if ai_success else ExecutionStatus.FAILURE
                        logger.info(f"🤖 Applied AI verdict with {ai_confidence:.1%} confidence: {ai_verdict}")
                else:
                    logger.info(f"🤖 AI analysis confirms basic determination: {ai_verdict} ({ai_confidence:.1%} confidence)")
            
            logger.info(f"✅ AI analysis completed: {ai_analysis.get('summary', {}).get('final_verdict', 'UNKNOWN')}")
            
        except Exception as e:
            logger.warning(f"🤖 AI analysis failed, continuing with basic determination: {e}")
            # Continue with the original determination if AI analysis fails
        
        return result
    
    def _process_test_execution(self, raw_result: Dict[str, Any], **context) -> StandardResult:
        """
        Process legacy TestExecutionResponse format.
        
        Args:
            raw_result: Legacy test execution response
            **context: Additional context
            
        Returns:
            StandardResult: Processed result
        """
        test_type = TestType(context.get("test_type", "case"))
        
        # Extract basic fields
        success = raw_result.get("success", False)
        test_id = raw_result.get("test_id") or context.get("test_id")
        error = raw_result.get("error")
        execution_time = raw_result.get("execution_time")
        
        # Create result
        result = StandardResult(
            test_type=test_type,
            test_id=test_id,
            suite_id=context.get("suite_id"),
            project_id=context.get("project_id"),
            status=ExecutionStatus.SUCCESS if success else ExecutionStatus.FAILURE,
            configuration=context.get("configuration", {}),
            success=success,
            message=raw_result.get("message")
        )
        
        # Add error if present
        if error:
            result.add_error(error)
        
        # Parse execution time if available
        if execution_time and isinstance(execution_time, str):
            try:
                if execution_time.endswith("ms"):
                    result.duration_ms = int(execution_time[:-2])
                elif execution_time.endswith("s"):
                    result.duration_ms = int(float(execution_time[:-1]) * 1000)
            except ValueError:
                pass
        
        # Process nested result data
        if "result" in raw_result:
            nested_result = raw_result["result"]
            if isinstance(nested_result, dict):
                self._extract_nested_data(result, nested_result)
        
        return result
    
    def _process_suite_execution(self, raw_result: Dict[str, Any], **context) -> StandardResult:
        """
        Process legacy SuiteExecutionResponse format.
        
        Args:
            raw_result: Legacy suite execution response
            **context: Additional context
            
        Returns:
            StandardResult: Processed result
        """
        suite_id = raw_result.get("suite_id") or context.get("suite_id")
        result = StandardResult(
            test_type=TestType.SUITE,
            execution_id=context.get("execution_id"),
            suite_id=suite_id,
            project_id=context.get("project_id"),
            status=ExecutionStatus.SUCCESS if raw_result.get("success", False) else ExecutionStatus.FAILURE,
            configuration=context.get("configuration", {}),
            success=raw_result.get("success", False)
        )
        
        # Extract suite-specific data
        result.summary.total_test_cases = len(raw_result["results"])
        result.summary.passed_test_cases = raw_result.get("passed", 0)
        result.summary.failed_test_cases = raw_result.get("failed", 0)
        
        # Parse execution time
        execution_time = raw_result.get("execution_time")
        if execution_time and isinstance(execution_time, str):
            try:
                if execution_time.endswith("ms"):
                    result.duration_ms = int(execution_time[:-2])
            except ValueError:
                pass
        
        # Process individual test results if available
        for i, test_result_dict in enumerate(raw_result["results"]):
            # Result here is a dict version of StandardResult
            is_success = test_result_dict.get("status") == "success"
            if is_success:
                result.summary.passed_test_cases += 1
            else:
                result.summary.failed_test_cases += 1

            step = TestStep(
                step_number=i + 1,
                action_type="test_case_execution",
                description=f"Test case execution: {test_result_dict.get('execution_id')}",
                success=is_success,
                error_message=json.dumps(test_result_dict.get("summary", {}).get("error")) if not is_success else None
            )
            result.add_step(step)
        
        # Add error if present
        if raw_result.get("error"):
            result.add_error(raw_result["error"])
        
        return result
    
    def _process_codegen_execution(self, raw_result: Dict[str, Any], **context) -> StandardResult:
        """
        Process CodegenExecutionResponse format.
        
        Args:
            raw_result: Codegen execution response
            **context: Additional context
            
        Returns:
            StandardResult: Processed result
        """
        # Map codegen statuses to standard statuses
        status_mapping = {
            "starting": ExecutionStatus.PENDING,
            "running": ExecutionStatus.RUNNING,
            "completed": ExecutionStatus.SUCCESS,
            "failed": ExecutionStatus.FAILURE
        }
        
        codegen_status = raw_result.get("status", "failed")
        status = status_mapping.get(codegen_status, ExecutionStatus.ERROR)
        
        result = StandardResult(
            test_type=TestType.CODEGEN,
            execution_id=raw_result.get("execution_id"),
            status=status,
            message=raw_result.get("message"),
            configuration=context.get("configuration", {})
        )
        
        # Process nested result data
        if "result" in raw_result:
            nested_result = raw_result["result"]
            if isinstance(nested_result, dict):
                self._extract_nested_data(result, nested_result)
        
        # Add generated code if available
        if "generated_code" in raw_result:
            result.artifacts.generated_code = raw_result["generated_code"]
        
        return result
    
    def _process_smoke_test(self, raw_result: Any, **context) -> StandardResult:
        """
        Process smoke test results.
        
        Args:
            raw_result: Smoke test result
            **context: Additional context
            
        Returns:
            StandardResult: Processed result
        """
        # Smoke tests are typically browser history or simple success/failure
        if isinstance(raw_result, dict):
            return self._process_test_execution(raw_result, test_type="smoke", **context)
        else:
            return self._process_browser_history(raw_result, test_type="smoke", **context)
    
    def _process_full_test(self, raw_result: Any, **context) -> StandardResult:
        """
        Process full test results.
        
        Args:
            raw_result: Full test result
            **context: Additional context
            
        Returns:
            StandardResult: Processed result
        """
        # Full tests are typically browser history with Gherkin scenarios
        if isinstance(raw_result, dict):
            result = self._process_test_execution(raw_result, test_type="full", **context)
        else:
            result = self._process_browser_history(raw_result, test_type="full", **context)
        
        # Extract Gherkin scenarios if available
        if isinstance(raw_result, dict) and "gherkin_scenarios" in raw_result:
            result.artifacts.gherkin_scenarios = raw_result["gherkin_scenarios"]
        
        return result
    
    def _extract_step_from_model_action(self, action: Dict[str, Any], step_number: int) -> TestStep:
        """Extracts a TestStep from a model_action entry in the history."""
        
        
        # Handle new browser-use structure where actions are direct keys
        action_type = "unknown"
        description = f"Step {step_number}"
        success = "error" not in action
        error_message = action.get("error")
        
        # Determine action type from the keys (new structure)
        action_keys = [key for key in action.keys() if key != 'interacted_element']
        logger.info(f"🔍 STEP DEBUG {step_number}: Filtered action_keys: {action_keys}")
        
        if action_keys:
            action_type = action_keys[0]  # The action type is the first non-interacted_element key
            logger.info(f"🔍 STEP DEBUG {step_number}: Determined action_type: {action_type}")
            
            # Get more descriptive information from the action data
            action_data = action.get(action_type, {})
            if isinstance(action_data, dict):
                # Try to build a description from the action data
                if action_type == "go_to_url":
                    url = action_data.get("url", "")
                    description = f"Navigate to {url}"
                elif action_type == "done":
                    text = action_data.get("text", "")
                    description = f"Task completed: {text}"
                elif action_type == "click":
                    description = "Click element"
                elif action_type == "type":
                    text = action_data.get("text", "")
                    description = f"Type: {text}"
                elif action_type == "scroll":
                    description = "Scroll page"
                else:
                    description = f"{action_type.replace('_', ' ').title()}"
        else:
            logger.warning(f"⚠️ STEP DEBUG {step_number}: No valid action_keys found, action_type remains 'unknown'")
        
        # Legacy fallback for old structure
        if action_type == "unknown" and "action" in action:
            action_type = action.get("action", {}).get("type", "unknown")
            description = action.get("summary", f"Step {step_number}")
            logger.info(f"🔍 STEP DEBUG {step_number}: Using legacy fallback, action_type: {action_type}")
        
        if action_type == "unknown":
            logger.warning(f"⚠️ STEP DEBUG {step_number}: Final action_type is still 'unknown' - this will show as 'unknown' in UI")

        return TestStep(
            step_number=step_number,
            action_type=action_type,
            description=description,
            success=success,
            error_message=error_message,
            metadata=action # Store the whole action for rich details
        )
    
    def _extract_step_from_agent_history(self, agent_history: Any, step_number: int) -> TestStep:
        """
        Extract a TestStep from an AgentHistory object (new browser-use structure).
        
        Args:
            agent_history: AgentHistory object from browser-use
            step_number: Step number for this step
            
        Returns:
            TestStep: Extracted step with rich information
        """
        action_type = "unknown"
        description = f"Step {step_number}"
        success = True  # Default to success unless we find a real error
        error_message = None
        screenshot_url = None
        element_info = None
        url = None
        metadata = {}
        
        try:
            # Extract current URL from state  
            if hasattr(agent_history, 'state') and agent_history.state:
                state = agent_history.state
            if hasattr(state, 'url') and state.url:
                url = state.url  # This will populate the step.url field
            
            # Look for screenshot in state
            if hasattr(state, 'screenshot') and state.screenshot:
                # Don't set screenshot_url here - it will be updated with actual artifact paths later
                # Just log that we found screenshot data
                logger.debug(f"Step {step_number}: Found screenshot data in state")
            
            # Store state info in metadata
            if hasattr(state, '__dict__'):
                metadata['state'] = {}
                for attr_name, attr_value in state.__dict__.items():
                    if attr_value is not None:
                        if 'screenshot' in attr_name.lower() and attr_value:
                            # Don't generate hardcoded screenshot names - artifacts will provide the real paths
                            logger.debug(f"Step {step_number}: Found screenshot attribute {attr_name}")
                        
                        # Handle JSON serialization for different types
                        if isinstance(attr_value, str):
                            metadata['state'][attr_name] = attr_value[:200] if len(attr_value) > 200 else attr_value
                        elif isinstance(attr_value, list):
                            # Handle list of objects (like TabInfo)
                            serialized_list = []
                            for item in attr_value:
                                if hasattr(item, 'model_dump'):  # Pydantic v2
                                    serialized_list.append(item.model_dump())
                                elif hasattr(item, 'dict'):  # Pydantic v1
                                    serialized_list.append(item.dict())
                                elif hasattr(item, '__dict__'):  # Regular object
                                    serialized_list.append(item.__dict__)
                                else:
                                    serialized_list.append(str(item))
                            metadata['state'][attr_name] = serialized_list
                        elif hasattr(attr_value, 'model_dump'):  # Pydantic v2 object
                            metadata['state'][attr_name] = attr_value.model_dump()
                        elif hasattr(attr_value, 'dict'):  # Pydantic v1 object
                            metadata['state'][attr_name] = attr_value.dict()
                        elif hasattr(attr_value, '__dict__'):  # Regular object
                            metadata['state'][attr_name] = attr_value.__dict__
                        else:
                            # For primitive types or unknown objects, convert to string
                            metadata['state'][attr_name] = str(attr_value)
        
            # Extract from model_output if available  
            if hasattr(agent_history, 'model_output') and agent_history.model_output:
                model_output = agent_history.model_output
            
            # Extract thinking/description - this is the rich description we want!
            if hasattr(model_output, 'thinking') and model_output.thinking:
                thinking = model_output.thinking.strip()
                # Take first sentence or first 200 chars for description
                if '. ' in thinking:
                    description = thinking.split('. ')[0] + '.'
                else:
                    description = thinking[:200] + "..." if len(thinking) > 200 else thinking
            
            # Extract actions to determine action type and get rich interaction data
            if hasattr(model_output, 'actions') and model_output.actions:
                primary_action = None
                all_actions = []
                
                for action in model_output.actions:
                    action_data = {}
                    if hasattr(action, '__dict__'):
                        action_data = action.__dict__.copy()
                    
                    # Convert action to dict if it has type
                    if hasattr(action, 'type'):
                        action_type_found = action.type
                        action_data['type'] = action_type_found
                        
                        # Extract specific action information
                        if action_type_found == "go_to_url" and hasattr(action, 'url'):
                            url = action.url  # Override URL with action URL
                            description = f"Navigate to {action.url}"
                            action_type = "go_to_url"
                            primary_action = action_data
                        elif action_type_found == "click":
                            if hasattr(action, 'coordinate'):
                                element_info = {"coordinate": action.coordinate}
                                description = f"Click at {action.coordinate}"
                            elif hasattr(action, 'selector'):
                                element_info = {"selector": action.selector}
                                description = f"Click element: {action.selector}"
                            action_type = "click"
                            primary_action = action_data
                        elif action_type_found == "type" and hasattr(action, 'text'):
                            description = f"Type: {action.text[:50]}..." if len(action.text) > 50 else f"Type: {action.text}"
                            action_type = "type"
                            primary_action = action_data
                        elif action_type_found == "done":
                            if hasattr(action, 'text'):
                                description = f"Task completed: {action.text}"
                            if hasattr(action, 'success'):
                                success = action.success
                                # Store success flag in action data for later detection
                                action_data['success'] = action.success
                                logger.info(f"Extracted done action with success={action.success} in step {step_number}")
                            # ALSO check for success in the action dict representation
                            elif hasattr(action, '__dict__') and 'success' in action.__dict__:
                                success = action.__dict__['success']
                                action_data['success'] = success
                                logger.info(f"Extracted done action with success={success} (from __dict__) in step {step_number}")
                            action_type = "done"
                            primary_action = action_data
                        elif action_type_found == "write_file":
                            if hasattr(action, 'file_name'):
                                description = f"Write file: {action.file_name}"
                            action_type = "write_file"
                            primary_action = action_data
                        elif action_type_found == "scroll":
                            description = "Scroll page"
                            action_type = "scroll"
                            primary_action = action_data
                        else:
                            # Generic action handling
                            action_type = action_type_found
                            description = f"{action_type_found.replace('_', ' ').title()}"
                            primary_action = action_data
                    
                    all_actions.append(action_data)
                
                # Store all actions in metadata
                metadata['actions'] = all_actions
                if primary_action:
                    metadata['primary_action'] = primary_action
            
            # Store model output thinking in metadata
            if hasattr(model_output, 'thinking'):
                metadata['thinking'] = model_output.thinking
        
            # Extract from result if available (errors, success info)
            if hasattr(agent_history, 'result') and agent_history.result:
                result_list = agent_history.result
            if isinstance(result_list, list):
                for result_item in result_list:
                    if hasattr(result_item, 'error') and result_item.error:
                        error_str = str(result_item.error)
                        # Only mark as failed if this is NOT an LLM parsing error
                        if ("Could not parse response" in error_str or
                            "MALFORMED_FUNCTION_CALL" in error_str or
                            "Failed to parse model output" in error_str):
                            logger.debug(f"Step {step_number}: Ignoring LLM parsing error: {error_str[:100]}...")
                            # Keep success=True, don't set error_message
                        else:
                            # This is a real execution error
                            error_message = error_str
                            success = False
                            logger.debug(f"Step {step_number}: Adding real error: {error_str}")
                    elif hasattr(result_item, 'success') and result_item.success is not None:
                        # Only override success if we haven't already set it to False due to real errors
                        if success:  # Don't override False (real error) with result.success
                            success = result_item.success
        
            # Store result info in metadata
            metadata['result'] = [str(r) for r in result_list] if isinstance(result_list, list) else str(result_list)
        
        except Exception as e:
            error_message = f"Failed to extract step info: {str(e)}"
            success = False
    
        return TestStep(
        step_number=step_number,
        action_type=action_type,
        description=description,
        success=success,
        error_message=error_message,
        screenshot_url=screenshot_url,
        element_info=element_info,
        url=url,
        metadata=metadata
    )
    
    async def _extract_screenshots_from_history(self, history: Any, result: StandardResult):
        """
        Extract screenshots from browser-use history and update step screenshot URLs.
        
        Args:
            history: History object from browser-use
            result: StandardResult to enrich with screenshot URLs
        """
        # Check if history has a list of agent histories
        if hasattr(history, 'history') and isinstance(history.history, list):
            for i, agent_history in enumerate(history.history):
                # Extract screenshot from state
                if hasattr(agent_history, 'state') and agent_history.state:
                    state = agent_history.state
                    if hasattr(state, 'screenshot') and state.screenshot:
                        screenshot_data = state.screenshot
                        
                        # LOS ARTIFACTS YA FUERON CREADOS POR EL COLLECTOR
                        # Buscar el artifact correspondiente a este step y actualizar la URL
                        if i < len(result.steps):
                            # Convert base64 to bytes SOLO para verificar que tenemos screenshot data
                            if isinstance(screenshot_data, str):
                                # Remove data URL prefix if present
                                if screenshot_data.startswith('data:image/'):
                                    screenshot_data = screenshot_data.split(',', 1)[1]
                                
                                try:
                                    screenshot_bytes = base64.b64decode(screenshot_data)
                                    logger.debug(f"✅ Valid base64 screenshot data found for step {i+1} ({len(screenshot_bytes)} bytes)")
                                except Exception as e:
                                    logger.warning(f"❌ Invalid base64 screenshot data for step {i+1}: {e}")
                                    continue
                                # Buscar artifact por step_name en la base de datos
                                from src.database.models.artifact import Artifact as ArtifactModel
                                step_name = f"step_{i+1}"
                                
                                # Debug: Ver qué artifacts hay realmente para este execution_id
                                logger.info(f"🔍 Searching for artifacts with execution_id: {result.execution_id}")
                                all_artifacts = await ArtifactModel.find({"execution_id": result.execution_id}).to_list()
                                logger.info(f"🔍 Found {len(all_artifacts)} total artifacts for execution")
                                
                                for idx, art in enumerate(all_artifacts):
                                    logger.info(f"🔍 Artifact {idx+1}: id={art.artifact_id}, type={art.type}, step_name={art.metadata.get('step_name') if art.metadata else 'No metadata'}")
                                
                                # Buscar artifact que coincida con este step
                                query = {
                                    "execution_id": result.execution_id,
                                    "type": "screenshot"
                                }
                                logger.info(f"🔍 Searching with query: {query}")
                                
                                # Intentar múltiples estrategias de búsqueda
                                artifact = await ArtifactModel.find_one({
                                    "execution_id": result.execution_id,
                                    "metadata.step_name": step_name,
                                    "type": "screenshot"
                                })
                                
                                if not artifact:
                                    # Buscar sin step_name específico
                                    all_screenshots = await ArtifactModel.find({
                                        "execution_id": result.execution_id,
                                        "type": "screenshot"
                                    }).to_list()
                                    
                                    logger.info(f"🔍 Found {len(all_screenshots)} screenshot artifacts")
                                    
                                    # Usar el screenshot correspondiente al índice del step
                                    if i < len(all_screenshots):
                                        artifact = all_screenshots[i]
                                        logger.info(f"🔍 Using screenshot {i} for step {i+1}: {artifact.artifact_id}")
                                
                                if artifact:
                                    # Verificar si tiene blob_key (R2) o file_path (local)
                                    if hasattr(artifact, 'blob_key') and artifact.blob_key:
                                        # R2 artifact - usar blob_key para construir URL
                                        screenshot_url = f"/api/artifacts/{artifact.artifact_id}"
                                        result.steps[i].screenshot_url = screenshot_url
                                        logger.info(f"✅ Updated step {i+1} screenshot_url to R2 artifact: {screenshot_url}")
                                    elif hasattr(artifact, 'file_path') and artifact.file_path:
                                        # Local artifact
                                        result.steps[i].screenshot_url = str(artifact.file_path)
                                        logger.info(f"✅ Updated step {i+1} screenshot_url to local path: {artifact.file_path}")
                                    else:
                                        logger.warning(f"❌ Artifact {artifact.artifact_id} has no blob_key or file_path")
                                else:
                                    logger.warning(f"❌ No artifact found for step {i+1} with step_name: {step_name}")
                                    # NO procesar como base64, usar el path como está
                                    result.steps[i].screenshot_url = screenshot_data  # Esto será procesado después por el response transformer
                            
                            logger.debug(f"Screenshot data processed for step {i+1}, artifacts already created by collector")
                                
                                
    def _extract_rich_data_from_history(self, history: Any, result: StandardResult):
        """
        Extract rich interaction data (URLs, elements) from browser-use history.
        
        Args:
            history: History object from browser-use
            result: StandardResult to enrich with data
        """
        visited_urls = set()
        interacted_elements = []
        
        # Check if history has a list of agent histories
        if hasattr(history, 'history') and isinstance(history.history, list):
            for i, agent_history in enumerate(history.history):
                # Extract URLs from state
                if hasattr(agent_history, 'state') and agent_history.state:
                    state = agent_history.state
                    if hasattr(state, 'url') and state.url:
                        visited_urls.add(state.url)
                        
                        # Update the corresponding step with URL info
                        if i < len(result.steps):
                            result.steps[i].url = state.url
                
                # Extract interacted elements
                if hasattr(agent_history, 'interacted_element') and agent_history.interacted_element:
                    element_info = agent_history.interacted_element
                    interacted_elements.append(element_info)
                    
                    # Update the corresponding step with element info
                    if i < len(result.steps):
                        result.steps[i].element_info = element_info
                
                # Extract URLs from actions
                if hasattr(agent_history, 'model_output') and agent_history.model_output:
                    model_output = agent_history.model_output
                    if hasattr(model_output, 'actions') and model_output.actions:
                        for action in model_output.actions:
                            if hasattr(action, 'type') and action.type == "go_to_url":
                                if hasattr(action, 'url') and action.url:
                                    visited_urls.add(action.url)
                                    
                                    # Update the corresponding step with URL info
                                    if i < len(result.steps):
                                        result.steps[i].url = action.url
        
        # Store visited URLs as metadata
        if visited_urls:
            result.metadata = result.metadata or {}
            result.metadata['visited_urls'] = list(visited_urls)
            
        # Store interacted elements as metadata
        if interacted_elements:
            result.metadata = result.metadata or {}
            result.metadata['interacted_elements'] = interacted_elements
            

    def _update_step_screenshots_with_artifacts(self, result: StandardResult, screenshot_artifacts: List):
        """
        Update step screenshot URLs with actual artifact file paths.
        
        Args:
            result: StandardResult to update
            screenshot_artifacts: List of screenshot artifacts
        """
        if not screenshot_artifacts:
            return
        
        # Create a mapping of step_name to artifact
        step_artifacts = {}
        for artifact in screenshot_artifacts:
            if hasattr(artifact, 'metadata') and artifact.metadata:
                step_name = artifact.metadata.get('step_name', '')
                if step_name and step_name.startswith('step_'):
                    try:
                        # Extract step number from step_name (e.g., "step_1" -> 0)
                        step_index = int(step_name.replace('step_', '')) - 1
                        step_artifacts[step_index] = artifact
                    except ValueError:
                        logger.warning(f"Invalid step_name format: {step_name}")
        
        # Update step screenshot URLs with actual paths from artifacts
        for i, step in enumerate(result.steps):
            if i in step_artifacts:
                artifact = step_artifacts[i]
                # Use the response transformer to convert the path to a proper URL
                if hasattr(artifact, 'file_path'):
                    file_path = str(artifact.file_path)
                    
                    # Convert the path to an accessible URL using the response transformer
                    # This handles both local paths and R2-only virtual paths
                    screenshot_url = convert_screenshot_paths_to_urls(
                        {"screenshot": file_path}
                    ).get("screenshot", file_path)
                    
                    step.screenshot_url = screenshot_url
                    logger.debug(f"Updated step {i+1} screenshot URL to: {step.screenshot_url}")
                
                # Also handle artifact_id directly for R2-only artifacts
                elif hasattr(artifact, 'artifact_id') and artifact.artifact_id:
                    from src.utilities.response_transformers import generate_artifact_url
                    screenshot_url = generate_artifact_url(artifact.artifact_id)
                    step.screenshot_url = screenshot_url
                    logger.debug(f"Updated step {i+1} screenshot URL (artifact_id) to: {step.screenshot_url}")
            else:
                logger.debug(f"No artifact found for step {i+1}")
        
        logger.info(f"Updated {len(step_artifacts)} steps with actual screenshot paths")
            
    
    def _format_step_result(self, result_item) -> str:
        """
        Format a step result item in a human-readable way.
        
        Args:
            result_item: Raw result item from browser-use
            
        Returns:
            str: Human-readable formatted result
        """
        if not hasattr(result_item, '__dict__'):
            return str(result_item)
        
        # Extract key information
        is_done = getattr(result_item, 'is_done', None)
        success = getattr(result_item, 'success', None)
        error = getattr(result_item, 'error', None)
        long_term_memory = getattr(result_item, 'long_term_memory', '')
        extracted_content = getattr(result_item, 'extracted_content', '')
        
        # Build human-readable format
        parts = []
        
        # Status information
        if success is not None:
            status = "✅ Success" if success else "❌ Failed"
            parts.append(f"Status: {status}")
        
        if is_done is not None:
            completion = "✅ Complete" if is_done else "🔄 In Progress"
            parts.append(f"Completion: {completion}")
        
        # Error information
        if error and str(error).strip() and str(error).strip() != "None":
            parts.append(f"Error: {str(error)}")
        
        # Memory/content information
        if long_term_memory and str(long_term_memory).strip():
            memory_text = str(long_term_memory).strip()
            if len(memory_text) > 100:
                memory_text = memory_text[:100] + "..."
            parts.append(f"Memory: {memory_text}")
        
        if extracted_content and str(extracted_content).strip():
            content_text = str(extracted_content).strip()
            if len(content_text) > 100:
                content_text = content_text[:100] + "..."
            parts.append(f"Content: {content_text}")
        
        # Join parts with newlines for better readability
        if parts:
            return "\n".join(parts)
        else:
            return "No significant result data"
        
        return str(result_item)
        
    async def batch_process(self, raw_results: List[tuple], **context) -> List[StandardResult]:
        """
        Processes a batch of raw results.
        
        Args:
            raw_results: List of (raw_result, result_type) tuples
            **context: Shared context for all results
            
        Returns:
            List[StandardResult]: Processed results
        """
        results = []
        
        for raw_result, result_type in raw_results:
            processed = await self.process(raw_result, result_type, **context)
            results.append(processed)
            # Add error result for failed processing
            error_result = create_error_result(
                test_type=TestType.SMOKE,  # Default
                error_message=f"Batch processing failed: {str(e)}"
            )
            results.append(error_result)
        
        return results
    
    def _apply_llm_validation(self, result: StandardResult, current_success: bool, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Apply LLM-based validation to improve result accuracy.
        
        Args:
            result: The StandardResult being processed
            current_success: Current success determination
            context: Execution context with test details
            
        Returns:
            Dict containing LLM validation results, or None if validation fails
        """
        # Check if LLM validation is disabled via environment variable
        import os
        if os.environ.get("DISABLE_LLM_VALIDATION", "false").lower() == "true":
            logger.debug("LLM validation disabled via DISABLE_LLM_VALIDATION environment variable")
            return None
        
        # Only apply LLM validation for test cases and smoke tests
        if result.test_type not in [TestType.CASE, TestType.SMOKE]:
            return None
        
        # Check if we have enough context for meaningful validation
        if not result.steps or len(result.steps) < 2:
            logger.debug("Skipping LLM validation: insufficient step data")
            return None
        
        # Import and initialize the LLM validator
        from src.utilities.llm_result_validator import create_llm_validator
        import os
        
        api_key = os.environ.get("GOOGLE_API_KEY")
        if not api_key:
            logger.warning("No GOOGLE_API_KEY found, skipping LLM validation")
            return None
        
        validator = create_llm_validator(api_key)
        
        # Prepare test context for validation
        test_objective = context.get("test_instructions", "")
        gherkin_scenario = context.get("gherkin_scenario", "")
        
        # If we don't have explicit test objective, try to construct from available data
        if not test_objective and hasattr(result, 'metadata') and result.metadata:
            test_objective = result.metadata.get("test_description", "")
        
        # Convert result to dict format for validation
        result_data = result.to_dict()
        
        # Apply LLM validation
        validation_result = validator.validate_execution_result(
            result_data=result_data,
            test_objective=test_objective,
            gherkin_scenario=gherkin_scenario,
            force_revalidate=False  # Use cache if available
        )
        
        logger.info(f"LLM validation completed for {result.execution_id}")
        return validation_result
        
        return None
    
    def get_supported_types(self) -> List[str]:
        """Returns a list of supported result types."""
        return list(self.processors.keys())


# Global transformer instance
result_transformer = ResultTransformer()


async def process_result(raw_result: Any, result_type: str, **context) -> StandardResult:
    """
    Convenience function to process a single result.
    
    Args:
        raw_result: Raw result data
        result_type: Type of result
        **context: Additional context
        
    Returns:
        StandardResult: Processed result
    """
    return await result_transformer.process(raw_result, result_type, **context)


async def process_results_batch(raw_results: List[tuple], **context) -> List[StandardResult]:
    """
    Convenience function to process multiple results.
    
    Args:
        raw_results: List of (raw_result, result_type) tuples
        **context: Shared context
        
    Returns:
        List[StandardResult]: Processed results
    """
    return await result_transformer.batch_process(raw_results, **context)


async def transform_agent_history_to_standard_result(history: Any, **context) -> StandardResult:
    """
    Transform agent history (from browser-use) to StandardResult format.
    
    This function is specifically designed to work with the CodeGen executor service
    and provides a direct interface for transforming browser-use agent history objects
    into the standardized result format used throughout the QAK platform.
    
    Args:
        history: Browser-use agent history object or mock history object
        **context: Additional context including:
            - test_type: Type of test (default: "codegen")
            - test_name: Name of the test
            - configuration: Browser configuration
            - target_url: Target URL for the test
            - current_iteration: Current iteration number
            - execution_times: Number of execution times
            
    Returns:
        StandardResult: Standardized result object
        
    Example:
        >>> mock_history = MockHistory({"history": [...], "summary": {...}})
        >>> context = {
        ...     "test_type": "codegen",
        ...     "test_name": "CodeGen Test - session_123",
        ...     "configuration": {"browser": "chrome"},
        ...     "target_url": "https://example.com",
        ...     "execution_id": "exec_456"
        ... }
        >>> result = await transform_agent_history_to_standard_result(mock_history, **context)
    """
    return await result_transformer.process(history, "browser_history", **context)