"""
Performance Monitor for QAK Test Execution System

Monitors and tracks system performance metrics, execution times,
resource usage, and provides performance insights and alerts.
"""

from typing import Dict, Any, Optional, List, Callable
import asyncio
import logging
import time
import psutil
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import statistics

logger = logging.getLogger(__name__)


class MetricType(str, Enum):
    """Types of metrics that can be monitored."""
    EXECUTION_TIME = "execution_time"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"
    RESOURCE_USAGE = "resource_usage"
    BROWSER_POOL = "browser_pool"
    ARTIFACT_STORAGE = "artifact_storage"
    API_LATENCY = "api_latency"
    QUEUE_SIZE = "queue_size"
    CUSTOM = "custom"


class AlertLevel(str, Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class MetricValue:
    """Represents a single metric measurement."""
    timestamp: datetime
    value: float
    labels: Dict[str, str] = field(default_factory=dict)
    
    def age_seconds(self) -> float:
        """Get age of metric in seconds."""
        return (datetime.now() - self.timestamp).total_seconds()


@dataclass
class MetricSeries:
    """Collection of metric values over time."""
    metric_name: str
    metric_type: MetricType
    values: deque = field(default_factory=lambda: deque(maxlen=1000))
    max_age_seconds: int = 3600  # 1 hour
    
    def add_value(self, value: float, labels: Dict[str, str] = None):
        """Add a new metric value."""
        metric_value = MetricValue(
            timestamp=datetime.now(),
            value=value,
            labels=labels or {}
        )
        self.values.append(metric_value)
        self._cleanup_old_values()
    
    def _cleanup_old_values(self):
        """Remove values older than max_age_seconds."""
        cutoff_time = datetime.now() - timedelta(seconds=self.max_age_seconds)
        while self.values and self.values[0].timestamp < cutoff_time:
            self.values.popleft()
    
    def get_latest(self) -> Optional[float]:
        """Get the latest metric value."""
        return self.values[-1].value if self.values else None
    
    def get_average(self, window_seconds: int = 300) -> Optional[float]:
        """Get average value over specified window."""
        cutoff_time = datetime.now() - timedelta(seconds=window_seconds)
        recent_values = [
            v.value for v in self.values 
            if v.timestamp >= cutoff_time
        ]
        return statistics.mean(recent_values) if recent_values else None
    
    def get_percentile(self, percentile: float, window_seconds: int = 300) -> Optional[float]:
        """Get percentile value over specified window."""
        cutoff_time = datetime.now() - timedelta(seconds=window_seconds)
        recent_values = [
            v.value for v in self.values 
            if v.timestamp >= cutoff_time
        ]
        if not recent_values:
            return None
        
        sorted_values = sorted(recent_values)
        index = int((percentile / 100) * len(sorted_values))
        return sorted_values[min(index, len(sorted_values) - 1)]
    
    def get_rate(self, window_seconds: int = 60) -> Optional[float]:
        """Get rate of change per second over window."""
        cutoff_time = datetime.now() - timedelta(seconds=window_seconds)
        recent_values = [
            v for v in self.values 
            if v.timestamp >= cutoff_time
        ]
        
        if len(recent_values) < 2:
            return None
        
        time_span = (recent_values[-1].timestamp - recent_values[0].timestamp).total_seconds()
        if time_span == 0:
            return None
        
        return len(recent_values) / time_span


@dataclass
class Alert:
    """Performance alert."""
    alert_id: str
    level: AlertLevel
    metric_name: str
    message: str
    threshold: float
    current_value: float
    timestamp: datetime
    resolved: bool = False
    resolved_at: Optional[datetime] = None


class PerformanceMonitor:
    """
    Monitors system performance and provides metrics, alerts, and insights.
    
    Features:
    - Real-time metric collection
    - Configurable thresholds and alerts
    - Performance trend analysis
    - Resource usage monitoring
    - Integration with core services
    - Dashboard-ready metrics export
    """
    
    def __init__(
        self,
        collection_interval: int = 30,
        metric_retention_hours: int = 24,
        enable_system_metrics: bool = True,
        enable_alerts: bool = True
    ):
        """
        Initialize performance monitor.
        
        Args:
            collection_interval: Seconds between metric collections
            metric_retention_hours: Hours to retain metric data
            enable_system_metrics: Whether to collect system metrics
            enable_alerts: Whether to generate alerts
        """
        self.collection_interval = collection_interval
        self.metric_retention_seconds = metric_retention_hours * 3600
        self.enable_system_metrics = enable_system_metrics
        self.enable_alerts = enable_alerts
        
        # Metric storage
        self.metrics: Dict[str, MetricSeries] = {}
        self.alerts: Dict[str, Alert] = {}
        self.alert_thresholds: Dict[str, Dict[str, float]] = {}
        self.alert_callbacks: List[Callable[[Alert], None]] = []
        
        # Collection state
        self.collection_task: Optional[asyncio.Task] = None
        self.is_collecting = False
        
        # Performance counters
        self.execution_counter = 0
        self.error_counter = 0
        self.last_reset_time = datetime.now()
        
        logger.info(f"PerformanceMonitor initialized (interval: {collection_interval}s)")
    
    async def initialize(self):
        """Initialize the performance monitor."""
        # Set up default thresholds
        self._setup_default_thresholds()
        
        # Start metric collection
        if self.enable_system_metrics:
            self.collection_task = asyncio.create_task(self._collection_loop())
        
        logger.info("PerformanceMonitor initialized successfully")
    
    def _setup_default_thresholds(self):
        """Set up default alert thresholds."""
        self.alert_thresholds = {
            "execution_time_avg": {
                "warning": 30.0,  # seconds
                "error": 60.0,
                "critical": 120.0
            },
            "error_rate": {
                "warning": 0.05,  # 5%
                "error": 0.10,    # 10%
                "critical": 0.25  # 25%
            },
            "cpu_usage": {
                "warning": 70.0,  # percentage
                "error": 85.0,
                "critical": 95.0
            },
            "memory_usage": {
                "warning": 70.0,  # percentage
                "error": 85.0,
                "critical": 95.0
            },
            "browser_pool_utilization": {
                "warning": 80.0,  # percentage
                "error": 90.0,
                "critical": 95.0
            }
        }
    
    def record_execution_time(
        self, 
        execution_type: str, 
        duration_ms: float,
        success: bool = True,
        labels: Dict[str, str] = None
    ):
        """Record test execution time."""
        metric_labels = {"type": execution_type, "success": str(success)}
        if labels:
            metric_labels.update(labels)
        
        # Record execution time
        self._record_metric(
            f"execution_time_{execution_type}",
            MetricType.EXECUTION_TIME,
            duration_ms,
            metric_labels
        )
        
        # Update counters
        self.execution_counter += 1
        if not success:
            self.error_counter += 1
        
        # Check for alerts
        if self.enable_alerts:
            self._check_execution_time_alerts(execution_type, duration_ms)
    
    def record_throughput(self, operations_per_second: float, operation_type: str = "executions"):
        """Record throughput metric."""
        self._record_metric(
            f"throughput_{operation_type}",
            MetricType.THROUGHPUT,
            operations_per_second,
            {"type": operation_type}
        )
    
    def record_api_latency(self, endpoint: str, latency_ms: float, status_code: int = 200):
        """Record API endpoint latency."""
        self._record_metric(
            f"api_latency_{endpoint.replace('/', '_')}",
            MetricType.API_LATENCY,
            latency_ms,
            {"endpoint": endpoint, "status": str(status_code)}
        )
    
    def record_browser_pool_stats(self, stats: Dict[str, Any]):
        """Record browser pool statistics."""
        for metric_name, value in stats.items():
            if isinstance(value, (int, float)):
                self._record_metric(
                    f"browser_pool_{metric_name}",
                    MetricType.BROWSER_POOL,
                    value
                )
    
    def record_artifact_storage_stats(self, stats: Dict[str, Any]):
        """Record artifact storage statistics."""
        for metric_name, value in stats.items():
            if isinstance(value, (int, float)):
                self._record_metric(
                    f"artifact_storage_{metric_name}",
                    MetricType.ARTIFACT_STORAGE,
                    value
                )
    
    def record_custom_metric(
        self, 
        name: str, 
        value: float, 
        labels: Dict[str, str] = None
    ):
        """Record a custom metric."""
        self._record_metric(name, MetricType.CUSTOM, value, labels)
    
    def _record_metric(
        self, 
        name: str, 
        metric_type: MetricType, 
        value: float, 
        labels: Dict[str, str] = None
    ):
        """Internal method to record a metric."""
        if name not in self.metrics:
            self.metrics[name] = MetricSeries(
                metric_name=name,
                metric_type=metric_type,
                max_age_seconds=self.metric_retention_seconds
            )
        
        self.metrics[name].add_value(value, labels)
        logger.debug(f"Recorded metric {name}: {value}")
    
    async def _collection_loop(self):
        """Background loop for collecting system metrics."""
        while True:
            try:
                self.is_collecting = True
                
                # Collect system metrics
                await self._collect_system_metrics()
                
                # Calculate derived metrics
                await self._calculate_derived_metrics()
                
                # Check alerts
                if self.enable_alerts:
                    await self._check_all_alerts()
                
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in performance collection loop: {e}")
                await asyncio.sleep(self.collection_interval)
            finally:
                self.is_collecting = False
    
    async def _collect_system_metrics(self):
        """Collect system resource metrics."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self._record_metric("system_cpu_usage", MetricType.RESOURCE_USAGE, cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            self._record_metric("system_memory_usage", MetricType.RESOURCE_USAGE, memory.percent)
            self._record_metric("system_memory_available_mb", MetricType.RESOURCE_USAGE, memory.available / 1024 / 1024)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self._record_metric("system_disk_usage", MetricType.RESOURCE_USAGE, disk_percent)
            
            # Network I/O
            network = psutil.net_io_counters()
            self._record_metric("system_network_bytes_sent", MetricType.RESOURCE_USAGE, network.bytes_sent)
            self._record_metric("system_network_bytes_recv", MetricType.RESOURCE_USAGE, network.bytes_recv)
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
    
    async def _calculate_derived_metrics(self):
        """Calculate derived metrics from collected data."""
        try:
            # Calculate error rate
            error_rate = 0.0
            if self.execution_counter > 0:
                error_rate = self.error_counter / self.execution_counter
            
            self._record_metric("error_rate", MetricType.ERROR_RATE, error_rate)
            
            # Calculate average execution times
            for metric_name, series in self.metrics.items():
                if series.metric_type == MetricType.EXECUTION_TIME:
                    avg_time = series.get_average(300)  # 5-minute average
                    if avg_time is not None:
                        self._record_metric(f"{metric_name}_avg", MetricType.EXECUTION_TIME, avg_time)
            
        except Exception as e:
            logger.error(f"Failed to calculate derived metrics: {e}")
    
    def _check_execution_time_alerts(self, execution_type: str, duration_ms: float):
        """Check for execution time alerts."""
        duration_seconds = duration_ms / 1000
        threshold_key = "execution_time_avg"
        
        if threshold_key in self.alert_thresholds:
            thresholds = self.alert_thresholds[threshold_key]
            
            for level_name, threshold in thresholds.items():
                if duration_seconds > threshold:
                    self._trigger_alert(
                        f"execution_time_{execution_type}_{level_name}",
                        AlertLevel(level_name),
                        f"execution_time_{execution_type}",
                        f"Execution time {duration_seconds:.2f}s exceeds {level_name} threshold {threshold}s",
                        threshold,
                        duration_seconds
                    )
                    break  # Only trigger highest severity level
    
    async def _check_all_alerts(self):
        """Check all configured alert thresholds."""
        for metric_name, series in self.metrics.items():
            latest_value = series.get_latest()
            if latest_value is None:
                continue
            
            # Check if this metric has thresholds
            for threshold_key, thresholds in self.alert_thresholds.items():
                if threshold_key in metric_name or metric_name.endswith(threshold_key):
                    for level_name, threshold in thresholds.items():
                        alert_id = f"{metric_name}_{level_name}"
                        
                        if latest_value > threshold:
                            # Trigger alert
                            self._trigger_alert(
                                alert_id,
                                AlertLevel(level_name),
                                metric_name,
                                f"Metric {metric_name} value {latest_value:.2f} exceeds {level_name} threshold {threshold}",
                                threshold,
                                latest_value
                            )
                        else:
                            # Resolve alert if it was active
                            self._resolve_alert(alert_id)
    
    def _trigger_alert(
        self,
        alert_id: str,
        level: AlertLevel,
        metric_name: str,
        message: str,
        threshold: float,
        current_value: float
    ):
        """Trigger a performance alert."""
        # Don't retrigger existing alerts
        if alert_id in self.alerts and not self.alerts[alert_id].resolved:
            return
        
        alert = Alert(
            alert_id=alert_id,
            level=level,
            metric_name=metric_name,
            message=message,
            threshold=threshold,
            current_value=current_value,
            timestamp=datetime.now()
        )
        
        self.alerts[alert_id] = alert
        
        # Notify callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"Error in alert callback: {e}")
        
        logger.warning(f"Performance alert [{level}]: {message}")
    
    def _resolve_alert(self, alert_id: str):
        """Resolve an active alert."""
        if alert_id in self.alerts and not self.alerts[alert_id].resolved:
            self.alerts[alert_id].resolved = True
            self.alerts[alert_id].resolved_at = datetime.now()
            logger.info(f"Resolved performance alert: {alert_id}")
    
    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """Add callback to be notified of alerts."""
        self.alert_callbacks.append(callback)
    
    def get_metric_summary(self, metric_name: str, window_seconds: int = 300) -> Dict[str, Any]:
        """Get summary statistics for a metric."""
        if metric_name not in self.metrics:
            return {}
        
        series = self.metrics[metric_name]
        
        return {
            "metric_name": metric_name,
            "latest": series.get_latest(),
            "average": series.get_average(window_seconds),
            "p50": series.get_percentile(50, window_seconds),
            "p95": series.get_percentile(95, window_seconds),
            "p99": series.get_percentile(99, window_seconds),
            "data_points": len(series.values),
            "window_seconds": window_seconds
        }
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health metrics."""
        health = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": (datetime.now() - self.last_reset_time).total_seconds(),
            "metrics": {},
            "alerts": {
                "active": sum(1 for a in self.alerts.values() if not a.resolved),
                "total": len(self.alerts)
            }
        }
        
        # Get key metrics
        key_metrics = [
            "system_cpu_usage", "system_memory_usage", "error_rate",
            "browser_pool_utilization", "artifact_storage_total_size_mb"
        ]
        
        for metric_name in key_metrics:
            if metric_name in self.metrics:
                latest = self.metrics[metric_name].get_latest()
                if latest is not None:
                    health["metrics"][metric_name] = latest
        
        # Determine overall status based on alerts
        critical_alerts = [a for a in self.alerts.values() if not a.resolved and a.level == AlertLevel.CRITICAL]
        error_alerts = [a for a in self.alerts.values() if not a.resolved and a.level == AlertLevel.ERROR]
        warning_alerts = [a for a in self.alerts.values() if not a.resolved and a.level == AlertLevel.WARNING]
        
        if critical_alerts:
            health["status"] = "critical"
        elif error_alerts:
            health["status"] = "degraded"
        elif warning_alerts:
            health["status"] = "warning"
        
        return health
    
    def get_performance_report(self, hours: int = 1) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        window_seconds = hours * 3600
        
        report = {
            "period_hours": hours,
            "generated_at": datetime.now().isoformat(),
            "summary": {
                "total_executions": self.execution_counter,
                "total_errors": self.error_counter,
                "error_rate": self.error_counter / self.execution_counter if self.execution_counter > 0 else 0
            },
            "metrics": {},
            "alerts": {
                "active": [
                    {
                        "id": a.alert_id,
                        "level": a.level,
                        "message": a.message,
                        "timestamp": a.timestamp.isoformat()
                    }
                    for a in self.alerts.values() if not a.resolved
                ],
                "resolved": [
                    {
                        "id": a.alert_id,
                        "level": a.level,
                        "message": a.message,
                        "duration_seconds": (a.resolved_at - a.timestamp).total_seconds() if a.resolved_at else 0
                    }
                    for a in self.alerts.values() if a.resolved
                ]
            }
        }
        
        # Add metric summaries
        for metric_name in self.metrics:
            report["metrics"][metric_name] = self.get_metric_summary(metric_name, window_seconds)
        
        return report
    
    def reset_counters(self):
        """Reset execution and error counters."""
        self.execution_counter = 0
        self.error_counter = 0
        self.last_reset_time = datetime.now()
        logger.info("Performance counters reset")
    
    async def shutdown(self):
        """Shutdown the performance monitor."""
        logger.info("Shutting down PerformanceMonitor")
        
        if self.collection_task:
            self.collection_task.cancel()
        
        logger.info("PerformanceMonitor shutdown complete")
    
    def start_execution(self, execution_id: str, test_type: str):
        """Start tracking an execution."""
        labels = {
            "execution_id": execution_id,
            "test_type": test_type
        }
        
        # Record the start time for duration calculation
        self._record_metric(MetricType.EXECUTION_TIME, time.time() * 1000, labels)
        
        logger.debug(f"Started tracking execution {execution_id} ({test_type})")
    
    def end_execution(self, execution_id: str, status: str):
        """End tracking an execution and record the total duration."""
        # We'll use this to record completion
        labels = {
            "execution_id": execution_id,
            "status": status
        }
        
        # This could be expanded to calculate actual duration if we stored start times
        logger.debug(f"Ended tracking execution {execution_id} (status: {status})")


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


def record_execution_time(execution_type: str, duration_ms: float, success: bool = True):
    """Convenience function to record execution time."""
    performance_monitor.record_execution_time(execution_type, duration_ms, success)


def record_api_latency(endpoint: str, latency_ms: float, status_code: int = 200):
    """Convenience function to record API latency."""
    performance_monitor.record_api_latency(endpoint, latency_ms, status_code)