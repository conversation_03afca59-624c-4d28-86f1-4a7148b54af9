"""
Execution Metrics for QAK Test Execution System

Calculates and analyzes execution metrics, performance trends,
and provides insights for optimization and reporting.
"""

from typing import Dict, Any, Optional, List, Tuple
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import statistics
from collections import defaultdict

from src.models.standard_result import StandardResult, TestType, ExecutionStatus

logger = logging.getLogger(__name__)


class MetricCategory(str, Enum):
    """Categories of execution metrics."""
    PERFORMANCE = "performance"
    RELIABILITY = "reliability"
    EFFICIENCY = "efficiency"
    QUALITY = "quality"
    USAGE = "usage"


class TrendDirection(str, Enum):
    """Trend directions for metrics."""
    IMPROVING = "improving"
    STABLE = "stable"
    DEGRADING = "degrading"
    UNKNOWN = "unknown"


@dataclass
class MetricPoint:
    """Single metric measurement point."""
    timestamp: datetime
    value: float
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MetricTrend:
    """Trend analysis for a metric."""
    direction: TrendDirection
    change_percent: float
    confidence: float  # 0.0 to 1.0
    data_points: int
    time_span_hours: float


@dataclass
class ExecutionInsight:
    """Performance insight derived from metrics."""
    insight_type: str
    title: str
    description: str
    severity: str  # info, warning, error
    suggested_actions: List[str]
    metrics_involved: List[str]
    impact_estimate: str


class ExecutionMetrics:
    """
    Calculates and analyzes test execution metrics.
    
    Features:
    - Performance metric calculation
    - Trend analysis and forecasting  
    - Execution pattern analysis
    - Bottleneck identification
    - Performance insights and recommendations
    - Comparative analysis between test types
    """
    
    def __init__(self, retention_hours: int = 168):  # 1 week default
        """
        Initialize execution metrics calculator.
        
        Args:
            retention_hours: Hours to retain metric data
        """
        self.retention_hours = retention_hours
        
        # Metric storage
        self.execution_history: List[StandardResult] = []
        self.metric_points: Dict[str, List[MetricPoint]] = defaultdict(list)
        
        # Analysis cache
        self.cached_trends: Dict[str, MetricTrend] = {}
        self.cached_insights: List[ExecutionInsight] = []
        self.cache_timestamp: Optional[datetime] = None
        self.cache_ttl_minutes = 15
        
        logger.info(f"ExecutionMetrics initialized (retention: {retention_hours}h)")
    
    def record_execution(self, result: StandardResult):
        """
        Record a test execution result for metric calculation.
        
        Args:
            result: Standardized execution result
        """
        try:
            # Add to execution history
            self.execution_history.append(result)
            self._cleanup_old_history()
            
            # Extract and record various metrics
            self._extract_performance_metrics(result)
            self._extract_reliability_metrics(result)
            self._extract_efficiency_metrics(result)
            self._extract_quality_metrics(result)
            
            # Invalidate cache
            self._invalidate_cache()
            
            logger.debug(f"Recorded execution metrics for {result.execution_id}")
            
        except Exception as e:
            logger.error(f"Failed to record execution metrics: {e}")
    
    def _extract_performance_metrics(self, result: StandardResult):
        """Extract performance-related metrics."""
        timestamp = result.completed_at or datetime.now()
        
        # Execution duration
        if result.duration_ms:
            self._record_metric_point(
                "execution_duration_ms",
                timestamp,
                result.duration_ms,
                {"test_type": result.test_type, "success": result.success}
            )
        
        # Steps per minute
        if result.duration_ms and result.steps:
            steps_per_minute = (len(result.steps) / result.duration_ms) * 60000
            self._record_metric_point(
                "steps_per_minute",
                timestamp,
                steps_per_minute,
                {"test_type": result.test_type}
            )
    
    def _extract_reliability_metrics(self, result: StandardResult):
        """Extract reliability-related metrics."""
        timestamp = result.completed_at or datetime.now()
        
        # Success rate (binary: 1 for success, 0 for failure)
        success_value = 1.0 if result.success else 0.0
        self._record_metric_point(
            "success_rate",
            timestamp,
            success_value,
            {"test_type": result.test_type}
        )
        
        # Error count
        error_count = len(result.errors) if result.errors else 0
        self._record_metric_point(
            "error_count",
            timestamp,
            error_count,
            {"test_type": result.test_type}
        )
    
    def _extract_efficiency_metrics(self, result: StandardResult):
        """Extract efficiency-related metrics."""
        timestamp = result.completed_at or datetime.now()
        
        # Actions per second
        if result.duration_ms and result.steps:
            actions_per_second = (len(result.steps) / result.duration_ms) * 1000
            self._record_metric_point(
                "actions_per_second",
                timestamp,
                actions_per_second,
                {"test_type": result.test_type}
            )
        
        # Artifact generation rate
        artifact_count = len(result.artifacts) if result.artifacts else 0
        self._record_metric_point(
            "artifacts_per_execution",
            timestamp,
            artifact_count,
            {"test_type": result.test_type}
        )
    
    def _extract_quality_metrics(self, result: StandardResult):
        """Extract quality-related metrics."""
        timestamp = result.completed_at or datetime.now()
        
        # Coverage score (placeholder - could be extracted from result metadata)
        coverage_score = result.metadata.get("coverage_score", 0.0) if result.metadata else 0.0
        if coverage_score > 0:
            self._record_metric_point(
                "coverage_score",
                timestamp,
                coverage_score,
                {"test_type": result.test_type}
            )
    
    def _record_metric_point(
        self, 
        metric_name: str, 
        timestamp: datetime, 
        value: float, 
        context: Dict[str, Any]
    ):
        """Record a metric data point."""
        point = MetricPoint(
            timestamp=timestamp,
            value=value,
            context=context
        )
        
        self.metric_points[metric_name].append(point)
        self._cleanup_old_metrics(metric_name)
    
    def _cleanup_old_history(self):
        """Remove execution history older than retention period."""
        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
        self.execution_history = [
            result for result in self.execution_history
            if (result.completed_at or result.started_at) >= cutoff_time
        ]
    
    def _cleanup_old_metrics(self, metric_name: str):
        """Remove metric points older than retention period."""
        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
        self.metric_points[metric_name] = [
            point for point in self.metric_points[metric_name]
            if point.timestamp >= cutoff_time
        ]
    
    def _invalidate_cache(self):
        """Invalidate analysis cache."""
        self.cached_trends.clear()
        self.cached_insights.clear()
        self.cache_timestamp = None
    
    def get_metric_summary(
        self, 
        metric_name: str, 
        hours: int = 24,
        test_type: Optional[TestType] = None
    ) -> Dict[str, Any]:
        """
        Get summary statistics for a metric.
        
        Args:
            metric_name: Name of metric to analyze
            hours: Hours to look back
            test_type: Filter by specific test type
            
        Returns:
            Dictionary with metric summary
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        points = [
            p for p in self.metric_points.get(metric_name, [])
            if p.timestamp >= cutoff_time and
            (test_type is None or p.context.get("test_type") == test_type)
        ]
        
        if not points:
            return {"metric_name": metric_name, "data_points": 0}
        
        values = [p.value for p in points]
        
        return {
            "metric_name": metric_name,
            "data_points": len(values),
            "min": min(values),
            "max": max(values),
            "mean": statistics.mean(values),
            "median": statistics.median(values),
            "std_dev": statistics.stdev(values) if len(values) > 1 else 0,
            "p95": self._percentile(values, 95),
            "p99": self._percentile(values, 99),
            "hours": hours,
            "test_type": test_type
        }
    
    def get_trend_analysis(
        self, 
        metric_name: str, 
        hours: int = 72,
        test_type: Optional[TestType] = None
    ) -> MetricTrend:
        """
        Analyze trend for a specific metric.
        
        Args:
            metric_name: Name of metric to analyze
            hours: Hours to analyze
            test_type: Filter by specific test type
            
        Returns:
            Trend analysis
        """
        cache_key = f"{metric_name}_{hours}_{test_type or 'all'}"
        
        # Check cache
        if (cache_key in self.cached_trends and 
            self.cache_timestamp and 
            (datetime.now() - self.cache_timestamp).total_seconds() < self.cache_ttl_minutes * 60):
            return self.cached_trends[cache_key]
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        points = [
            p for p in self.metric_points.get(metric_name, [])
            if p.timestamp >= cutoff_time and
            (test_type is None or p.context.get("test_type") == test_type)
        ]
        
        if len(points) < 2:
            trend = MetricTrend(
                direction=TrendDirection.UNKNOWN,
                change_percent=0.0,
                confidence=0.0,
                data_points=len(points),
                time_span_hours=hours
            )
        else:
            trend = self._calculate_trend(points, hours)
        
        # Cache result
        self.cached_trends[cache_key] = trend
        self.cache_timestamp = datetime.now()
        
        return trend
    
    def _calculate_trend(self, points: List[MetricPoint], hours: int) -> MetricTrend:
        """Calculate trend from metric points."""
        if len(points) < 2:
            return MetricTrend(
                direction=TrendDirection.UNKNOWN,
                change_percent=0.0,
                confidence=0.0,
                data_points=len(points),
                time_span_hours=hours
            )
        
        # Sort by timestamp
        sorted_points = sorted(points, key=lambda p: p.timestamp)
        
        # Calculate linear regression slope
        x_values = [(p.timestamp - sorted_points[0].timestamp).total_seconds() for p in sorted_points]
        y_values = [p.value for p in sorted_points]
        
        slope = self._linear_regression_slope(x_values, y_values)
        
        # Calculate percentage change
        first_value = sorted_points[0].value
        last_value = sorted_points[-1].value
        
        if first_value != 0:
            change_percent = ((last_value - first_value) / abs(first_value)) * 100
        else:
            change_percent = 0.0
        
        # Determine direction
        if abs(change_percent) < 5:  # Less than 5% change is considered stable
            direction = TrendDirection.STABLE
        elif change_percent > 0:
            direction = TrendDirection.IMPROVING if slope > 0 else TrendDirection.DEGRADING
        else:
            direction = TrendDirection.DEGRADING if slope < 0 else TrendDirection.IMPROVING
        
        # Calculate confidence based on data points and consistency
        confidence = min(1.0, len(points) / 50.0)  # More points = higher confidence
        
        return MetricTrend(
            direction=direction,
            change_percent=change_percent,
            confidence=confidence,
            data_points=len(points),
            time_span_hours=hours
        )
    
    def _linear_regression_slope(self, x_values: List[float], y_values: List[float]) -> float:
        """Calculate slope of linear regression."""
        if len(x_values) != len(y_values) or len(x_values) < 2:
            return 0.0
        
        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x_squared = sum(x * x for x in x_values)
        
        denominator = n * sum_x_squared - sum_x * sum_x
        if denominator == 0:
            return 0.0
        
        return (n * sum_xy - sum_x * sum_y) / denominator
    
    def get_performance_insights(self, hours: int = 24) -> List[ExecutionInsight]:
        """
        Generate performance insights based on metrics analysis.
        
        Args:
            hours: Hours to analyze
            
        Returns:
            List of insights and recommendations
        """
        # Check cache
        if (self.cached_insights and 
            self.cache_timestamp and 
            (datetime.now() - self.cache_timestamp).total_seconds() < self.cache_ttl_minutes * 60):
            return self.cached_insights
        
        insights = []
        
        # Analyze execution time trends
        duration_trend = self.get_trend_analysis("execution_duration_ms", hours)
        if duration_trend.direction == TrendDirection.DEGRADING and duration_trend.confidence > 0.5:
            insights.append(ExecutionInsight(
                insight_type="performance_degradation",
                title="Execution Times Increasing",
                description=f"Test execution times have increased by {abs(duration_trend.change_percent):.1f}% over the last {hours} hours",
                severity="warning",
                suggested_actions=[
                    "Review recent changes that might impact performance",
                    "Check browser pool utilization",
                    "Consider optimizing test scenarios"
                ],
                metrics_involved=["execution_duration_ms"],
                impact_estimate="Medium - Users may experience slower test results"
            ))
        
        # Analyze error rates
        error_trend = self.get_trend_analysis("error_count", hours)
        if error_trend.direction == TrendDirection.DEGRADING and error_trend.confidence > 0.3:
            insights.append(ExecutionInsight(
                insight_type="reliability_issue",
                title="Error Rate Increasing",
                description=f"Test error rates have increased over the last {hours} hours",
                severity="error",
                suggested_actions=[
                    "Investigate recent failures",
                    "Check infrastructure stability",
                    "Review test environment configuration"
                ],
                metrics_involved=["error_count", "success_rate"],
                impact_estimate="High - Test reliability is compromised"
            ))
        
        # Analyze efficiency metrics
        efficiency_summary = self.get_metric_summary("actions_per_second", hours)
        if efficiency_summary["data_points"] > 0 and efficiency_summary["mean"] < 2.0:
            insights.append(ExecutionInsight(
                insight_type="efficiency_opportunity",
                title="Low Test Execution Efficiency",
                description=f"Average actions per second is {efficiency_summary['mean']:.2f}, below recommended threshold",
                severity="info",
                suggested_actions=[
                    "Optimize test step timing",
                    "Consider parallel execution",
                    "Review browser pool configuration"
                ],
                metrics_involved=["actions_per_second"],
                impact_estimate="Medium - Tests could run faster with optimization"
            ))
        
        # Cache results
        self.cached_insights = insights
        self.cache_timestamp = datetime.now()
        
        return insights
    
    def get_comparative_analysis(
        self, 
        metric_name: str, 
        hours: int = 24
    ) -> Dict[TestType, Dict[str, Any]]:
        """
        Compare metric across different test types.
        
        Args:
            metric_name: Metric to compare
            hours: Hours to analyze
            
        Returns:
            Dictionary with comparison by test type
        """
        comparison = {}
        
        for test_type in TestType:
            summary = self.get_metric_summary(metric_name, hours, test_type)
            if summary["data_points"] > 0:
                comparison[test_type] = summary
        
        return comparison
    
    def get_execution_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get comprehensive execution statistics.
        
        Args:
            hours: Hours to analyze
            
        Returns:
            Dictionary with execution statistics
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_executions = [
            result for result in self.execution_history
            if (result.completed_at or result.started_at) >= cutoff_time
        ]
        
        if not recent_executions:
            return {"total_executions": 0, "hours": hours}
        
        # Basic counts
        total_executions = len(recent_executions)
        successful_executions = sum(1 for r in recent_executions if r.success)
        failed_executions = total_executions - successful_executions
        
        # By test type
        by_test_type = defaultdict(int)
        for result in recent_executions:
            by_test_type[result.test_type] += 1
        
        # Duration statistics
        durations = [r.duration_ms for r in recent_executions if r.duration_ms]
        
        return {
            "total_executions": total_executions,
            "successful_executions": successful_executions,
            "failed_executions": failed_executions,
            "success_rate": successful_executions / total_executions if total_executions > 0 else 0,
            "by_test_type": dict(by_test_type),
            "duration_stats": {
                "min_ms": min(durations) if durations else 0,
                "max_ms": max(durations) if durations else 0,
                "avg_ms": statistics.mean(durations) if durations else 0,
                "median_ms": statistics.median(durations) if durations else 0
            },
            "hours": hours
        }
    
    def _percentile(self, values: List[float], percentile: float) -> float:
        """Calculate percentile value."""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = (percentile / 100) * (len(sorted_values) - 1)
        
        if index.is_integer():
            return sorted_values[int(index)]
        else:
            lower = sorted_values[int(index)]
            upper = sorted_values[int(index) + 1]
            fraction = index - int(index)
            return lower + fraction * (upper - lower)
    
    def export_metrics(self, hours: int = 24) -> Dict[str, Any]:
        """
        Export all metrics for external analysis.
        
        Args:
            hours: Hours of data to export
            
        Returns:
            Dictionary with all metric data
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "hours": hours,
            "metrics": {}
        }
        
        for metric_name, points in self.metric_points.items():
            recent_points = [
                {
                    "timestamp": p.timestamp.isoformat(),
                    "value": p.value,
                    "context": p.context
                }
                for p in points if p.timestamp >= cutoff_time
            ]
            
            if recent_points:
                export_data["metrics"][metric_name] = {
                    "data_points": recent_points,
                    "summary": self.get_metric_summary(metric_name, hours)
                }
        
        return export_data


# Global execution metrics instance
execution_metrics = ExecutionMetrics()


def record_execution(result: StandardResult):
    """Convenience function to record execution metrics."""
    execution_metrics.record_execution(result)


def get_performance_insights(hours: int = 24) -> List[ExecutionInsight]:
    """Convenience function to get performance insights."""
    return execution_metrics.get_performance_insights(hours) 