"""
Response Translation Service for AgentQA
Handles post-execution translation of browser agent responses using the new unified LLM architecture
"""

import os
from typing import Optional
# Use the new TextTranslator from unified architecture
from src.services.llm.use_cases import TextTranslator
import logging

logger = logging.getLogger(__name__)

class ResponseTranslationService:
    """Service for translating browser agent responses using the new unified LLM architecture."""
    
    def __init__(self):
        """Initialize the translation service with new architecture."""
        self.text_translator = TextTranslator()
    
    def translate_agent_response(
        self, 
        response_text: str, 
        target_language: str = "es",
        context: Optional[str] = None
    ) -> str:
        """
        Translate browser agent response using the new unified architecture.
        
        Args:
            response_text: Original response from browser agent
            target_language: Target language code ('es', 'en')
            context: Optional context about the task
            
        Returns:
            Translated response maintaining technical accuracy
        """
        if target_language == "en" or not response_text.strip():
            return response_text
            
        try:
            # Use the new TextTranslator with versioned prompts
            result = self.text_translator.translate(
                text=response_text,
                target_language=target_language,
                source_language="en",
                preserve_technical_terms=True
            )
            
            if result["success"]:
                return result["translated_text"]
            else:
                logger.error(f"Translation failed: {result.get('error', 'Unknown error')}")
                return response_text  # Fallback to original
                
        except Exception as e:
            logger.error(f"Error translating agent response: {e}")
            return response_text  # Fallback to original
            
        except Exception as e:
            logger.error(f"Translation failed: {e}")
            # Fallback: return original response with language note
            return f"[Response in English - Translation failed]\n{response_text}"
    
    def _build_translation_prompt(
        self, 
        text: str, 
        target_language: str, 
        context: Optional[str]
    ) -> str:
        """Build translation prompt maintaining technical context."""
        
        language_names = {
            "es": "Spanish",
            "en": "English"
        }
        
        target_lang_name = language_names.get(target_language, target_language)
        
        prompt = f"""
You are a professional translator specializing in software testing and browser automation.

Translate the following browser automation agent response to {target_lang_name}.

CRITICAL REQUIREMENTS:
1. Preserve ALL technical terms (CSS selectors, XPaths, element IDs, etc.)
2. Keep URLs, error codes, and technical identifiers unchanged
3. Maintain the structure and formatting of the response
4. Preserve any code snippets or technical instructions exactly
5. Only translate user-facing messages and descriptions

Original Response:
{text}

Translated Response:"""
        
        return prompt
