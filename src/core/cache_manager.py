"""
Cache Manager for QAK Test Execution System

Provides an in-memory LRU cache with TTL support. This first version focuses
on performance-critical look-ups (e.g. configuration resolutions, recent
execution results). Additional persistent and distributed layers can be added
later without breaking the public API.
"""

from __future__ import annotations

import asyncio
import logging
from collections import OrderedDict
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """A single item stored in the cache."""

    value: Any
    ttl_seconds: Optional[int]
    created_at: datetime = field(default_factory=datetime.utcnow)

    def is_expired(self) -> bool:
        if self.ttl_seconds is None:
            return False
        return datetime.utcnow() - self.created_at > timedelta(seconds=self.ttl_seconds)


class CacheManager:
    """Simple in-memory cache with TTL and LRU eviction."""

    def __init__(self, max_items: int = 5_000, cleanup_interval: int = 60):
        """Create a new cache.

        Args:
            max_items: Maximum number of items to store at once. When exceeded,
                the least-recently-used items are evicted.
            cleanup_interval: Seconds between background cleanup runs to remove
                expired items.
        """
        self.max_items = max_items
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._hits = 0
        self._misses = 0
        self._evictions = 0
        self._cleanup_interval = cleanup_interval
        self._cleanup_task: Optional[asyncio.Task] = None

    # ---------------------------------------------------------------------
    # Public API
    # ---------------------------------------------------------------------

    async def initialize(self):
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop(), name="cache-cleanup")
            logger.info("CacheManager background cleanup task started")

    async def shutdown(self):
        if self._cleanup_task is not None:
            self._cleanup_task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._cleanup_task
            self._cleanup_task = None
            logger.info("CacheManager cleanup task stopped")

    def get(self, key: str, default: Any = None) -> Any:
        """Return cached value or *default* if not found/expired."""
        entry = self._cache.get(key)
        if entry is None:
            self._misses += 1
            return default
        if entry.is_expired():
            # Expired -> remove and miss
            self._misses += 1
            self._cache.pop(key, None)
            return default
        # Move to end -> mark as recently used
        self._cache.move_to_end(key)
        self._hits += 1
        return entry.value

    def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None):
        """Store a value in cache."""
        # If key exists update; else may need eviction
        if key in self._cache:
            self._cache.move_to_end(key)
        self._cache[key] = CacheEntry(value=value, ttl_seconds=ttl_seconds)
        # Evict LRU entries if we exceed capacity
        while len(self._cache) > self.max_items:
            self._cache.popitem(last=False)
            self._evictions += 1

    def delete(self, key: str):
        self._cache.pop(key, None)

    def clear(self):
        self._cache.clear()

    # ------------------------------------------------------------------
    # Stats & helpers
    # ------------------------------------------------------------------

    def stats(self) -> Dict[str, Any]:
        total = self._hits + self._misses
        hit_rate = self._hits / total if total else 0
        return {
            "items": len(self._cache),
            "hits": self._hits,
            "misses": self._misses,
            "evictions": self._evictions,
            "hit_rate": hit_rate,
        }

    # ------------------------------------------------------------------
    # Internals
    # ------------------------------------------------------------------

    async def _cleanup_loop(self):
        """Periodically remove expired entries."""
        try:
            while True:
                await asyncio.sleep(self._cleanup_interval)
                self._remove_expired()
        except asyncio.CancelledError:
            pass

    def _remove_expired(self):
        keys_to_remove = [k for k, v in self._cache.items() if v.is_expired()]
        for k in keys_to_remove:
            self._cache.pop(k, None)
            self._evictions += 1
        if keys_to_remove:
            logger.debug("Cache cleanup removed %d expired items", len(keys_to_remove))


# Singleton instance
cache_manager = CacheManager()


async def cache_result(result: StandardResult, ttl_seconds: int = 3600):
    """Convenience function to cache a result."""
    await cache_manager.cache_result(result, ttl_seconds)


async def get_cached_result(execution_id: str) -> Optional[StandardResult]:
    """Convenience function to get cached result."""
    return await cache_manager.get_cached_result(execution_id)


def cache_key(*args, **kwargs) -> str:
    """Convenience function to generate cache key."""
    return cache_manager.generate_cache_key(*args, **kwargs) 