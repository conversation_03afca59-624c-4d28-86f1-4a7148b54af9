"""Container de servicios para inyección de dependencias."""

from src.core.playwright_codegen_service import PlaywrightCodegenService
from src.core.codegen_executor_service import CodegenExecutorService
from src.database.repositories.codegen_repository import CodegenSessionRepository

# Instancias globales únicas de los servicios
_codegen_service = None
_executor_service = None

def get_codegen_service() -> PlaywrightCodegenService:
    """Obtiene la instancia singleton del servicio de Playwright CodeGen."""
    global _codegen_service
    if _codegen_service is None:
        repo = CodegenSessionRepository()
        _codegen_service = PlaywrightCodegenService(codegen_repo=repo)
    return _codegen_service

def get_executor_service() -> CodegenExecutorService:
    """Obtiene la instancia singleton del servicio de ejecución."""
    global _executor_service
    if _executor_service is None:
        # Pasar la misma instancia del servicio de codegen
        _executor_service = CodegenExecutorService(get_codegen_service())
    return _executor_service
