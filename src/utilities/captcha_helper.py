"""
CAPTCHA Helper for AgentQA
Specialized configuration and utilities for handling Google CAPTCHAs
"""

import os
import logging
from typing import Optional, Dict, Any, List
from src.utilities.browser_helper import BrowserHelperConfig, create_and_run_agent
# Configurar path para browser_use local
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../libs'))

from browser_use import BrowserSession, BrowserProfile

logger = logging.getLogger(__name__)

class CaptchaConfig(BrowserHelperConfig):
    """Configuración especializada para manejo de CAPTCHAs"""
    
    def __init__(self, **kwargs):
        # Configuración base optimizada para CAPTCHAs
        defaults = {
            # Configuración stealth
            'stealth': True,
            'headless': False,  # Modo visible es mejor para CAPTCHAs
            
            # Configuración de tiempos más humanos
            'wait_between_actions': 2.0,
            'minimum_wait_page_load_time': 1.5,
            'wait_for_network_idle_page_load_time': 3.0,
            'maximum_wait_page_load_time': 20.0,
            
            # Configuración de navegador realista
            'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
            'viewport': {'width': 1366, 'height': 768},
            'locale': 'en-US',
            'timezone_id': 'America/New_York',
            
            # Perfil persistente para mantener cookies
            'user_data_dir': '~/.config/browseruse/profiles/captcha_profile',
            'profile_directory': 'CaptchaSession',
            
            # Dominios permitidos para Google/reCAPTCHA
            'allowed_domains': [
                '*.google.com',
                '*.recaptcha.net', 
                '*.gstatic.com',
                '*.googleusercontent.com'
            ],
            
            # Configuración visual
            'highlight_elements': True,
            'viewport_expansion': 300,
            'deterministic_rendering': False,  # Deshabilitar para parecer más humano
            
            # Configuración de memoria y visión
            'use_vision': True,
            'enable_memory': True,
            'max_steps': 50,  # Menos pasos para evitar comportamiento robótico
        }
        
        # Merge with provided kwargs
        merged_config = {**defaults, **kwargs}
        super().__init__(**merged_config)


def create_captcha_browser_profile(proxy_config: Optional[Dict] = None) -> BrowserProfile:
    """Crear perfil de navegador optimizado para CAPTCHAs"""
    
    profile_config = {
        'stealth': True,
        'headless': False,
        'user_data_dir': '~/.config/browseruse/profiles/captcha_profile',
        'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
        'viewport': {'width': 1366, 'height': 768},
        'locale': 'en-US',
        'timezone_id': 'America/New_York',
        'wait_between_actions': 2.0,
        'minimum_wait_page_load_time': 1.5,
        'wait_for_network_idle_page_load_time': 3.0,
        'maximum_wait_page_load_time': 20.0,
        'highlight_elements': True,
        'viewport_expansion': 300,
        'deterministic_rendering': False,
    }
    
    # Agregar configuración de proxy si se proporciona
    if proxy_config:
        profile_config['proxy'] = proxy_config
        logger.info(f"Using proxy configuration for CAPTCHA handling")
    
    return BrowserProfile(**profile_config)


async def execute_with_captcha_handling(
    scenario: str,
    controller_instance,
    api_key: Optional[str] = None,
    proxy_config: Optional[Dict] = None,
    max_retries: int = 3,
    **kwargs
) -> Any:
    """
    Ejecutar escenario con manejo especializado de CAPTCHAs
    
    Args:
        scenario: Escenario Gherkin a ejecutar
        controller_instance: Instancia del controlador de browser
        api_key: API key para el LLM
        proxy_config: Configuración de proxy opcional
        max_retries: Número máximo de reintentos si encuentra CAPTCHA
        **kwargs: Argumentos adicionales
    
    Returns:
        Resultado de la ejecución del agente
    """
    
    # Crear configuración especializada para CAPTCHAs
    captcha_config = CaptchaConfig(**kwargs)
    
    # Agregar configuración de proxy si se proporciona
    if proxy_config:
        captcha_config.proxy = proxy_config
    
    retry_count = 0
    last_error = None
    
    while retry_count < max_retries:
        try:
            logger.info(f"Attempting CAPTCHA-aware execution (attempt {retry_count + 1}/{max_retries})")
            
            # Agregar instrucciones específicas para CAPTCHA al escenario
            enhanced_scenario = enhance_scenario_for_captcha(scenario)
            
            # Ejecutar con configuración especializada
            result = await create_and_run_agent(
                scenario_text=enhanced_scenario,
                controller_instance=controller_instance,
                api_key=api_key,
                config=captcha_config,
                **kwargs
            )
            
            # Verificar si se completó exitosamente
            if result and hasattr(result, 'final_result'):
                logger.info("CAPTCHA-aware execution completed successfully")
                return result
            
            retry_count += 1
            
        except Exception as e:
            logger.warning(f"CAPTCHA handling attempt {retry_count + 1} failed: {str(e)}")
            last_error = e
            retry_count += 1
            
            # Esperar antes del siguiente intento
            if retry_count < max_retries:
                import asyncio
                await asyncio.sleep(5)  # Esperar 5 segundos entre intentos
    
    # Si todos los intentos fallaron
    logger.error(f"All CAPTCHA handling attempts failed. Last error: {last_error}")
    raise Exception(f"Failed to execute scenario after {max_retries} attempts. Last error: {last_error}")


def enhance_scenario_for_captcha(scenario: str) -> str:
    """Mejorar escenario con instrucciones específicas para manejo de CAPTCHA"""
    
    captcha_instructions = """
    
IMPORTANT CAPTCHA HANDLING INSTRUCTIONS:
- If you encounter a Google reCAPTCHA, try to solve it by:
  1. Looking for "I'm not a robot" checkbox and clicking it
  2. If image challenge appears, analyze the images carefully and select the correct ones
  3. Wait for verification to complete before proceeding
  4. If CAPTCHA fails, wait a few seconds and try again
- Use human-like timing between actions (2-3 seconds between clicks)
- If multiple CAPTCHAs appear, handle each one patiently
- Do not proceed to next steps until CAPTCHA is completely resolved
    
    """
    
    return captcha_instructions + scenario


# Configuraciones predefinidas para diferentes tipos de sitios
CAPTCHA_CONFIGS = {
    'google_search': {
        'allowed_domains': ['*.google.com', '*.recaptcha.net'],
        'wait_between_actions': 2.5,
        'max_steps': 30,
    },
    
    'google_services': {
        'allowed_domains': ['*.google.com', '*.googleapis.com', '*.recaptcha.net'],
        'wait_between_actions': 3.0,
        'max_steps': 40,
        'use_vision': True,
    },
    
    'general_web': {
        'allowed_domains': None,  # Sin restricciones
        'wait_between_actions': 2.0,
        'max_steps': 50,
    }
}


def get_captcha_config(site_type: str = 'general_web', **overrides) -> CaptchaConfig:
    """Obtener configuración predefinida para manejo de CAPTCHAs"""
    
    if site_type not in CAPTCHA_CONFIGS:
        logger.warning(f"Unknown site type '{site_type}', using 'general_web'")
        site_type = 'general_web'
    
    base_config = CAPTCHA_CONFIGS[site_type].copy()
    base_config.update(overrides)
    
    return CaptchaConfig(**base_config)


# Funciones de utilidad para integración con AgentQA
def create_captcha_aware_browser_session(proxy_config: Optional[Dict] = None) -> BrowserSession:
    """Crear sesión de navegador optimizada para CAPTCHAs"""
    
    profile = create_captcha_browser_profile(proxy_config)
    return BrowserSession(browser_profile=profile, keep_alive=True)


__all__ = [
    'CaptchaConfig',
    'create_captcha_browser_profile', 
    'execute_with_captcha_handling',
    'enhance_scenario_for_captcha',
    'get_captcha_config',
    'create_captcha_aware_browser_session',
    'CAPTCHA_CONFIGS'
]
