"""
Response Transformers for QAK - R2 Artifact Integration

Utilities for transforming backend responses for frontend consumption,
especially for handling R2-only artifacts and screenshot URL conversion.
"""

import json
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
import dataclasses
import enum

logger = logging.getLogger(__name__)


def convert_screenshot_paths_to_urls(data: Union[Dict, List, Any], base_url: str = "http://localhost:8000") -> Union[Dict, List, Any]:
    """
    Convert screenshot file paths to accessible URLs, supporting both local and R2-only artifacts.
    
    This function handles the migration from local file storage to R2-only mode by:
    1. Converting local file paths to the /api/artifacts/{artifact_id} endpoint
    2. Converting R2-only virtual paths to the same endpoint
    3. Leaving existing HTTP URLs unchanged
    
    Args:
        data: Data structure containing screenshot paths
        base_url: Base URL for the API server
        
    Returns:
        Transformed data with accessible URLs
    """
    # Handle dataclass objects (like TestStep)
    if dataclasses.is_dataclass(data) and not isinstance(data, type):
        # Convert dataclass to dict, transform, then reconstruct
        data_dict = dataclasses.asdict(data)
        transformed_dict = convert_screenshot_paths_to_urls(data_dict, base_url)
        # Return the same dataclass type with transformed data
        return type(data)(**transformed_dict)
    
    elif isinstance(data, dict):
        result = {}
        for key, value in data.items():
            if key in ['screenshot', 'screenshot_path', 'screenshot_url', 'file_path'] and value:
                result[key] = _convert_single_screenshot_path(value, base_url)
            elif key == 'screenshots' and isinstance(value, list):
                result[key] = [_convert_single_screenshot_path(path, base_url) for path in value if path]
            elif key == 'steps' and isinstance(value, list):
                # Handle steps array with screenshot information
                result[key] = [convert_screenshot_paths_to_urls(step, base_url) for step in value]
            else:
                result[key] = convert_screenshot_paths_to_urls(value, base_url)
        return result
    
    elif isinstance(data, list):
        return [convert_screenshot_paths_to_urls(item, base_url) for item in data]
    
    else:
        return data


def _convert_single_screenshot_path(path: str, base_url: str) -> str:
    """
    Convert a single screenshot path to an accessible URL.
    
    Args:
        path: Original path (local file, R2-only virtual path, or URL)
        base_url: Base URL for the API server
        
    Returns:
        Accessible URL
    """
    if not path:
        return path
    
    # Already a full HTTP URL, return as-is
    if path.startswith(('http://', 'https://')):
        return path
    
    # R2-only virtual path format: r2-only://execution_id/artifact_filename
    if path.startswith('r2-only://'):
        # Extract artifact ID from the filename
        # Format: r2-only://execution_id/artifact_id_step_name_screenshot.png
        try:
            path_parts = path.replace('r2-only://', '').split('/')
            if len(path_parts) >= 2:
                filename = path_parts[-1]  # Get the filename
                # Extract artifact ID (first part before underscore)
                artifact_id_parts = filename.split('_')
                if len(artifact_id_parts) >= 1:
                    # The artifact ID is typically the first 32-character hash
                    potential_id = artifact_id_parts[0]
                    if len(potential_id) == 32:  # MD5 hash length
                        return f"{base_url}/api/artifacts/{potential_id}"
                    
                # Fallback: try to find a 32-character hash in the filename
                for part in artifact_id_parts:
                    if len(part) == 32 and part.isalnum():
                        return f"{base_url}/api/artifacts/{part}"
        except Exception as e:
            logger.warning(f"Failed to parse R2-only path {path}: {e}")
            return path
    
    # Local file path (legacy format)
    if path.startswith('/') or '\\' in path:
        # Try to extract artifact ID from local path
        # Format: artifacts/2025/07/01/execution_id/screenshot/artifact_id_step_name_screenshot.png
        try:
            path_obj = Path(path)
            filename = path_obj.name
            
            # Extract artifact ID from filename
            artifact_id_parts = filename.split('_')
            for part in artifact_id_parts:
                if len(part) == 32 and part.isalnum():
                    return f"{base_url}/api/artifacts/{part}"
                    
            # Fallback: use filename without extension as potential ID
            stem = path_obj.stem
            if len(stem) == 32 and stem.isalnum():
                return f"{base_url}/api/artifacts/{stem}"
                
        except Exception as e:
            logger.warning(f"Failed to parse local path {path}: {e}")
    
    # Relative path or unrecognized format - try to extract artifact ID
    try:
        # Look for 32-character alphanumeric strings (MD5 hash format)
        import re
        artifact_id_match = re.search(r'[a-f0-9]{32}', path)
        if artifact_id_match:
            artifact_id = artifact_id_match.group()
            return f"{base_url}/api/artifacts/{artifact_id}"
    except Exception as e:
        logger.warning(f"Failed to extract artifact ID from path {path}: {e}")
    
    # Fallback: return original path
    logger.debug(f"Could not convert path to URL, returning original: {path}")
    return path


def transform_backend_response_to_frontend_format(response_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transform backend response format to frontend-compatible format.
    
    This includes:
    - Converting datetime objects to ISO strings
    - Converting screenshot paths to URLs
    - Ensuring all fields are JSON serializable
    
    Args:
        response_data: Raw backend response data
        
    Returns:
        Frontend-compatible response data
    """
    # First clean the data for JSON serialization
    cleaned_data = clean_data_for_json_serialization(response_data)
    
    # Then convert screenshot paths to URLs
    transformed_data = convert_screenshot_paths_to_urls(cleaned_data)
    
    # Add any additional frontend-specific transformations here
    if isinstance(transformed_data, dict):
        # Ensure start_time and end_time are present for frontend compatibility
        if 'execution_time' in transformed_data and 'start_time' not in transformed_data:
            transformed_data['start_time'] = transformed_data.get('created_at', 
                datetime.now().isoformat())
        
        if 'execution_time' in transformed_data and 'end_time' not in transformed_data:
            # Calculate end_time from start_time + execution_time if possible
            start_time = transformed_data.get('start_time')
            execution_time = transformed_data.get('execution_time', 0)
            if start_time and execution_time:
                try:
                    start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                    end_dt = start_dt + datetime.timedelta(seconds=execution_time)
                    transformed_data['end_time'] = end_dt.isoformat()
                except Exception as e:
                    logger.warning(f"Failed to calculate end_time: {e}")
                    transformed_data['end_time'] = start_time
    
    return transformed_data


def clean_data_for_json_serialization(data: Any) -> Any:
    """
    Clean data structure to ensure JSON serialization compatibility.
    
    Handles:
    - datetime objects -> ISO strings
    - Path objects -> strings
    - Sets -> lists
    - Complex objects -> dictionaries
    - None values preservation
    
    Args:
        data: Data to clean
        
    Returns:
        JSON-serializable data
    """
    if data is None:
        return None
    
    elif isinstance(data, datetime):
        return data.isoformat()
    
    elif isinstance(data, Path):
        return str(data)
    
    elif isinstance(data, enum.Enum):
        return data.value if hasattr(data, 'value') else data.name
    
    elif isinstance(data, set):
        return list(data)
    
    elif isinstance(data, dict):
        cleaned = {}
        for key, value in data.items():
            # Ensure keys are strings
            clean_key = str(key) if not isinstance(key, str) else key
            cleaned[clean_key] = clean_data_for_json_serialization(value)
        return cleaned
    
    elif isinstance(data, (list, tuple)):
        return [clean_data_for_json_serialization(item) for item in data]
    
    elif hasattr(data, '__dict__'):
        # Handle objects with attributes
        try:
            if hasattr(data, 'model_dump'):
                # Pydantic models
                return clean_data_for_json_serialization(data.model_dump())
            elif hasattr(data, 'dict'):
                # Some ORM models
                return clean_data_for_json_serialization(data.dict())
            else:
                # Generic objects
                return clean_data_for_json_serialization(data.__dict__)
        except Exception as e:
            logger.warning(f"Failed to serialize object {type(data)}: {e}")
            return str(data)
    
    elif isinstance(data, (str, int, float, bool)):
        return data
    
    else:
        # Fallback: convert to string
        try:
            # Try JSON serialization test
            json.dumps(data)
            return data
        except (TypeError, ValueError):
            logger.debug(f"Converting non-serializable type {type(data)} to string")
            return str(data)


def ensure_artifact_urls(data: Dict[str, Any], artifact_service = None) -> Dict[str, Any]:
    """
    Ensure all artifact references have proper URLs for frontend consumption.
    
    This function works with the ArtifactService to convert artifact IDs
    and paths to proper URLs that the frontend can use to display images.
    
    Args:
        data: Response data containing artifact references
        artifact_service: Optional ArtifactService instance for database lookups
        
    Returns:
        Data with proper artifact URLs
    """
    # Convert screenshot paths using the standard transformer
    data_with_urls = convert_screenshot_paths_to_urls(data)
    
    # If we have an artifact service, we could do additional lookups here
    # to ensure all artifact IDs have proper URLs, but for now the path
    # conversion should handle most cases
    
    return data_with_urls


def format_step_data_for_frontend(steps: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Format step data specifically for frontend Rich Results Viewer consumption.
    
    Args:
        steps: List of step dictionaries
        
    Returns:
        Frontend-formatted step data
    """
    formatted_steps = []
    
    for i, step in enumerate(steps):
        formatted_step = clean_data_for_json_serialization(step)
        
        # Ensure step has required fields for frontend
        if 'step_number' not in formatted_step:
            formatted_step['step_number'] = i + 1
        
        if 'action_type' not in formatted_step and 'action' in formatted_step:
            # Try to extract action type from action data
            action = formatted_step.get('action', {})
            if isinstance(action, dict):
                formatted_step['action_type'] = action.get('type', 'unknown')
            else:
                formatted_step['action_type'] = 'unknown'
        
        # Ensure screenshot URL is properly formatted
        if 'screenshot' in formatted_step:
            formatted_step['screenshot'] = _convert_single_screenshot_path(
                formatted_step['screenshot'], 
                os.getenv('API_BASE_URL', 'http://localhost:8000')
            )
        
        formatted_steps.append(formatted_step)
    
    return formatted_steps


# Helper function for R2-only mode detection
def is_r2_only_mode() -> bool:
    """Check if the system is running in R2-only mode."""
    return os.getenv('ARTIFACT_R2_ONLY_MODE', 'false').lower() == 'true'


# Helper function for artifact URL generation
def generate_artifact_url(artifact_id: str, base_url: Optional[str] = None) -> str:
    """
    Generate a proper artifact URL for the given artifact ID.
    
    Args:
        artifact_id: The artifact ID
        base_url: Optional base URL (defaults to environment or localhost)
        
    Returns:
        Complete artifact URL
    """
    if not base_url:
        base_url = os.getenv('API_BASE_URL', 'http://localhost:8000')
    
    return f"{base_url}/api/artifacts/{artifact_id}"
