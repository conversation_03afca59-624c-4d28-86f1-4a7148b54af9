import os
import json
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional, Union

# Import Environment for multi-environment support
try:
    from ..database.models.environment import Environment
except ImportError:
    # Fallback if Environment is not available
    Environment = None


class TestCase:
    """
    Representa un caso de prueba individual dentro de una suite de pruebas.
    """
    def __init__(self,
                 name: str,
                 description: str = "",
                 instrucciones: str = "",
                 historia_de_usuario: str = "",
                 gherkin: str = "",
                 url: str = "",
                 tags: List[str] = None,
                 test_id: str = None):
        self.test_id = test_id or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.instrucciones = instrucciones
        self.historia_de_usuario = historia_de_usuario
        self.gherkin = gherkin
        self.url = url
        self.tags = tags or []
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
        self.history_files = []  # Lista de archivos JSON de historial de ejecución
        self.status = "Not Executed"  # Not Executed, Passed, Failed, Blocked
        self.last_execution = None
        self.code = ""  # Código de automatización generado
        self.framework = ""  # Framework utilizado para la automatización

    def to_dict(self) -> Dict[str, Any]:
        """Convierte el caso de prueba a un diccionario para serialización."""
        return {
            "test_id": self.test_id,
            "name": self.name,
            "description": self.description,
            "instrucciones": self.instrucciones,
            "historia_de_usuario": self.historia_de_usuario,
            "gherkin": self.gherkin,
            "url": self.url,
            "tags": self.tags,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "history_files": self.history_files,
            "status": self.status,
            "last_execution": self.last_execution,
            "code": self.code,
            "framework": self.framework
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestCase':
        """Crea un caso de prueba a partir de un diccionario."""
        # Compatibilidad hacia atrás: convertir steps y expected_results a instrucciones e historia_de_usuario
        instrucciones = data.get("instrucciones", "")
        historia_de_usuario = data.get("historia_de_usuario", "")

        # Si no hay instrucciones pero hay steps, convertir steps a instrucciones
        if not instrucciones and "steps" in data:
            steps = data.get("steps", [])
            if steps:
                instrucciones = "\n".join([f"{i+1}. {step}" for i, step in enumerate(steps)])

        # Si no hay historia_de_usuario pero hay expected_results, convertir a historia_de_usuario
        if not historia_de_usuario and "expected_results" in data:
            expected_results = data.get("expected_results", [])
            if expected_results:
                historia_de_usuario = "Resultados esperados:\n" + "\n".join([f"- {result}" for result in expected_results])

        test_case = cls(
            name=data["name"],
            description=data.get("description", ""),
            instrucciones=instrucciones,
            historia_de_usuario=historia_de_usuario,
            gherkin=data.get("gherkin", ""),
            url=data.get("url", ""),
            tags=data.get("tags", []),
            test_id=data.get("test_id")
        )
        test_case.created_at = data.get("created_at", test_case.created_at)
        test_case.updated_at = data.get("updated_at", test_case.updated_at)
        test_case.history_files = data.get("history_files", [])
        test_case.status = data.get("status", "Not Executed")
        test_case.last_execution = data.get("last_execution")
        test_case.code = data.get("code", "")
        test_case.framework = data.get("framework", "")
        return test_case

    def add_history_file(self, file_path: str) -> None:
        """Añade un archivo de historial de ejecución al caso de prueba."""
        if file_path not in self.history_files:
            self.history_files.append(file_path)
            self.updated_at = datetime.now().isoformat()

    def update_status(self, status: str, execution_time: str = None) -> None:
        """Actualiza el estado del caso de prueba."""
        self.status = status
        self.last_execution = execution_time or datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()


class TestSuite:
    """
    Representa una suite de pruebas que contiene múltiples casos de prueba.
    """
    def __init__(self,
                 name: str,
                 description: str = "",
                 tags: List[str] = None,
                 execution_times: int = 1,
                 suite_id: str = None):
        self.suite_id = suite_id or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.tags = tags if tags is not None else []
        self.test_cases: Dict[str, TestCase] = {}
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
        self.execution_times = execution_times

        # Inicializar estadísticas de ejecución
        self.total_executions = 0
        self.total_test_runs = 0
        self.successful_test_runs = 0
        self.failed_test_runs = 0
        self.success_rate = 0.0
        self.last_execution_at: Optional[str] = None
        self.execution_summary: Dict[str, Any] = {}

    def set_execution_times(self, times: int) -> None:
        """Sets the number of times this suite should be executed."""
        self.execution_times = max(1, times)  # Ensure at least 1 execution
        self.updated_at = datetime.now().isoformat()

    def update_execution_statistics(self, total_tests: int, passed_tests: int, failed_tests: int, execution_rounds: int = 1) -> None:
        """Actualiza las estadísticas de ejecución de la suite.
        
        Args:
            total_tests: Número total de tests por iteración
            passed_tests: Número total de tests que pasaron en todas las iteraciones
            failed_tests: Número total de tests que fallaron en todas las iteraciones
            execution_rounds: Número de iteraciones ejecutadas
        """
        self.total_executions += execution_rounds
        self.total_test_runs += (total_tests * execution_rounds)
        self.successful_test_runs += passed_tests
        self.failed_test_runs += failed_tests
        self.last_execution_at = datetime.now().isoformat()
        
        # Calcular la tasa de éxito promedio
        if self.total_test_runs > 0:
            self.success_rate = round((self.successful_test_runs / self.total_test_runs) * 100, 2)
        else:
            self.success_rate = 0.0
            
        self.updated_at = datetime.now().isoformat()

    def get_execution_summary(self) -> Dict[str, Any]:
        """Devuelve un resumen de las estadísticas de ejecución."""
        return {
            "total_executions": self.total_executions,
            "total_test_runs": self.total_test_runs,
            "successful_test_runs": self.successful_test_runs,
            "failed_test_runs": self.failed_test_runs,
            "success_rate": self.success_rate,
            "last_execution_at": self.last_execution_at,
            "average_tests_per_execution": round(self.total_test_runs / self.total_executions, 2) if self.total_executions > 0 else 0
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convierte la suite de pruebas a un diccionario para serialización."""
        return {
            "suite_id": self.suite_id,
            "name": self.name,
            "description": self.description,
            "tags": self.tags,
            "execution_times": self.execution_times,
            "test_cases": {test_id: test_case.to_dict() for test_id, test_case in self.test_cases.items()},
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            # Estadísticas de ejecución
            "total_executions": self.total_executions,
            "total_test_runs": self.total_test_runs,
            "successful_test_runs": self.successful_test_runs,
            "failed_test_runs": self.failed_test_runs,
            "success_rate": self.success_rate,
            "last_execution_at": self.last_execution_at,
            "execution_summary": self.get_execution_summary()
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestSuite':
        """Crea una suite de pruebas a partir de un diccionario."""
        suite = cls(
            name=data["name"],
            description=data.get("description", ""),
            tags=data.get("tags", []),
            execution_times=data.get("execution_times", 1),
            suite_id=data.get("suite_id")
        )
        # Cargar los casos de prueba
        if "test_cases" in data and isinstance(data["test_cases"], dict):
            for test_id, test_data in data["test_cases"].items():
                suite.test_cases[test_id] = TestCase.from_dict(test_data)
            
        suite.created_at = data.get("created_at", suite.created_at)
        suite.updated_at = data.get("updated_at", suite.updated_at)
        
        # Cargar estadísticas de ejecución
        suite.total_executions = data.get("total_executions", 0)
        suite.total_test_runs = data.get("total_test_runs", 0)
        suite.successful_test_runs = data.get("successful_test_runs", 0)
        suite.failed_test_runs = data.get("failed_test_runs", 0)
        suite.success_rate = data.get("success_rate", 0.0)
        suite.last_execution_at = data.get("last_execution_at")
        suite.execution_summary = data.get("execution_summary", {})
        
        return suite

    def add_test_case(self, test_case: TestCase) -> None:
        """Añade un caso de prueba a la suite."""
        self.test_cases[test_case.test_id] = test_case
        self.updated_at = datetime.now().isoformat()

    def remove_test_case(self, test_id: str) -> bool:
        """Elimina un caso de prueba de la suite."""
        if test_id in self.test_cases:
            del self.test_cases[test_id]
            self.updated_at = datetime.now().isoformat()
            return True
        return False

    def get_test_case(self, test_id: str) -> Optional[TestCase]:
        """Obtiene un caso de prueba por su ID."""
        return self.test_cases.get(test_id)

    def get_all_test_cases(self) -> List[TestCase]:
        """Obtiene todos los casos de prueba de la suite."""
        return list(self.test_cases.values())


class GitHubConfig:
    """
    Representa la configuración de GitHub para un proyecto.
    """
    def __init__(self,
                enabled: bool = False,
                repo: Optional[str] = None,
                token: Optional[str] = None):
        self.enabled = enabled
        self.repo = repo
        self.token = token

    def to_dict(self) -> Dict[str, Any]:
        """Convierte la configuración a un diccionario para serialización."""
        return {
            "enabled": self.enabled,
            "repo": self.repo,
            "token": self.token
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GitHubConfig':
        """Crea una configuración a partir de un diccionario."""
        return cls(
            enabled=data.get("enabled", False),
            repo=data.get("repo"),
            token=data.get("token")
        )


class Project:
    """
    Representa un proyecto de pruebas que contiene múltiples suites de pruebas.
    """
    def __init__(self,
                 name: str,
                 description: str = "",
                 tags: List[str] = None,
                 project_id: str = None,
                 github_config: Optional[GitHubConfig] = None):
        self.project_id = project_id or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.tags = tags or []
        self.test_suites = {}  # Dict[suite_id, TestSuite]
        self.environments = []  # List[Environment] - Multi-environment support
        self.default_environment_id = None  # Default environment ID
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
        self.github_config = github_config or GitHubConfig()

    def to_dict(self) -> Dict[str, Any]:
        """Convierte el proyecto a un diccionario para serialización."""
        # Convert environments to dict format
        environments_dict = []
        if Environment is not None:
            for env in self.environments:
                if hasattr(env, 'to_dict'):
                    environments_dict.append(env.to_dict())
                elif hasattr(env, '__dict__'):
                    # Fallback for simple environment objects
                    environments_dict.append(vars(env))
        
        return {
            "project_id": self.project_id,
            "name": self.name,
            "description": self.description,
            "tags": self.tags,
            "test_suites": {suite_id: suite.to_dict() for suite_id, suite in self.test_suites.items()},
            "environments": environments_dict,
            "default_environment_id": self.default_environment_id,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "github_config": self.github_config.to_dict() if self.github_config else None
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Project':
        """Crea un proyecto a partir de un diccionario."""
        github_config_data = data.get("github_config")
        github_config = GitHubConfig.from_dict(github_config_data) if github_config_data else None
        
        project = cls(
            name=data["name"],
            description=data.get("description", ""),
            tags=data.get("tags", []),
            project_id=data.get("project_id"),
            github_config=github_config
        )
        project.created_at = data.get("created_at", project.created_at)
        project.updated_at = data.get("updated_at", project.updated_at)

        # Cargar suites de pruebas
        test_suites_data = data.get("test_suites", {})
        for suite_id, suite_data in test_suites_data.items():
            project.test_suites[suite_id] = TestSuite.from_dict(suite_data)
        
        # Cargar environments
        environments_data = data.get("environments", [])
        if Environment is not None and environments_data:
            for env_data in environments_data:
                try:
                    # Create Environment instance from dict
                    environment = Environment(**env_data)
                    project.environments.append(environment)
                except Exception as e:
                    # Skip invalid environments
                    continue
        
        # Set default environment ID
        project.default_environment_id = data.get("default_environment_id")

        return project

    def add_test_suite(self, test_suite: TestSuite) -> None:
        """Añade una suite de pruebas al proyecto."""
        self.test_suites[test_suite.suite_id] = test_suite
        self.updated_at = datetime.now().isoformat()

    def remove_test_suite(self, suite_id: str) -> bool:
        """Elimina una suite de pruebas del proyecto."""
        if suite_id in self.test_suites:
            del self.test_suites[suite_id]
            self.updated_at = datetime.now().isoformat()
            return True
        return False

    def get_test_suite(self, suite_id: str) -> Optional[TestSuite]:
        """Obtiene una suite de pruebas por su ID."""
        return self.test_suites.get(suite_id)

    def get_all_test_suites(self) -> List[TestSuite]:
        """Obtiene todas las suites de pruebas del proyecto."""
        return list(self.test_suites.values())

    # Environment Management Methods
    
    def add_environment(self, environment):
        """Add an environment to this project."""
        if Environment is None:
            # Fallback if Environment class is not available
            return
            
        # If this environment is set as default, unset other defaults
        if environment.is_default:
            for env in self.environments:
                env.is_default = False
            self.default_environment_id = environment.env_id
        
        # Check if environment already exists, if so, update it
        existing_env = self.get_environment(environment.env_id)
        if existing_env:
            # Update existing environment
            for i, env in enumerate(self.environments):
                if env.env_id == environment.env_id:
                    self.environments[i] = environment
                    break
        else:
            # Add new environment
            self.environments.append(environment)
        
        self.updated_at = datetime.now().isoformat()
    
    def get_environment(self, env_id: str):
        """Get an environment by its ID."""
        for env in self.environments:
            if env.env_id == env_id:
                return env
        return None
    
    def remove_environment(self, env_id: str) -> bool:
        """Remove an environment from this project."""
        env_to_remove = None
        for i, env in enumerate(self.environments):
            if env.env_id == env_id:
                env_to_remove = i
                break
        
        if env_to_remove is not None:
            removed_env = self.environments.pop(env_to_remove)
            
            # If we removed the default environment, unset the default
            if removed_env.is_default:
                self.default_environment_id = None
                # Optionally set the first remaining environment as default
                if self.environments:
                    self.environments[0].is_default = True
                    self.default_environment_id = self.environments[0].env_id
            
            self.updated_at = datetime.now().isoformat()
            return True
        return False
    
    def get_default_environment(self):
        """Get the default environment for this project."""
        for env in self.environments:
            if env.is_default:
                return env
        return None
    
    def set_default_environment(self, env_id: str) -> bool:
        """Set an environment as the default for this project."""
        # First, unset all defaults
        for env in self.environments:
            env.is_default = False
        
        # Set the specified environment as default
        target_env = self.get_environment(env_id)
        if target_env:
            target_env.is_default = True
            self.default_environment_id = env_id
            self.updated_at = datetime.now().isoformat()
            return True
        return False
