"""
LLM-based result validator for test execution analysis.

This module provides intelligent validation of test execution results using the
new unified LLM architecture with OpenRouter optimization.
"""

import json
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime
# Use the new unified LLM architecture
from src.services.llm.llm_service_factory import get_llm_factory
from src.services.llm.base_llm_service import LLMRequest

logger = logging.getLogger(__name__)


class LLMResultValidator:
    """
    Uses the unified LLM architecture to intelligently validate test execution results.
    
    The validator analyzes:
    - Individual step outcomes and patterns
    - Final agent state and reasoning
    - Overall execution flow and context
    - Expected vs actual outcomes based on test objectives
    """
    
    def __init__(self, api_key: str = None):
        """Initialize the LLM validator with new architecture."""
        # Use the new LLM factory instead of direct API key
        self.llm_factory = get_llm_factory()
        self._validation_cache = {}  # Cache to avoid re-evaluating same results
        
    def validate_execution_result(
        self, 
        result_data: Dict[str, Any], 
        test_objective: Optional[str] = None,
        gherkin_scenario: Optional[str] = None,
        force_revalidate: bool = False
    ) -> Dict[str, Any]:
        """
        Validate a test execution result using LLM analysis.
        
        Args:
            result_data: The StandardResult data to validate
            test_objective: Optional test objective/description for context
            gherkin_scenario: Optional Gherkin scenario for expected behavior
            force_revalidate: Whether to bypass cache and re-validate
            
        Returns:
            Dict containing validation results with:
            - validated_success: bool - LLM-determined success status
            - validation_confidence: float - Confidence score 0-1
            - validation_reasoning: str - LLM explanation of decision
            - individual_step_analysis: List - Analysis of each step
            - overall_assessment: str - Summary assessment
            - validation_timestamp: str - When validation was performed
        """
        try:
            # Create cache key
            execution_id = result_data.get("execution_id", "unknown")
            cache_key = f"{execution_id}_{hash(str(result_data))}"
            
            # Return cached result if available and not forcing revalidation
            if not force_revalidate and cache_key in self._validation_cache:
                logger.info(f"Returning cached validation for execution {execution_id}")
                return self._validation_cache[cache_key]
            
            logger.info(f"Starting LLM validation for execution {execution_id}")
            
            # Extract relevant data for analysis
            steps = result_data.get("steps", [])
            metadata = result_data.get("metadata", {})
            original_success = result_data.get("success", False)
            error = result_data.get("error")
            final_result = result_data.get("final_result", "")
            
            # Prepare validation prompt
            validation_prompt = self._build_validation_prompt(
                steps=steps,
                metadata=metadata,
                original_success=original_success,
                error=error,
                final_result=final_result,
                test_objective=test_objective,
                gherkin_scenario=gherkin_scenario
            )
            
            # Get LLM validation
            llm_response = self._query_llm_for_validation(validation_prompt)
            
            # Parse and structure the response
            validation_result = self._parse_validation_response(llm_response)
            
            # Add metadata
            validation_result.update({
                "validation_timestamp": datetime.now().isoformat(),
                "original_success_status": original_success,
                "execution_id": execution_id
            })
            
            # Cache the result
            self._validation_cache[cache_key] = validation_result
            
            logger.info(
                f"LLM validation completed for {execution_id}: "
                f"original={original_success}, validated={validation_result.get('validated_success')}"
            )
            
            return validation_result
            
        except Exception as e:
            logger.exception(f"Error during LLM validation: {e}")
            # Return fallback validation
            return {
                "validated_success": result_data.get("success", False),
                "validation_confidence": 0.0,
                "validation_reasoning": f"Validation failed due to error: {str(e)}",
                "individual_step_analysis": [],
                "overall_assessment": "Could not perform LLM validation",
                "validation_timestamp": datetime.now().isoformat(),
                "original_success_status": result_data.get("success", False),
                "execution_id": result_data.get("execution_id", "unknown"),
                "validation_error": str(e)
            }
    
    def _build_validation_prompt(
        self,
        steps: List[Dict],
        metadata: Dict,
        original_success: bool,
        error: Optional[str],
        final_result: str,
        test_objective: Optional[str],
        gherkin_scenario: Optional[str]
    ) -> str:
        """Build the validation prompt for the LLM."""
        
        prompt_parts = ["""
You are an expert QA analyst tasked with validating the results of automated test executions.

Analyze the following test execution data and determine if the test truly succeeded or failed, regardless of the original system classification.

**TEST CONTEXT:**"""]
        
        if test_objective:
            prompt_parts.append("- Test Objective: {}".format(test_objective))
        
        if gherkin_scenario:
            prompt_parts.append("- Expected Behavior (Gherkin): {}".format(gherkin_scenario))
        
        prompt_parts.append("- Original System Classification: {}".format("SUCCESS" if original_success else "FAILED"))
        prompt_parts.append("- Final Agent Result: {}".format(final_result))
        
        if error:
            prompt_parts.append("- Error Reported: {}".format(error))
        
        prompt_parts.append("\n**EXECUTION STEPS ANALYSIS:**")
        prompt_parts.append("Below are the {} steps executed by the automation agent:\n".format(len(steps)))
        
        # Add step details
        for i, step in enumerate(steps, 1):
            action_type = step.get("action_type", "unknown")
            thinking = step.get("thinking", "")
            if len(thinking) > 200:
                thinking = thinking[:200] + "..."
            url = step.get("url", "")
            error_msg = step.get("error", "")
            
            step_text = "\nStep {} - {}:\n- Thinking: {}\n- URL: {}".format(i, action_type, thinking, url)
            if error_msg:
                step_text += "\n- Error: {}".format(error_msg)
            prompt_parts.append(step_text)
        
        # Add metadata
        prompt_parts.append("\n**METADATA:**")
        prompt_parts.append("- Total Steps: {}".format(metadata.get('total_steps', len(steps))))
        prompt_parts.append("- Visited URLs: {}".format(metadata.get('visited_urls', [])))
        prompt_parts.append("- Interacted Elements: {}".format(len(metadata.get('interacted_elements', []))))
        
        # Add validation instructions
        prompt_parts.append("""
**VALIDATION TASK:**
Based on the execution steps, final state, and expected objectives, provide a JSON response with:

```json
{{
  "validated_success": true/false,
  "validation_confidence": 0.0-1.0,
  "validation_reasoning": "Detailed explanation of why this test succeeded or failed",
  "individual_step_analysis": [
    {{
      "step_number": 1,
      "assessment": "success/warning/failure",
      "reasoning": "Brief analysis of this step"
    }}
  ],
  "overall_assessment": "Summary of the overall test execution quality and outcome",
  "key_success_indicators": ["List of positive indicators found"],
  "key_failure_indicators": ["List of negative indicators found"]
}}
```

**VALIDATION CRITERIA:**
- Focus on whether the intended test objective was achieved
- Consider the final state reached by the agent
- Analyze error patterns vs successful completions
- Evaluate if the agent's reasoning indicates successful task completion
- Don't just rely on technical success/failure codes

Provide only the JSON response, no additional text.""")
        
        return "\n".join(prompt_parts)
    
    def _query_llm_for_validation(self, prompt: str) -> str:
        """Query the LLM using the new unified architecture."""
        try:
            # Create LLM request using new architecture
            request = LLMRequest(
                messages=[
                    {
                        "role": "system", 
                        "content": "You are an expert QA analyst specializing in test result validation and analysis."
                    },
                    {"role": "user", "content": prompt}
                ],
                use_case="validation",
                language="en",
                max_tokens=2000,
                temperature=0.1  # Low temperature for consistent analysis
            )
            
            # Execute through LLM factory (OpenRouter + fallback)
            response = self.llm_factory.make_request(request)
            
            if response.success:
                return response.content
            else:
                logger.error(f"LLM validation request failed: {response.error}")
                raise Exception(f"LLM validation failed: {response.error}")
            
        except Exception as e:
            logger.exception(f"Error querying LLM for validation: {e}")
            raise
    
    def _parse_validation_response(self, llm_response: str) -> Dict[str, Any]:
        """Parse the LLM JSON response into a structured dict."""
        try:
            # Extract JSON from response (in case there's extra text)
            json_start = llm_response.find('{')
            json_end = llm_response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = llm_response[json_start:json_end]
                return json.loads(json_str)
            else:
                # Fallback parsing
                return {
                    "validated_success": False,
                    "validation_confidence": 0.0,
                    "validation_reasoning": "Could not parse LLM response as JSON",
                    "individual_step_analysis": [],
                    "overall_assessment": llm_response[:500],
                    "key_success_indicators": [],
                    "key_failure_indicators": ["Failed to parse validation response"]
                }
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM validation response as JSON: {e}")
            return {
                "validated_success": False,
                "validation_confidence": 0.0,
                "validation_reasoning": f"JSON parsing error: {str(e)}",
                "individual_step_analysis": [],
                "overall_assessment": "LLM response could not be parsed",
                "key_success_indicators": [],
                "key_failure_indicators": ["JSON parsing failed"]
            }


def create_llm_validator(api_key: str) -> LLMResultValidator:
    """Factory function to create an LLM validator instance."""
    return LLMResultValidator(api_key)
