"""<PERSON><PERSON><PERSON><PERSON> que contiene agentes especializados para diferentes tareas."""

from typing import Dict, Any, Optional, List, Literal
# Use the new unified LLM architecture instead of direct Gemini
from src.services.llm.use_cases import StoryEnhancer, ManualTestCaseGenerator, GherkinGenerator
# Configurar path para browser_use local
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../libs'))

from browser_use import Browser, Agent as BrowserAgent
import os
from dotenv import load_dotenv

from src.utilities.utils import controller
from src.core.prompt_service import PromptService
from typing import Literal
LanguageType = Literal["en", "es"]
from src.utilities.browser_helper import (
    create_and_run_agent,
    create_robust_config
)

# Load environment variables
load_dotenv()

class StoryAgent:
    """Agente para mejorar historias de usuario y generar escenarios de prueba usando la nueva arquitectura LLM."""

    def __init__(self, api_key: str = None, language: Optional[LanguageType] = None):
        """Inicializa el agente de historias con la nueva arquitectura.

        Args:
            api_key: API key para el LLM (opcional con nueva arquitectura)
            language: Idioma de las respuestas ('en' o 'es')
        """
        # Use the new unified services
        self.story_enhancer = StoryEnhancer()
        self.manual_test_generator = ManualTestCaseGenerator()
        self.gherkin_generator = GherkinGenerator()
        self.language = language or os.getenv("PROMPT_LANGUAGE", "en")
        # Keep prompt service for backward compatibility
        self.prompt_service = PromptService()

    def enhance_story(self, user_story: str) -> str:
        """Mejora una historia de usuario usando la nueva arquitectura.

        Args:
            user_story: Historia de usuario original

        Returns:
            str: Historia de usuario mejorada
        """
        # Use the new StoryEnhancer with versioned prompts
        result = self.story_enhancer.enhance_story(user_story, self.language)
        
        if result["success"]:
            return result["enhanced_story"]
        else:
            # Fallback to original if enhancement fails
            return user_story

    def generate_manual_tests(self, enhanced_story: str) -> str:
        """Genera casos de prueba manuales usando la nueva arquitectura.

        Args:
            enhanced_story: Historia de usuario mejorada

        Returns:
            str: Casos de prueba manuales en formato markdown
        """
        # Use the new ManualTestCaseGenerator
        result = self.manual_test_generator.generate_from_story(enhanced_story, self.language)
        
        if result["success"]:
            return result["test_cases"]
        else:
            # Fallback to simple format if generation fails
            return f"Error generating manual tests: {result.get('error', 'Unknown error')}"

    def generate_gherkin(self, manual_tests: str) -> str:
        """Genera escenarios Gherkin usando la nueva arquitectura.

        Args:
            manual_tests: Casos de prueba manuales

        Returns:
            str: Escenarios Gherkin
        """
        # Use the new GherkinGenerator
        result = self.gherkin_generator.generate_from_instructions(
            instructions=manual_tests,
            language=self.language
        )
        
        if result["success"]:
            return result["gherkin"]
        else:
            # Fallback to error message if generation fails
            return f"Error generating Gherkin: {result.get('error', 'Unknown error')}"
        return gherkin

class BrowserAutomationAgent:
    """Agente para automatizar interacciones con navegadores usando la nueva arquitectura LLM."""

    def __init__(self, api_key: str = None, language: Optional[LanguageType] = None):
        """Inicializa el agente de automatización de navegador con nueva arquitectura.

        Args:
            api_key: API key para el LLM (opcional con nueva arquitectura)
            language: Idioma de las respuestas ('en' o 'es')
        """
        # New architecture doesn't require direct API key management
        self.language = language or os.getenv("PROMPT_LANGUAGE", "en")

    async def execute_scenario(self, scenario: str) -> Dict[str, Any]:
        """Ejecuta un escenario Gherkin en un navegador.

        Args:
            scenario: Escenario Gherkin a ejecutar

        Returns:
            Dict[str, Any]: Resultado de la ejecucion
        """
        # Crear configuración optimizada para agentes de automatización
        agent_config = create_robust_config(
            headless=True,  # Headless para agentes automatizados
            use_vision=True,  # Visión completa para mejor análisis
            max_steps=100,  # Pasos adecuados para escenarios típicos
            minimum_wait_page_load_time=0.5,
            wait_for_network_idle_page_load_time=1.0,
            maximum_wait_page_load_time=10.0,
            wait_between_actions=0.5,
            viewport_expansion=500,  # Contexto visual balanceado
            deterministic_rendering=False,  # Renderizado más natural (recomendado)
            highlight_elements=True  # Mantener highlighting para debugging
        )

        # Utilizar la función helper mejorada para crear y ejecutar el agente
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,  # controller se importa globalmente
            api_key=self.api_key,  # Pasar la api_key de la instancia
            language=self.language,  # Pasar el idioma de la instancia
            config=agent_config  # Usar configuración optimizada
        )

        return {
            "history": history,
            "success": True,  # Asumir éxito si no hay excepciones
            "final_result": history.final_result() if hasattr(history, 'final_result') else None
        }