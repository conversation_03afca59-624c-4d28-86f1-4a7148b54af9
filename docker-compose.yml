version: '3.8'

services:
  qak-platform:
    build: .
    container_name: qak-platform
    ports:
      - "80:80"     # Frontend y API unificados
      - "8000:8000" # API directa (opcional)
      - "3000:3000" # Frontend directo (opcional)
    env_file:
      - .env
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - NEXT_PUBLIC_API_BASE_URL=http://localhost/api
    volumes:
      # Persistir datos importantes
      - ./data/conversations:/app/conversations
      - ./data/codegen_sessions:/app/codegen_sessions
      - ./data/projects:/app/projects
      - ./data/semantic_memories:/app/semantic_memories
      - ./data/monitoring_data:/app/monitoring_data
      - ./data/logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Servicio opcional para desarrollo con volúmenes de código
  qak-platform-dev:
    build: .
    container_name: qak-platform-dev
    ports:
      - "8080:80"   # Frontend y API unificados en puerto 8080
    env_file:
      - .env
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api
    volumes:
      # Persistir datos importantes
      - ./data/conversations:/app/conversations
      - ./data/codegen_sessions:/app/codegen_sessions
      - ./data/projects:/app/projects
      - ./data/semantic_memories:/app/semantic_memories
      - ./data/monitoring_data:/app/monitoring_data
      - ./data/logs:/app/logs
      # Montar código para desarrollo (descomenta si necesitas editar código)
      # - ./src:/app/src
      # - ./memory-bank:/app/memory-bank
    restart: unless-stopped
    profiles:
      - dev

volumes:
  qak_data:
    driver: local 