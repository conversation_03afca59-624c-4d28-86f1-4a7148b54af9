version: '3.8'

services:
  # Redis for Celery broker and backend
  redis:
    image: redis:7-alpine
    container_name: qak-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped

  # Celery Worker
  celery-worker:
    build: .
    container_name: qak-celery-worker
    command: python celery_worker.py
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379/0
      - CELERY_LOG_LEVEL=INFO
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - LLM_MODEL=${LLM_MODEL:-gemini-2.5-flash-exp}
      - PROMPT_LANGUAGE=${PROMPT_LANGUAGE:-es}
    volumes:
      - .:/app
      - /app/.venv  # Exclude virtual environment
    restart: unless-stopped

  # Optional: Flower for monitoring Celery tasks
  flower:
    build: .
    container_name: qak-flower
    command: celery -A src.core.background_jobs.celery_app flower --port=5555
    ports:
      - "5555:5555"
    depends_on:
      - redis
      - celery-worker
    environment:
      - REDIS_URL=redis://redis:6379/0
    profiles:
      - monitoring

volumes:
  redis_data: