import logging
import sys
import os

from dotenv import load_dotenv

load_dotenv()

from browser_use.config import CONFIG

try:
	import logfire
	from logfire import configure
except ImportError:
	logfire = None


def addLoggingLevel(levelName, levelNum, methodName=None):
	"""
	Comprehensively adds a new logging level to the `logging` module and the
	currently configured logging class.

	`levelName` becomes an attribute of the `logging` module with the value
	`levelNum`. `methodName` becomes a convenience method for both `logging`
	itself and the class returned by `logging.getLoggerClass()` (usually just
	`logging.Logger`). If `methodName` is not specified, `levelName.lower()` is
	used.

	To avoid accidental clobberings of existing attributes, this method will
	raise an `AttributeError` if the level name is already an attribute of the
	`logging` module or if the method name is already present

	Example
	-------
	>>> addLoggingLevel('TRACE', logging.DEBUG - 5)
	>>> logging.getLogger(__name__).setLevel('TRACE')
	>>> logging.getLogger(__name__).trace('that worked')
	>>> logging.trace('so did this')
	>>> logging.TRACE
	5

	"""
	if not methodName:
		methodName = levelName.lower()

	if hasattr(logging, levelName):
		raise AttributeError(f'{levelName} already defined in logging module')
	if hasattr(logging, methodName):
		raise AttributeError(f'{methodName} already defined in logging module')
	if hasattr(logging.getLoggerClass(), methodName):
		raise AttributeError(f'{methodName} already defined in logger class')

	# This method was inspired by the answers to Stack Overflow post
	# http://stackoverflow.com/q/2183233/2988730, especially
	# http://stackoverflow.com/a/13638084/2988730
	def logForLevel(self, message, *args, **kwargs):
		if self.isEnabledFor(levelNum):
			self._log(levelNum, message, args, **kwargs)

	def logToRoot(message, *args, **kwargs):
		logging.log(levelNum, message, *args, **kwargs)

	logging.addLevelName(levelNum, levelName)
	setattr(logging, levelName, levelNum)
	setattr(logging.getLoggerClass(), methodName, logForLevel)
	setattr(logging, methodName, logToRoot)


def _configure_logfire():
	"""Configure Logfire with comprehensive settings"""
	if not (CONFIG.BROWSER_USE_LOGFIRE and logfire):
		return False
	
	try:
		# As per LangChain/LangSmith docs for OTel integration
		os.environ['LANGSMITH_TRACING'] = 'true'
		os.environ['LANGSMITH_OTEL_ENABLED'] = 'true'

		# Get configuration from environment
		logfire_token = os.getenv('LOGFIRE_TOKEN')
		logfire_service_name = "aery-browser"  # Fixed service name for browser_use
		logfire_env = os.getenv('LOGFIRE_ENVIRONMENT', 'development')

		# Set standard OTEL env vars which Logfire and other tools may use
		os.environ.setdefault('OTEL_SERVICE_NAME', logfire_service_name)
		os.environ.setdefault('OTEL_RESOURCE_ATTRIBUTES', f'deployment.environment={logfire_env}')
		
		# Log configuration details for debugging
		logger = logging.getLogger('browser_use')
		logger.debug(f"Attempting Logfire configuration - Token present: {bool(logfire_token)}, Service: {logfire_service_name}, Env: {logfire_env}")
		
		# Configure Logfire with aery-browser service name
		logfire.configure(
			token=logfire_token,
			service_name="aery-browser",  # Fixed service name for browser_use
			service_version=os.getenv('LOGFIRE_SERVICE_VERSION', '0.1.0'),
		)
		
		# Setup logging handler for capturing logs in Logfire
		# Use LogfireLoggingHandler for browser_use logs
		logfire_handler = logfire.LogfireLoggingHandler()
		
		# Add handler to browser_use logger specifically (not root)
		browser_use_logger = logging.getLogger('browser_use')
		if logfire_handler not in browser_use_logger.handlers:
			browser_use_logger.addHandler(logfire_handler)
		
		# Also handle libs/* logs under aery-browser service
		libs_logger = logging.getLogger('libs')
		if logfire_handler not in libs_logger.handlers:
			libs_logger.addHandler(logfire_handler)
		libs_logger.setLevel(logging.INFO)
		libs_logger.propagate = False  # Don't propagate to root
		
		# Instrument popular libraries
		try:
			logfire.instrument_httpx()
			logger.debug("✅ Successfully instrumented httpx")
		except ImportError as e:
			logger.debug(f"Failed to instrument httpx: {e}")
			logger.debug("Install with: pip install 'logfire[httpx]' or pip install 'opentelemetry-instrumentation-httpx'")
		except Exception as e:
			logger.debug(f"Failed to instrument httpx: {e}")
		
		try:
			logfire.instrument_openai()
			logger.debug("✅ Successfully instrumented openai")
		except Exception as e:
			logger.debug(f"Failed to instrument openai: {e}")
		
		try:
			logfire.instrument_anthropic()
			logger.debug("✅ Successfully instrumented anthropic")
		except Exception as e:
			logger.debug(f"Failed to instrument anthropic: {e}")
		
		try:
			# Check if the method exists before calling it
			if hasattr(logfire, 'instrument_google_generativeai'):
				logfire.instrument_google_generativeai()
				logger.debug("✅ Successfully instrumented google generativeai")
			else:
				logger.debug("Google GenerativeAI instrumentation not available in current logfire version")
		except Exception as e:
			logger.debug(f"Failed to instrument google generativeai: {e}")
		
		# Log successful configuration
		logger.info(f"🔥 Logfire configured successfully - Service: {logfire_service_name}, Environment: {logfire_env}")
		
		if not logfire_token:
			logger.warning("⚠️ LOGFIRE_TOKEN not set - logs will be sent to local development mode")
		
		return True
		
	except Exception as e:
		logger = logging.getLogger('browser_use')
		logger.error(f"❌ Failed to configure Logfire: {type(e).__name__}: {str(e)}")
		logger.debug(f"Full Logfire configuration error traceback", exc_info=True)
		return False


def setup_logging():
	# Configure Logfire first if enabled
	logfire_configured = _configure_logfire()
	
	# Try to add RESULT level, but ignore if it already exists
	try:
		addLoggingLevel('RESULT', 35)  # This allows ERROR, FATAL and CRITICAL
	except AttributeError:
		pass  # Level already exists, which is fine

	log_type = CONFIG.BROWSER_USE_LOGGING_LEVEL

	# Check if handlers are already set up
	if logging.getLogger().hasHandlers():
		return

	# Clear existing handlers
	root = logging.getLogger()
	root.handlers = []

	class BrowserUseFormatter(logging.Formatter):
		def format(self, record):
			# if isinstance(record.name, str) and record.name.startswith('browser_use.'):
			# 	record.name = record.name.split('.')[-2]
			return super().format(record)

	# Setup single handler for all loggers
	console = logging.StreamHandler(sys.stdout)

	# adittional setLevel here to filter logs
	if log_type == 'result':
		console.setLevel('RESULT')
		console.setFormatter(BrowserUseFormatter('%(message)s'))
	else:
		console.setFormatter(BrowserUseFormatter('%(levelname)-8s [%(name)s] %(message)s'))

	# Configure root logger only
	root.addHandler(console)

	# switch cases for log_type
	if log_type == 'result':
		root.setLevel('RESULT')  # string usage to avoid syntax error
	elif log_type == 'debug':
		root.setLevel(logging.DEBUG)
	else:
		root.setLevel(logging.INFO)

	# Configure browser_use logger
	browser_use_logger = logging.getLogger('browser_use')
	browser_use_logger.propagate = False  # Don't propagate to root logger
	browser_use_logger.addHandler(console)
	browser_use_logger.setLevel(root.level)  # Set same level as root logger

	# Configure libs logger to also be handled by aery-browser service
	libs_logger = logging.getLogger('libs')
	libs_logger.propagate = False  # Don't propagate to root logger
	libs_logger.addHandler(console)
	libs_logger.setLevel(root.level)  # Set same level as root logger

	logger = logging.getLogger('browser_use')
	
	# Log Logfire status
	if logfire_configured:
		logger.info('🔥 Logfire integration active - logs will be sent to Logfire')
	elif CONFIG.BROWSER_USE_LOGFIRE:
		logger.warning('⚠️ BROWSER_USE_LOGFIRE=true but Logfire configuration failed')
	
	# Silence or adjust third-party loggers
	third_party_loggers = [
		'WDM',
		'httpx',
		'selenium',
		'playwright',
		'urllib3',
		'asyncio',
		'langchain',
		'langsmith',
		'langsmith.client',
		'openai',
		'httpcore',
		'charset_normalizer',
		'anthropic._base_client',
		'PIL.PngImagePlugin',
		'trafilatura.htmlprocessing',
		'trafilatura',
		'mem0',
		'mem0.vector_stores.faiss',
		'mem0.vector_stores',
		'mem0.memory',
		'groq',
	]
	for logger_name in third_party_loggers:
		third_party = logging.getLogger(logger_name)
		third_party.setLevel(logging.ERROR)
		third_party.propagate = False

	return logger
