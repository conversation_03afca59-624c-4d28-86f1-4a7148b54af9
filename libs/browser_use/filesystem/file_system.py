import asyncio
import re
import logging
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path

from browser_use.filesystem.storage_backend import create_storage_backend, StorageBackend

INVALID_FILENAME_ERROR_MESSAGE = 'Error: Invalid filename format. Must be alphanumeric with .txt or .md extension.'

logger = logging.getLogger(__name__)


class FileSystem:
	def __init__(self, dir_path: str):
		# Initialize the storage backend
		self.storage_backend: StorageBackend = create_storage_backend(dir_path)
		self.file_system_path = dir_path
		
		# Disable batching for immediate responsiveness in testing scenarios
		try:
			from browser_use.agent.working_memory import OptimizedWorkingMemory
			# Disable batching (threshold=1) for immediate updates during testing
			self._optimized_working_memory = OptimizedWorkingMemory(self, batch_threshold=1)
			logger.info("📝 OptimizedWorkingMemory enabled with immediate updates (threshold=1)")
		except ImportError as e:
			logger.warning(f"OptimizedWorkingMemory not available: {e}")
			self._optimized_working_memory = None
		except Exception as e:
			logger.error(f"Failed to initialize OptimizedWorkingMemory: {e}")
			self._optimized_working_memory = None
		
		# Initialize default files asynchronously
		import asyncio
		try:
			loop = asyncio.get_running_loop()
			# If there's a running loop, schedule the initialization as a task
			loop.create_task(self.storage_backend.initialize_default_files())
		except RuntimeError:
			# If no event loop is running, create one temporarily
			asyncio.run(self.storage_backend.initialize_default_files())

	def get_dir(self) -> str:
		"""Get the directory path (compatibility method)"""
		return self.file_system_path

	def _is_valid_filename(self, file_name: str) -> bool:
		"""Check if filename matches the required pattern: name.extension"""
		return self.storage_backend._is_valid_filename(file_name)

	def display_file(self, file_name: str) -> str | None:
		"""Display file content"""
		return self.storage_backend.display_file(file_name)

	async def read_file(self, file_name: str) -> str:
		"""Read file content"""
		return await self.storage_backend.read_file(file_name)

	async def write_file(self, file_name: str, content: str) -> str:
		"""Write content to file"""
		"""Write content to file with optimized handling for todo.md"""
		# If OptimizedWorkingMemory is active and handling todo.md, it will intercept
		return await self.storage_backend.write_file(file_name, content)

	async def append_file(self, file_name: str, content: str) -> str:
		"""Append content to file"""
		return await self.storage_backend.append_file(file_name, content)

	def describe(self) -> str:
		"""List all files with their line counts."""
		return self.storage_backend.describe()

	def get_todo_contents(self) -> str:
		"""Get contents of todo.md file"""
		import asyncio
		try:
			loop = asyncio.get_running_loop()
			# If there's a running loop, we can't use run_until_complete
			# Return a placeholder or schedule as task
			import concurrent.futures
			import threading
			
			# Use ThreadPoolExecutor to run the async function
			def run_async():
				new_loop = asyncio.new_event_loop()
				asyncio.set_event_loop(new_loop)
				try:
					return new_loop.run_until_complete(self.storage_backend.get_todo_contents())
				finally:
					new_loop.close()
			
			with concurrent.futures.ThreadPoolExecutor() as executor:
				future = executor.submit(run_async)
				return future.result()
		except RuntimeError:
			# If no event loop is running, create one temporarily
			return asyncio.run(self.storage_backend.get_todo_contents())

	async def force_flush_todo(self) -> str | None:
		"""Force flush any pending todo.md updates if OptimizedWorkingMemory is active"""
		if self._optimized_working_memory:
			return await self._optimized_working_memory.flush()
		return None
