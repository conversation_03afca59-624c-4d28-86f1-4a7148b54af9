# Storage Backend System

This system provides flexible storage backends for browser-use's filesystem operations. Instead of always using local file storage, you can now configure different storage backends using environment variables.

## Supported Backends

### 1. FileSystem (Default)
Stores files locally on the filesystem.

```bash
export STORAGE_BACKEND=filesystem
```

### 2. Upstash Redis
Stores files in Upstash Redis cloud storage. Supports both REST API and TCP connection.

#### Option A: REST API (Recommended for serverless)
```bash
export STORAGE_BACKEND=upstash
export UPSTASH_REDIS_REST_URL=https://your-redis.upstash.io
export UPSTASH_REDIS_REST_TOKEN=your-rest-token
export STORAGE_TTL=14400  # Optional: TTL in seconds (default: 4 hours)
```

#### Option B: TCP/TLS Connection
```bash
export STORAGE_BACKEND=upstash
export UPSTASH_REDIS_URL=rediss://your-redis.upstash.io:6380
export UPSTASH_REDIS_TOKEN=your-tcp-token
export STORAGE_TTL=14400  # Optional: TTL in seconds (default: 4 hours)
```

**Note:** If both REST and TCP credentials are provided, the REST API will be used as it's more suitable for serverless environments.

### 3. Redis
Stores files in a standard Redis instance.

```bash
export STORAGE_BACKEND=redis
export REDIS_URL=redis://localhost:6379  # or your Redis server URL
export STORAGE_TTL=14400  # Optional: TTL in seconds (default: 4 hours)
```

## Installation

For Redis backends, install the optional storage dependencies:

```bash
pip install browser-use[storage]
```

## How It Works

The storage backend system uses a factory pattern to create the appropriate storage backend based on the `STORAGE_BACKEND` environment variable:

1. **StorageBackend** - Abstract base class defining the interface
2. **FileSystemStorageBackend** - Original filesystem implementation  
3. **UpstashStorageBackend** - Upstash Redis implementation
4. **RedisStorageBackend** - Standard Redis implementation

## Key Features

- **Transparent Migration**: Existing code using FileSystem continues to work unchanged
- **Automatic Fallback**: If credentials are missing, falls back to filesystem storage
- **TTL Support**: Key-value backends support automatic expiration
- **Async Support**: All operations are async-compatible
- **Error Handling**: Graceful error handling with informative messages

## File Operations

All backends support the same operations:

- `read_file(file_name)` - Read file contents
- `write_file(file_name, content)` - Write/overwrite file
- `append_file(file_name, content)` - Append to existing file
- `list_files()` - List all files with line counts
- `file_exists(file_name)` - Check if file exists
- `delete_file(file_name)` - Delete a file

## Configuration Details

### TTL (Time To Live)
For Redis/Upstash backends, files are automatically deleted after the TTL expires. Default is 4 hours (14400 seconds).

### Key Naming
Redis keys use the format: `filesystem:{sanitized_base_path}:{filename}`

### Error Handling
- Missing credentials → Falls back to filesystem
- Connection failures → Clear error messages
- Invalid backends → Falls back to filesystem with warning

## Example Usage

```python
from browser_use.filesystem import FileSystem

# The storage backend is automatically selected based on environment variables
fs = FileSystem("/tmp/my_agent")

# All operations work the same regardless of backend
await fs.write_file("notes.md", "Hello World!")
content = await fs.read_file("notes.md")
await fs.append_file("notes.md", "\nMore content!")
```

## Migration Notes

- Existing filesystem data won't automatically migrate to cloud backends
- When switching backends, you start with a clean slate
- File paths in `get_dir()` still work for local backends, cloud backends return the base path
- Default files (`todo.md`, `results.md`) are automatically created on initialization

## Security Considerations

- Keep your Redis credentials secure
- Consider using TTL to limit data persistence
- Be aware that cloud storage may have different security models than local files
- Test thoroughly when switching backends in production 