import asyncio
import json
import logging
import re
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from typing import Any, Dict, List, Optional

from browser_use.config import CONFIG

logger = logging.getLogger(__name__)

INVALID_FILENAME_ERROR_MESSAGE = 'Error: Invalid filename format. Must be alphanumeric with .txt or .md extension.'


class StorageBackend(ABC):
    """Abstract base class for storage backends"""
    
    def __init__(self, base_path: str):
        self.base_path = base_path
        self.logger = logger
    
    @abstractmethod
    async def read_file(self, file_name: str) -> str:
        """Read a file from storage"""
        pass
    
    @abstractmethod
    async def write_file(self, file_name: str, content: str) -> str:
        """Write a file to storage"""
        pass
    
    @abstractmethod
    async def append_file(self, file_name: str, content: str) -> str:
        """Append content to a file in storage"""
        pass
    
    @abstractmethod
    async def list_files(self) -> Dict[str, int]:
        """List all files with their line counts"""
        pass
    
    @abstractmethod
    async def file_exists(self, file_name: str) -> bool:
        """Check if a file exists"""
        pass
    
    @abstractmethod
    async def delete_file(self, file_name: str) -> str:
        """Delete a file from storage"""
        pass
    
    async def initialize_default_files(self) -> None:
        """Initialize default files if they don't exist"""
        default_files = {
            'todo.md': '',
            'results.md': ''
        }
        
        for file_name, content in default_files.items():
            if not await self.file_exists(file_name):
                await self.write_file(file_name, content)
                self.logger.info(f"📄 Initialized default file: {file_name}")
    
    def describe(self) -> str:
        """Get description of all files"""
        import asyncio
        import concurrent.futures
        
        try:
            loop = asyncio.get_running_loop()
            # If there's a running loop, use ThreadPoolExecutor
            def run_async():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(self.list_files())
                finally:
                    new_loop.close()
            
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_async)
                files = future.result()
        except RuntimeError:
            # If no event loop is running, create one temporarily
            files = asyncio.run(self.list_files())
        
        description = ''
        for file_name, line_count in files.items():
            if line_count == -1:
                description += f'- {file_name} — [error reading file]\n'
            else:
                description += f'- {file_name} — {line_count} lines\n'
        return description
    
    async def get_todo_contents(self) -> str:
        """Get contents of todo.md file"""
        content = await self.read_file('todo.md')
        # Remove the "Read from file todo.md:" prefix if present
        if content.startswith('Read from file todo.md:\n'):
            return content[len('Read from file todo.md:\n'):]
        return content
    
    def display_file(self, file_name: str) -> str | None:
        """Display file content synchronously"""
        if not self._is_valid_filename(file_name):
            return None
        
        import asyncio
        import concurrent.futures
        
        try:
            try:
                loop = asyncio.get_running_loop()
                # If there's a running loop, use ThreadPoolExecutor
                def run_async():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(self.read_file(file_name))
                    finally:
                        new_loop.close()
                
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_async)
                    content = future.result()
            except RuntimeError:
                # If no event loop is running, create one temporarily
                content = asyncio.run(self.read_file(file_name))
            
            if content.startswith(f'Read from file {file_name}:\n'):
                return content[len(f'Read from file {file_name}:\n'):]
            elif "not found" in content or "Error:" in content:
                return None
            return content
        except Exception:
            return None
    
    @staticmethod
    def _is_valid_filename(file_name: str) -> bool:
        """Check if filename matches the required pattern: name.extension"""
        pattern = r'^[a-zA-Z0-9_\-]+\.(txt|md)$'
        return bool(re.match(pattern, file_name))


class FileSystemStorageBackend(StorageBackend):
    """Original filesystem storage backend"""
    
    def __init__(self, base_path: str):
        super().__init__(base_path)
        # Create a base directory
        self.base_dir = Path(base_path)
        self.base_dir.mkdir(parents=True, exist_ok=True)

        # Create and use a dedicated subfolder for all operations
        self.dir = self.base_dir / 'data_storage'
        if self.dir.exists():
            raise ValueError(
                'File system directory already exists - stopping for safety purposes. Please delete it first if you want to use this directory.'
            )
        self.dir.mkdir(exist_ok=True)

        # Initialize default files
        self.results_file = self.dir / 'results.md'
        self.todo_file = self.dir / 'todo.md'
        self.results_file.touch(exist_ok=True)
        self.todo_file.touch(exist_ok=True)
    
    async def read_file(self, file_name: str) -> str:
        if not self._is_valid_filename(file_name):
            return INVALID_FILENAME_ERROR_MESSAGE

        path = self.dir / file_name
        if not path.exists():
            return f"File '{file_name}' not found."

        try:
            # Create a new executor for this operation
            with ThreadPoolExecutor() as executor:
                # Run file read in a thread to avoid blocking
                content = await asyncio.get_event_loop().run_in_executor(executor, lambda: path.read_text())
            return f'Read from file {file_name}:\n{content}'
        except Exception:
            return f"Error: Could not read file '{file_name}'."
    
    async def write_file(self, file_name: str, content: str) -> str:
        if not self._is_valid_filename(file_name):
            return INVALID_FILENAME_ERROR_MESSAGE

        try:
            path = self.dir / file_name
            # Create a new executor for this operation
            with ThreadPoolExecutor() as executor:
                # Run file write in a thread to avoid blocking
                await asyncio.get_event_loop().run_in_executor(executor, lambda: path.write_text(content))
            return f'Data written to {file_name} successfully.'
        except Exception:
            return f"Error: Could not write to file '{file_name}'."
    
    async def append_file(self, file_name: str, content: str) -> str:
        if not self._is_valid_filename(file_name):
            return INVALID_FILENAME_ERROR_MESSAGE

        path = self.dir / file_name
        if not path.exists():
            return f"File '{file_name}' not found."
        try:
            # Create a new executor for this operation
            with ThreadPoolExecutor() as executor:
                # Run file append in a thread to avoid blocking
                def append_to_file():
                    with path.open('a') as f:
                        f.write(content)

                await asyncio.get_event_loop().run_in_executor(executor, append_to_file)
            return f'Data appended to {file_name} successfully.'
        except Exception as e:
            return f"Error: Could not append to file '{file_name}'. {str(e)}"
    
    async def list_files(self) -> Dict[str, int]:
        files = {}
        for f in self.dir.iterdir():
            if f.is_file():
                try:
                    num_lines = len(f.read_text().splitlines())
                    files[f.name] = num_lines
                except Exception:
                    files[f.name] = -1
        return files
    
    async def file_exists(self, file_name: str) -> bool:
        if not self._is_valid_filename(file_name):
            return False
        path = self.dir / file_name
        return path.exists()
    
    async def delete_file(self, file_name: str) -> str:
        if not self._is_valid_filename(file_name):
            return INVALID_FILENAME_ERROR_MESSAGE

        path = self.dir / file_name
        if not path.exists():
            return f"File '{file_name}' not found."
        
        try:
            path.unlink()
            return f"File '{file_name}' deleted successfully."
        except Exception as e:
            return f"Error: Could not delete file '{file_name}'. {str(e)}"


class UpstashStorageBackend(StorageBackend):
    """Upstash Redis storage backend"""
    
    def __init__(self, base_path: str):
        super().__init__(base_path)
        
        # Check for REST API credentials first
        rest_url = getattr(CONFIG, 'UPSTASH_REDIS_REST_URL', '')
        rest_token = getattr(CONFIG, 'UPSTASH_REDIS_REST_TOKEN', '')
        
        if rest_url and rest_token:
            self._init_rest_client(rest_url, rest_token)
        else:
            self._init_redis_client()
    
    def _init_rest_client(self, rest_url: str, rest_token: str):
        """Initialize REST API client for Upstash"""
        try:
            import requests
            self.use_rest = True
            self.rest_url = rest_url.rstrip('/')
            self.rest_token = rest_token
            self.session = requests.Session()
            self.session.headers.update({
                'Authorization': f'Bearer {rest_token}',
                'Content-Type': 'application/json'
            })
            
            # Test connection
            response = self.session.post(f'{self.rest_url}/ping')
            if response.status_code != 200:
                raise Exception(f"REST API test failed: {response.status_code}")
            
            self.logger.info("✅ Connected to Upstash Redis via REST API")
            
        except ImportError as e:
            self.logger.warning(f"⚠️ Requests not available: {e}, falling back to filesystem")
            raise
        except Exception as e:
            self.logger.warning(f"⚠️ Upstash REST connection failed: {e}, falling back to filesystem")
            raise
    
    def _init_redis_client(self):
        """Initialize TCP Redis client for Upstash"""
        try:
            import redis
            
            url = getattr(CONFIG, 'UPSTASH_REDIS_URL', '')
            token = getattr(CONFIG, 'UPSTASH_REDIS_TOKEN', '')
            
            if not url or not token:
                self.logger.warning("⚠️ Upstash credentials missing, falling back to filesystem")
                raise ImportError("Missing Upstash credentials")
            
            self.use_rest = False
            
            # Parse Upstash URL properly
            if url.startswith('rediss://'):
                url_parts = url.replace('rediss://', '').split(':')
                host = url_parts[0]
                port = int(url_parts[1]) if len(url_parts) > 1 else 6380
            else:
                host = url.split('@')[-1].split(':')[0]
                port = int(url.split(':')[-1])
            
            self.redis = redis.Redis(
                host=host,
                port=port,
                password=token,
                ssl=True,
                decode_responses=True
            )
            
            # Test connection
            self.redis.ping()
            self.logger.info("✅ Connected to Upstash Redis via TCP")
            
        except ImportError as e:
            self.logger.warning(f"⚠️ Redis not available: {e}, falling back to filesystem")
            raise
        except Exception as e:
            self.logger.warning(f"⚠️ Upstash connection failed: {e}, falling back to filesystem")
            raise
    
    def _get_key(self, file_name: str) -> str:
        """Generate Redis key for file"""
        # Sanitize base_path for use in Redis key
        safe_base_path = re.sub(r'[^a-zA-Z0-9_-]', '_', self.base_path)
        return f"filesystem:{safe_base_path}:{file_name}"
    
    def _rest_request(self, command: str, *args) -> Any:
        """Make a REST API request to Upstash"""
        payload = [command] + list(args)
        response = self.session.post(f'{self.rest_url}', json=payload)
        response.raise_for_status()
        return response.json().get('result')
    
    async def read_file(self, file_name: str) -> str:
        if not self._is_valid_filename(file_name):
            return INVALID_FILENAME_ERROR_MESSAGE
        
        try:
            key = self._get_key(file_name)
            if self.use_rest:
                content = self._rest_request('GET', key)
            else:
                content = self.redis.get(key)
            
            if content is None:
                return f"File '{file_name}' not found."
            return f'Read from file {file_name}:\n{content}'
        except Exception as e:
            return f"Error: Could not read file '{file_name}'. {str(e)}"
    
    async def write_file(self, file_name: str, content: str) -> str:
        if not self._is_valid_filename(file_name):
            return INVALID_FILENAME_ERROR_MESSAGE
        
        try:
            key = self._get_key(file_name)
            ttl = getattr(CONFIG, 'STORAGE_TTL', 604800)
            
            if self.use_rest:
                if ttl > 0:
                    self._rest_request('SETEX', key, ttl, content)
                else:
                    self._rest_request('SET', key, content)
            else:
                if ttl > 0:
                    self.redis.setex(key, ttl, content)
                else:
                    self.redis.set(key, content)
            
            return f'Data written to {file_name} successfully.'
        except Exception as e:
            return f"Error: Could not write to file '{file_name}'. {str(e)}"
    
    async def append_file(self, file_name: str, content: str) -> str:
        if not self._is_valid_filename(file_name):
            return INVALID_FILENAME_ERROR_MESSAGE
        
        try:
            key = self._get_key(file_name)
            
            if self.use_rest:
                existing = self._rest_request('GET', key) or ""
            else:
                existing = self.redis.get(key) or ""
            
            new_content = str(existing) + content
            
            ttl = getattr(CONFIG, 'STORAGE_TTL', 604800)
            if self.use_rest:
                if ttl > 0:
                    self._rest_request('SETEX', key, ttl, new_content)
                else:
                    self._rest_request('SET', key, new_content)
            else:
                if ttl > 0:
                    self.redis.setex(key, ttl, new_content)
                else:
                    self.redis.set(key, new_content)
            
            return f'Data appended to {file_name} successfully.'
        except Exception as e:
            return f"Error: Could not append to file '{file_name}'. {str(e)}"
    
    async def list_files(self) -> Dict[str, int]:
        try:
            safe_base_path = re.sub(r'[^a-zA-Z0-9_-]', '_', self.base_path)
            pattern = f"filesystem:{safe_base_path}:*"
            
            if self.use_rest:
                keys = self._rest_request('KEYS', pattern) or []
            else:
                keys = self.redis.keys(pattern)
            
            files = {}
            for key in keys:
                file_name = key.split(':')[-1]
                try:
                    if self.use_rest:
                        content = self._rest_request('GET', key) or ""
                    else:
                        content = self.redis.get(key) or ""
                    
                    num_lines = len(str(content).splitlines())
                    files[file_name] = num_lines
                except Exception:
                    files[file_name] = -1
            return files
        except Exception:
            return {}
    
    async def file_exists(self, file_name: str) -> bool:
        if not self._is_valid_filename(file_name):
            return False
        try:
            key = self._get_key(file_name)
            if self.use_rest:
                result = self._rest_request('EXISTS', key)
                return result > 0
            else:
                return self.redis.exists(key) > 0
        except Exception:
            return False
    
    async def delete_file(self, file_name: str) -> str:
        if not self._is_valid_filename(file_name):
            return INVALID_FILENAME_ERROR_MESSAGE
        
        try:
            key = self._get_key(file_name)
            if self.use_rest:
                result = self._rest_request('DEL', key)
                deleted = result > 0
            else:
                deleted = self.redis.delete(key) > 0
            
            if deleted:
                return f"File '{file_name}' deleted successfully."
            else:
                return f"File '{file_name}' not found."
        except Exception as e:
            return f"Error: Could not delete file '{file_name}'. {str(e)}"


class RedisStorageBackend(StorageBackend):
    """Standard Redis storage backend"""
    
    def __init__(self, base_path: str):
        super().__init__(base_path)
        try:
            import redis
            
            redis_url = getattr(CONFIG, 'REDIS_URL', 'redis://localhost:6379')
            self.redis = redis.from_url(redis_url, decode_responses=True)
            
            # Test connection
            self.redis.ping()
            self.logger.info(f"✅ Connected to Redis: {redis_url}")
            
        except ImportError as e:
            self.logger.warning(f"⚠️ Redis not available: {e}, falling back to filesystem")
            raise
        except Exception as e:
            self.logger.warning(f"⚠️ Redis connection failed: {e}, falling back to filesystem")
            raise
    
    def _get_key(self, file_name: str) -> str:
        """Generate Redis key for file"""
        # Sanitize base_path for use in Redis key
        safe_base_path = re.sub(r'[^a-zA-Z0-9_-]', '_', self.base_path)
        return f"filesystem:{safe_base_path}:{file_name}"
    
    async def read_file(self, file_name: str) -> str:
        if not self._is_valid_filename(file_name):
            return INVALID_FILENAME_ERROR_MESSAGE
        
        try:
            key = self._get_key(file_name)
            content = self.redis.get(key)
            if content is None:
                return f"File '{file_name}' not found."
            return f'Read from file {file_name}:\n{content}'
        except Exception as e:
            return f"Error: Could not read file '{file_name}'. {str(e)}"
    
    async def write_file(self, file_name: str, content: str) -> str:
        if not self._is_valid_filename(file_name):
            return INVALID_FILENAME_ERROR_MESSAGE
        
        try:
            key = self._get_key(file_name)
            ttl = getattr(CONFIG, 'STORAGE_TTL', 604800)
            if ttl > 0:
                self.redis.setex(key, ttl, content)
            else:
                self.redis.set(key, content)
            return f'Data written to {file_name} successfully.'
        except Exception as e:
            return f"Error: Could not write to file '{file_name}'. {str(e)}"
    
    async def append_file(self, file_name: str, content: str) -> str:
        if not self._is_valid_filename(file_name):
            return INVALID_FILENAME_ERROR_MESSAGE
        
        try:
            key = self._get_key(file_name)
            existing = self.redis.get(key) or ""
            new_content = existing + content
            
            ttl = getattr(CONFIG, 'STORAGE_TTL', 604800)
            if ttl > 0:
                self.redis.setex(key, ttl, new_content)
            else:
                self.redis.set(key, new_content)
            return f'Data appended to {file_name} successfully.'
        except Exception as e:
            return f"Error: Could not append to file '{file_name}'. {str(e)}"
    
    async def list_files(self) -> Dict[str, int]:
        try:
            safe_base_path = re.sub(r'[^a-zA-Z0-9_-]', '_', self.base_path)
            pattern = f"filesystem:{safe_base_path}:*"
            keys = self.redis.keys(pattern)
            
            files = {}
            for key in keys:
                file_name = key.split(':')[-1]
                try:
                    content = self.redis.get(key) or ""
                    num_lines = len(content.splitlines())
                    files[file_name] = num_lines
                except Exception:
                    files[file_name] = -1
            return files
        except Exception:
            return {}
    
    async def file_exists(self, file_name: str) -> bool:
        if not self._is_valid_filename(file_name):
            return False
        try:
            key = self._get_key(file_name)
            return self.redis.exists(key) > 0
        except Exception:
            return False
    
    async def delete_file(self, file_name: str) -> str:
        if not self._is_valid_filename(file_name):
            return INVALID_FILENAME_ERROR_MESSAGE
        
        try:
            key = self._get_key(file_name)
            if self.redis.delete(key) > 0:
                return f"File '{file_name}' deleted successfully."
            else:
                return f"File '{file_name}' not found."
        except Exception as e:
            return f"Error: Could not delete file '{file_name}'. {str(e)}"


def create_storage_backend(base_path: str) -> StorageBackend:
    """Factory function to create the appropriate storage backend"""
    
    backend_type = getattr(CONFIG, 'STORAGE_BACKEND', 'filesystem').lower()
    
    if backend_type == 'upstash':
        try:
            return UpstashStorageBackend(base_path)
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize Upstash backend: {e}, falling back to filesystem")
            return FileSystemStorageBackend(base_path)
    
    elif backend_type == 'redis':
        try:
            return RedisStorageBackend(base_path)
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize Redis backend: {e}, falling back to filesystem")
            return FileSystemStorageBackend(base_path)
    
    elif backend_type == 'filesystem':
        return FileSystemStorageBackend(base_path)
    
    else:
        logger.warning(f"⚠️ Unknown storage backend '{backend_type}', falling back to filesystem")
        return FileSystemStorageBackend(base_path) 