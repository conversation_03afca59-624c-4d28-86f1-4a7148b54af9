import logging
import os
from pathlib import Path

from dotenv import load_dotenv
from uuid_extensions import uuid7str

load_dotenv()

from browser_use.config import CONFIG
from browser_use.telemetry.views import BaseTelemetryEvent
from browser_use.utils import singleton

logger = logging.getLogger(__name__)


def xdg_cache_home() -> Path:
	default = Path.home() / '.cache'
	if CONFIG.XDG_CACHE_HOME and (path := Path(CONFIG.XDG_CACHE_HOME)).is_absolute():
		return path
	return default


@singleton
class ProductTelemetry:
	"""
	Service for capturing telemetry data - disabled by default.
	"""

	def __init__(self) -> None:
		logger.debug('Telemetry disabled')

	def capture(self, event: BaseTelemetryEvent) -> None:
		# Telemetry is disabled - do nothing
		pass

	def flush(self) -> None:
		# Telemetry is disabled - do nothing
		pass

	@property
	def user_id(self) -> str:
		return 'DISABLED'
