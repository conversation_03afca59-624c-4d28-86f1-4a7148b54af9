import asyncio
import logging
from typing import Any, Dict, List, Optional, Tuple
from playwright.async_api import Page, ElementHandle, TimeoutError
from browser_use.browser.session import BrowserSession
from browser_use.dom.views import DOMElementNode
from browser_use.agent.views import ActionResult

logger = logging.getLogger(__name__)

class RecoveryStrategy:
    """Base class for recovery strategies"""
    
    async def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """Check if this strategy can handle the given error"""
        raise NotImplementedError
    
    async def recover(self, error: Exception, context: Dict[str, Any]) -> ActionResult:
        """Attempt to recover from the error"""
        raise NotImplementedError

class ElementNotFoundRecovery(RecoveryStrategy):
    """Recovery strategy for element not found errors"""
    
    async def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        error_msg = str(error).lower()
        return any(phrase in error_msg for phrase in [
            'element not found', 'no such element', 'element is not attached',
            'selector resolved to null', 'element handle is disposed'
        ])
    
    async def recover(self, error: Exception, context: Dict[str, Any]) -> ActionResult:
        browser_session: BrowserSession = context['browser_session']
        original_selector = context.get('selector', '')
        page = await browser_session.get_current_page()
        
        logger.info(f"🔄 Attempting element recovery for selector: {original_selector}")
        
        # Strategy 1: Wait and retry
        try:
            await page.wait_for_selector(original_selector, timeout=5000)
            return ActionResult(
                extracted_content=f"Element found after waiting: {original_selector}",
                include_in_memory=True
            )
        except TimeoutError:
            pass
        
        # Strategy 2: Try alternative selectors
        alternative_selectors = self._generate_alternative_selectors(original_selector)
        for alt_selector in alternative_selectors:
            try:
                element = await page.query_selector(alt_selector)
                if element:
                    logger.info(f"✅ Found element with alternative selector: {alt_selector}")
                    return ActionResult(
                        extracted_content=f"Element found with alternative selector: {alt_selector}",
                        include_in_memory=True
                    )
            except Exception:
                continue
        
        # Strategy 3: Scroll and search
        scroll_result = await self._scroll_and_search(page, original_selector)
        if scroll_result:
            return scroll_result
        
        # Strategy 4: Find similar elements
        similar_elements = await self._find_similar_elements(page, context)
        if similar_elements:
            return ActionResult(
                extracted_content=f"Found {len(similar_elements)} similar elements that might work",
                include_in_memory=True
            )
        
        return ActionResult(
            error=f"Could not recover from element not found: {original_selector}",
            include_in_memory=True
        )
    
    def _generate_alternative_selectors(self, original_selector: str) -> List[str]:
        """Generate alternative selectors based on the original"""
        alternatives = []
        
        # If it's an index-based selector, try nearby indices
        if '[' in original_selector and ']' in original_selector:
            import re
            match = re.search(r'\[(\d+)\]', original_selector)
            if match:
                index = int(match.group(1))
                for offset in [-1, 1, -2, 2]:
                    new_index = index + offset
                    if new_index >= 0:
                        alt_selector = original_selector.replace(f'[{index}]', f'[{new_index}]')
                        alternatives.append(alt_selector)
        
        # Try removing specificity
        if '>' in original_selector:
            # Remove direct child selectors
            alternatives.append(original_selector.replace(' > ', ' '))
        
        # Try with different attribute priorities
        if 'data-testid' not in original_selector:
            alternatives.append(f"[data-testid*='{original_selector.split('.')[-1]}']")
        
        return alternatives[:5]  # Limit alternatives
    
    async def _scroll_and_search(self, page: Page, selector: str) -> Optional[ActionResult]:
        """Scroll through page to find element"""
        logger.info("🔍 Scrolling to search for element...")
        
        # Try scrolling down
        for i in range(3):
            await page.keyboard.press('PageDown')
            await asyncio.sleep(0.5)
            try:
                element = await page.query_selector(selector)
                if element:
                    logger.info(f"✅ Found element after scrolling down {i+1} times")
                    return ActionResult(
                        extracted_content=f"Element found after scrolling down",
                        include_in_memory=True
                    )
            except Exception:
                pass
        
        # Try scrolling up
        for i in range(3):
            await page.keyboard.press('PageUp')
            await asyncio.sleep(0.5)
            try:
                element = await page.query_selector(selector)
                if element:
                    logger.info(f"✅ Found element after scrolling up {i+1} times")
                    return ActionResult(
                        extracted_content=f"Element found after scrolling up",
                        include_in_memory=True
                    )
            except Exception:
                pass
        
        return None
    
    async def _find_similar_elements(self, page: Page, context: Dict[str, Any]) -> List[str]:
        """Find elements similar to what we're looking for"""
        # This would use the DOM analysis to find similar interactive elements
        # For now, return empty list
        return []

class NavigationTimeoutRecovery(RecoveryStrategy):
    """Recovery strategy for navigation timeouts"""
    
    async def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        error_msg = str(error).lower()
        return any(phrase in error_msg for phrase in [
            'timeout', 'navigation timeout', 'page load timeout'
        ])
    
    async def recover(self, error: Exception, context: Dict[str, Any]) -> ActionResult:
        browser_session: BrowserSession = context['browser_session']
        page = await browser_session.get_current_page()
        url = context.get('url', page.url)
        
        logger.info(f"🔄 Attempting navigation recovery for URL: {url}")
        
        # Strategy 1: Wait for network idle
        try:
            await page.wait_for_load_state('networkidle', timeout=10000)
            return ActionResult(
                extracted_content=f"Page loaded after waiting for network idle",
                include_in_memory=True
            )
        except TimeoutError:
            pass
        
        # Strategy 2: Refresh page
        try:
            await page.reload(wait_until='domcontentloaded', timeout=15000)
            logger.info("✅ Page recovered after refresh")
            return ActionResult(
                extracted_content="Page recovered after refresh",
                include_in_memory=True
            )
        except Exception as refresh_error:
            logger.warning(f"Refresh failed: {refresh_error}")
        
        # Strategy 3: Navigate again
        try:
            await page.goto(url, wait_until='domcontentloaded', timeout=20000)
            logger.info("✅ Page recovered after re-navigation")
            return ActionResult(
                extracted_content="Page recovered after re-navigation",
                include_in_memory=True
            )
        except Exception as nav_error:
            return ActionResult(
                error=f"Could not recover from navigation timeout: {nav_error}",
                include_in_memory=True
            )

class CaptchaRecovery(RecoveryStrategy):
    """Recovery strategy for CAPTCHA detection"""
    
    async def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        # Check if page contains CAPTCHA elements
        browser_session: BrowserSession = context.get('browser_session')
        if not browser_session:
            return False
        
        try:
            page = await browser_session.get_current_page()
            captcha_selectors = [
                '[class*="captcha"]', '[id*="captcha"]',
                '[class*="recaptcha"]', '[id*="recaptcha"]',
                'iframe[src*="recaptcha"]'
            ]
            
            for selector in captcha_selectors:
                element = await page.query_selector(selector)
                if element:
                    return True
        except Exception:
            pass
        
        return False
    
    async def recover(self, error: Exception, context: Dict[str, Any]) -> ActionResult:
        logger.warning("🤖 CAPTCHA detected - manual intervention may be required")
        
        # Strategy 1: Wait for human to solve it
        browser_session: BrowserSession = context['browser_session']
        page = await browser_session.get_current_page()
        
        # Wait up to 60 seconds for CAPTCHA to be solved
        for i in range(12):  # 12 * 5 seconds = 60 seconds
            await asyncio.sleep(5)
            
            # Check if CAPTCHA is gone
            captcha_present = False
            captcha_selectors = [
                '[class*="captcha"]', '[id*="captcha"]',
                '[class*="recaptcha"]', '[id*="recaptcha"]'
            ]
            
            for selector in captcha_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element and await element.is_visible():
                        captcha_present = True
                        break
                except Exception:
                    pass
            
            if not captcha_present:
                logger.info("✅ CAPTCHA appears to be solved")
                return ActionResult(
                    extracted_content="CAPTCHA solved, continuing...",
                    include_in_memory=True
                )
        
        return ActionResult(
            error="CAPTCHA timeout - manual intervention required",
            include_in_memory=True
        )

class IntelligentRecoverySystem:
    """Main recovery system that orchestrates different recovery strategies"""
    
    def __init__(self):
        self.strategies: List[RecoveryStrategy] = [
            ElementNotFoundRecovery(),
            NavigationTimeoutRecovery(),
            CaptchaRecovery(),
        ]
        self.recovery_attempts = {}  # Track recovery attempts to avoid infinite loops
        self.max_recovery_attempts = 3
    
    async def attempt_recovery(
        self, 
        error: Exception, 
        context: Dict[str, Any]
    ) -> Tuple[bool, ActionResult]:
        """
        Attempt to recover from an error
        
        Returns:
            Tuple of (success, result)
        """
        error_key = f"{type(error).__name__}:{str(error)[:50]}"
        
        # Check if we've already tried recovering from this error too many times
        if error_key in self.recovery_attempts:
            if self.recovery_attempts[error_key] >= self.max_recovery_attempts:
                logger.warning(f"Max recovery attempts reached for error: {error_key}")
                return False, ActionResult(
                    error=f"Max recovery attempts reached: {error}",
                    include_in_memory=True
                )
        
        self.recovery_attempts[error_key] = self.recovery_attempts.get(error_key, 0) + 1
        
        logger.info(f"🔧 Attempting recovery for error: {type(error).__name__}")
        
        # Try each recovery strategy
        for strategy in self.strategies:
            try:
                if await strategy.can_handle(error, context):
                    logger.info(f"Using recovery strategy: {strategy.__class__.__name__}")
                    result = await strategy.recover(error, context)
                    
                    if not result.error:  # Recovery successful
                        logger.info("✅ Recovery successful!")
                        # Reset attempt count on successful recovery
                        if error_key in self.recovery_attempts:
                            del self.recovery_attempts[error_key]
                        return True, result
                    else:
                        logger.warning(f"Recovery strategy failed: {result.error}")
                        
            except Exception as recovery_error:
                logger.error(f"Recovery strategy {strategy.__class__.__name__} failed: {recovery_error}")
                continue
        
        logger.error("❌ All recovery strategies failed")
        return False, ActionResult(
            error=f"Recovery failed for error: {error}",
            include_in_memory=True
        )
    
    def reset_recovery_attempts(self):
        """Reset recovery attempt counters"""
        self.recovery_attempts.clear() 