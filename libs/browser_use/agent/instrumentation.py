from functools import wraps
from typing import Callable, Coroutine, Any, ParamSpec, TypeVar
from browser_use.config import CONFIG

try:
    import logfire
    from logfire import configure

except ImportError:
    logfire = None

P = ParamSpec('P')
R = TypeVar('R')

def instrument_with_logfire(func: Callable[P, Coroutine[Any, Any, R]]) -> Callable[P, Coroutine[Any, Any, R]]:
    """
    A decorator that conditionally wraps an async function with logfire.span.
    """
    @wraps(func)
    async def wrapper(*args: P.args, **kwargs: P.kwargs) -> R:
        if CONFIG.BROWSER_USE_LOGFIRE and logfire:
            # The first argument to span is `msg_template`, not `name`.
            with logfire.span(func.__name__):
                return await func(*args, **kwargs)
        else:
            return await func(*args, **kwargs)
    return wrapper 