# 🧠 Sistema de Memoria Semántica para browser-use

## ¿Qué es la Memoria Semántica?

La memoria semántica es un sistema inteligente que permite al agente **aprender de experiencias pasadas** y aplicar ese conocimiento en situaciones similares futuras. A diferencia de la memoria procedimental que solo recuerda pasos específicos, la memoria semántica entiende **patrones y conceptos**.

## 🎯 Beneficios Principales

### 1. **Aprendizaje Automático**
- El agente mejora con cada interacción
- Aprende patrones comunes de sitios web
- Reduce errores repetitivos

### 2. **Adaptación Inteligente**
- Se adapta a diferentes sitios web
- Reconoce elementos similares en contextos diferentes
- Sugiere mejores estrategias basadas en experiencia

### 3. **Eficiencia Mejorada**
- Reduce tiempo de ejecución
- Menos reintentos fallidos
- Mayor tasa de éxito en tareas

## 🔧 Características Técnicas

### **Tipos de Patrones que Aprende**

1. **Patrones de UI** - Elementos comunes como botones de login, formularios
2. **Flujos de Trabajo** - Secuencias típicas como login → dashboard
3. **Recuperación de Errores** - Estrategias que funcionan cuando algo falla
4. **Patrones Específicos de Sitio** - Comportamientos únicos por dominio

### **Sistema de Aprendizaje**

```python
# El sistema aprende automáticamente cuando:
- Se registran ≥3 interacciones similares
- La tasa de éxito es ≥70%
- Los patrones son consistentes
```

## 📊 Ejemplo de Funcionamiento

### **Antes (Sin Memoria Semántica)**
```
Agente: "Busco un botón de login"
Sistema: "Intento elemento por elemento..."
Resultado: 50% de éxito, lento
```

### **Después (Con Memoria Semántica)**
```
Agente: "Busco un botón de login"
Memoria: "En sitios similares, busca elementos con texto 'Sign In', 'Login', 'Entrar'"
Sistema: "Aplico conocimiento previo..."
Resultado: 90% de éxito, rápido
```

## 🚀 Uso Básico

### **1. Inicialización**
```python
from browser_use.agent.semantic_memory import SemanticMemory

# Crear memoria semántica
memory = SemanticMemory(agent_id="mi_agente")
```

### **2. Registrar Interacciones**
```python
await memory.record_interaction(
    action_type="click_element",
    target_element={"index": 5, "text": "Login"},
    context="login form authentication",
    url="https://example.com/login",
    success=True,
    result="Successfully clicked login button"
)
```

### **3. Obtener Sugerencias**
```python
suggestions = await memory.get_suggestions_for_action(
    action_type="click_element",
    context="login button",
    url="https://newsite.com/signin"
)

for suggestion in suggestions:
    print(f"Sugerencia: {suggestion['advice']}")
    print(f"Confianza: {suggestion['confidence']}")
```

### **4. Guardar/Cargar Memoria**
```python
# Guardar
await memory.save_memory("my_agent_memory.json")

# Cargar
await memory.load_memory("my_agent_memory.json")
```

## 📈 Estadísticas y Monitoreo

```python
stats = memory.get_memory_stats()
print(f"Patrones aprendidos: {stats['learned_patterns']}")
print(f"Tasa de éxito reciente: {stats['recent_success_rate']}")
print(f"Patrones más usados: {stats['most_used_patterns']}")
```

## 🎓 Patrones de Template Incluidos

El sistema viene con patrones predefinidos para casos comunes:

### **Login Forms**
- Indicadores: `["email", "password", "login", "sign in"]`
- Secuencia: `find_email → enter_email → find_password → enter_password → click_login`
- Señales de éxito: `["dashboard", "welcome", "profile"]`

### **Shopping Cart**
- Indicadores: `["add to cart", "buy", "purchase", "checkout"]`
- Secuencia: `select_item → add_to_cart → view_cart → checkout`
- Señales de éxito: `["added", "cart updated", "proceed"]`

### **Search Functionality**
- Indicadores: `["search", "find", "query", "magnifying glass"]`
- Secuencia: `find_search_box → enter_query → submit_search`
- Señales de éxito: `["results", "found", "matches"]`

## 🔄 Integración con el Agente

La memoria semántica se integra automáticamente con el agente:

```python
# Al crear el agente, la memoria se inicializa automáticamente
agent = Agent(
    task="Login to website",
    llm=llm,
    # La memoria semántica se activa automáticamente
)

# Durante la ejecución:
# 1. Se registran todas las interacciones
# 2. Se aprenden patrones automáticamente
# 3. Se proporcionan sugerencias en tiempo real
# 4. Se guarda la memoria al finalizar
```

## 🛠️ Configuración Avanzada

### **Ajustar Umbrales de Aprendizaje**
```python
memory.min_interactions_for_pattern = 5  # Más conservador
memory.min_success_rate_for_pattern = 0.8  # Mayor precisión
```

### **Limpiar Memoria Antigua**
```python
memory.clear_old_interactions(max_interactions=500)
```

### **Filtrar por Dominio**
```python
# Solo obtener sugerencias para dominios específicos
suggestions = await memory.get_suggestions_for_action(
    action_type="click_element",
    context="login",
    url="https://target-domain.com"
)
```

## 📝 Mejores Prácticas

### **1. Naming Conventions**
- Usa nombres descriptivos para `action_type`
- Incluye contexto relevante en `context`
- Sé consistente con la nomenclatura

### **2. Contexto Rico**
```python
# ❌ Mal
context = "button"

# ✅ Bien  
context = "login button primary navigation authentication form"
```

### **3. Manejo de Errores**
```python
try:
    suggestions = await memory.get_suggestions_for_action(...)
except Exception as e:
    logger.warning(f"Semantic memory error: {e}")
    # Continuar sin sugerencias
```

### **4. Persistencia**
- Guarda la memoria regularmente
- Usa nombres únicos por agente/tarea
- Considera rotación de archivos antiguos

## 🔍 Debugging y Troubleshooting

### **Ver Patrones Aprendidos**
```python
for pattern in memory.store.patterns:
    print(f"Patrón: {pattern.description}")
    print(f"Confianza: {pattern.confidence_score}")
    print(f"Uso: {pattern.usage_count} veces")
```

### **Analizar Interacciones Recientes**
```python
recent = memory.interaction_history[-10:]  # Últimas 10
for interaction in recent:
    print(f"{interaction.action_type}: {'✅' if interaction.success else '❌'}")
```

### **Logs de Debug**
```python
import logging
logging.getLogger('browser_use.agent.semantic_memory').setLevel(logging.DEBUG)
```

## 🚦 Limitaciones Actuales

1. **Sin Dependencias Externas**: Usa búsqueda de texto simple (no embeddings)
2. **Memoria Local**: No se comparte entre diferentes instancias
3. **Análisis Básico**: Patrones simples basados en frecuencia

## 🔮 Roadmap Futuro

- [ ] Integración con embeddings (sentence-transformers)
- [ ] Memoria compartida entre agentes
- [ ] Análisis semántico más avanzado
- [ ] UI para visualizar patrones aprendidos
- [ ] Exportar/importar patrones entre proyectos

## 📞 Soporte

Para preguntas o problemas:
1. Revisa los logs de debug
2. Verifica las estadísticas de memoria
3. Prueba con el archivo demo incluido
4. Reporta issues con ejemplos específicos

---

**¡La memoria semántica hace que tu agente sea más inteligente con cada uso! 🧠✨** 