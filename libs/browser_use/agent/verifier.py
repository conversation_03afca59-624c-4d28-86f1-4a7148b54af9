from __future__ import annotations

import re
from dataclasses import dataclass
from typing import List

from browser_use.browser.views import BrowserStateSummary
from browser_use.agent.views import ActionModel, ActionResult


@dataclass
class VerificationResult:
    success: bool
    details: str


class PostActionVerifier:
    """Very simple post-action verifier for critical flows like login."""

    # --- Public API -----------------------------------------------------

    def verify(self, actions: List[ActionModel], browser_state: BrowserStateSummary) -> ActionResult | None:
        """Run verifications that apply to the provided actions.

        Currently supports:
        • Login flow – checks for absence of password field & presence of logout indicators.
        """
        if self._looks_like_login_flow(actions):
            return self._verify_login_success(browser_state)
        if self._looks_like_form_submission(actions):
            return self._verify_form_submission(browser_state)
        if self._looks_like_file_upload(actions):
            return self._verify_file_upload(browser_state)
        if self._looks_like_navigation(actions):
            return self._verify_navigation(actions, browser_state)
        return None

    # -------------------------------------------------------------------

    def _looks_like_login_flow(self, actions: List[ActionModel]) -> bool:
        """Heuristic to decide if the sequence corresponds to a login attempt."""
        has_password_input = False
        has_email_input = False
        has_click = False

        for act in actions:
            data = act.model_dump(exclude_unset=True)
            if "input_text" in data:
                # We do not have field names, so rely on simple text heuristics
                text_val: str = data["input_text"].get("text", "")
                if re.search(r"@|email", text_val, re.IGNORECASE):
                    has_email_input = True
                if re.search(r"\*|password|123", text_val, re.IGNORECASE):
                    has_password_input = True
            elif "click_element_by_index" in data:
                has_click = True

        return has_email_input and has_password_input and has_click

    # -------------------------------------------------------------------

    def _verify_login_success(self, browser_state: BrowserStateSummary) -> ActionResult | None:
        """Check page indicators that login succeeded."""
        selector_map = browser_state.selector_map

        # Look for any input of type password ⇒ login likely NOT complete
        for elem in selector_map.values():
            if elem.tag_name.lower() == "input" and elem.attributes.get("type", "").lower() == "password":
                return ActionResult(
                    error="Login verification failed – password field still present.",
                    include_in_memory=True,
                )

        # Look for common logout indicators
        logout_keywords = [
            "logout",
            "log out",
            "sign out",
            "cerrar sesión",
            "salir",
        ]
        for elem in selector_map.values():
            # Check attributes and aggregated text (if method exists)
            attrs_text = " ".join(str(v) for v in elem.attributes.values())
            text = ""
            try:
                text = elem.get_all_text_till_next_clickable_element(max_depth=1)
            except Exception:
                pass
            combined = (attrs_text + " " + text).lower()
            if any(k in combined for k in logout_keywords):
                return ActionResult(
                    extracted_content="Login verificado exitosamente – se detectó botón/enlace de logout.",
                    include_in_memory=True,
                    long_term_memory="Login completado con éxito",
                )

        # As fallback use URL heuristic: if "login" or "signin" no longer in URL
        if not re.search(r"login|signin", browser_state.url, re.IGNORECASE):
            return ActionResult(
                extracted_content="Login verificado por cambio de URL.",
                include_in_memory=True,
                long_term_memory="Login completado con éxito",
            )

        # If none matched, fail
        return ActionResult(error="No se pudo verificar el login.", include_in_memory=True)

    # -------------------------------------------------------------------
    # Form Submission Verification
    # -------------------------------------------------------------------

    def _looks_like_form_submission(self, actions: List[ActionModel]) -> bool:
        has_submit_click = any("click_element_by_index" in act.model_dump(exclude_unset=True) for act in actions)
        many_inputs = sum(1 for a in actions if "input_text" in a.model_dump(exclude_unset=True)) >= 2
        return many_inputs and has_submit_click

    def _verify_form_submission(self, browser_state: BrowserStateSummary) -> ActionResult:
        # Check for common success indicators
        success_keywords = [
            "gracias", "thank you", "éxito", "success", "enviado", "submitted",
        ]
        error_keywords = ["error", "falló", "invalid", "required"]

        page_text = browser_state.element_tree.get_all_text_till_next_clickable_element(max_depth=-1).lower()

        if any(k in page_text for k in error_keywords):
            return ActionResult(error="Errores detectados tras envío de formulario.", include_in_memory=True)
        if any(k in page_text for k in success_keywords):
            return ActionResult(
                extracted_content="Formulario enviado correctamente.",
                include_in_memory=True,
                long_term_memory="Form submission success",
            )
        return ActionResult(error="No se pudo verificar envío de formulario.", include_in_memory=True)

    # -------------------------------------------------------------------
    # File Upload Verification
    # -------------------------------------------------------------------

    def _looks_like_file_upload(self, actions: List[ActionModel]) -> bool:
        return any("upload_files" in act.model_dump(exclude_unset=True) for act in actions)

    def _verify_file_upload(self, browser_state: BrowserStateSummary) -> ActionResult:
        success_kw = ["uploaded", "subido", "cargado", "upload complete", "upload successful"]
        page_text = browser_state.element_tree.get_all_text_till_next_clickable_element(max_depth=-1).lower()
        if any(k in page_text for k in success_kw):
            return ActionResult(
                extracted_content="Carga de archivos verificada exitosamente.",
                include_in_memory=True,
                long_term_memory="File upload success",
            )
        return ActionResult(error="No se pudo verificar la carga de archivos.", include_in_memory=True)

    # -------------------------------------------------------------------
    # Navigation Verification
    # -------------------------------------------------------------------

    def _looks_like_navigation(self, actions: List[ActionModel]) -> bool:
        return any("go_to_url" in act.model_dump(exclude_unset=True) for act in actions)

    def _verify_navigation(self, actions: List[ActionModel], browser_state: BrowserStateSummary) -> ActionResult:
        # Extract target URL from action
        target_url = None
        for act in actions:
            data = act.model_dump(exclude_unset=True)
            if "go_to_url" in data:
                target_url = data["go_to_url"].get("url")
                break
        if target_url and target_url.rstrip('/') == browser_state.url.rstrip('/'):
            return ActionResult(
                extracted_content=f"Navegación verificada a {browser_state.url}.",
                include_in_memory=True,
                long_term_memory="Navigation success",
            )
        return ActionResult(error="La navegación no llegó a la URL esperada.", include_in_memory=True) 