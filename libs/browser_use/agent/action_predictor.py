from __future__ import annotations

"""ActionSequencePredictor

Predice secuencias de acciones para agrupar acciones comunes como login flows.
"""

import logging
from typing import List, Optional, Dict, Any

from browser_use.agent.views import ActionModel
from browser_use.agent.pattern_knowledge import WebPatternKnowledgeBase

logger = logging.getLogger(__name__)


class ActionSequencePredictor:
    """Predice y agrupa secuencias de acciones comunes."""

    def __init__(self):
        self.pattern_knowledge = WebPatternKnowledgeBase()

    def predict_sequence(
        self, 
        current_actions: List[ActionModel], 
        page_content: str = "",
        elements_text: List[str] = None
    ) -> Optional[List[ActionModel]]:
        """
        Predice la secuencia completa de acciones basada en el patrón detectado.
        
        Args:
            current_actions: Lista de acciones actuales del modelo
            page_content: Contenido de la página para contexto
            elements_text: Lista de texto de elementos para contexto
            
        Returns:
            Lista de acciones completada si se detecta un patrón, None en caso contrario
        """
        if not current_actions:
            return None
            
        try:
            # Detectar si es un flujo de login
            if self._detect_login_flow(current_actions, page_content, elements_text or []):
                return self._complete_login_sequence(current_actions, page_content, elements_text or [])
            
            # Detectar si es un flujo de búsqueda
            if self._detect_search_flow(current_actions, page_content, elements_text or []):
                return self._complete_search_sequence(current_actions, page_content, elements_text or [])
                
            return None
            
        except Exception as e:
            logger.debug(f"Error in predict_sequence: {e}")
            return None

    def _detect_login_flow(self, actions: List[ActionModel], page_content: str, elements_text: List[str]) -> bool:
        """Detecta si las acciones actuales representan un flujo de login."""
        try:
            # Buscar indicadores de login en el contenido de la página
            login_keywords = ["login", "sign in", "iniciar sesión", "email", "password", "contraseña"]
            content_lower = page_content.lower()
            
            has_login_content = any(keyword in content_lower for keyword in login_keywords)
            
            # Revisar si hay campos de email y password en los elementos
            elements_text_lower = [text.lower() for text in elements_text]
            has_email_field = any("email" in text or "@" in text for text in elements_text_lower)
            has_password_field = any("password" in text or "contraseña" in text for text in elements_text_lower)
            
            # Revisar las acciones actuales
            action_types = []
            for action in actions:
                action_data = action.model_dump(exclude_unset=True)
                if "input_text" in action_data:
                    action_types.append("input")
                elif "click_element_by_index" in action_data:
                    action_types.append("click")
                    
            # Es un login flow si:
            # 1. Hay contenido relacionado con login
            # 2. Hay campos de email y password
            # 3. Las acciones incluyen inputs (para email/password)
            return (has_login_content and has_email_field and has_password_field and 
                   "input" in action_types)
                   
        except Exception as e:
            logger.debug(f"Error detecting login flow: {e}")
            return False

    def _detect_search_flow(self, actions: List[ActionModel], page_content: str, elements_text: List[str]) -> bool:
        """Detecta si las acciones actuales representan un flujo de búsqueda."""
        try:
            # Buscar indicadores de búsqueda
            search_keywords = ["search", "buscar", "find", "query"]
            content_lower = page_content.lower()
            
            has_search_content = any(keyword in content_lower for keyword in search_keywords)
            
            # Revisar elementos de búsqueda
            elements_text_lower = [text.lower() for text in elements_text]
            has_search_field = any("search" in text or "buscar" in text for text in elements_text_lower)
            
            # Revisar acciones
            has_input_and_click = False
            action_types = []
            for action in actions:
                action_data = action.model_dump(exclude_unset=True)
                if "input_text" in action_data:
                    action_types.append("input")
                elif "click_element_by_index" in action_data:
                    action_types.append("click")
                    
            has_input_and_click = "input" in action_types and "click" in action_types
            
            return has_search_content and has_search_field and has_input_and_click
            
        except Exception as e:
            logger.debug(f"Error detecting search flow: {e}")
            return False

    def _complete_login_sequence(self, current_actions: List[ActionModel], page_content: str, elements_text: List[str]) -> List[ActionModel]:
        """Completa la secuencia de login si está incompleta."""
        try:
            # Analizar qué acciones ya tenemos
            has_email_input = False
            has_password_input = False
            has_submit_click = False
            
            for action in current_actions:
                action_data = action.model_dump(exclude_unset=True)
                if "input_text" in action_data:
                    text_value = action_data["input_text"].get("text", "").lower()
                    if "@" in text_value or "email" in text_value:
                        has_email_input = True
                    elif any(pwd in text_value for pwd in ["password", "123", "admin"]):
                        has_password_input = True
                elif "click_element_by_index" in action_data:
                    has_submit_click = True
            
            # Si ya tenemos todas las acciones necesarias, return current
            if has_email_input and has_password_input and has_submit_click:
                logger.info("🎯 Login sequence already complete")
                return current_actions
                
            # Si no, intentar completar la secuencia
            completed_actions = list(current_actions)
            
            # Esta lógica es simplificada - en un escenario real necesitaríamos
            # analizar mejor los elementos disponibles
            logger.info("🎯 Login sequence completion attempted")
            return completed_actions
            
        except Exception as e:
            logger.debug(f"Error completing login sequence: {e}")
            return current_actions

    def _complete_search_sequence(self, current_actions: List[ActionModel], page_content: str, elements_text: List[str]) -> List[ActionModel]:
        """Completa la secuencia de búsqueda si está incompleta."""
        try:
            # Lógica similar para búsqueda
            logger.info("🎯 Search sequence completion attempted")
            return current_actions
            
        except Exception as e:
            logger.debug(f"Error completing search sequence: {e}")
            return current_actions

 