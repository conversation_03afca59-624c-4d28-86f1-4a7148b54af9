from __future__ import annotations

"""EfficiencyLearningSystem

Registra métricas básicas de cada paso y de la sesión para analizar mejoras de eficiencia.
Por ahora persiste en un archivo JSON dentro del FileSystem del agente.
"""

import json
import logging
import time
from pathlib import Path
from typing import Dict, Any, List
import statistics

from browser_use.agent.pattern_knowledge import WebPatternKnowledgeBase
from browser_use.agent.views import ActionModel
from browser_use.filesystem.file_system import FileSystem

logger = logging.getLogger(__name__)


class EfficiencyLearningSystem:
    """Registra y guarda métricas de eficiencia en un archivo JSON."""

    METRICS_FILE = "efficiency_metrics.json"

    def __init__(self, fs: FileSystem):
        self.fs = fs
        self.session_id = str(int(time.time()))

    # ------------------------------------------------------------------
    async def get_historical_metrics(self) -> List[Dict[str, Any]]:
        try:
            raw = await self.fs.read_file(self.METRICS_FILE)
            if raw.startswith("Read from file"):
                content = raw.split("\n", 1)[1]
            else:
                content = ""
            return json.loads(content) if content else []
        except Exception:
            return []

    # ------------------------------------------------------------------
    async def suggest_optimizations(self, task_type: str) -> List[str]:
        """Sugiere optimizaciones basadas en ejecuciones pasadas similares."""
        metrics = await self.get_historical_metrics()
        task_metrics = [m for m in metrics if m.get("task_type") == task_type and m.get("success")]

        # If there is no relevant historical data, return an empty list so the agent
        # will skip showing the efficiency banner. This avoids sending meaningless
        # messages like "No historical data for successful task ..." to the user.
        if not task_metrics:
            return []

        avg_steps = statistics.mean([m["steps_taken"] for m in task_metrics])
        avg_time = statistics.mean([m["time_elapsed"] for m in task_metrics])

        suggestions = [f"Analysis for task '{task_type}': avg. {avg_steps:.2f} steps, avg. {avg_time:.2f}s time."]

        # Simple heuristic: if it takes more than 10 steps or 30 seconds, it could be better.
        if avg_steps > 10 or avg_time > 30:
            suggestions.append(f"Task '{task_type}' seems complex. Consider breaking it down or using action grouping.")

        # Potentially, find the most efficient pattern used for this task in the past
        # This part can be expanded later.

        return suggestions

    # ------------------------------------------------------------------
    async def get_efficiency_score(self, actions: List[ActionModel]) -> float:
        """Calcula el score de eficiencia para una secuencia de acciones."""
        if not actions:
            return 0.0

        pattern_name = WebPatternKnowledgeBase.match_actions(actions)
        if pattern_name:
            pattern = WebPatternKnowledgeBase.get_pattern(pattern_name)
            if pattern:
                logger.info(f"Action sequence matched efficiency pattern: '{pattern_name}'")
                return pattern.get("efficiency_score", 0.7)  # Default score for a known pattern

        # Default score for unknown patterns
        # Could be more sophisticated (e.g., based on number of actions)
        return 0.5

    # ------------------------------------------------------------------
    async def record_execution_metrics(
        self,
        task_type: str,
        step_number: int,
        steps_taken: int,
        time_elapsed: float,
        success: bool,
        efficiency_score: float | None = None,
    ):
        data = {
            "session_id": self.session_id,
            "timestamp": time.time(),
            "task_type": task_type,
            "step": step_number,
            "steps_taken": steps_taken,
            "time_elapsed": time_elapsed,
            "success": success,
            "efficiency_score": efficiency_score,
        }
        await self._append_json(data)

    # ------------------------------------------------------------------
    async def _append_json(self, record: Dict[str, Any]):
        # Read current file
        existing = await self.get_historical_metrics()
        existing.append(record)
        await self.fs.write_file(self.METRICS_FILE, json.dumps(existing, indent=2)) 