from __future__ import annotations

"""OptimizedWorkingMemory

Reduce redundant writes to `todo.md` by batching updates and only writing when
changes are significant or the batch threshold is reached.

Designed to drop-in wrap a `FileSystem` instance used by browser-use agents.
"""

from typing import Optional
import asyncio
import logging

from browser_use.filesystem.file_system import FileSystem


class OptimizedWorkingMemory:
    """Batching layer for writes to *todo.md*.

    Parameters
    ----------
    file_system:
        The underlying FileSystem instance used by the agent.
    batch_threshold:
        Number of pending updates to accumulate before flushing to disk.
    logger:
        Optional logger instance.
    """

    def __init__(self, file_system: FileSystem, batch_threshold: int = 3, logger: Optional[logging.Logger] = None):
        self.file_system = file_system
        self.batch_threshold = max(1, batch_threshold)
        self._buffer: list[str] = []
        self._last_written: str = ""
        self.logger = logger or logging.getLogger(__name__)

        # Monkeypatch the underlying file_system.write_file for transparent batching
        self._orig_write_file = file_system.write_file

        async def _patched_write(file_name: str, content: str):
            if file_name.strip().lower() == "todo.md":
                return await self.write_todo(content)
            return await self._orig_write_file(file_name, content)

        # Bind the patched method to the file_system instance
        setattr(file_system, "write_file", _patched_write)

    # ------------------------------------------------------------------
    # Public helpers
    # ------------------------------------------------------------------

    async def write_todo(self, content: str) -> str:
        """Buffer or write *todo.md* content.

        If the content hasn't changed since last write, the call is a NO-OP.
        After *batch_threshold* distinct updates, the latest content is flushed
        to disk.
        """
        content = content.strip()
        if content == self._last_written:
            self.logger.debug("📝 OptimizedWorkingMemory: Skipping unchanged write to todo.md")
            return "Skipped unchanged write to todo.md."

        # If different, add to buffer
        self._buffer.append(content)
        if len(self._buffer) < self.batch_threshold:
            self.logger.debug(
                f"📝 OptimizedWorkingMemory: Buffered update {len(self._buffer)}/{self.batch_threshold} to todo.md"
            )
            return f"Buffered update to todo.md ({len(self._buffer)}/{self.batch_threshold})."

        # Flush latest content
        latest_content = self._buffer[-1]
        result = await self._orig_write_file("todo.md", latest_content)
        self._last_written = latest_content
        self._buffer.clear()
        self.logger.info("📝 OptimizedWorkingMemory: Flushed batched updates to todo.md")
        return result

    # Convenience proxies for direct writes if needed
    async def flush(self) -> str | None:
        """Force-flush pending updates regardless of threshold."""
        if not self._buffer:
            return None
        latest_content = self._buffer[-1]
        result = await self._orig_write_file("todo.md", latest_content)
        self._last_written = latest_content
        self._buffer.clear()
        self.logger.info("📝 OptimizedWorkingMemory: Manual flush executed for todo.md")
        return result

    # Transparent passthrough for other file operations
    def __getattr__(self, item):
        # If attribute isn't found on self, delegate to underlying file_system
        return getattr(self.file_system, item) 