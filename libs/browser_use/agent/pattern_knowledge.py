from __future__ import annotations

"""WebPatternKnowledgeBase

Contiene patrones comunes de interacción web con metadatos de eficiencia.
"""

from typing import Dict, List
import re
import logging

from browser_use.agent.views import ActionModel

logger = logging.getLogger(__name__)


class WebPatternKnowledgeBase:
    """Base de conocimiento in-memory con patrones de interacción."""

    COMMON_PATTERNS: Dict[str, Dict] = {
        "login_flow": {
            "sequence": ["input_text", "input_text", "click_element_by_index"],
            "keywords": ["email", "password", "login", "submit", "sign in"],
            "groupable": True,
            "efficiency_score": 0.95,
        },
        "search_flow": {
            "sequence": ["input_text", "click_element_by_index"],
            "keywords": ["search", "query", "find"],
            "groupable": True,
            "efficiency_score": 0.90,
        },
        "form_fill": {
            "sequence": ["input_text", "click_element_by_index"],
            "keywords": ["form", "submit", "send"],
            "groupable": True,
            "efficiency_score": 0.85,
        },
    }

    @classmethod
    def list_patterns(cls) -> List[str]:
        return list(cls.COMMON_PATTERNS.keys())

    @classmethod
    def get_pattern(cls, name: str) -> Dict | None:
        return cls.COMMON_PATTERNS.get(name)

    @classmethod
    def match_actions(cls, actions: List[ActionModel], page_content: str = "") -> str | None:
        """Devuelve el nombre del patrón que coincide con las acciones dadas."""
        if len(actions) < 2:
            return None
            
        action_names: List[str] = []
        for act in actions:
            d = act.model_dump(exclude_unset=True)
            if not d:
                continue
            action_names.append(next(iter(d)))

        # Análisis específico para login flow
        if cls._is_login_flow(actions, page_content):
            return "login_flow"
            
        # Análisis específico para search flow
        if cls._is_search_flow(actions, page_content):
            return "search_flow"
            
        # Análisis genérico para form fill
        if cls._is_form_flow(actions, page_content):
            return "form_fill"
            
        return None

    @classmethod
    def _is_login_flow(cls, actions: List[ActionModel], page_content: str) -> bool:
        """Detecta si es un flujo de login."""
        if len(actions) < 3:
            return False
            
        # Buscar 2 inputs seguidos de un click
        action_types = []
        for act in actions:
            d = act.model_dump(exclude_unset=True)
            if d:
                action_types.append(next(iter(d)))
        
        # Patrón: input_text, input_text, click
        if action_types[:3] == ["input_text", "input_text", "click_element_by_index"]:
            # Verificar contenido relacionado con login
            login_keywords = ["email", "password", "login", "sign in", "username", "passwd"]
            page_lower = page_content.lower()
            return any(keyword in page_lower for keyword in login_keywords)
            
        return False

    @classmethod
    def _is_search_flow(cls, actions: List[ActionModel], page_content: str) -> bool:
        """Detecta si es un flujo de búsqueda."""
        if len(actions) < 2:
            return False
            
        action_types = []
        for act in actions:
            d = act.model_dump(exclude_unset=True)
            if d:
                action_types.append(next(iter(d)))
        
        # Patrón: input_text, click
        if action_types[:2] == ["input_text", "click_element_by_index"]:
            search_keywords = ["search", "query", "find", "look", "buscar"]
            page_lower = page_content.lower()
            return any(keyword in page_lower for keyword in search_keywords)
            
        return False

    @classmethod
    def _is_form_flow(cls, actions: List[ActionModel], page_content: str) -> bool:
        """Detecta si es un flujo de formulario genérico."""
        if len(actions) < 2:
            return False
            
        action_types = []
        for act in actions:
            d = act.model_dump(exclude_unset=True)
            if d:
                action_types.append(next(iter(d)))
        
        # Al menos un input seguido de un click
        input_count = action_types.count("input_text")
        has_click = "click_element_by_index" in action_types
        
        if input_count >= 1 and has_click:
            form_keywords = ["form", "submit", "send", "save", "create", "register"]
            page_lower = page_content.lower()
            return any(keyword in page_lower for keyword in form_keywords)
            
        return False

    @staticmethod
    def _sequence_matches(pattern_seq: List[str], actions: List[str]) -> bool:
        """Comprueba coincidencia secuencial exacta al inicio."""
        if not pattern_seq or len(actions) < len(pattern_seq):
            return False
        return actions[:len(pattern_seq)] == pattern_seq 