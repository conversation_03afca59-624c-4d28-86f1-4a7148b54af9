from __future__ import annotations

import json
import logging
from typing import Dict, Any, List, TypedDict

from browser_use.filesystem.file_system import FileSystem

logger = logging.getLogger(__name__)

class RecoveryStrategy(TypedDict):
    """Define la estructura de una estrategia de recovery."""
    name: str
    actions: List[Dict[str, Any]]
    confidence: float

class PatternBasedRecovery:
    """
    Sistema de recovery que usa y aprende patrones de fallos y recuperaciones.
    """

    PATTERNS_FILE = "recovery_patterns.json"

    def __init__(self, fs: FileSystem):
        self.fs = fs
        self._patterns: List[Dict[str, Any]] = []

    async def _load_patterns(self):
        """Carga los patrones de recovery desde el archivo."""
        if self._patterns:
            return
        try:
            content = await self.fs.read_file(self.PATTERNS_FILE)
            self._patterns = json.loads(content)
        except (FileNotFoundError, json.JSONDecodeError):
            self._patterns = []

    async def recover_from_failure(self, failure_context: Dict[str, Any]) -> RecoveryStrategy | None:
        """
        Intenta determinar una estrategia de recovery basada en patrones aprendidos.
        El failure_context debería contener información sobre el error, la URL, la última acción, etc.
        """
        await self._load_patterns()
        
        error_type = str(type(failure_context.get("error", "")))
        
        # Lógica de matching muy simple por ahora: buscar por tipo de error
        best_match = None
        highest_confidence = 0.0

        for pattern in self._patterns:
            if pattern.get("error_type") == error_type:
                if pattern.get("confidence", 0) > highest_confidence:
                    best_match = pattern
                    highest_confidence = pattern.get("confidence", 0)
        
        return best_match

    async def learn_from_recovery(self, failure_context: Dict[str, Any], recovery_strategy: RecoveryStrategy, success: bool):
        """
        Aprende de una recuperación. Si fue exitosa, refuerza el patrón.
        Si es un nuevo tipo de recuperación exitosa, lo añade.
        """
        if not success:
            return

        await self._load_patterns()

        error_type = str(type(failure_context.get("error", "")))
        
        # Por ahora, simplemente añadimos la estrategia si es un nuevo tipo de error.
        # Una implementación más avanzada podría actualizar confianzas.
        existing_pattern = next((p for p in self._patterns if p.get("error_type") == error_type), None)
        
        if not existing_pattern:
            new_pattern = {
                "error_type": error_type,
                "actions": recovery_strategy["actions"],
                "confidence": 0.75, # Confianza inicial
            }
            self._patterns.append(new_pattern)
            await self._save_patterns()

    async def _save_patterns(self):
        """Guarda los patrones actualizados en el archivo."""
        await self.fs.write_file(self.PATTERNS_FILE, json.dumps(self._patterns, indent=2)) 