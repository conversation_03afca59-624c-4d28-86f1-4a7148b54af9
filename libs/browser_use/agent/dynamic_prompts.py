from __future__ import annotations

"""DynamicPromptManager

Gestiona la selección de prompts optimizados basados en el contexto de la página.
"""

from typing import Dict, Callable

from browser_use.browser.views import BrowserStateSummary


class DynamicPromptManager:
    """Gestiona prompts dinámicos basados en el contexto de la página."""

    def detect_page_type(self, browser_state: BrowserStateSummary) -> str | None:
        """
        Detecta el tipo de página basándose en análisis del DOM y heurísticas de contenido.
        """
        url = browser_state.url.lower()
        title = browser_state.title.lower()

        # Prioridad 1: Login (alta especificidad)
        has_password_input = any(e.get("type") == "password" for e in browser_state.all_input_elements)
        has_email_or_text_input = any(e.get("type") in ["email", "text"] for e in browser_state.all_input_elements)
        login_button = any(
            "login" in b.get("text", "").lower() or "sign in" in b.get("text", "").lower()
            for b in browser_state.all_clickable_elements
        )

        if has_password_input and has_email_or_text_input and login_button:
            return "login_page"

        # Prioridad 2: Búsqueda
        search_input = any(
            e.get("type") == "search"
            or "search" in e.get("placeholder", "").lower()
            or "search" in e.get("aria-label", "").lower()
            for e in browser_state.all_input_elements
        )
        search_button = any("search" in b.get("text", "").lower() for b in browser_state.all_clickable_elements)

        if search_input and search_button:
            return "search_page"

        # Prioridad 3: Formularios genéricos (e.g., registro, contacto)
        submit_button = any(
            b.get("type") == "submit"
            or "submit" in b.get("text", "").lower()
            or "send" in b.get("text", "").lower()
            or "register" in b.get("text", "").lower()
            for b in browser_state.all_clickable_elements
        )
        if len(browser_state.all_input_elements) >= 3 and submit_button:
            return "form_page"

        # Fallback a heurísticas de texto si el análisis DOM no es concluyente
        page_text = browser_state.page_text.lower()
        login_keywords = ["login", "sign in", "authenticate"]
        if any(k in page_text for k in login_keywords) and has_password_input:
            return "login_page"

        return None

    def get_login_optimization_prompt(self) -> str:
        return """--- Login Page Detected ---
OPTIMIZATION: Group login actions for maximum efficiency.
- Combine input for email/username, password, and the submit click into a single step.
- After action, automatically verify success by looking for a user menu, logout button, or a URL change.
- If login fails, check for specific error messages before retrying.
"""

    def get_form_optimization_prompt(self) -> str:
        return """--- Form Page Detected ---
OPTIMIZATION: Group form-filling actions.
- Fill all related input fields, text areas, and select dropdowns in a single step.
- End the sequence with the form submission action.
- Verify success by checking for a confirmation message or redirection.
"""

    def get_search_optimization_prompt(self) -> str:
        return """--- Search Page Detected ---
OPTIMIZATION: Group search actions.
- Combine typing the search query and clicking the search button into one step.
- Verify that search results are displayed on the page.
"""

    def get_ecommerce_optimization_prompt(self) -> str:
        return """--- Ecommerce Context Detected ---
OPTIMIZATION: Streamline checkout and product interaction.
- Add items to cart in grouped actions if possible.
- For checkout, fill shipping, billing, and payment information in grouped steps.
- Verify each major step of the checkout process (e.g., shipping calculated, payment confirmed).
"""

    def get_context_specific_prompt(self, page_type: str, task_type: str | None = None) -> str | None:
        """
        Retorna un prompt optimizado para el contexto específico de la página.
        `page_type` puede ser: "login_page", "form_page", "search_page", "ecommerce".
        """
        # Map page types to their respective prompt-generating methods
        context_prompts: Dict[str, Callable[[], str]] = {
            "login_page": self.get_login_optimization_prompt,
            "form_page": self.get_form_optimization_prompt,
            "search_page": self.get_search_optimization_prompt,
            "ecommerce": self.get_ecommerce_optimization_prompt,
        }

        # Get the prompt-generating function for the given page_type
        prompt_function = context_prompts.get(page_type)

        if prompt_function:
            return prompt_function()

        return None 