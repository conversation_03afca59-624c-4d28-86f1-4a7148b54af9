from __future__ import annotations
import fnmatch
from typing import Dict, Any
from urllib.parse import urlparse


class SiteSpecificOptimizations:
    """
    Contiene configuraciones y optimizaciones específicas para sitios web comunes
    para mejorar la precisión y eficiencia del agente.
    """

    SITE_PATTERNS: Dict[str, Dict[str, Any]] = {
        "*.google.com": {
            "login_selectors": {
                "user": "#identifierId",
                "password": "input[type='password']",
            },
            "submit_delay_ms": 100,
            "common_flows": ["google_login", "google_search"],
        },
        "*.github.com": {
            "login_selectors": {
                "user": "#login_field",
                "password": "#password",
            },
            "requires_2fa_check": True,
            "common_flows": ["github_login", "repository_navigation"],
        },
        "*.facebook.com": {
            "login_selectors": {
                "user": "#email",
                "password": "#pass",
            },
            "common_flows": ["facebook_login"],
        },
    }

    @classmethod
    def get_optimizations_for_url(cls, url: str) -> Dict[str, Any] | None:
        """
        Encuentra y devuelve las optimizaciones para una URL dada.
        """
        if not url:
            return None

        hostname = urlparse(url).hostname
        if not hostname:
            return None

        for pattern, optimizations in cls.SITE_PATTERNS.items():
            if fnmatch.fnmatch(hostname, pattern):
                return optimizations

        return None 