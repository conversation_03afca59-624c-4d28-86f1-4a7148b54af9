"""
Semantic Memory System for QAK browser-use Agents

This module provides an enhanced semantic memory system that learns from
agent interactions and stores knowledge in a MongoDB database. It enables
agents to leverage past experiences for more intelligent decision-making.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import uuid
from collections import Counter

# Database integration
from src.database.repositories import SemanticPatternRepository, SemanticInteractionRepository
from src.database.models import SemanticPattern as SemanticPatternDoc, SemanticInteraction as SemanticInteractionDoc, PatternContext, TargetElement

logger = logging.getLogger(__name__)

@dataclass
class SemanticPattern:
    """Represents a learned pattern from agent interactions (in-memory representation)"""
    pattern_id: str
    pattern_type: str
    description: str
    context: Dict[str, Any]
    success_indicators: List[str]
    failure_indicators: List[str]
    confidence_score: float
    usage_count: int
    last_used: datetime
    merged_from: Optional[str] = None

@dataclass
class InteractionMemory:
    """Stores details about a specific interaction (in-memory representation)"""
    timestamp: datetime
    url: str
    action_type: str
    target_element: Dict[str, Any]
    context: str
    success: bool
    result: str
    lesson_learned: Optional[str] = None

class SemanticMemory:
    """
    DB-backed semantic memory system for browser-use agents.
    This class interacts with MongoDB to store and retrieve learned patterns
    and interaction histories, removing the dependency on local JSON files.
    """

    def __init__(
        self,
        agent_id: str,
        pattern_repo: SemanticPatternRepository,
        interaction_repo: SemanticInteractionRepository,
        max_recent_interactions: int = 100,
    ):
        self.agent_id = agent_id
        self.pattern_repo = pattern_repo
        self.interaction_repo = interaction_repo
        self.max_recent_interactions = max_recent_interactions
        self.in_memory_patterns: List[SemanticPattern] = []
        
        # Learning thresholds
        self.min_interactions_for_pattern = 5
        self.min_success_rate_for_pattern = 0.6
        
        logger.info(f"🧠 Initialized MongoDB-backed semantic memory for agent {self.agent_id}")

    async def load_patterns_from_db(self) -> None:
        """
        Loads relevant patterns for the agent from the database into a local
        cache for faster access during a session.
        """
        logger.info(f"Loading patterns for agent '{self.agent_id}' from database...")
        # Here you could add logic to fetch agent-specific or domain-specific patterns
        pattern_docs = await self.pattern_repo.get_all_patterns(limit=1000)
        self.in_memory_patterns = [
            SemanticPattern(
                pattern_id=p.pattern_id,
                pattern_type=p.pattern_type,
                description=p.description,
                context=p.context.dict() if p.context else {},
                success_indicators=p.success_indicators,
                failure_indicators=p.failure_indicators,
                confidence_score=p.confidence_score,
                usage_count=p.usage_count,
                last_used=p.last_used,
                merged_from=p.merged_from,
            )
            for p in pattern_docs
        ]
        logger.info(f"Loaded {len(self.in_memory_patterns)} patterns into memory.")

    async def record_interaction(
        self,
        action_type: str,
        target_element: Dict[str, Any],
        context: str,
        url: str,
        success: bool,
        result: str,
        lesson_learned: Optional[str] = None
    ) -> None:
        """Records a new interaction directly into the MongoDB database."""
        interaction_doc = SemanticInteractionDoc(
            interaction_id=str(uuid.uuid4()),
            agent_id=self.agent_id,
            timestamp=datetime.now(),
            url=url,
            action_type=action_type,
            target_element=TargetElement(element_data=target_element),
            context=context,
            success=success,
            result=result,
            lesson_learned=lesson_learned
        )
        await self.interaction_repo.add_interaction(interaction_doc)
        logger.debug(f"Recorded interaction for agent {self.agent_id}: {action_type} on {url}")

    async def analyze_and_learn_patterns(self) -> None:
        """
        Analyzes recent interactions from the DB for the current agent,
        learns new patterns, and saves them back to the DB.
        """
        logger.info(f"Analyzing interactions for agent '{self.agent_id}' to learn new patterns...")
        
        interactions_cursor = self.interaction_repo.get_recent_interactions(self.agent_id, limit=200)
        interactions_data = await interactions_cursor.to_list(length=200)
        
        interactions = [
            InteractionMemory(
                timestamp=i.timestamp,
                url=i.url,
                action_type=i.action_type,
                target_element=i.target_element.element_data if i.target_element else {},
                context=i.context,
                success=i.success,
                result=i.result
            ) for i in interactions_data
        ]

        if len(interactions) < self.min_interactions_for_pattern:
            logger.info("Not enough interactions to analyze for new patterns.")
            return

        grouped_interactions = self._group_similar_interactions(interactions)
        
        new_patterns_learned = 0
        for group in grouped_interactions:
            if len(group) >= self.min_interactions_for_pattern:
                pattern = self._extract_pattern_from_group(group)
                if pattern and pattern.confidence_score >= self.min_success_rate_for_pattern:
                    pattern_doc = SemanticPatternDoc(
                        pattern_id=pattern.pattern_id,
                        pattern_type=pattern.pattern_type,
                        description=pattern.description,
                        context=PatternContext(**pattern.context),
                        success_indicators=pattern.success_indicators,
                        failure_indicators=pattern.failure_indicators,
                        confidence_score=pattern.confidence_score,
                        usage_count=pattern.usage_count,
                        last_used=pattern.last_used
                    )
                    await self.pattern_repo.upsert_pattern(pattern_doc)
                    new_patterns_learned += 1
        
        if new_patterns_learned > 0:
            logger.info(f"💡 Learned and saved {new_patterns_learned} new patterns for agent {self.agent_id}.")
            await self.load_patterns_from_db() # Refresh in-memory patterns

    def _group_similar_interactions(self, interactions: List[InteractionMemory]) -> List[List[InteractionMemory]]:
        """Groups interactions by action type and URL domain."""
        groups = {}
        for interaction in interactions:
            domain = self._extract_domain(interaction.url)
            key = (interaction.action_type, domain)
            if key not in groups:
                groups[key] = []
            groups[key].append(interaction)
        return list(groups.values())

    def _extract_pattern_from_group(self, group: List[InteractionMemory]) -> Optional[SemanticPattern]:
        """Extracts a single pattern from a group of similar interactions."""
        if not group:
            return None
        
        success_rate = sum(1 for i in group if i.success) / len(group)
        
        # Use the most recent interaction for context details
        most_recent = max(group, key=lambda i: i.timestamp)
        
        common_context = self._find_common_context(group)
        description = f"Learned {most_recent.action_type} pattern for {common_context.get('domain', 'unknown site')}"
        
        pattern_id_seed = f"{description}{common_context.get('domain', '')}{most_recent.action_type}"
        
        return SemanticPattern(
            pattern_id=f"learned_{uuid.uuid5(uuid.NAMESPACE_DNS, pattern_id_seed)}",
            pattern_type="learned",
            description=description,
            context=common_context,
            success_indicators=self._extract_success_indicators(group),
            failure_indicators=self._extract_failure_indicators(group),
            confidence_score=success_rate,
            usage_count=len(group),
            last_used=most_recent.timestamp
        )

    def _find_common_context(self, interactions: List[InteractionMemory]) -> Dict[str, Any]:
        """Finds common contextual elements from a group of interactions."""
        domains = [self._extract_domain(i.url) for i in interactions]
        domain_counts = Counter(domains)
        most_common_domain = domain_counts.most_common(1)[0][0] if domain_counts else None
        
        return {"domain": most_common_domain}

    def _extract_success_indicators(self, interactions: List[InteractionMemory]) -> List[str]:
        """Extracts common keywords from successful interaction results."""
        # This is a simplified implementation. A more advanced version would use NLP.
        success_texts = [i.result for i in interactions if i.success and i.result]
        word_counts = Counter(" ".join(success_texts).lower().split())
        return [word for word, count in word_counts.most_common(5)]

    def _extract_failure_indicators(self, interactions: List[InteractionMemory]) -> List[str]:
        """Extracts common keywords from failed interaction results."""
        failure_texts = [i.result for i in interactions if not i.success and i.result]
        word_counts = Counter(" ".join(failure_texts).lower().split())
        return [word for word, count in word_counts.most_common(5)]

    def _extract_domain(self, url: str) -> str:
        """Extracts the domain from a URL."""
        try:
            from urllib.parse import urlparse
            return urlparse(url).netloc
        except Exception:
            return "unknown_domain"

    async def get_suggestions_for_action(
        self,
        context: str,
        url: str
    ) -> List[SemanticPattern]:
        """
        Finds relevant patterns from the in-memory store based on the
        current context and URL.
        """
        # In a real-world scenario, you might query the DB directly for scalability
        # but for performance, we use the in-memory cache loaded at the start.
        query_words = set(context.lower().split())
        domain = self._extract_domain(url)
        
        relevant_patterns = []
        for pattern in self.in_memory_patterns:
            if pattern.context.get("domain") == domain:
                pattern_words = set(pattern.description.lower().split())
                if query_words.intersection(pattern_words):
                    relevant_patterns.append(pattern)
                    
        relevant_patterns.sort(key=lambda p: p.confidence_score, reverse=True)
        return relevant_patterns[:5]

    async def get_memory_stats(self) -> Dict[str, Any]:
        """Retrieves statistics about the agent's memory from the database."""
        total_patterns = await self.pattern_repo.get_patterns_count()
        total_interactions = await self.interaction_repo.get_interactions_count(self.agent_id)
        
        return {
            "agent_id": self.agent_id,
            "patterns_in_db": total_patterns,
            "interactions_in_db_for_agent": total_interactions,
            "patterns_in_memory_cache": len(self.in_memory_patterns)
        } 