from __future__ import annotations

"""ActionContextAnalyzer

Determina si un conjunto de acciones puede ejecutarse de forma agrupada usando
heurísticas sobre el árbol DOM y el tipo de acciones.
"""

from typing import List
import logging

from browser_use.agent.views import ActionModel
from browser_use.browser.views import BrowserStateSummary

logger = logging.getLogger(__name__)


class ActionContextAnalyzer:
    """Analiza si las acciones pueden agruparse de manera segura."""

    def __init__(self):
        self.logger = logger

    # Public API ---------------------------------------------------------
    def can_group_actions(self, actions: List[ActionModel], browser_state: BrowserStateSummary) -> bool:
        """Devuelve True si todas las acciones pueden ejecutarse juntas."""
        if len(actions) <= 1:
            return True

        # Heurística 1: todas las acciones deben ser del mismo tipo de contexto frame
        common_frame = self._get_common_frame(actions, browser_state)
        if not common_frame:
            return False

        # Heurística 2: no incluir acciones que disparen navegación
        if any(self._triggers_navigation(a) for a in actions):
            return False

        # Heurística 3: límite de acciones de escritura + 1 submit/enter
        input_count = sum(self._is_input(a) for a in actions)
        submit_count = sum(self._is_submit(a) for a in actions)
        if submit_count > 1:
            return False
        if submit_count == 1 and input_count == 0:
            return False

        return True

    # ------------------------------------------------------------------
    def _get_common_frame(self, actions: List[ActionModel], browser_state: BrowserStateSummary) -> bool:
        """Comprueba si los índices de iframe (si existen) son iguales."""
        frames = set()
        for action in actions:
            data = action.model_dump(exclude_unset=True)
            # Suponemos que cada acción tiene target_element -> iframe_idx si aplica
            element = next(iter(data.values()), {})
            if isinstance(element, dict) and 'iframe_index' in element:
                frames.add(element['iframe_index'])
        return len(frames) <= 1

    @staticmethod
    def _triggers_navigation(action: ActionModel) -> bool:
        data = action.model_dump(exclude_unset=True)
        nav_keys = {'go_to_url', 'click_element_that_navigates', 'navigate'}
        return any(k in data for k in nav_keys)

    @staticmethod
    def _is_input(action: ActionModel) -> bool:
        data = action.model_dump(exclude_unset=True)
        return any('input_text' in k or 'fill' in k for k in data)

    @staticmethod
    def _is_submit(action: ActionModel) -> bool:
        data = action.model_dump(exclude_unset=True)
        return any('click' in k or 'submit' in k for k in data) 