"""
<PERSON><PERSON> and <PERSON><PERSON>ler for browser-use.

This module provides robust handling of JavaScript alerts and dynamic modals.
Follows clean architecture with clear separation of concerns.

Features:
- JavaScript alert/confirm/prompt handling
- Dynamic modal detection and closing
- Multiple close strategies
- Timeout handling
- Error recovery
"""

import asyncio
import logging
from typing import Optional, Dict, Any, Literal

from playwright.async_api import Page, Dialog
from browser_use.agent.views import ActionResult
from browser_use.browser.session import BrowserSession

from .models.alert_models import (
    HandleAlertAction,
    HandleModalAction,
    AlertResponse,
    ModalResponse,
)

logger = logging.getLogger(__name__)


class AlertModalHandler:
    """
    Service class for handling JavaScript alerts and dynamic modals.
    
    This class encapsulates all the business logic for alert/modal handling,
    keeping the action registration clean and focused.
    """
    
    def __init__(self):
        self._dialog_responses: Dict[str, Any] = {}
        self._modal_selectors = [
            '[role="dialog"]',
            '.modal',
            '.popup',
            '.overlay',
            '[data-modal]',
            '.dialog',
            '.lightbox',
        ]
        self._close_button_selectors = [
            '[aria-label*="close" i]',
            '[title*="close" i]',
            '.close',
            '.modal-close',
            '.dialog-close',
            '[data-dismiss]',
            'button[class*="close"]',
            '.btn-close',
        ]
    
    async def handle_javascript_alert(
        self,
        params: HandleAlertAction,
        page: Page
    ) -> AlertResponse:
        """Handle JavaScript alerts (alert, confirm, prompt)."""
        try:
            dialog_info = None
            dialog_handled = False
            
            async def dialog_handler(dialog: Dialog):
                nonlocal dialog_info, dialog_handled
                dialog_info = {
                    'type': dialog.type,
                    'message': dialog.message,
                    'default_value': dialog.default_value if dialog.type == 'prompt' else None
                }
                
                try:
                    if params.action == 'accept':
                        if dialog.type == 'prompt' and params.text:
                            await dialog.accept(params.text)
                        else:
                            await dialog.accept()
                    elif params.action == 'dismiss':
                        await dialog.dismiss()
                    elif params.action == 'get_text':
                        await dialog.dismiss()
                    
                    dialog_handled = True
                    logger.info(f"Handled {dialog.type} dialog: {dialog.message}")
                    
                except Exception as e:
                    logger.error(f"Error handling dialog: {e}")
                    await dialog.dismiss()
            
            page.on('dialog', dialog_handler)
            
            try:
                if params.auto_detect:
                    await asyncio.sleep(0.1)
                    start_time = asyncio.get_event_loop().time()
                    while not dialog_handled and (asyncio.get_event_loop().time() - start_time) * 1000 < params.timeout:
                        await asyncio.sleep(0.1)
                
                if dialog_info:
                    return AlertResponse(
                        success=True,
                        alert_type=dialog_info['type'],
                        alert_text=dialog_info['message'],
                        action_taken=params.action if dialog_handled else 'detected_only',
                        error=None
                    )
                else:
                    return AlertResponse(
                        success=False,
                        alert_type=None,
                        alert_text=None,
                        action_taken=None,
                        error="No JavaScript alert detected within timeout period"
                    )
                    
            finally:
                page.remove_listener('dialog', dialog_handler)
                
        except Exception as e:
            logger.error(f"Error in alert handling: {e}")
            return AlertResponse(
                success=False,
                alert_type=None,
                alert_text=None,
                action_taken=None,
                error=f"Alert handling failed: {str(e)}"
            )
    
    async def handle_dynamic_modal(
        self,
        params: HandleModalAction,
        page: Page
    ) -> ModalResponse:
        """Handle dynamic modal dialogs (not JavaScript alerts)."""
        try:
            modal_found = False
            modal_selector = None
            close_method_used = None
            
            modal_selector = await self._detect_modal(page, params.modal_selector)
            if modal_selector:
                modal_found = True
                logger.info(f"Modal detected with selector: {modal_selector}")
            else:
                return ModalResponse(
                    success=False,
                    modal_found=False,
                    close_method_used=None,
                    modal_selector=None,
                    error="No modal detected on the page"
                )
            
            close_success = False
            
            if params.close_method == 'escape':
                close_success = await self._close_modal_with_escape(page)
                close_method_used = 'escape'
            elif params.close_method == 'click_overlay':
                close_success = await self._close_modal_with_overlay_click(page, modal_selector)
                close_method_used = 'click_overlay'
            elif params.close_method == 'click_x':
                close_success = await self._close_modal_with_close_button(page, modal_selector, params.close_button_selector)
                close_method_used = 'click_x'
            elif params.close_method == 'click_button':
                close_success = await self._close_modal_with_button(page, modal_selector)
                close_method_used = 'click_button'
            
            await asyncio.sleep(0.5)
            modal_still_present = await self._detect_modal(page, modal_selector)
            
            return ModalResponse(
                success=close_success and not modal_still_present,
                modal_found=modal_found,
                close_method_used=close_method_used,
                modal_selector=modal_selector,
                error=None,
                metadata={
                    'modal_closed': not modal_still_present,
                    'close_attempted': close_success
                }
            )
            
        except Exception as e:
            logger.error(f"Error in modal handling: {e}")
            return ModalResponse(
                success=False,
                modal_found=False,
                close_method_used=None,
                modal_selector=None,
                error=f"Modal handling failed: {str(e)}"
            )
    
    async def _detect_modal(self, page: Page, custom_selector: Optional[str] = None) -> Optional[str]:
        """Detect if a modal is present on the page."""
        selectors_to_check = []
        
        if custom_selector:
            selectors_to_check.append(custom_selector)
        
        selectors_to_check.extend(self._modal_selectors)
        
        for selector in selectors_to_check:
            try:
                element = await page.query_selector(selector)
                if element:
                    is_visible = await element.is_visible()
                    if is_visible:
                        return selector
            except Exception:
                continue
        
        return None
    
    async def _close_modal_with_escape(self, page: Page) -> bool:
        """Close modal using Escape key."""
        try:
            await page.keyboard.press('Escape')
            return True
        except Exception as e:
            logger.error(f"Failed to close modal with Escape: {e}")
            return False
    
    async def _close_modal_with_overlay_click(self, page: Page, modal_selector: str) -> bool:
        """Close modal by clicking outside the modal content."""
        try:
            await page.click(modal_selector, position={'x': 5, 'y': 5})
            return True
        except Exception as e:
            logger.error(f"Failed to close modal with overlay click: {e}")
            return False
    
    async def _close_modal_with_close_button(
        self,
        page: Page,
        modal_selector: str,
        custom_close_selector: Optional[str] = None
    ) -> bool:
        """Close modal by clicking the close button."""
        try:
            close_selectors = []
            
            if custom_close_selector:
                close_selectors.append(custom_close_selector)
            
            for base_selector in self._close_button_selectors:
                close_selectors.append(f"{modal_selector} {base_selector}")
            
            for selector in close_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element and await element.is_visible():
                        await element.click()
                        return True
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to close modal with close button: {e}")
            return False
    
    async def _close_modal_with_button(self, page: Page, modal_selector: str) -> bool:
        """Close modal by clicking any button (Cancel, OK, Close, etc.)."""
        try:
            button_selectors = [
                f"{modal_selector} button",
                f"{modal_selector} [role='button']",
                f"{modal_selector} .btn",
                f"{modal_selector} input[type='button']",
            ]
            
            for selector in button_selectors:
                try:
                    buttons = await page.query_selector_all(selector)
                    for button in buttons:
                        if await button.is_visible():
                            text = await button.inner_text()
                            if any(word in text.lower() for word in ['close', 'cancel', 'ok', 'dismiss']):
                                await button.click()
                                return True
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to close modal with button: {e}")
            return False


def register_alert_modal_actions(controller):
    """Register alert and modal handling actions with the controller."""
    handler = AlertModalHandler()
    
    @controller.registry.action(
        'Handle JavaScript alert, confirm or prompt dialogs',
        param_model=HandleAlertAction
    )
    async def handle_js_alert(params: HandleAlertAction, page):
        """Handle JavaScript alerts, confirms, and prompts."""
        response = await handler.handle_javascript_alert(params, page)
        
        if response.success:
            result_text = f"Alert handled: {response.alert_type} - {response.alert_text}"
            if response.action_taken:
                result_text += f" (Action: {response.action_taken})"
        else:
            result_text = f"Alert handling failed: {response.error}"
        
        return ActionResult(
            extracted_content=result_text,
            include_in_memory=True,
            success=response.success,
            error=response.error if not response.success else None
        )
    
    @controller.registry.action(
        'Handle dynamic modal dialogs and overlays',
        param_model=HandleModalAction
    )
    async def handle_modal(params: HandleModalAction, page):
        """Handle dynamic modal dialogs."""
        response = await handler.handle_dynamic_modal(params, page)
        
        if response.success:
            result_text = f"Modal handled successfully using {response.close_method_used}"
            if response.modal_selector:
                result_text += f" (Selector: {response.modal_selector})"
        else:
            result_text = f"Modal handling failed: {response.error}"
        
        return ActionResult(
            extracted_content=result_text,
            include_in_memory=True,
            success=response.success,
            error=response.error if not response.success else None
        ) 