# HTTP Request Handler for Browser-Use Extensions
import asyncio
import json
import logging
import mimetypes
import os
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse, urljoin

import aiohttp
import aiofiles
from browser_use.browser.session import BrowserSession
from browser_use.browser.types import Page
from browser_use.controller.extensions.models.request_models import (
    HttpRequestAction,
    FileUploadAction,
    CompareApiAction,
    ProxyRequestAction,
    HttpResponse,
    FileUploadResponse,
    ApiComparisonResult,
    ProxyResponse,
)

logger = logging.getLogger(__name__)


class HttpRequestHandler:
    """
    Handler for HTTP requests with browser context integration.
    Provides functionality for making HTTP requests using browser cookies/headers,
    file uploads, API comparisons, and proxy requests.
    """

    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.logger = logger.getChild(self.__class__.__name__)

    async def _ensure_session(self) -> aiohttp.ClientSession:
        """Ensure aiohttp session is created and return it."""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=300)  # 5 minutes default
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session

    async def _get_browser_cookies(self, page: Page) -> Dict[str, str]:
        """Extract cookies from the browser page."""
        try:
            cookies = await page.context.cookies()
            return {cookie['name']: cookie['value'] for cookie in cookies}
        except Exception as e:
            self.logger.warning(f"Failed to get browser cookies: {e}")
            return {}

    async def _get_browser_headers(self, page: Page) -> Dict[str, str]:
        """Get common browser headers from the current page."""
        try:
            # Get basic headers that browsers typically send
            user_agent = await page.evaluate("() => navigator.userAgent")
            current_url = page.url
            
            headers = {
                'User-Agent': user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            
            # Add referer if we have a current URL
            if current_url and current_url != 'about:blank':
                headers['Referer'] = current_url
                
            return headers
        except Exception as e:
            self.logger.warning(f"Failed to get browser headers: {e}")
            return {
                'User-Agent': 'Mozilla/5.0 (compatible; browser-use/1.0)',
                'Accept': 'application/json, text/plain, */*',
            }

    async def make_http_request(
        self,
        params: HttpRequestAction,
        page: Page
    ) -> HttpResponse:
        """
        Make an HTTP request with optional browser context (cookies, headers).
        """
        start_time = time.time()
        session = await self._ensure_session()
        
        try:
            # Prepare headers
            headers = {}
            if params.use_browser_headers:
                browser_headers = await self._get_browser_headers(page)
                headers.update(browser_headers)
            
            if params.headers:
                headers.update(params.headers)

            # Prepare cookies
            cookies = {}
            if params.use_browser_cookies:
                browser_cookies = await self._get_browser_cookies(page)
                cookies.update(browser_cookies)

            # Prepare request kwargs
            request_kwargs = {
                'method': params.method,
                'url': params.url,
                'headers': headers,
                'cookies': cookies,
                'timeout': aiohttp.ClientTimeout(total=params.timeout),
                'allow_redirects': params.follow_redirects,
                'ssl': params.verify_ssl,
            }

            # Add data for non-GET requests
            if params.data and params.method in ['POST', 'PUT', 'PATCH']:
                if isinstance(params.data, dict):
                    request_kwargs['json'] = params.data
                    headers['Content-Type'] = 'application/json'
                else:
                    request_kwargs['data'] = params.data

            # Add query parameters
            if params.params:
                request_kwargs['params'] = params.params

            self.logger.info(f"Making {params.method} request to {params.url}")
            
            async with session.request(**request_kwargs) as response:
                # Get response data
                try:
                    if response.content_type == 'application/json':
                        data = await response.json()
                    else:
                        data = await response.text()
                except Exception:
                    data = await response.text()

                # Extract response cookies
                response_cookies = {}
                if response.cookies:
                    response_cookies = {k: v.value for k, v in response.cookies.items()}

                request_time = time.time() - start_time
                
                # Determine final URL after redirects
                redirect_url = str(response.url) if str(response.url) != params.url else None

                return HttpResponse(
                    success=200 <= response.status < 400,
                    status_code=response.status,
                    headers=dict(response.headers),
                    data=data,
                    cookies=response_cookies,
                    redirect_url=redirect_url,
                    request_time=request_time,
                    metadata={
                        'request_method': params.method,
                        'request_url': params.url,
                        'used_browser_cookies': params.use_browser_cookies,
                        'used_browser_headers': params.use_browser_headers,
                        'redirects_followed': params.follow_redirects,
                    }
                )

        except asyncio.TimeoutError:
            request_time = time.time() - start_time
            error_msg = f"Request timeout after {params.timeout} seconds"
            self.logger.error(error_msg)
            return HttpResponse(
                success=False,
                status_code=408,
                headers={},
                request_time=request_time,
                error=error_msg
            )
        except Exception as e:
            request_time = time.time() - start_time
            error_msg = f"Request failed: {str(e)}"
            self.logger.error(error_msg)
            return HttpResponse(
                success=False,
                status_code=0,
                headers={},
                request_time=request_time,
                error=error_msg
            )

    async def upload_files(
        self,
        params: FileUploadAction,
        page: Page
    ) -> FileUploadResponse:
        """
        Upload files via HTTP request with browser context.
        """
        start_time = time.time()
        session = await self._ensure_session()
        
        uploaded_files = []
        failed_files = []
        total_size = 0
        upload_urls = {}
        file_ids = {}

        try:
            # Prepare headers and cookies
            headers = {}
            cookies = {}
            
            if params.use_browser_context:
                browser_headers = await self._get_browser_headers(page)
                headers.update(browser_headers)
                browser_cookies = await self._get_browser_cookies(page)
                cookies.update(browser_cookies)

            # Remove Content-Type header to let aiohttp set it for multipart
            headers.pop('Content-Type', None)

            for file_path in params.file_paths:
                file_path_obj = Path(file_path)
                
                if not file_path_obj.exists():
                    failed_files.append(file_path)
                    self.logger.warning(f"File not found: {file_path}")
                    continue

                try:
                    # Prepare form data
                    data = aiohttp.FormData()
                    
                    # Add additional form data if provided
                    if params.form_data:
                        for key, value in params.form_data.items():
                            data.add_field(key, value)

                    # Add the file
                    file_size = file_path_obj.stat().st_size
                    total_size += file_size
                    
                    # Determine content type
                    content_type, _ = mimetypes.guess_type(file_path)
                    if content_type is None:
                        content_type = 'application/octet-stream'

                    async with aiofiles.open(file_path, 'rb') as f:
                        file_content = await f.read()
                        data.add_field(
                            params.file_field_name,
                            file_content,
                            filename=file_path_obj.name,
                            content_type=content_type
                        )

                    # Make the upload request
                    timeout = aiohttp.ClientTimeout(total=params.timeout)
                    async with session.post(
                        params.url,
                        data=data,
                        headers=headers,
                        cookies=cookies,
                        timeout=timeout
                    ) as response:
                        
                        if 200 <= response.status < 400:
                            uploaded_files.append(file_path)
                            upload_urls[file_path_obj.name] = str(response.url)
                            
                            # Try to extract file ID from response
                            try:
                                if response.content_type == 'application/json':
                                    response_data = await response.json()
                                    if 'id' in response_data:
                                        file_ids[file_path_obj.name] = str(response_data['id'])
                                    elif 'file_id' in response_data:
                                        file_ids[file_path_obj.name] = str(response_data['file_id'])
                            except Exception:
                                pass  # Not critical if we can't extract file ID
                            
                            self.logger.info(f"Successfully uploaded: {file_path}")
                        else:
                            failed_files.append(file_path)
                            self.logger.error(f"Upload failed for {file_path}: HTTP {response.status}")

                except Exception as e:
                    failed_files.append(file_path)
                    self.logger.error(f"Failed to upload {file_path}: {str(e)}")

            upload_time = time.time() - start_time
            success = len(uploaded_files) > 0 and len(failed_files) == 0

            return FileUploadResponse(
                success=success,
                uploaded_files=uploaded_files,
                failed_files=failed_files,
                upload_urls=upload_urls or None,
                file_ids=file_ids or None,
                total_size=total_size,
                upload_time=upload_time,
                error=f"Failed to upload {len(failed_files)} files" if failed_files else None,
                metadata={
                    'upload_url': params.url,
                    'file_field_name': params.file_field_name,
                    'total_files': len(params.file_paths),
                    'successful_uploads': len(uploaded_files),
                    'failed_uploads': len(failed_files),
                }
            )

        except Exception as e:
            upload_time = time.time() - start_time
            error_msg = f"Upload operation failed: {str(e)}"
            self.logger.error(error_msg)
            return FileUploadResponse(
                success=False,
                uploaded_files=[],
                failed_files=params.file_paths,
                total_size=0,
                upload_time=upload_time,
                error=error_msg
            )

    async def compare_with_api(
        self,
        params: CompareApiAction,
        page: Page
    ) -> ApiComparisonResult:
        """
        Compare data from the current page with API response.
        """
        try:
            # Extract data from the page
            page_data = {}
            for key, selector in params.page_selectors.items():
                try:
                    element = await page.query_selector(selector)
                    if element:
                        text_content = await element.inner_text()
                        page_data[key] = text_content.strip()
                    else:
                        page_data[key] = None
                        self.logger.warning(f"Element not found for selector '{selector}' (key: {key})")
                except Exception as e:
                    page_data[key] = None
                    self.logger.warning(f"Failed to extract data for {key}: {str(e)}")

            # Make API request
            api_request_params = HttpRequestAction(
                url=params.api_endpoint,
                method=params.api_method,
                headers=params.api_headers or {},
                use_browser_cookies=True,  # Use browser context for API calls
                use_browser_headers=True,
            )
            
            api_response = await self.make_http_request(api_request_params, page)
            
            if not api_response.success:
                return ApiComparisonResult(
                    matches=False,
                    api_data={},
                    page_data=page_data,
                    comparison_details={},
                    api_request_info=api_response,
                    error=f"API request failed: {api_response.error}"
                )

            # Extract API data
            api_data = api_response.data
            if isinstance(api_data, str):
                try:
                    api_data = json.loads(api_data)
                except json.JSONDecodeError:
                    api_data = {"response": api_data}

            # Perform comparison
            comparison_details = {}
            mismatches = []
            
            # Use comparison_fields mapping if provided, otherwise use page_selectors keys
            fields_to_compare = params.comparison_fields or {k: k for k in params.page_selectors.keys()}
            
            for page_key, api_key in fields_to_compare.items():
                page_value = page_data.get(page_key)
                api_value = self._extract_nested_value(api_data, api_key)
                
                # Get comparison rule for this field
                rule = 'exact'  # default
                if params.comparison_rules and page_key in params.comparison_rules:
                    rule = params.comparison_rules[page_key]
                
                # Perform comparison based on rule
                match_result = self._compare_values(page_value, api_value, rule, params.tolerance)
                
                comparison_details[page_key] = {
                    'page_value': page_value,
                    'api_value': api_value,
                    'rule': rule,
                    'matches': match_result['matches'],
                    'details': match_result['details']
                }
                
                if not match_result['matches']:
                    mismatches.append({
                        'field': page_key,
                        'page_value': page_value,
                        'api_value': api_value,
                        'rule': rule,
                        'reason': match_result['details']
                    })

            overall_match = len(mismatches) == 0

            return ApiComparisonResult(
                matches=overall_match,
                api_data=api_data,
                page_data=page_data,
                comparison_details=comparison_details,
                mismatches=mismatches,
                api_request_info=api_response,
                metadata={
                    'fields_compared': len(fields_to_compare),
                    'matches_found': len(fields_to_compare) - len(mismatches),
                    'comparison_timestamp': time.time(),
                }
            )

        except Exception as e:
            error_msg = f"Comparison failed: {str(e)}"
            self.logger.error(error_msg)
            return ApiComparisonResult(
                matches=False,
                api_data={},
                page_data={},
                comparison_details={},
                mismatches=[],
                api_request_info=HttpResponse(
                    success=False,
                    status_code=0,
                    headers={},
                    request_time=0,
                    error="No API request made due to comparison error"
                ),
                error=error_msg
            )

    async def proxy_request(
        self,
        params: ProxyRequestAction,
        page: Page
    ) -> ProxyResponse:
        """
        Make a request through the browser as a proxy, maintaining session state.
        """
        try:
            initial_url = page.url
            initial_cookies = await self._get_browser_cookies(page)
            
            # Navigate to the target URL to make the request through the browser
            if params.preserve_session:
                # Use the browser to navigate and maintain session
                await page.goto(params.url, wait_until='networkidle')
                
                # Execute JavaScript if provided
                js_result = None
                if params.execute_js_on_response:
                    try:
                        js_result = await page.evaluate(params.execute_js_on_response)
                    except Exception as e:
                        self.logger.warning(f"JavaScript execution failed: {e}")
                
                # Get response data from the page
                response_data = None
                if params.return_response_data:
                    try:
                        # Try to get JSON data if the page contains it
                        response_data = await page.evaluate("""
                            () => {
                                const preElement = document.querySelector('pre');
                                if (preElement) {
                                    try {
                                        return JSON.parse(preElement.textContent);
                                    } catch {
                                        return preElement.textContent;
                                    }
                                }
                                return document.body.textContent;
                            }
                        """)
                    except Exception as e:
                        self.logger.warning(f"Failed to extract response data: {e}")
                        response_data = await page.content()
                
                final_url = page.url
                final_cookies = await self._get_browser_cookies(page)
                
                # Check if cookies were updated
                cookies_updated = initial_cookies != final_cookies
                
                # Check if browser state changed
                browser_state_changed = initial_url != final_url or cookies_updated
                
                return ProxyResponse(
                    success=True,
                    response_data=response_data,
                    final_url=final_url,
                    browser_state_changed=browser_state_changed,
                    cookies_updated=cookies_updated,
                    js_execution_result=js_result,
                    metadata={
                        'initial_url': initial_url,
                        'method': params.method,
                        'preserve_session': params.preserve_session,
                        'had_js_code': params.execute_js_on_response is not None,
                    }
                )
            else:
                # Make a regular HTTP request without affecting browser state
                request_params = HttpRequestAction(
                    url=params.url,
                    method=params.method,
                    use_browser_cookies=params.inject_auth,
                    use_browser_headers=params.inject_auth,
                )
                
                http_response = await self.make_http_request(request_params, page)
                
                return ProxyResponse(
                    success=http_response.success,
                    response_data=http_response.data,
                    final_url=http_response.redirect_url or params.url,
                    browser_state_changed=False,
                    cookies_updated=False,
                    js_execution_result=None,
                    error=http_response.error,
                    metadata={
                        'http_status': http_response.status_code,
                        'request_time': http_response.request_time,
                        'method': params.method,
                        'preserve_session': params.preserve_session,
                    }
                )

        except Exception as e:
            error_msg = f"Proxy request failed: {str(e)}"
            self.logger.error(error_msg)
            return ProxyResponse(
                success=False,
                response_data=None,
                final_url=params.url,
                browser_state_changed=False,
                cookies_updated=False,
                js_execution_result=None,
                error=error_msg
            )

    def _extract_nested_value(self, data: Dict[str, Any], key_path: str) -> Any:
        """Extract nested value from dictionary using dot notation (e.g., 'user.name')."""
        try:
            keys = key_path.split('.')
            value = data
            for key in keys:
                if isinstance(value, dict):
                    value = value.get(key)
                elif isinstance(value, list) and key.isdigit():
                    index = int(key)
                    value = value[index] if 0 <= index < len(value) else None
                else:
                    return None
            return value
        except Exception:
            return None

    def _compare_values(
        self,
        page_value: Any,
        api_value: Any,
        rule: str,
        tolerance: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Compare two values based on the specified rule."""
        try:
            if rule == 'exact':
                matches = str(page_value) == str(api_value)
                details = f"Exact match: '{page_value}' == '{api_value}'"
            
            elif rule == 'contains':
                matches = str(api_value).lower() in str(page_value).lower()
                details = f"Contains check: '{api_value}' in '{page_value}'"
            
            elif rule == 'regex':
                import re
                matches = bool(re.search(str(api_value), str(page_value)))
                details = f"Regex match: pattern '{api_value}' in '{page_value}'"
            
            elif rule == 'numeric':
                try:
                    page_num = float(page_value) if page_value is not None else 0
                    api_num = float(api_value) if api_value is not None else 0
                    tolerance_val = tolerance.get('numeric', 0.1) if tolerance else 0.1
                    matches = abs(page_num - api_num) <= tolerance_val
                    details = f"Numeric comparison: |{page_num} - {api_num}| <= {tolerance_val}"
                except (ValueError, TypeError):
                    matches = False
                    details = f"Failed to convert to numbers: '{page_value}', '{api_value}'"
            
            else:
                # Default to exact match
                matches = str(page_value) == str(api_value)
                details = f"Default exact match: '{page_value}' == '{api_value}'"
            
            return {
                'matches': matches,
                'details': details
            }
            
        except Exception as e:
            return {
                'matches': False,
                'details': f"Comparison error: {str(e)}"
            }

    async def close(self):
        """Close the HTTP session."""
        if self.session and not self.session.closed:
            await self.session.close()


def register_http_request_actions(controller):
    """Register HTTP request actions with the controller."""
    handler = HttpRequestHandler()
    
    @controller.registry.action(
        'Make HTTP request with browser context (cookies, headers)',
        param_model=HttpRequestAction
    )
    async def http_request_with_context(params: HttpRequestAction, page):
        """Make an HTTP request using browser context."""
        try:
            result = await handler.make_http_request(params, page)
            
            # Format for ActionResult
            if result.success:
                content = f"HTTP {params.method} request to {params.url} successful (status: {result.status_code})"
                if result.data and isinstance(result.data, dict):
                    content += f"\nResponse data keys: {list(result.data.keys())}"
                elif result.data:
                    content += f"\nResponse length: {len(str(result.data))} characters"
                
                memory = f"HTTP {params.method} {params.url} -> {result.status_code}"
                
                from browser_use.agent.views import ActionResult
                return ActionResult(
                    extracted_content=content,
                    long_term_memory=memory,
                    include_in_memory=True
                )
            else:
                error_msg = f"HTTP request failed: {result.error or f'Status {result.status_code}'}"
                from browser_use.agent.views import ActionResult
                return ActionResult(
                    error=error_msg,
                    include_in_memory=True
                )
                
        except Exception as e:
            logger.error(f"HTTP request action failed: {e}")
            from browser_use.agent.views import ActionResult
            return ActionResult(
                error=f"HTTP request failed: {str(e)}",
                include_in_memory=True
            )

    @controller.registry.action(
        'Upload files via HTTP request with browser context',
        param_model=FileUploadAction
    )
    async def upload_files_http(params: FileUploadAction, page):
        """Upload files via HTTP request."""
        try:
            result = await handler.upload_files(params, page)
            
            if result.success:
                content = f"Successfully uploaded {len(result.uploaded_files)} files"
                if result.failed_files:
                    content += f" ({len(result.failed_files)} failed)"
                content += f"\nTotal size: {result.total_size:,} bytes"
                content += f"\nUpload time: {result.upload_time:.2f}s"
                
                memory = f"Uploaded {len(result.uploaded_files)} files to {params.url}"
                
                from browser_use.agent.views import ActionResult
                return ActionResult(
                    extracted_content=content,
                    long_term_memory=memory,
                    include_in_memory=True
                )
            else:
                error_msg = f"File upload failed: {result.error}"
                from browser_use.agent.views import ActionResult
                return ActionResult(
                    error=error_msg,
                    include_in_memory=True
                )
                
        except Exception as e:
            logger.error(f"File upload action failed: {e}")
            from browser_use.agent.views import ActionResult
            return ActionResult(
                error=f"File upload failed: {str(e)}",
                include_in_memory=True
            )

    @controller.registry.action(
        'Compare current page data with API response',
        param_model=CompareApiAction
    )
    async def compare_page_with_api(params: CompareApiAction, page):
        """Compare page data with API response."""
        try:
            result = await handler.compare_with_api(params, page)
            
            content = f"API comparison completed: {'✅ MATCH' if result.matches else '❌ MISMATCH'}"
            content += f"\nFields compared: {len(result.comparison_details)}"
            
            if result.mismatches:
                content += f"\nMismatches found: {len(result.mismatches)}"
                for mismatch in result.mismatches[:3]:  # Show first 3 mismatches
                    content += f"\n  - {mismatch['field']}: page='{mismatch['page_value']}' vs api='{mismatch['api_value']}'"
            
            memory = f"API comparison: {'MATCH' if result.matches else 'MISMATCH'} ({len(result.mismatches)} differences)"
            
            from browser_use.agent.views import ActionResult
            return ActionResult(
                extracted_content=content,
                long_term_memory=memory,
                include_in_memory=True
            )
                
        except Exception as e:
            logger.error(f"API comparison action failed: {e}")
            from browser_use.agent.views import ActionResult
            return ActionResult(
                error=f"API comparison failed: {str(e)}",
                include_in_memory=True
            )

    @controller.registry.action(
        'Make request through browser as proxy maintaining session',
        param_model=ProxyRequestAction
    )
    async def proxy_request_via_browser(params: ProxyRequestAction, page):
        """Make a request through the browser as a proxy."""
        try:
            result = await handler.proxy_request(params, page)
            
            if result.success:
                content = f"Proxy request to {params.url} successful"
                content += f"\nFinal URL: {result.final_url}"
                content += f"\nBrowser state changed: {result.browser_state_changed}"
                content += f"\nCookies updated: {result.cookies_updated}"
                
                if result.js_execution_result is not None:
                    content += f"\nJavaScript result: {result.js_execution_result}"
                
                memory = f"Proxy request {params.method} {params.url} -> {result.final_url}"
                
                from browser_use.agent.views import ActionResult
                return ActionResult(
                    extracted_content=content,
                    long_term_memory=memory,
                    include_in_memory=True
                )
            else:
                error_msg = f"Proxy request failed: {result.error}"
                from browser_use.agent.views import ActionResult
                return ActionResult(
                    error=error_msg,
                    include_in_memory=True
                )
                
        except Exception as e:
            logger.error(f"Proxy request action failed: {e}")
            from browser_use.agent.views import ActionResult
            return ActionResult(
                error=f"Proxy request failed: {str(e)}",
                include_in_memory=True
            )

    # Store handler reference for cleanup
    controller._http_handler = handler
    
