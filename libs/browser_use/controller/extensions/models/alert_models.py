"""
Models for JavaScript alert and modal handling.

These models define the structure for alert/modal related actions and responses.
Follows clean architecture principles with clear data contracts.
"""

from typing import Literal, Optional, Dict, Any
from pydantic import BaseModel, Field


class HandleAlertAction(BaseModel):
    """Model for handling JavaScript alerts (alert, confirm, prompt)."""
    
    action: Literal['accept', 'dismiss', 'get_text'] = Field(
        description="Action to take on the alert"
    )
    text: Optional[str] = Field(
        default=None,
        description="Text to input for prompt dialogs"
    )
    timeout: int = Field(
        default=5000,
        description="Timeout in milliseconds to wait for alert"
    )
    auto_detect: bool = Field(
        default=True,
        description="Whether to automatically detect and handle alerts"
    )


class HandleModalAction(BaseModel):
    """Model for handling dynamic modal dialogs."""
    
    close_method: Literal['escape', 'click_overlay', 'click_x', 'click_button'] = Field(
        default='escape',
        description="Method to close the modal"
    )
    wait_timeout: int = Field(
        default=5000,
        description="Timeout in milliseconds to wait for modal"
    )
    modal_selector: Optional[str] = Field(
        default=None,
        description="CSS selector for the modal element"
    )
    close_button_selector: Optional[str] = Field(
        default=None,
        description="CSS selector for the close button"
    )


class AlertResponse(BaseModel):
    """Response model for alert handling operations."""
    
    success: bool = Field(description="Whether the alert was handled successfully")
    alert_type: Optional[str] = Field(description="Type of alert (alert, confirm, prompt)")
    alert_text: Optional[str] = Field(description="Text content of the alert")
    action_taken: Optional[str] = Field(description="Action that was taken")
    error: Optional[str] = Field(description="Error message if operation failed")


class ModalResponse(BaseModel):
    """Response model for modal handling operations."""
    
    success: bool = Field(description="Whether the modal was handled successfully")
    modal_found: bool = Field(description="Whether a modal was detected")
    close_method_used: Optional[str] = Field(description="Method used to close the modal")
    modal_selector: Optional[str] = Field(description="Selector of the modal that was closed")
    error: Optional[str] = Field(description="Error message if operation failed")
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional metadata about the modal interaction"
    ) 