"""
Models for intelligent form handling.

These models handle smart form detection, field mapping,
validation, and automated filling operations.
"""

from typing import Optional, List, Dict, Any, Literal
from pydantic import BaseModel, Field


class FormFieldInfo(BaseModel):
    """Model representing information about a form field."""
    
    name: str = Field(description="Field name attribute")
    field_type: str = Field(description="Input type (text, email, password, etc.)")
    label: Optional[str] = Field(description="Associated label text")
    placeholder: Optional[str] = Field(description="Placeholder text")
    required: bool = Field(description="Whether the field is required")
    current_value: Optional[str] = Field(description="Current field value")
    selector: str = Field(description="CSS selector for the field")
    validation_pattern: Optional[str] = Field(
        description="Regex pattern for field validation"
    )
    max_length: Optional[int] = Field(description="Maximum input length")
    min_length: Optional[int] = Field(description="Minimum input length")
    field_id: Optional[str] = Field(description="Field ID attribute")
    aria_label: Optional[str] = Field(description="ARIA label for accessibility")


class FormStructure(BaseModel):
    """Model representing the complete structure of a form."""
    
    form_selector: str = Field(description="CSS selector for the form element")
    action_url: Optional[str] = Field(description="Form action URL")
    method: str = Field(description="Form method (GET, POST, etc.)")
    fields: List[FormFieldInfo] = Field(description="List of form fields")
    submit_button_selector: Optional[str] = Field(
        description="CSS selector for submit button"
    )
    reset_button_selector: Optional[str] = Field(
        description="CSS selector for reset button"
    )
    fieldsets: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="List of fieldset groups"
    )
    form_id: Optional[str] = Field(description="Form ID attribute")
    is_multipart: bool = Field(
        default=False,
        description="Whether form supports file uploads"
    )
    csrf_token: Optional[str] = Field(
        description="CSRF token if present"
    )


class SmartFormFillAction(BaseModel):
    """Model for intelligent form filling operations."""
    
    form_data: Dict[str, str] = Field(
        description="Data to fill in the form (field_name: value)"
    )
    form_selector: Optional[str] = Field(
        default=None,
        description="CSS selector for the specific form to fill"
    )
    submit: bool = Field(
        default=False,
        description="Whether to submit the form after filling"
    )
    validate_before_submit: bool = Field(
        default=True,
        description="Whether to validate fields before submitting"
    )
    field_mapping_strategy: Literal['exact', 'fuzzy', 'smart'] = Field(
        default='smart',
        description="Strategy for mapping data to form fields"
    )
    fill_delay: int = Field(
        default=100,
        description="Delay in milliseconds between field fills"
    )
    clear_existing: bool = Field(
        default=True,
        description="Whether to clear existing field values"
    )
    focus_before_fill: bool = Field(
        default=True,
        description="Whether to focus field before filling"
    )
    trigger_events: bool = Field(
        default=True,
        description="Whether to trigger input/change events"
    )
    skip_readonly: bool = Field(
        default=True,
        description="Whether to skip readonly fields"
    )


class DetectFormAction(BaseModel):
    """Model for form structure detection operations."""
    
    form_selector: Optional[str] = Field(
        default=None,
        description="CSS selector for specific form to analyze"
    )
    include_hidden_fields: bool = Field(
        default=False,
        description="Whether to include hidden form fields"
    )
    analyze_validation: bool = Field(
        default=True,
        description="Whether to analyze field validation rules"
    )
    detect_fieldsets: bool = Field(
        default=True,
        description="Whether to detect fieldset groupings"
    )
    extract_labels: bool = Field(
        default=True,
        description="Whether to extract field labels"
    )


class FormFillResponse(BaseModel):
    """Response model for form filling operations."""
    
    success: bool = Field(description="Whether form filling was successful")
    fields_filled: int = Field(description="Number of fields successfully filled")
    fields_skipped: int = Field(description="Number of fields skipped")
    fields_failed: int = Field(description="Number of fields that failed to fill")
    form_submitted: bool = Field(description="Whether the form was submitted")
    validation_errors: List[Dict[str, str]] = Field(
        description="List of validation errors encountered"
    )
    field_mapping: Dict[str, str] = Field(
        description="Mapping of data keys to actual field selectors"
    )
    unmapped_data: List[str] = Field(
        description="List of data keys that couldn't be mapped"
    )
    fill_duration: float = Field(
        description="Time taken to fill the form in milliseconds"
    )
    error: Optional[str] = Field(
        description="General error message if operation failed"
    )
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warning messages"
    ) 