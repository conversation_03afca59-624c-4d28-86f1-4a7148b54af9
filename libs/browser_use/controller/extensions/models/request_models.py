"""
Models for HTTP requests with browser context.

These models handle HTTP requests that maintain browser session state,
cookies, headers, and authentication context.
"""

from typing import Optional, Dict, Any, List, Literal, Union
from pydantic import BaseModel, Field, HttpUrl


class HttpRequestAction(BaseModel):
    """Model for making HTTP requests with browser context."""
    
    url: str = Field(description="URL for the HTTP request")
    method: Literal['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'] = Field(
        default='GET',
        description="HTTP method to use"
    )
    use_browser_cookies: bool = Field(
        default=True,
        description="Whether to use cookies from the browser session"
    )
    use_browser_headers: bool = Field(
        default=True,
        description="Whether to use headers from the browser session (User-Agent, Referer, etc.)"
    )
    data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Request body data (for POST, PUT, PATCH requests)"
    )
    headers: Optional[Dict[str, str]] = Field(
        default=None,
        description="Additional headers to include in the request"
    )
    params: Optional[Dict[str, str]] = Field(
        default=None,
        description="URL query parameters"
    )
    timeout: int = Field(
        default=30,
        description="Request timeout in seconds"
    )
    follow_redirects: bool = Field(
        default=True,
        description="Whether to follow HTTP redirects"
    )
    verify_ssl: bool = Field(
        default=True,
        description="Whether to verify SSL certificates"
    )


class FileUploadAction(BaseModel):
    """Model for uploading files via HTTP requests."""
    
    url: str = Field(description="URL for the file upload")
    file_paths: List[str] = Field(description="List of local file paths to upload")
    file_field_name: str = Field(
        default="file",
        description="Name of the file field in the form"
    )
    form_data: Optional[Dict[str, str]] = Field(
        default=None,
        description="Additional form data to include with the upload"
    )
    use_browser_context: bool = Field(
        default=True,
        description="Whether to use browser cookies and headers"
    )
    timeout: int = Field(
        default=300,
        description="Upload timeout in seconds"
    )
    chunk_size: Optional[int] = Field(
        default=None,
        description="Chunk size for large file uploads (bytes)"
    )


class CompareApiAction(BaseModel):
    """Model for comparing browser state with API response."""
    
    api_endpoint: str = Field(description="API endpoint URL to fetch data from")
    page_selectors: Dict[str, str] = Field(
        description="CSS selectors to extract data from the current page"
    )
    comparison_fields: Optional[Dict[str, str]] = Field(
        default=None,
        description="Mapping of API field names to page selector keys for comparison"
    )
    comparison_rules: Optional[Dict[str, str]] = Field(
        default=None,
        description="Custom comparison rules (exact, contains, regex, etc.)"
    )
    api_method: Literal['GET', 'POST'] = Field(
        default='GET',
        description="HTTP method for the API request"
    )
    api_headers: Optional[Dict[str, str]] = Field(
        default=None,
        description="Headers for the API request"
    )
    tolerance: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Tolerance settings for numeric or date comparisons"
    )


class ProxyRequestAction(BaseModel):
    """Model for making requests through the browser as a proxy."""
    
    url: str = Field(description="URL to request")
    method: Literal['GET', 'POST', 'PUT', 'DELETE'] = Field(
        default='GET',
        description="HTTP method"
    )
    inject_auth: bool = Field(
        default=True,
        description="Whether to inject browser authentication"
    )
    preserve_session: bool = Field(
        default=True,
        description="Whether to maintain the browser session state"
    )
    return_response_data: bool = Field(
        default=True,
        description="Whether to return the response data"
    )
    execute_js_on_response: Optional[str] = Field(
        default=None,
        description="JavaScript code to execute on the response page"
    )


class HttpResponse(BaseModel):
    """Response model for HTTP request operations."""
    
    success: bool = Field(description="Whether the request was successful")
    status_code: int = Field(description="HTTP status code")
    headers: Dict[str, str] = Field(description="Response headers")
    data: Optional[Union[str, Dict[str, Any]]] = Field(
        description="Response data (JSON parsed if applicable)"
    )
    cookies: Optional[Dict[str, str]] = Field(
        description="Cookies received in the response"
    )
    redirect_url: Optional[str] = Field(
        description="Final URL if redirected"
    )
    request_time: float = Field(description="Request duration in seconds")
    error: Optional[str] = Field(description="Error message if request failed")
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata about the request"
    )


class FileUploadResponse(BaseModel):
    """Response model for file upload operations."""
    
    success: bool = Field(description="Whether the upload was successful")
    uploaded_files: List[str] = Field(description="List of successfully uploaded files")
    failed_files: List[str] = Field(
        default_factory=list,
        description="List of files that failed to upload"
    )
    upload_urls: Optional[Dict[str, str]] = Field(
        description="Mapping of file names to their upload URLs"
    )
    file_ids: Optional[Dict[str, str]] = Field(
        description="Mapping of file names to their server-assigned IDs"
    )
    total_size: int = Field(description="Total size of uploaded files in bytes")
    upload_time: float = Field(description="Upload duration in seconds")
    error: Optional[str] = Field(description="Error message if upload failed")
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata about the upload"
    )


class ApiComparisonResult(BaseModel):
    """Response model for API comparison operations."""
    
    matches: bool = Field(description="Whether API and page data match")
    api_data: Dict[str, Any] = Field(description="Data retrieved from API")
    page_data: Dict[str, Any] = Field(description="Data extracted from page")
    comparison_details: Dict[str, Dict[str, Any]] = Field(
        description="Detailed comparison results for each field"
    )
    mismatches: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="List of fields that don't match"
    )
    api_request_info: HttpResponse = Field(
        description="Details about the API request made"
    )
    error: Optional[str] = Field(description="Error message if comparison failed")
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata about the comparison"
    )


class ProxyResponse(BaseModel):
    """Response model for proxy request operations."""
    
    success: bool = Field(description="Whether the proxy request was successful")
    response_data: Optional[Union[str, Dict[str, Any]]] = Field(
        description="Response data from the proxied request"
    )
    final_url: str = Field(description="Final URL after any redirects")
    browser_state_changed: bool = Field(
        description="Whether the browser state was modified"
    )
    cookies_updated: bool = Field(
        description="Whether new cookies were set"
    )
    js_execution_result: Optional[Any] = Field(
        description="Result of JavaScript execution if any"
    )
    error: Optional[str] = Field(description="Error message if request failed")
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata about the proxy request"
    ) 