"""
Models for advanced iframe handling operations.
"""

from typing import Optional, List, Dict, Any, Literal, Union
from pydantic import BaseModel, Field


class DetectIframeAction(BaseModel):
    """Model for iframe detection operations."""
    
    include_cross_origin: bool = Field(
        default=True,
        description="Whether to include cross-origin iframes in detection"
    )
    include_hidden: bool = Field(
        default=False,
        description="Whether to include hidden iframes"
    )
    detect_nested: bool = Field(
        default=True,
        description="Whether to detect nested iframes"
    )
    timeout: int = Field(
        default=10000,
        description="Timeout in milliseconds for iframe detection"
    )
    filter_by_domain: Optional[List[str]] = Field(
        default=None,
        description="Filter iframes by domain patterns (e.g., ['*.google.com'])"
    )
    exclude_ads: bool = Field(
        default=True,
        description="Whether to exclude advertising iframes"
    )


class SwitchIframeContextAction(BaseModel):
    """Model for switching iframe context operations."""
    
    iframe_selector: str = Field(
        description="CSS selector or iframe identifier to switch to"
    )
    wait_for_load: bool = Field(
        default=True,
        description="Whether to wait for iframe to load completely"
    )
    load_timeout: int = Field(
        default=15000,
        description="Timeout in milliseconds to wait for iframe loading"
    )
    verify_content: bool = Field(
        default=True,
        description="Whether to verify iframe content is accessible"
    )


class ExecuteInIframeAction(BaseModel):
    """Model for executing actions inside iframe."""
    
    iframe_selector: str = Field(
        description="CSS selector to identify the target iframe"
    )
    action_type: Literal[
        'click', 'type_text', 'extract_text', 'get_elements', 
        'wait_for_element', 'take_screenshot', 'execute_script'
    ] = Field(
        description="Type of action to execute inside iframe"
    )
    target_selector: Optional[str] = Field(
        default=None,
        description="Selector for element inside iframe (if applicable)"
    )
    action_params: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Parameters specific to the action being executed"
    )
    wait_for_iframe: bool = Field(
        default=True,
        description="Whether to wait for iframe to be ready"
    )
    switch_back: bool = Field(
        default=True,
        description="Whether to switch back to main frame after action"
    )
    timeout: int = Field(
        default=30000,
        description="Timeout for the entire operation in milliseconds"
    )


class MonitorIframeAction(BaseModel):
    """Model for monitoring iframe changes and events."""
    
    iframe_selector: Optional[str] = Field(
        default=None,
        description="Specific iframe to monitor (if None, monitors all)"
    )
    monitor_duration: int = Field(
        default=30000,
        description="Duration in milliseconds to monitor changes"
    )
    events_to_monitor: List[str] = Field(
        default_factory=lambda: ['load', 'resize', 'focus', 'blur'],
        description="Types of events to monitor on iframes"
    )
    content_change_detection: bool = Field(
        default=True,
        description="Whether to detect content changes inside iframes"
    )
    check_interval: int = Field(
        default=2000,
        description="Interval in milliseconds between checks"
    )


class ExtractIframeContentAction(BaseModel):
    """Model for extracting content from iframes."""
    
    iframe_selector: Optional[str] = Field(
        default=None,
        description="Specific iframe selector (if None, processes all)"
    )
    content_type: Literal['text', 'html', 'structured'] = Field(
        default='structured',
        description="Type of content to extract"
    )
    include_nested: bool = Field(
        default=True,
        description="Whether to include nested iframe content"
    )
    max_depth: int = Field(
        default=3,
        description="Maximum nesting depth to process"
    )
    format_output: bool = Field(
        default=True,
        description="Whether to format the output for readability"
    )
    exclude_hidden: bool = Field(
        default=True,
        description="Whether to exclude hidden iframe content"
    )


class IframeInfo(BaseModel):
    """Model representing detected iframe information."""
    
    selector: str = Field(description="CSS selector for the iframe")
    src: Optional[str] = Field(description="Source URL of the iframe")
    id: Optional[str] = Field(description="ID attribute of the iframe")
    name: Optional[str] = Field(description="Name attribute of the iframe")
    title: Optional[str] = Field(description="Title attribute of the iframe")
    width: Optional[Union[str, int]] = Field(description="Width of the iframe")
    height: Optional[Union[str, int]] = Field(description="Height of the iframe")
    is_visible: bool = Field(description="Whether the iframe is visible")
    is_cross_origin: bool = Field(description="Whether iframe is cross-origin")
    is_nested: bool = Field(description="Whether iframe is nested inside another")
    parent_iframe: Optional[str] = Field(
        description="Selector of parent iframe if nested"
    )
    nesting_level: int = Field(
        default=0,
        description="Nesting level (0 = top level)"
    )
    access_allowed: bool = Field(
        description="Whether content access is allowed"
    )
    load_state: str = Field(
        default="unknown",
        description="Loading state of the iframe"
    )
    content_type: Optional[str] = Field(
        description="Type of content detected in iframe"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata about the iframe"
    )


class IframeDetectionResponse(BaseModel):
    """Response model for iframe detection operations."""
    
    iframes_detected: bool = Field(description="Whether any iframes were detected")
    iframe_count: int = Field(description="Total number of iframes detected")
    iframe_info: List[IframeInfo] = Field(
        default_factory=list,
        description="List of detected iframe information"
    )
    cross_origin_count: int = Field(
        description="Number of cross-origin iframes detected"
    )
    nested_count: int = Field(
        description="Number of nested iframes detected"
    )
    accessible_count: int = Field(
        description="Number of accessible iframes"
    )
    detection_time: float = Field(
        description="Time taken for detection in milliseconds"
    )
    page_url: str = Field(description="URL of the page where detection was performed")
    error: Optional[str] = Field(description="Error message if detection failed")
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings during detection"
    )


class IframeContextResponse(BaseModel):
    """Response model for iframe context operations."""
    
    success: bool = Field(description="Whether context switch was successful")
    iframe_selector: str = Field(description="Selector of the target iframe")
    iframe_url: Optional[str] = Field(description="URL of the iframe content")
    content_accessible: bool = Field(description="Whether iframe content is accessible")
    load_time: float = Field(description="Time taken to load iframe in milliseconds")
    context_switched: bool = Field(description="Whether context was successfully switched")
    previous_context: Optional[str] = Field(
        description="Previous context identifier"
    )
    error: Optional[str] = Field(description="Error message if operation failed")
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings during operation"
    )


class IframeExecutionResponse(BaseModel):
    """Response model for iframe execution operations."""
    
    success: bool = Field(description="Whether action execution was successful")
    action_type: str = Field(description="Type of action that was executed")
    iframe_selector: str = Field(description="Selector of the target iframe")
    execution_time: float = Field(
        description="Time taken to execute action in milliseconds"
    )
    result_data: Optional[Any] = Field(
        description="Result data from the executed action"
    )
    context_restored: bool = Field(
        description="Whether original context was restored"
    )
    elements_found: Optional[int] = Field(
        description="Number of elements found (for element operations)"
    )
    screenshot_path: Optional[str] = Field(
        description="Path to screenshot if action was take_screenshot"
    )
    error: Optional[str] = Field(description="Error message if execution failed")
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings during execution"
    )


class IframeMonitoringResponse(BaseModel):
    """Response model for iframe monitoring operations."""
    
    monitoring_completed: bool = Field(description="Whether monitoring was completed")
    events_detected: List[Dict[str, Any]] = Field(
        description="List of events detected during monitoring"
    )
    content_changes: List[Dict[str, Any]] = Field(
        description="List of content changes detected"
    )
    total_events: int = Field(description="Total number of events detected")
    monitoring_duration: float = Field(
        description="Actual monitoring duration in milliseconds"
    )
    iframes_monitored: int = Field(description="Number of iframes monitored")
    error: Optional[str] = Field(description="Error message if monitoring failed")
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings during monitoring"
    )


class IframeContentResponse(BaseModel):
    """Response model for iframe content extraction operations."""
    
    extraction_successful: bool = Field(
        description="Whether content extraction was successful"
    )
    content_data: Dict[str, Any] = Field(
        description="Extracted content organized by iframe"
    )
    iframes_processed: int = Field(description="Number of iframes processed")
    total_content_size: int = Field(
        description="Total size of extracted content in characters"
    )
    nested_levels_processed: int = Field(
        description="Number of nesting levels processed"
    )
    cross_origin_iframes: int = Field(
        description="Number of cross-origin iframes encountered"
    )
    extraction_time: float = Field(
        description="Time taken for extraction in milliseconds"
    )
    content_summary: Optional[str] = Field(
        description="Summary of the extracted content"
    )
    error: Optional[str] = Field(description="Error message if extraction failed")
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings during extraction"
    ) 