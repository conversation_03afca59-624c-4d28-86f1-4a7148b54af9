"""
Models for browser-use controller extensions.

This module contains all the Pydantic models used by the extension actions.
Following separation of concerns principle.
"""

from .alert_models import (
    HandleAlertAction,
    HandleModalAction,
    AlertResponse,
    ModalResponse,
)

from .request_models import (
    HttpRequestAction,
    CompareApiAction,
    HttpResponse,
    ApiComparisonResult,
)

from .upload_models import (
    UploadFilesAction,
    MonitorUploadAction,
    UploadResponse,
    UploadProgress,
)

from .form_models import (
    SmartFormFillAction,
    DetectFormAction,
    FormFieldInfo,
    FormStructure,
    FormFillResponse,
)

from .iframe_models import (
    DetectIframeAction,
    SwitchIframeContextAction,
    ExecuteInIframeAction,
    MonitorIframeAction,
    ExtractIframeContentAction,
    IframeInfo,
    IframeDetectionResponse,
    IframeContextResponse,
    IframeExecutionResponse,
    IframeMonitoringResponse,
    IframeContentResponse,
)

from .dropdown_models import (
    DetectDropdownAction,
    SmartDropdownSelectAction,
    MonitorDropdownAction,
    DropdownInfo,
    DropdownDetectionResponse,
    DropdownSelectionResponse,
    DropdownMonitoringResponse,
)

__all__ = [
    # Alert models
    'HandleAlertAction',
    'HandleModalAction',
    'AlertResponse',
    'ModalResponse',
    
    # Request models
    'HttpRequestAction',
    'CompareApiAction',
    'HttpResponse',
    'ApiComparisonResult',
    
    # Upload models
    'UploadFilesAction',
    'MonitorUploadAction',
    'UploadResponse',
    'UploadProgress',
    
    # Form models
    'SmartFormFillAction',
    'DetectFormAction',
    'FormFieldInfo',
    'FormStructure',
    'FormFillResponse',
    
    # Iframe models
    'DetectIframeAction',
    'SwitchIframeContextAction',
    'ExecuteInIframeAction',
    'MonitorIframeAction',
    'ExtractIframeContentAction',
    'IframeInfo',
    'IframeDetectionResponse',
    'IframeContextResponse',
    'IframeExecutionResponse',
    'IframeMonitoringResponse',
    'IframeContentResponse',
    
    # Dropdown models
    'DetectDropdownAction',
    'SmartDropdownSelectAction',
    'MonitorDropdownAction',
    'DropdownInfo',
    'DropdownDetectionResponse',
    'DropdownSelectionResponse',
    'DropdownMonitoringResponse',
] 