"""
Models for CAPTCHA detection and handling operations.
"""

from typing import Optional, List, Dict, Any, Literal
from pydantic import BaseModel, Field


class DetectCaptchaAction(BaseModel):
    """Model for CAPTCHA detection operations."""
    
    detection_timeout: int = Field(
        default=5000,
        description="Timeout in milliseconds to detect CAPTCHA presence"
    )
    captcha_types: List[str] = Field(
        default_factory=lambda: ['recaptcha', 'hcaptcha', 'image', 'audio', 'text'],
        description="Types of CAPTCHAs to detect"
    )
    detection_selectors: Optional[List[str]] = Field(
        default=None,
        description="Custom CSS selectors to detect CAPTCHA elements"
    )
    include_iframe_scan: bool = Field(
        default=True,
        description="Whether to scan inside iframes for CAPTCHAs"
    )
    detailed_analysis: bool = Field(
        default=True,
        description="Whether to perform detailed CAPTCHA type analysis"
    )


class HandleCaptchaAction(BaseModel):
    """Model for CAPTCHA handling operations."""
    
    strategy: Literal['auto', 'wait_human', 'audio_challenge', 'refresh_page', 'retry_later'] = Field(
        default='auto',
        description="Primary strategy for handling the CAPTCHA"
    )
    max_wait_time: int = Field(
        default=60000,
        description="Maximum time in milliseconds to wait for CAPTCHA resolution"
    )
    max_retry_attempts: int = Field(
        default=3,
        description="Maximum number of retry attempts"
    )
    human_intervention_timeout: int = Field(
        default=180000,
        description="Timeout for human intervention in milliseconds (3 minutes)"
    )
    auto_refresh_on_failure: bool = Field(
        default=True,
        description="Whether to refresh page on repeated failures"
    )
    audio_challenge_preference: bool = Field(
        default=False,
        description="Whether to prefer audio challenges over image challenges"
    )
    wait_between_attempts: int = Field(
        default=5000,
        description="Time to wait between retry attempts in milliseconds"
    )
    captcha_selector: Optional[str] = Field(
        default=None,
        description="Specific CSS selector for the CAPTCHA element to handle"
    )
    preserve_page_state: bool = Field(
        default=True,
        description="Whether to preserve page state during CAPTCHA handling"
    )


class SolveCaptchaAction(BaseModel):
    """Model for automated CAPTCHA solving operations."""
    
    captcha_type: Literal['recaptcha', 'hcaptcha', 'image_grid', 'text_input', 'audio'] = Field(
        description="Type of CAPTCHA to solve"
    )
    challenge_description: Optional[str] = Field(
        default=None,
        description="Description of the CAPTCHA challenge (e.g., 'Select all traffic lights')"
    )
    image_urls: Optional[List[str]] = Field(
        default=None,
        description="URLs or base64 data of CAPTCHA images for analysis"
    )
    audio_url: Optional[str] = Field(
        default=None,
        description="URL of audio CAPTCHA challenge"
    )
    grid_size: Optional[str] = Field(
        default=None,
        description="Size of image grid (e.g., '3x3', '4x4')"
    )
    use_vision_analysis: bool = Field(
        default=True,
        description="Whether to use AI vision for image analysis"
    )
    confidence_threshold: float = Field(
        default=0.7,
        description="Minimum confidence score for automated solving (0.0-1.0)"
    )
    manual_fallback: bool = Field(
        default=True,
        description="Whether to fall back to manual solving if automated fails"
    )


class MonitorCaptchaAction(BaseModel):
    """Model for monitoring CAPTCHA resolution status."""
    
    monitoring_duration: int = Field(
        default=30000,
        description="Duration in milliseconds to monitor CAPTCHA status"
    )
    check_interval: int = Field(
        default=2000,
        description="Interval in milliseconds between status checks"
    )
    success_indicators: Optional[List[str]] = Field(
        default=None,
        description="CSS selectors indicating successful CAPTCHA resolution"
    )
    failure_indicators: Optional[List[str]] = Field(
        default=None,
        description="CSS selectors indicating CAPTCHA failure"
    )
    monitor_network_requests: bool = Field(
        default=True,
        description="Whether to monitor network requests for CAPTCHA validation"
    )


class CaptchaInfo(BaseModel):
    """Model representing detected CAPTCHA information."""
    
    captcha_type: str = Field(description="Type of CAPTCHA detected")
    element_selector: str = Field(description="CSS selector for the CAPTCHA element")
    iframe_url: Optional[str] = Field(description="URL of iframe containing CAPTCHA")
    challenge_text: Optional[str] = Field(description="Text of the CAPTCHA challenge")
    site_key: Optional[str] = Field(description="Site key for reCAPTCHA or hCAPTCHA")
    difficulty_level: Optional[str] = Field(description="Estimated difficulty level")
    is_invisible: bool = Field(description="Whether this is an invisible CAPTCHA")
    requires_interaction: bool = Field(description="Whether user interaction is required")
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata about the CAPTCHA"
    )


class CaptchaDetectionResponse(BaseModel):
    """Response model for CAPTCHA detection operations."""
    
    captcha_detected: bool = Field(description="Whether a CAPTCHA was detected")
    captcha_count: int = Field(description="Number of CAPTCHAs detected")
    captcha_info: List[CaptchaInfo] = Field(
        default_factory=list,
        description="List of detected CAPTCHA information"
    )
    detection_time: float = Field(description="Time taken for detection in milliseconds")
    page_url: str = Field(description="URL of the page where detection was performed")
    error: Optional[str] = Field(description="Error message if detection failed")
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings during detection"
    )


class CaptchaHandlingResponse(BaseModel):
    """Response model for CAPTCHA handling operations."""
    
    success: bool = Field(description="Whether CAPTCHA handling was successful")
    strategy_used: str = Field(description="Strategy that was used for handling")
    resolution_time: float = Field(description="Time taken to resolve in milliseconds")
    attempts_made: int = Field(description="Number of attempts made")
    captcha_type: Optional[str] = Field(description="Type of CAPTCHA that was handled")
    human_intervention_required: bool = Field(description="Whether human intervention was required")
    page_state_preserved: bool = Field(description="Whether page state was preserved")
    next_action_recommendation: Optional[str] = Field(
        description="Recommendation for the next action to take"
    )
    error: Optional[str] = Field(description="Error message if handling failed")
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings during handling"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata about the handling process"
    )


class CaptchaSolvingResponse(BaseModel):
    """Response model for CAPTCHA solving operations."""
    
    solved: bool = Field(description="Whether the CAPTCHA was successfully solved")
    solution_method: str = Field(description="Method used for solving")
    confidence_score: float = Field(description="Confidence score of the solution (0.0-1.0)")
    solution_data: Optional[Dict[str, Any]] = Field(
        description="Data about the solution (selected indices, text input, etc.)"
    )
    solving_time: float = Field(description="Time taken to solve in milliseconds")
    verification_status: Optional[str] = Field(
        description="Status of solution verification (pending, success, failed)"
    )
    fallback_used: bool = Field(description="Whether fallback method was used")
    error: Optional[str] = Field(description="Error message if solving failed")
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings during solving"
    )


class CaptchaMonitoringResponse(BaseModel):
    """Response model for CAPTCHA monitoring operations."""
    
    monitoring_completed: bool = Field(description="Whether monitoring was completed")
    final_status: str = Field(description="Final CAPTCHA status (resolved, failed, timeout)")
    status_changes: List[Dict[str, Any]] = Field(
        description="List of status changes during monitoring"
    )
    total_monitoring_time: float = Field(description="Total monitoring time in milliseconds")
    resolution_detected_at: Optional[float] = Field(
        description="Timestamp when resolution was detected"
    )
    network_activity: Optional[List[Dict[str, Any]]] = Field(
        description="Network requests related to CAPTCHA validation"
    )
    error: Optional[str] = Field(description="Error message if monitoring failed")
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings during monitoring"
    ) 