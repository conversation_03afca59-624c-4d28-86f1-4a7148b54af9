"""
Models for intelligent dropdown handling.

These models handle complex dropdown detection, classification,
and interaction strategies for both native and custom dropdowns.
"""

from typing import Optional, List, Dict, Any, Literal
from pydantic import BaseModel, Field


class DetectDropdownAction(BaseModel):
    """Model for dropdown detection and classification operations."""
    
    element_index: Optional[int] = Field(
        default=None,
        description="Index of specific element to analyze (if None, analyzes all dropdowns on page)"
    )
    include_custom: bool = Field(
        default=True,
        description="Whether to detect custom dropdowns (divs with ARIA roles)"
    )
    include_native: bool = Field(
        default=True,
        description="Whether to detect native HTML select elements"
    )
    analyze_options: bool = Field(
        default=True,
        description="Whether to analyze available options in dropdown"
    )
    detect_state: bool = Field(
        default=True,
        description="Whether to detect current dropdown state (open/closed)"
    )
    timeout: int = Field(
        default=5000,
        description="Timeout in milliseconds for dropdown detection"
    )


class SmartDropdownSelectAction(BaseModel):
    """Model for intelligent dropdown selection with fallback strategies."""
    
    dropdown_index: int = Field(
        description="Index of the dropdown element to interact with"
    )
    target_value: str = Field(
        description="Text or value to select from dropdown"
    )
    search_strategy: Literal['exact', 'contains', 'fuzzy', 'smart'] = Field(
        default='smart',
        description="Strategy for finding the target option"
    )
    max_attempts: int = Field(
        default=5,
        description="Maximum number of attempts before giving up"
    )
    wait_between_attempts: int = Field(
        default=1000,
        description="Milliseconds to wait between retry attempts"
    )
    force_open_dropdown: bool = Field(
        default=True,
        description="Whether to force dropdown to open before selection"
    )
    verify_selection: bool = Field(
        default=True,
        description="Whether to verify selection was successful"
    )
    fallback_to_keyboard: bool = Field(
        default=True,
        description="Whether to try keyboard navigation as fallback"
    )
    fallback_strategies: List[str] = Field(
        default_factory=lambda: ['click_direct', 'keyboard_nav', 'coordinate_click', 'input_text'],
        description="List of fallback strategies to try in order"
    )


class MonitorDropdownAction(BaseModel):
    """Model for monitoring dropdown state changes."""
    
    dropdown_index: int = Field(
        description="Index of dropdown to monitor"
    )
    monitoring_duration: int = Field(
        default=10000,
        description="Duration in milliseconds to monitor changes"
    )
    check_interval: int = Field(
        default=500,
        description="Interval in milliseconds between state checks"
    )
    monitor_options: bool = Field(
        default=True,
        description="Whether to monitor changes in available options"
    )
    monitor_visibility: bool = Field(
        default=True,
        description="Whether to monitor dropdown visibility changes"
    )


class DropdownInfo(BaseModel):
    """Model representing detailed dropdown information."""
    
    element_index: int = Field(description="Index of the dropdown element")
    dropdown_type: Literal['native', 'aria_combobox', 'aria_listbox', 'custom'] = Field(
        description="Type of dropdown detected"
    )
    trigger_selector: str = Field(description="CSS selector for dropdown trigger")
    options_container_selector: Optional[str] = Field(
        description="CSS selector for options container"
    )
    is_open: bool = Field(description="Whether dropdown is currently open")
    is_searchable: bool = Field(description="Whether dropdown supports text search")
    current_value: Optional[str] = Field(description="Currently selected value")
    available_options: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="List of available options with text and value"
    )
    supports_keyboard_nav: bool = Field(
        description="Whether dropdown supports keyboard navigation"
    )
    timing_requirements: Dict[str, int] = Field(
        default_factory=dict,
        description="Required delays for interaction (open_delay, option_delay, etc.)"
    )
    interaction_pattern: Optional[str] = Field(
        description="Detected interaction pattern (click_to_open, hover_to_open, etc.)"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata about the dropdown"
    )


class DropdownDetectionResponse(BaseModel):
    """Response model for dropdown detection operations."""
    
    dropdowns_found: bool = Field(description="Whether any dropdowns were detected")
    dropdown_count: int = Field(description="Number of dropdowns detected")
    dropdown_info: List[DropdownInfo] = Field(
        default_factory=list,
        description="Detailed information about detected dropdowns"
    )
    native_count: int = Field(description="Number of native select elements")
    custom_count: int = Field(description="Number of custom dropdowns")
    detection_time: float = Field(description="Time taken for detection in milliseconds")
    page_url: str = Field(description="URL where detection was performed")
    error: Optional[str] = Field(description="Error message if detection failed")
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings during detection"
    )


class DropdownSelectionResponse(BaseModel):
    """Response model for dropdown selection operations."""
    
    success: bool = Field(description="Whether selection was successful")
    strategy_used: str = Field(description="Strategy that successfully selected the option")
    attempts_made: int = Field(description="Number of attempts made")
    selection_time: float = Field(description="Time taken to complete selection in milliseconds")
    dropdown_type: str = Field(description="Type of dropdown that was interacted with")
    selected_value: Optional[str] = Field(description="Value that was successfully selected")
    verified: bool = Field(description="Whether selection was verified")
    dropdown_state_after: str = Field(description="State of dropdown after selection (open/closed)")
    fallback_used: bool = Field(description="Whether fallback strategies were needed")
    interaction_sequence: List[str] = Field(
        description="Sequence of actions that led to successful selection"
    )
    error: Optional[str] = Field(description="Error message if selection failed")
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings during selection"
    )


class DropdownMonitoringResponse(BaseModel):
    """Response model for dropdown monitoring operations."""
    
    monitoring_completed: bool = Field(description="Whether monitoring was completed")
    state_changes: List[Dict[str, Any]] = Field(
        description="List of state changes detected during monitoring"
    )
    final_state: str = Field(description="Final dropdown state")
    options_changed: bool = Field(description="Whether available options changed")
    visibility_changes: int = Field(description="Number of visibility state changes")
    total_monitoring_time: float = Field(description="Total monitoring time in milliseconds")
    error: Optional[str] = Field(description="Error message if monitoring failed")
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings during monitoring"
    ) 