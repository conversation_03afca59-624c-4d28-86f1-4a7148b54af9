"""
Models for advanced file upload operations.

These models handle various file upload methods including drag & drop,
multiple files, progress monitoring, and validation.
"""

from typing import Optional, List, Dict, Any, Literal
from pydantic import BaseModel, Field


class UploadFilesAction(BaseModel):
    """Model for advanced file upload operations."""
    
    file_paths: List[str] = Field(description="List of file paths to upload")
    method: Literal['input', 'drag_drop', 'api'] = Field(
        default='input',
        description="Upload method to use"
    )
    target_selector: Optional[str] = Field(
        default=None,
        description="CSS selector for file input or drop zone"
    )
    drop_zone_selector: Optional[str] = Field(
        default=None,
        description="CSS selector for drag & drop zone"
    )
    wait_for_completion: bool = Field(
        default=True,
        description="Whether to wait for upload completion"
    )
    validate_upload: bool = Field(
        default=True,
        description="Whether to validate upload success"
    )
    max_file_size_mb: Optional[int] = Field(
        default=None,
        description="Maximum file size in MB (validation)"
    )
    allowed_extensions: Optional[List[str]] = Field(
        default=None,
        description="List of allowed file extensions"
    )
    upload_timeout: int = Field(
        default=60000,
        description="Timeout in milliseconds for each file upload"
    )
    retry_attempts: int = Field(
        default=3,
        description="Number of retry attempts for failed uploads"
    )


class MonitorUploadAction(BaseModel):
    """Model for monitoring file upload progress."""
    
    timeout: int = Field(
        default=60000,
        description="Timeout in milliseconds to monitor upload"
    )
    check_interval: int = Field(
        default=1000,
        description="Interval in milliseconds between progress checks"
    )
    progress_selector: Optional[str] = Field(
        default=None,
        description="CSS selector for progress indicator"
    )
    completion_selector: Optional[str] = Field(
        default=None,
        description="CSS selector indicating upload completion"
    )
    error_selector: Optional[str] = Field(
        default=None,
        description="CSS selector for error messages"
    )


class UploadProgress(BaseModel):
    """Model representing upload progress information."""
    
    file_name: str = Field(description="Name of the file being uploaded")
    progress_percentage: float = Field(
        description="Upload progress as percentage (0-100)"
    )
    bytes_uploaded: Optional[int] = Field(
        description="Number of bytes uploaded"
    )
    total_bytes: Optional[int] = Field(
        description="Total file size in bytes"
    )
    upload_speed: Optional[float] = Field(
        description="Upload speed in bytes per second"
    )
    estimated_time_remaining: Optional[int] = Field(
        description="Estimated time remaining in seconds"
    )
    status: Literal['uploading', 'completed', 'failed', 'paused'] = Field(
        description="Current upload status"
    )


class UploadResponse(BaseModel):
    """Response model for file upload operations."""
    
    success: bool = Field(description="Whether all uploads were successful")
    uploaded_files: List[Dict[str, Any]] = Field(
        description="List of successfully uploaded files with metadata"
    )
    failed_files: List[Dict[str, Any]] = Field(
        description="List of files that failed to upload with error details"
    )
    total_files: int = Field(description="Total number of files attempted")
    total_size_bytes: Optional[int] = Field(
        description="Total size of all files in bytes"
    )
    upload_duration: float = Field(
        description="Total upload duration in milliseconds"
    )
    method_used: str = Field(description="Upload method that was used")
    validation_results: Optional[Dict[str, Any]] = Field(
        description="Results of file validation checks"
    )
    progress_snapshots: List[UploadProgress] = Field(
        description="Progress snapshots during upload"
    )
    error: Optional[str] = Field(
        description="General error message if operation failed"
    )
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warning messages"
    ) 