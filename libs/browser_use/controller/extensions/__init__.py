"""
Browser-use controller extensions.

This module contains advanced browser automation tools that extend
the core functionality with specialized capabilities.

Each extension follows clean architecture principles:
- Models: Data contracts and validation (in models/)
- Services: Business logic and implementation
- Actions: Integration with the controller registry

Extensions included:
- Alert/Modal handling
- HTTP requests with browser context
- Advanced file uploads
- Smart form filling
- CAPTCHA detection and handling
- Smart dropdown handling
"""

from .alerts_modals import register_alert_modal_actions, AlertModalHandler
from .http_requests import register_http_request_actions
from .file_uploads import register_file_upload_actions
from .smart_forms import register_smart_forms_actions
from .captcha_handler import register_captcha_actions
from .iframe_manager import register_iframe_actions


__all__ = [
    'AlertModalHandler',
    'register_alert_modal_actions',
    'register_http_request_actions',
    'register_file_upload_actions',
    'register_smart_forms_actions',
    'register_captcha_actions',
    'register_iframe_actions',
]

def register_all_extensions(controller):
    """
    Register all available browser extensions with the controller.
    
    This function registers enhanced functionality including:
    - Alert and modal handling
    - HTTP requests with browser context
    - File upload capabilities
    - Advanced form handling
    - CAPTCHA detection, solving, and monitoring
    - Advanced iframe detection and management
    - Smart dropdown detection and interaction
    
    Args:
        controller: The Browser-Use controller instance
    """
    # Core browser extensions
    register_alert_modal_actions(controller)
    register_http_request_actions(controller)
    register_file_upload_actions(controller)
    register_captcha_actions(controller)
    register_iframe_actions(controller)
    
