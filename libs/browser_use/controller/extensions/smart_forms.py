"""
Smart Forms Handler for Browser-Use.

This handler provides intelligent form detection, field mapping,
validation, and automated filling capabilities.
"""

import asyncio
import logging
import re
import time
from typing import Dict, List, Optional, Any, Tuple

from playwright.async_api import Page, ElementHandle

from browser_use.agent.views import ActionResult
from browser_use.controller.extensions.models.form_models import (
    SmartFormFillAction,
    DetectFormAction,
    FormStructure,
    FormFieldInfo,
    FormFillResponse
)

logger = logging.getLogger(__name__)


class SmartFormsHandler:
    """
    Handler for intelligent form operations including automatic field detection,
    smart filling strategies, validation, and multi-step form support.
    """

    def __init__(self):
        self.logger = logger
        self.field_type_patterns = self._init_field_patterns()
        self.common_field_names = self._init_common_field_names()

    def _init_field_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for smart field type detection."""
        return {
            'email': [r'email', r'e-mail', r'mail'],
            'password': [r'password', r'passwd', r'pwd'],
            'phone': [r'phone', r'tel', r'mobile', r'celular'],
            'name': [r'name', r'nombre', r'nom'],
            'first_name': [r'first.?name', r'firstname', r'fname', r'given.?name'],
            'last_name': [r'last.?name', r'lastname', r'lname', r'surname', r'family.?name'],
            'address': [r'address', r'direccion', r'addr'],
            'city': [r'city', r'ciudad', r'town'],
            'zip': [r'zip', r'postal', r'code', r'zipcode'],
            'country': [r'country', r'pais', r'nation'],
            'date': [r'date', r'fecha', r'birth', r'dob'],
            'url': [r'url', r'website', r'site', r'link'],
            'company': [r'company', r'organization', r'org', r'empresa'],
            'title': [r'title', r'job', r'position', r'cargo'],
        }

    def _init_common_field_names(self) -> Dict[str, List[str]]:
        """Initialize common field name mappings for fuzzy matching."""
        return {
            'email': ['email', 'e-mail', 'mail', 'user_email', 'login_email'],
            'password': ['password', 'passwd', 'pwd', 'pass', 'login_password'],
            'username': ['username', 'user', 'login', 'userid', 'user_name'],
            'first_name': ['first_name', 'firstname', 'fname', 'given_name', 'nombre'],
            'last_name': ['last_name', 'lastname', 'lname', 'surname', 'apellido'],
            'full_name': ['name', 'full_name', 'fullname', 'nombre_completo'],
            'phone': ['phone', 'telephone', 'tel', 'mobile', 'celular'],
            'address': ['address', 'street', 'direccion', 'addr'],
            'city': ['city', 'town', 'ciudad'],
            'state': ['state', 'province', 'region', 'estado'],
            'zip': ['zip', 'zipcode', 'postal_code', 'codigo_postal'],
            'country': ['country', 'nation', 'pais'],
        }

    async def smart_form_fill(
        self,
        params: SmartFormFillAction,
        page: Page
    ) -> FormFillResponse:
        """
        Intelligently fill a form with provided data using smart field detection
        and mapping strategies.
        """
        start_time = time.time()
        
        try:
            # Detect form structure
            form_structure = await self._detect_form_structure(page, params.form_selector)
            
            if not form_structure:
                return FormFillResponse(
                    success=False,
                    fields_filled=0,
                    fields_skipped=0,
                    fields_failed=0,
                    form_submitted=False,
                    validation_errors=[],
                    field_mapping={},
                    unmapped_data=list(params.form_data.keys()),
                    fill_duration=0,
                    error="No form structure detected"
                )

            # Map form data to form fields
            field_mapping, unmapped_data = await self._map_data_to_fields(
                params.form_data,
                form_structure.fields,
                params.field_mapping_strategy
            )

            # Fill the form fields
            fill_results = await self._fill_form_fields(
                page,
                field_mapping,
                params
            )

            # Validate before submit if required
            validation_errors = []
            if params.validate_before_submit and params.submit:
                validation_errors = await self._validate_form_fields(page, form_structure)

            # Submit form if requested and validation passed
            form_submitted = False
            if params.submit and not validation_errors:
                form_submitted = await self._submit_form(page, form_structure)

            fill_duration = (time.time() - start_time) * 1000

            return FormFillResponse(
                success=fill_results['success'],
                fields_filled=fill_results['filled'],
                fields_skipped=fill_results['skipped'],
                fields_failed=fill_results['failed'],
                form_submitted=form_submitted,
                validation_errors=validation_errors,
                field_mapping=field_mapping,
                unmapped_data=unmapped_data,
                fill_duration=fill_duration,
                warnings=fill_results['warnings']
            )

        except Exception as e:
            fill_duration = (time.time() - start_time) * 1000
            error_msg = f"Smart form fill failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            return FormFillResponse(
                success=False,
                fields_filled=0,
                fields_skipped=0,
                fields_failed=0,
                form_submitted=False,
                validation_errors=[],
                field_mapping={},
                unmapped_data=list(params.form_data.keys()),
                fill_duration=fill_duration,
                error=error_msg
            )

    async def detect_form_structure(
        self,
        params: DetectFormAction,
        page: Page
    ) -> FormStructure:
        """
        Detect and analyze the structure of forms on the current page.
        """
        try:
            form_structure = await self._detect_form_structure(
                page, 
                params.form_selector,
                params.include_hidden_fields,
                params.analyze_validation,
                params.detect_fieldsets,
                params.extract_labels
            )
            
            return form_structure or FormStructure(
                form_selector="",
                method="GET",
                fields=[]
            )

        except Exception as e:
            error_msg = f"Form structure detection failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise

    async def _detect_form_structure(
        self,
        page: Page,
        form_selector: Optional[str] = None,
        include_hidden: bool = False,
        analyze_validation: bool = True,
        detect_fieldsets: bool = True,
        extract_labels: bool = True
    ) -> Optional[FormStructure]:
        """Internal method to detect form structure."""
        
        # Find the form element
        if form_selector:
            form_elements = await page.query_selector_all(form_selector)
        else:
            form_elements = await page.query_selector_all('form')

        if not form_elements:
            # Try to find forms within the page content
            form_elements = await page.query_selector_all('div[role="form"], fieldset, .form, .formulario')

        if not form_elements:
            return None

        # Use the first form found
        form_element = form_elements[0]
        
        # Get form attributes
        form_selector_final = await self._generate_selector_for_element(page, form_element)
        action_url = await form_element.get_attribute('action')
        method = await form_element.get_attribute('method') or 'GET'
        form_id = await form_element.get_attribute('id')
        enctype = await form_element.get_attribute('enctype') or ''
        is_multipart = 'multipart/form-data' in enctype.lower()

        # Detect form fields
        fields = await self._detect_form_fields(
            page, 
            form_element, 
            include_hidden, 
            analyze_validation, 
            extract_labels
        )

        # Find submit and reset buttons
        submit_button = await self._find_submit_button(page, form_element)
        reset_button = await self._find_reset_button(page, form_element)

        # Extract CSRF token if present
        csrf_token = await self._extract_csrf_token(page, form_element)

        # Detect fieldsets if requested
        fieldsets = []
        if detect_fieldsets:
            fieldsets = await self._detect_fieldsets(page, form_element)

        return FormStructure(
            form_selector=form_selector_final,
            action_url=action_url,
            method=method.upper(),
            fields=fields,
            submit_button_selector=submit_button,
            reset_button_selector=reset_button,
            fieldsets=fieldsets,
            form_id=form_id,
            is_multipart=is_multipart,
            csrf_token=csrf_token
        )

    async def _detect_form_fields(
        self,
        page: Page,
        form_element: ElementHandle,
        include_hidden: bool,
        analyze_validation: bool,
        extract_labels: bool
    ) -> List[FormFieldInfo]:
        """Detect all form fields within a form element."""
        
        fields = []
        
        # Common input selectors
        input_selectors = [
            'input:not([type="submit"]):not([type="button"]):not([type="reset"])',
            'textarea',
            'select',
        ]
        
        if not include_hidden:
            input_selectors[0] += ':not([type="hidden"])'

        for selector in input_selectors:
            elements = await form_element.query_selector_all(selector)
            
            for element in elements:
                field_info = await self._analyze_field_element(
                    page, 
                    element, 
                    analyze_validation, 
                    extract_labels
                )
                if field_info:
                    fields.append(field_info)

        return fields

    async def _analyze_field_element(
        self,
        page: Page,
        element: ElementHandle,
        analyze_validation: bool,
        extract_labels: bool
    ) -> Optional[FormFieldInfo]:
        """Analyze a single form field element."""
        
        try:
            # Basic attributes
            name = await element.get_attribute('name') or ''
            field_type = await element.get_attribute('type') or 'text'
            field_id = await element.get_attribute('id')
            placeholder = await element.get_attribute('placeholder')
            required = await element.get_attribute('required') is not None
            current_value = await element.get_attribute('value') or ''
            aria_label = await element.get_attribute('aria-label')
            
            # Handle select elements
            tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
            if tag_name == 'select':
                field_type = 'select'
            elif tag_name == 'textarea':
                field_type = 'textarea'

            # Generate selector
            selector = await self._generate_selector_for_element(page, element)

            # Extract label if requested
            label = None
            if extract_labels:
                label = await self._extract_field_label(page, element, field_id)

            # Analyze validation if requested
            validation_pattern = None
            max_length = None
            min_length = None
            
            if analyze_validation:
                pattern = await element.get_attribute('pattern')
                if pattern:
                    validation_pattern = pattern
                
                max_len_str = await element.get_attribute('maxlength')
                if max_len_str:
                    try:
                        max_length = int(max_len_str)
                    except ValueError:
                        pass
                
                min_len_str = await element.get_attribute('minlength')
                if min_len_str:
                    try:
                        min_length = int(min_len_str)
                    except ValueError:
                        pass

            return FormFieldInfo(
                name=name,
                field_type=field_type,
                label=label,
                placeholder=placeholder,
                required=required,
                current_value=current_value,
                selector=selector,
                validation_pattern=validation_pattern,
                max_length=max_length,
                min_length=min_length,
                field_id=field_id,
                aria_label=aria_label
            )

        except Exception as e:
            self.logger.warning(f"Failed to analyze field element: {e}")
            return None

    async def _extract_field_label(
        self, 
        page: Page, 
        element: ElementHandle, 
        field_id: Optional[str]
    ) -> Optional[str]:
        """Extract the label text for a form field."""
        
        try:
            # Try label[for="field_id"] first
            if field_id:
                label_element = await page.query_selector(f'label[for="{field_id}"]')
                if label_element:
                    label_text = await label_element.inner_text()
                    return label_text.strip()

            # Try to find parent label
            label_element = await element.evaluate('''
                el => {
                    let current = el.parentElement;
                    while (current && current.tagName !== 'BODY') {
                        if (current.tagName === 'LABEL') {
                            return current;
                        }
                        current = current.parentElement;
                    }
                    return null;
                }
            ''')
            
            if label_element:
                label_text = await page.evaluate('el => el.innerText', label_element)
                return label_text.strip()

            # Try to find preceding label or text
            preceding_text = await element.evaluate('''
                el => {
                    let prev = el.previousElementSibling;
                    if (prev && (prev.tagName === 'LABEL' || prev.tagName === 'SPAN')) {
                        return prev.innerText;
                    }
                    return null;
                }
            ''')
            
            if preceding_text:
                return preceding_text.strip()

        except Exception as e:
            self.logger.debug(f"Failed to extract field label: {e}")

        return None

    async def _map_data_to_fields(
        self,
        form_data: Dict[str, str],
        fields: List[FormFieldInfo],
        strategy: str
    ) -> Tuple[Dict[str, str], List[str]]:
        """Map form data keys to form field selectors using the specified strategy."""
        
        field_mapping = {}
        unmapped_data = []

        for data_key, data_value in form_data.items():
            mapped_field = None
            
            if strategy == 'exact':
                mapped_field = self._exact_field_match(data_key, fields)
            elif strategy == 'fuzzy':
                mapped_field = self._fuzzy_field_match(data_key, fields)
            elif strategy == 'smart':
                mapped_field = self._smart_field_match(data_key, fields)

            if mapped_field:
                field_mapping[mapped_field.selector] = data_value
            else:
                unmapped_data.append(data_key)

        return field_mapping, unmapped_data

    def _exact_field_match(self, data_key: str, fields: List[FormFieldInfo]) -> Optional[FormFieldInfo]:
        """Find exact match between data key and field name."""
        for field in fields:
            if field.name.lower() == data_key.lower():
                return field
        return None

    def _fuzzy_field_match(self, data_key: str, fields: List[FormFieldInfo]) -> Optional[FormFieldInfo]:
        """Find fuzzy match between data key and field attributes."""
        data_key_lower = data_key.lower()
        
        # Check common field name mappings
        for field_type, common_names in self.common_field_names.items():
            if data_key_lower in [name.lower() for name in common_names]:
                for field in fields:
                    if self._field_matches_type(field, field_type):
                        return field

        # Direct partial matching
        for field in fields:
            if (data_key_lower in field.name.lower() or
                (field.label and data_key_lower in field.label.lower()) or
                (field.placeholder and data_key_lower in field.placeholder.lower())):
                return field

        return None

    def _smart_field_match(self, data_key: str, fields: List[FormFieldInfo]) -> Optional[FormFieldInfo]:
        """Smart matching using patterns and context."""
        
        # First try exact match
        exact_match = self._exact_field_match(data_key, fields)
        if exact_match:
            return exact_match

        # Then try fuzzy match
        fuzzy_match = self._fuzzy_field_match(data_key, fields)
        if fuzzy_match:
            return fuzzy_match

        # Use pattern matching for smart detection
        data_key_lower = data_key.lower()
        
        for field_type, patterns in self.field_type_patterns.items():
            if any(re.search(pattern, data_key_lower) for pattern in patterns):
                for field in fields:
                    if self._field_matches_type(field, field_type):
                        return field

        return None

    def _field_matches_type(self, field: FormFieldInfo, field_type: str) -> bool:
        """Check if a field matches the expected type."""
        
        field_name_lower = field.name.lower()
        field_type_lower = field.field_type.lower()
        
        # Direct type matching
        if field_type == 'email' and field_type_lower == 'email':
            return True
        if field_type == 'password' and field_type_lower == 'password':
            return True
        if field_type == 'phone' and field_type_lower in ['tel', 'phone']:
            return True

        # Pattern matching on field attributes
        if field_type in self.field_type_patterns:
            patterns = self.field_type_patterns[field_type]
            
            # Check field name
            if any(re.search(pattern, field_name_lower) for pattern in patterns):
                return True
                
            # Check label
            if field.label:
                label_lower = field.label.lower()
                if any(re.search(pattern, label_lower) for pattern in patterns):
                    return True
                    
            # Check placeholder
            if field.placeholder:
                placeholder_lower = field.placeholder.lower()
                if any(re.search(pattern, placeholder_lower) for pattern in patterns):
                    return True

        return False

    async def _fill_form_fields(
        self,
        page: Page,
        field_mapping: Dict[str, str],
        params: SmartFormFillAction
    ) -> Dict[str, Any]:
        """Fill form fields with the mapped data."""
        
        filled = 0
        failed = 0
        skipped = 0
        warnings = []

        for selector, value in field_mapping.items():
            try:
                element = await page.query_selector(selector)
                if not element:
                    failed += 1
                    warnings.append(f"Element not found for selector: {selector}")
                    continue

                # Check if field is readonly
                if params.skip_readonly:
                    readonly = await element.get_attribute('readonly')
                    disabled = await element.get_attribute('disabled')
                    if readonly is not None or disabled is not None:
                        skipped += 1
                        warnings.append(f"Skipped readonly/disabled field: {selector}")
                        continue

                # Focus field if requested
                if params.focus_before_fill:
                    await element.focus()

                # Clear existing value if requested
                if params.clear_existing:
                    await element.fill('')

                # Fill the field
                await element.fill(value)

                # Trigger events if requested
                if params.trigger_events:
                    await element.dispatch_event('input')
                    await element.dispatch_event('change')

                filled += 1

                # Add delay between fills
                if params.fill_delay > 0:
                    await asyncio.sleep(params.fill_delay / 1000)

            except Exception as e:
                failed += 1
                warnings.append(f"Failed to fill field {selector}: {str(e)}")
                self.logger.warning(f"Failed to fill field {selector}: {e}")

        return {
            'success': failed == 0,
            'filled': filled,
            'failed': failed,
            'skipped': skipped,
            'warnings': warnings
        }

    async def _validate_form_fields(
        self, 
        page: Page, 
        form_structure: FormStructure
    ) -> List[Dict[str, str]]:
        """Validate form fields before submission."""
        
        validation_errors = []

        for field in form_structure.fields:
            try:
                element = await page.query_selector(field.selector)
                if not element:
                    continue

                # Check required fields
                if field.required:
                    current_value = await element.get_attribute('value') or ''
                    if not current_value.strip():
                        validation_errors.append({
                            'field': field.name or field.selector,
                            'error': 'Required field is empty'
                        })

                # Check pattern validation
                if field.validation_pattern:
                    current_value = await element.get_attribute('value') or ''
                    if current_value and not re.match(field.validation_pattern, current_value):
                        validation_errors.append({
                            'field': field.name or field.selector,
                            'error': f'Value does not match required pattern: {field.validation_pattern}'
                        })

                # Check length constraints
                if field.max_length or field.min_length:
                    current_value = await element.get_attribute('value') or ''
                    value_length = len(current_value)
                    
                    if field.max_length and value_length > field.max_length:
                        validation_errors.append({
                            'field': field.name or field.selector,
                            'error': f'Value exceeds maximum length of {field.max_length}'
                        })
                    
                    if field.min_length and value_length < field.min_length:
                        validation_errors.append({
                            'field': field.name or field.selector,
                            'error': f'Value is shorter than minimum length of {field.min_length}'
                        })

            except Exception as e:
                validation_errors.append({
                    'field': field.name or field.selector,
                    'error': f'Validation check failed: {str(e)}'
                })

        return validation_errors

    async def _submit_form(self, page: Page, form_structure: FormStructure) -> bool:
        """Submit the form using the detected submit button or form element."""
        
        try:
            # Try to click submit button first
            if form_structure.submit_button_selector:
                submit_button = await page.query_selector(form_structure.submit_button_selector)
                if submit_button:
                    await submit_button.click()
                    return True

            # Fallback to form submission
            form_element = await page.query_selector(form_structure.form_selector)
            if form_element:
                await form_element.evaluate('form => form.submit()')
                return True

        except Exception as e:
            self.logger.error(f"Failed to submit form: {e}")

        return False

    async def _generate_selector_for_element(self, page: Page, element: ElementHandle) -> str:
        """Generate a reliable CSS selector for an element."""
        
        try:
            selector = await element.evaluate('''
                element => {
                    if (element.id) {
                        return '#' + element.id;
                    }
                    
                    if (element.name) {
                        return '[name="' + element.name + '"]';
                    }
                    
                    let path = [];
                    let current = element;
                    
                    while (current && current.nodeType === Node.ELEMENT_NODE) {
                        let selector = current.nodeName.toLowerCase();
                        
                        if (current.className) {
                            selector += '.' + current.className.split(' ').join('.');
                        }
                        
                        path.unshift(selector);
                        current = current.parentNode;
                        
                        if (path.length > 5) break; // Limit depth
                    }
                    
                    return path.join(' > ');
                }
            ''')
            return selector
        except:
            return 'input'  # Fallback selector

    async def _find_submit_button(self, page: Page, form_element: ElementHandle) -> Optional[str]:
        """Find the submit button for a form."""
        
        try:
            # Look for input[type="submit"] or button[type="submit"]
            submit_inputs = await form_element.query_selector_all('input[type="submit"], button[type="submit"]')
            if submit_inputs:
                return await self._generate_selector_for_element(page, submit_inputs[0])

            # Look for buttons with submit-like text
            buttons = await form_element.query_selector_all('button')
            for button in buttons:
                text = await button.inner_text()
                if text and any(word in text.lower() for word in ['submit', 'send', 'enviar', 'guardar', 'save']):
                    return await self._generate_selector_for_element(page, button)

        except Exception as e:
            self.logger.debug(f"Failed to find submit button: {e}")

        return None

    async def _find_reset_button(self, page: Page, form_element: ElementHandle) -> Optional[str]:
        """Find the reset button for a form."""
        
        try:
            reset_inputs = await form_element.query_selector_all('input[type="reset"], button[type="reset"]')
            if reset_inputs:
                return await self._generate_selector_for_element(page, reset_inputs[0])

        except Exception as e:
            self.logger.debug(f"Failed to find reset button: {e}")

        return None

    async def _extract_csrf_token(self, page: Page, form_element: ElementHandle) -> Optional[str]:
        """Extract CSRF token from the form."""
        
        try:
            # Common CSRF token patterns
            csrf_patterns = [
                'input[name="_token"]',
                'input[name="csrf_token"]',
                'input[name="authenticity_token"]',
                'input[name="_csrf"]',
                'input[name="csrf"]'
            ]

            for pattern in csrf_patterns:
                csrf_input = await form_element.query_selector(pattern)
                if csrf_input:
                    token = await csrf_input.get_attribute('value')
                    if token:
                        return token

        except Exception as e:
            self.logger.debug(f"Failed to extract CSRF token: {e}")

        return None

    async def _detect_fieldsets(self, page: Page, form_element: ElementHandle) -> List[Dict[str, Any]]:
        """Detect fieldset groupings within the form."""
        
        fieldsets = []
        
        try:
            fieldset_elements = await form_element.query_selector_all('fieldset')
            
            for fieldset in fieldset_elements:
                legend = await fieldset.query_selector('legend')
                legend_text = None
                if legend:
                    legend_text = await legend.inner_text()

                fieldset_selector = await self._generate_selector_for_element(page, fieldset)
                
                fieldsets.append({
                    'selector': fieldset_selector,
                    'legend': legend_text,
                    'fields': []  # Could be expanded to include field references
                })

        except Exception as e:
            self.logger.debug(f"Failed to detect fieldsets: {e}")

        return fieldsets


def register_smart_forms_actions(controller):
    """Register smart forms actions with the controller."""
    
    handler = SmartFormsHandler()

    @controller.registry.action(
        'Auto-fill form with smart field detection and mapping',
        param_model=SmartFormFillAction
    )
    async def smart_form_fill(params: SmartFormFillAction, page):
        """Fill a form intelligently using smart field detection."""
        try:
            response = await handler.smart_form_fill(params, page)
            
            if response.success:
                success_msg = f"Successfully filled {response.fields_filled} fields"
                if response.form_submitted:
                    success_msg += " and submitted form"
                
                if response.warnings:
                    success_msg += f" (with {len(response.warnings)} warnings)"
                
                return ActionResult(
                    extracted_content=success_msg,
                    include_in_memory=True,
                    long_term_memory=f"Smart form fill: {response.fields_filled} fields filled, {response.fields_failed} failed, {response.fields_skipped} skipped"
                )
            else:
                error_details = response.error or "Unknown error"
                if response.validation_errors:
                    error_details += f" | Validation errors: {len(response.validation_errors)}"
                
                return ActionResult(
                    error=f"Smart form fill failed: {error_details}",
                    include_in_memory=True
                )

        except Exception as e:
            error_msg = f"Smart form fill operation failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ActionResult(error=error_msg, include_in_memory=True)

    @controller.registry.action(
        'Detect and analyze form structure on the current page',
        param_model=DetectFormAction
    )
    async def detect_form_structure(params: DetectFormAction, page):
        """Detect and analyze form structure with detailed field information."""
        try:
            form_structure = await handler.detect_form_structure(params, page)
            
            if form_structure and form_structure.fields:
                structure_summary = {
                    'form_selector': form_structure.form_selector,
                    'method': form_structure.method,
                    'action_url': form_structure.action_url,
                    'fields_count': len(form_structure.fields),
                    'required_fields': len([f for f in form_structure.fields if f.required]),
                    'field_types': list(set([f.field_type for f in form_structure.fields])),
                    'has_submit_button': bool(form_structure.submit_button_selector),
                    'is_multipart': form_structure.is_multipart,
                    'has_csrf_token': bool(form_structure.csrf_token)
                }
                
                fields_detail = []
                for field in form_structure.fields[:10]:  # Limit to first 10 for readability
                    fields_detail.append({
                        'name': field.name,
                        'type': field.field_type,
                        'label': field.label,
                        'required': field.required,
                        'selector': field.selector
                    })
                
                result_content = f"Form detected with {len(form_structure.fields)} fields. Structure: {structure_summary}"
                
                return ActionResult(
                    extracted_content=result_content,
                    include_in_memory=True,
                    long_term_memory=f"Form structure analyzed: {len(form_structure.fields)} fields, method: {form_structure.method}"
                )
            else:
                return ActionResult(
                    extracted_content="No form structure detected on the current page",
                    include_in_memory=True
                )

        except Exception as e:
            error_msg = f"Form structure detection failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ActionResult(error=error_msg, include_in_memory=True) 