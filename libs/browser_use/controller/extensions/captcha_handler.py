"""
Advanced CAPTCHA Detection and Handling System for Browser-Use.

This module provides comprehensive CAPTCHA detection, analysis, and handling capabilities
including automated solving strategies, human intervention coordination, and robust
recovery mechanisms.
"""

import asyncio
import logging
import time
import base64
import json
from typing import Optional, List, Dict, Any, Tuple
from playwright.async_api import Page, ElementHandle, TimeoutError as PlaywrightTimeoutError

from .models.captcha_models import (
    DetectCaptchaAction, HandleCaptchaAction, SolveCaptchaAction, MonitorCaptchaAction,
    CaptchaInfo, CaptchaDetectionResponse, CaptchaHandlingResponse, 
    CaptchaSolvingResponse, CaptchaMonitoringResponse
)

logger = logging.getLogger(__name__)


class AdvancedCaptchaHandler:
    """
    Advanced CAPTCHA detection and handling system with multiple resolution strategies.
    
    Features:
    - Multi-type CAPTCHA detection (reCAPTCHA, hCAPTCHA, image grids, audio, text)
    - Automated solving with AI vision analysis
    - Human intervention coordination
    - Robust error recovery and retry mechanisms
    - Network monitoring for validation
    - Page state preservation
    """
    
    def __init__(self):
        """Initialize the CAPTCHA handler with detection patterns and strategies."""
        self.detection_patterns = self._init_detection_patterns()
        self.solving_strategies = self._init_solving_strategies()
        self.network_listeners = {}
        
    def _init_detection_patterns(self) -> Dict[str, List[str]]:
        """Initialize comprehensive CAPTCHA detection patterns."""
        return {
            'recaptcha': [
                '.g-recaptcha', '#g-recaptcha', '[data-sitekey]',
                'iframe[src*="recaptcha"]', '.recaptcha-checkbox', '.rc-anchor',
                '.rc-imageselect', '[class*="recaptcha"]',
                '.g-recaptcha[data-size="invisible"]', '[data-callback]'
            ],
            'hcaptcha': [
                '.h-captcha', '#h-captcha', 'iframe[src*="hcaptcha"]',
                '[data-hcaptcha-sitekey]', '[class*="hcaptcha"]'
            ],
            'image_grid': [
                '[class*="captcha-image"]', '[class*="image-challenge"]',
                '.captcha-grid', '[id*="captcha-image"]', '.challenge-images'
            ],
            'text_input': [
                '[class*="captcha-text"]', '[id*="captcha-text"]',
                'input[name*="captcha"]', '.text-captcha', '[placeholder*="captcha"]'
            ],
            'audio': [
                '[class*="audio-challenge"]', '.captcha-audio',
                'audio[src*="captcha"]', '[aria-label*="audio challenge"]'
            ]
        }
    
    def _init_solving_strategies(self) -> Dict[str, str]:
        """Initialize CAPTCHA solving strategy mappings."""
        return {
            'recaptcha': 'handle_recaptcha',
            'hcaptcha': 'handle_hcaptcha', 
            'image_grid': 'handle_image_grid',
            'text_input': 'handle_text_input',
            'audio': 'handle_audio_challenge'
        }
    
    async def detect_captcha(self, params: DetectCaptchaAction, page: Page) -> CaptchaDetectionResponse:
        """Detect CAPTCHAs on the current page with comprehensive analysis."""
        start_time = time.time()
        detected_captchas = []
        warnings = []
        
        try:
            logger.info("🔍 Starting comprehensive CAPTCHA detection...")
            
            # Scan main page content
            main_captchas = await self._scan_page_for_captchas(
                page, params.captcha_types, params.detection_selectors
            )
            detected_captchas.extend(main_captchas)
            
            # Scan iframes if enabled
            if params.include_iframe_scan:
                iframe_captchas = await self._scan_iframes_for_captchas(page, params.captcha_types)
                detected_captchas.extend(iframe_captchas)
            
            # Perform detailed analysis if requested
            if params.detailed_analysis:
                for captcha in detected_captchas:
                    await self._analyze_captcha_details(page, captcha)
            
            detection_time = (time.time() - start_time) * 1000
            
            logger.info(f"✅ CAPTCHA detection completed: {len(detected_captchas)} found in {detection_time:.1f}ms")
            
            return CaptchaDetectionResponse(
                captcha_detected=len(detected_captchas) > 0,
                captcha_count=len(detected_captchas),
                captcha_info=detected_captchas,
                detection_time=detection_time,
                page_url=page.url,
                warnings=warnings
            )
            
        except Exception as e:
            detection_time = (time.time() - start_time) * 1000
            logger.error(f"❌ CAPTCHA detection failed: {e}")
            
            return CaptchaDetectionResponse(
                captcha_detected=False,
                captcha_count=0,
                captcha_info=[],
                detection_time=detection_time,
                page_url=page.url,
                error=str(e),
                warnings=warnings
            )
    
    async def handle_captcha(self, params: HandleCaptchaAction, page: Page) -> CaptchaHandlingResponse:
        """
        Handle detected CAPTCHAs using the specified strategy.
        
        Args:
            params: Handling parameters and strategy configuration
            page: Playwright page object
            
        Returns:
            CaptchaHandlingResponse with handling results
        """
        start_time = time.time()
        attempts_made = 0
        warnings = []
        
        try:
            logger.info(f"🛡️ Starting CAPTCHA handling with strategy: {params.strategy}")
            
            # First detect CAPTCHAs on the page
            detection_params = DetectCaptchaAction()
            detection_result = await self.detect_captcha(detection_params, page)
            
            if not detection_result.captcha_detected:
                            return CaptchaHandlingResponse(
                success=True,
                strategy_used=params.strategy,
                resolution_time=(time.time() - start_time) * 1000,
                attempts_made=0,
                captcha_type=None,
                human_intervention_required=False,
                page_state_preserved=True,
                next_action_recommendation="continue_with_task",
                error=None,
                warnings=["No CAPTCHA detected on page"]
            )
            
            # Handle each detected CAPTCHA
            for captcha_info in detection_result.captcha_info:
                attempts_made += 1
                
                if attempts_made > params.max_retry_attempts:
                    break
                
                success = await self._handle_single_captcha(
                    page, captcha_info, params, attempts_made
                )
                
                if success:
                    resolution_time = (time.time() - start_time) * 1000
                    
                    logger.info(f"✅ CAPTCHA handled successfully in {resolution_time:.1f}ms")
                    
                    return CaptchaHandlingResponse(
                        success=True,
                        strategy_used=params.strategy,
                        resolution_time=resolution_time,
                        attempts_made=attempts_made,
                        captcha_type=captcha_info.captcha_type,
                        human_intervention_required=params.strategy == 'wait_human',
                        page_state_preserved=params.preserve_page_state,
                        next_action_recommendation="continue_with_task",
                        warnings=warnings
                    )
                
                # Wait between attempts
                if attempts_made < params.max_retry_attempts:
                    await asyncio.sleep(params.wait_between_attempts / 1000)
            
            # All attempts failed
            resolution_time = (time.time() - start_time) * 1000
            
            return CaptchaHandlingResponse(
                success=False,
                strategy_used=params.strategy,
                resolution_time=resolution_time,
                attempts_made=attempts_made,
                captcha_type=None,
                human_intervention_required=True,
                page_state_preserved=params.preserve_page_state,
                next_action_recommendation="manual_intervention_required",
                error=f"Failed to resolve CAPTCHA after {attempts_made} attempts",
                warnings=warnings
            )
            
        except Exception as e:
            resolution_time = (time.time() - start_time) * 1000
            logger.error(f"❌ CAPTCHA handling failed: {e}")
            
            return CaptchaHandlingResponse(
                success=False,
                strategy_used=params.strategy,
                resolution_time=resolution_time,
                attempts_made=attempts_made,
                captcha_type=None,
                human_intervention_required=True,
                page_state_preserved=False,
                next_action_recommendation="error_recovery_required",
                error=str(e),
                warnings=warnings
            )
    
    async def solve_captcha(
        self, 
        params: SolveCaptchaAction, 
        page: Page
    ) -> CaptchaSolvingResponse:
        """
        Attempt to automatically solve a CAPTCHA using AI analysis.
        
        Args:
            params: Solving parameters and configuration
            page: Playwright page object
            
        Returns:
            CaptchaSolvingResponse with solving results
        """
        start_time = time.time()
        warnings = []
        
        try:
            logger.info(f"🧠 Attempting to solve {params.captcha_type} CAPTCHA...")
            
            # Get the appropriate solving strategy
            strategy_method = self.solving_strategies.get(params.captcha_type)
            if not strategy_method:
                raise ValueError(f"No solving strategy available for {params.captcha_type}")
            
            # Execute the solving strategy
            solving_method = getattr(self, strategy_method)
            result = await solving_method(page, params)
            
            solving_time = (time.time() - start_time) * 1000
            
            if result['success']:
                logger.info(f"✅ CAPTCHA solved successfully in {solving_time:.1f}ms")
                
                return CaptchaSolvingResponse(
                    solved=True,
                    solution_method=strategy_method,
                    confidence_score=result.get('confidence', 0.0),
                    solution_data=result.get('data', {}),
                    solving_time=solving_time,
                    verification_status=result.get('status', 'pending'),
                    fallback_used=False,
                    error=None,
                    warnings=warnings
                )
            else:
                # Try fallback if enabled
                if params.manual_fallback:
                    fallback_result = await self._manual_fallback_strategy(page, params)
                    solving_time = (time.time() - start_time) * 1000
                    
                    return CaptchaSolvingResponse(
                        solved=fallback_result['success'],
                        solution_method='manual_fallback',
                        confidence_score=0.5,
                        solution_data=fallback_result.get('data', {}),
                        solving_time=solving_time,
                        verification_status='manual',
                        fallback_used=True,
                        error=result.get('error'),
                        warnings=warnings + ['Automated solving failed, used manual fallback']
                    )
                
                return CaptchaSolvingResponse(
                    solved=False,
                    solution_method=strategy_method,
                    confidence_score=0.0,
                    solution_data={},
                    solving_time=solving_time,
                    verification_status='failed',
                    fallback_used=False,
                    error=result.get('error', 'Automated solving failed'),
                    warnings=warnings
                )
                
        except Exception as e:
            solving_time = (time.time() - start_time) * 1000
            logger.error(f"❌ CAPTCHA solving failed: {e}")
            
            return CaptchaSolvingResponse(
                solved=False,
                solution_method='unknown',
                confidence_score=0.0,
                solution_data={},
                solving_time=solving_time,
                verification_status='error',
                fallback_used=False,
                error=str(e),
                warnings=warnings
            )
    
    async def monitor_captcha(
        self, 
        params: MonitorCaptchaAction, 
        page: Page
    ) -> CaptchaMonitoringResponse:
        """
        Monitor CAPTCHA resolution status with network activity tracking.
        
        Args:
            params: Monitoring parameters and configuration
            page: Playwright page object
            
        Returns:
            CaptchaMonitoringResponse with monitoring results
        """
        start_time = time.time()
        status_changes = []
        network_activity = []
        warnings = []
        
        try:
            logger.info(f"📊 Starting CAPTCHA monitoring for {params.monitoring_duration}ms...")
            
            # Setup network monitoring if enabled
            if params.monitor_network_requests:
                await self._setup_network_monitoring(page, network_activity)
            
            # Monitor for resolution
            end_time = start_time + (params.monitoring_duration / 1000)
            last_status = "monitoring"
            resolution_time = None
            
            while time.time() < end_time:
                current_status = await self._check_captcha_status(page, params)
                
                if current_status != last_status:
                    status_changes.append({
                        'timestamp': time.time() * 1000,
                        'status': current_status,
                        'elapsed_time': (time.time() - start_time) * 1000
                    })
                    last_status = current_status
                
                # Check for resolution
                if current_status in ['resolved', 'success']:
                    resolution_time = time.time() * 1000
                    break
                elif current_status in ['failed', 'error']:
                    break
                
                await asyncio.sleep(params.check_interval / 1000)
            
            total_time = (time.time() - start_time) * 1000
            
            logger.info(f"📊 CAPTCHA monitoring completed: {last_status} in {total_time:.1f}ms")
            
            return CaptchaMonitoringResponse(
                monitoring_completed=True,
                final_status=last_status,
                status_changes=status_changes,
                total_monitoring_time=total_time,
                resolution_detected_at=resolution_time,
                network_activity=network_activity if params.monitor_network_requests else None,
                warnings=warnings
            )
            
        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            logger.error(f"❌ CAPTCHA monitoring failed: {e}")
            
            return CaptchaMonitoringResponse(
                monitoring_completed=False,
                final_status='error',
                status_changes=status_changes,
                total_monitoring_time=total_time,
                resolution_detected_at=None,
                network_activity=network_activity,
                error=str(e),
                warnings=warnings
            )
    
    async def _scan_page_for_captchas(
        self, 
        page: Page, 
        captcha_types: List[str], 
        custom_selectors: Optional[List[str]] = None
    ) -> List[CaptchaInfo]:
        """Scan the main page content for CAPTCHAs."""
        detected_captchas = []
        
        # Use custom selectors if provided
        if custom_selectors:
            for selector in custom_selectors:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    captcha_info = await self._create_captcha_info(page, element, 'custom')
                    if captcha_info:
                        detected_captchas.append(captcha_info)
        
        # Scan for each CAPTCHA type
        for captcha_type in captcha_types:
            if captcha_type in self.detection_patterns:
                for pattern in self.detection_patterns[captcha_type]:
                    try:
                        elements = await page.query_selector_all(pattern)
                        for element in elements:
                            captcha_info = await self._create_captcha_info(page, element, captcha_type)
                            if captcha_info and not self._is_duplicate_captcha(captcha_info, detected_captchas):
                                detected_captchas.append(captcha_info)
                    except Exception as e:
                        logger.debug(f"Error scanning pattern {pattern}: {e}")
        
        return detected_captchas
    
    async def _scan_iframes_for_captchas(
        self, 
        page: Page, 
        captcha_types: List[str]
    ) -> List[CaptchaInfo]:
        """Scan iframes for CAPTCHAs."""
        detected_captchas = []
        
        try:
            # Get all iframes
            iframes = await page.query_selector_all('iframe')
            
            for iframe in iframes:
                try:
                    iframe_src = await iframe.get_attribute('src')
                    if iframe_src and ('recaptcha' in iframe_src or 'hcaptcha' in iframe_src):
                        captcha_info = await self._create_captcha_info(
                            page, iframe, 'recaptcha' if 'recaptcha' in iframe_src else 'hcaptcha'
                        )
                        if captcha_info:
                            captcha_info.iframe_url = iframe_src
                            detected_captchas.append(captcha_info)
                except Exception as e:
                    logger.debug(f"Error scanning iframe: {e}")
        
        except Exception as e:
            logger.warning(f"Error scanning iframes: {e}")
        
        return detected_captchas
    
    async def _create_captcha_info(
        self, 
        page: Page, 
        element: ElementHandle, 
        captcha_type: str
    ) -> Optional[CaptchaInfo]:
        """Create CaptchaInfo object from detected element."""
        try:
            # Generate selector for the element
            selector = await self._generate_element_selector(page, element)
            
            # Extract additional information
            challenge_text = await self._extract_challenge_text(page, element)
            site_key = await self._extract_site_key(page, element)
            is_visible = await element.is_visible()
            
            return CaptchaInfo(
                captcha_type=captcha_type,
                element_selector=selector,
                iframe_url=None,
                challenge_text=challenge_text,
                site_key=site_key,
                difficulty_level=self._estimate_difficulty(captcha_type),
                is_invisible=not is_visible,
                requires_interaction=captcha_type in ['recaptcha', 'hcaptcha', 'image_grid'],
                metadata={
                    'detected_at': time.time(),
                    'is_visible': is_visible
                }
            )
            
        except Exception as e:
            logger.debug(f"Error creating CAPTCHA info: {e}")
            return None
    
    async def _analyze_captcha_details(self, page: Page, captcha: CaptchaInfo) -> None:
        """Perform detailed analysis of detected CAPTCHA."""
        try:
            # Try to get more detailed information about the CAPTCHA
            element = await page.query_selector(captcha.element_selector)
            if element:
                # Check for challenge frames or containers
                if captcha.captcha_type == 'recaptcha':
                    challenge_frame = await page.query_selector('.rc-imageselect, .rc-defaultchallenge')
                    if challenge_frame:
                        captcha.metadata['has_challenge_frame'] = True
                        
                # Check for audio options
                audio_button = await page.query_selector('[id*="audio"], [aria-label*="audio"]')
                if audio_button:
                    captcha.metadata['has_audio_option'] = True
                    
        except Exception as e:
            logger.debug(f"Error analyzing CAPTCHA details: {e}")
    
    async def _handle_single_captcha(
        self, 
        page: Page, 
        captcha_info: CaptchaInfo, 
        params: HandleCaptchaAction, 
        attempt: int
    ) -> bool:
        """Handle a single CAPTCHA using the specified strategy."""
        try:
            logger.info(f"🎯 Handling {captcha_info.captcha_type} CAPTCHA (attempt {attempt})")
            
            if params.strategy == 'auto':
                return await self._auto_strategy(page, captcha_info, params)
            elif params.strategy == 'wait_human':
                return await self._wait_human_strategy(page, captcha_info, params)
            elif params.strategy == 'audio_challenge':
                return await self._audio_challenge_strategy(page, captcha_info, params)
            elif params.strategy == 'refresh_page':
                return await self._refresh_page_strategy(page, captcha_info, params)
            elif params.strategy == 'retry_later':
                return await self._retry_later_strategy(page, captcha_info, params)
            else:
                logger.warning(f"Unknown strategy: {params.strategy}")
                return False
                
        except Exception as e:
            logger.error(f"Error handling CAPTCHA: {e}")
            return False
    
    async def _auto_strategy(self, page: Page, captcha_info: CaptchaInfo, params: HandleCaptchaAction) -> bool:
        """Automatic CAPTCHA handling strategy."""
        # For auto strategy, try different approaches based on CAPTCHA type
        if captcha_info.captcha_type == 'recaptcha':
            return await self._handle_recaptcha_auto(page, captcha_info, params)
        elif captcha_info.captcha_type == 'hcaptcha':
            return await self._handle_hcaptcha_auto(page, captcha_info, params)
        else:
            # Fall back to waiting for human intervention
            return await self._wait_human_strategy(page, captcha_info, params)
    
    async def _wait_human_strategy(self, page: Page, captcha_info: CaptchaInfo, params: HandleCaptchaAction) -> bool:
        """Wait for human to solve the CAPTCHA."""
        logger.info("⏳ Waiting for human intervention to solve CAPTCHA...")
        
        timeout = params.human_intervention_timeout / 1000
        check_interval = 2  # Check every 2 seconds
        
        for i in range(int(timeout / check_interval)):
            await asyncio.sleep(check_interval)
            
            # Check if CAPTCHA is still present
            try:
                element = await page.query_selector(captcha_info.element_selector)
                if not element or not await element.is_visible():
                    logger.info("✅ CAPTCHA resolved by human intervention")
                    return True
            except Exception:
                # If element is not found, assume CAPTCHA is resolved
                return True
        
        logger.warning("⏰ Human intervention timeout reached")
        return False
    
    async def _audio_challenge_strategy(self, page: Page, captcha_info: CaptchaInfo, params: HandleCaptchaAction) -> bool:
        """Try to use audio challenge for CAPTCHA solving."""
        try:
            # Look for audio challenge button
            audio_selectors = [
                '[id*="audio"]', '[aria-label*="audio"]', 
                '.rc-button-audio', '[title*="audio"]'
            ]
            
            for selector in audio_selectors:
                audio_button = await page.query_selector(selector)
                if audio_button and await audio_button.is_visible():
                    await audio_button.click()
                    await asyncio.sleep(2)
                    
                    # Wait for audio to load and then fall back to human intervention
                    return await self._wait_human_strategy(page, captcha_info, params)
            
            logger.warning("No audio challenge option found")
            return False
            
        except Exception as e:
            logger.error(f"Error with audio challenge strategy: {e}")
            return False
    
    async def _refresh_page_strategy(self, page: Page, captcha_info: CaptchaInfo, params: HandleCaptchaAction) -> bool:
        """Refresh the page to get a new CAPTCHA."""
        try:
            logger.info("🔄 Refreshing page to get new CAPTCHA...")
            await page.reload(wait_until='networkidle')
            await asyncio.sleep(3)
            
            # Check if CAPTCHA is still present after refresh
            element = await page.query_selector(captcha_info.element_selector)
            return element is None or not await element.is_visible()
            
        except Exception as e:
            logger.error(f"Error with refresh strategy: {e}")
            return False
    
    async def _retry_later_strategy(self, page: Page, captcha_info: CaptchaInfo, params: HandleCaptchaAction) -> bool:
        """Wait and retry the CAPTCHA later."""
        logger.info("⏳ Waiting before retrying CAPTCHA...")
        await asyncio.sleep(params.wait_between_attempts / 1000)
        return await self._wait_human_strategy(page, captcha_info, params)
    
    # Solving strategy methods (placeholder implementations)
    async def handle_recaptcha(self, page: Page, params: SolveCaptchaAction) -> Dict[str, Any]:
        """Handle reCAPTCHA solving (placeholder for AI vision integration)."""
        return {
            'success': False,
            'error': 'Automated reCAPTCHA solving not implemented yet',
            'confidence': 0.0
        }
    
    async def handle_hcaptcha(self, page: Page, params: SolveCaptchaAction) -> Dict[str, Any]:
        """Handle hCAPTCHA solving (placeholder for AI vision integration)."""
        return {
            'success': False, 
            'error': 'Automated hCAPTCHA solving not implemented yet',
            'confidence': 0.0
        }
    
    async def handle_image_grid(self, page: Page, params: SolveCaptchaAction) -> Dict[str, Any]:
        """Handle image grid CAPTCHA solving."""
        return {
            'success': False,
            'error': 'Automated image grid solving not implemented yet', 
            'confidence': 0.0
        }
    
    async def handle_text_input(self, page: Page, params: SolveCaptchaAction) -> Dict[str, Any]:
        """Handle text input CAPTCHA solving."""
        return {
            'success': False,
            'error': 'Automated text CAPTCHA solving not implemented yet',
            'confidence': 0.0
        }
    
    async def handle_audio_challenge(self, page: Page, params: SolveCaptchaAction) -> Dict[str, Any]:
        """Handle audio CAPTCHA solving."""
        return {
            'success': False,
            'error': 'Automated audio CAPTCHA solving not implemented yet',
            'confidence': 0.0
        }
    
    # Helper methods
    async def _generate_element_selector(self, page: Page, element: ElementHandle) -> str:
        """Generate a CSS selector for the given element."""
        try:
            # Try to get a unique selector for the element
            selector = await page.evaluate("""
                (element) => {
                    if (element.id) return '#' + element.id;
                    if (element.className) return '.' + element.className.split(' ').join('.');
                    return element.tagName.toLowerCase();
                }
            """, element)
            return selector
        except Exception:
            return 'unknown'
    
    async def _extract_challenge_text(self, page: Page, element: ElementHandle) -> Optional[str]:
        """Extract challenge text from CAPTCHA element."""
        try:
            text = await element.text_content()
            return text.strip() if text else None
        except Exception:
            return None
    
    async def _extract_site_key(self, page: Page, element: ElementHandle) -> Optional[str]:
        """Extract site key from CAPTCHA element."""
        try:
            site_key = await element.get_attribute('data-sitekey')
            if not site_key:
                site_key = await element.get_attribute('data-hcaptcha-sitekey')
            return site_key
        except Exception:
            return None
    
    def _estimate_difficulty(self, captcha_type: str) -> str:
        """Estimate CAPTCHA difficulty level."""
        difficulty_map = {
            'recaptcha': 'medium',
            'hcaptcha': 'medium', 
            'image_grid': 'hard',
            'text_input': 'easy',
            'audio': 'hard'
        }
        return difficulty_map.get(captcha_type, 'unknown')
    
    def _is_duplicate_captcha(self, captcha_info: CaptchaInfo, existing_captchas: List[CaptchaInfo]) -> bool:
        """Check if this CAPTCHA is a duplicate of an already detected one."""
        for existing in existing_captchas:
            if (existing.element_selector == captcha_info.element_selector and 
                existing.captcha_type == captcha_info.captcha_type):
                return True
        return False
    
    async def _setup_network_monitoring(self, page: Page, network_activity: List[Dict[str, Any]]) -> None:
        """Setup network monitoring for CAPTCHA validation requests."""
        def handle_request(request):
            if any(keyword in request.url.lower() for keyword in ['captcha', 'recaptcha', 'hcaptcha']):
                network_activity.append({
                    'timestamp': time.time() * 1000,
                    'type': 'request',
                    'url': request.url,
                    'method': request.method
                })
        
        def handle_response(response):
            if any(keyword in response.url.lower() for keyword in ['captcha', 'recaptcha', 'hcaptcha']):
                network_activity.append({
                    'timestamp': time.time() * 1000,
                    'type': 'response',
                    'url': response.url,
                    'status': response.status
                })
        
        page.on('request', handle_request)
        page.on('response', handle_response)
    
    async def _check_captcha_status(self, page: Page, params: MonitorCaptchaAction) -> str:
        """Check current CAPTCHA status."""
        try:
            # Check for success indicators
            if params.success_indicators:
                for selector in params.success_indicators:
                    element = await page.query_selector(selector)
                    if element and await element.is_visible():
                        return 'resolved'
            
            # Check for failure indicators  
            if params.failure_indicators:
                for selector in params.failure_indicators:
                    element = await page.query_selector(selector)
                    if element and await element.is_visible():
                        return 'failed'
            
            # Check if common CAPTCHA elements are still present
            captcha_selectors = [
                '.g-recaptcha', '.h-captcha', '[class*="captcha"]', 
                'iframe[src*="recaptcha"]', 'iframe[src*="hcaptcha"]'
            ]
            
            for selector in captcha_selectors:
                element = await page.query_selector(selector)
                if element and await element.is_visible():
                    return 'pending'
            
            return 'resolved'
            
        except Exception:
            return 'error'
    
    async def _handle_recaptcha_auto(self, page: Page, captcha_info: CaptchaInfo, params: HandleCaptchaAction) -> bool:
        """Auto-handle reCAPTCHA (placeholder for future implementation)."""
        # For now, fall back to human intervention
        return await self._wait_human_strategy(page, captcha_info, params)
    
    async def _handle_hcaptcha_auto(self, page: Page, captcha_info: CaptchaInfo, params: HandleCaptchaAction) -> bool:
        """Auto-handle hCAPTCHA (placeholder for future implementation)."""
        # For now, fall back to human intervention
        return await self._wait_human_strategy(page, captcha_info, params)
    
    async def _manual_fallback_strategy(self, page: Page, params: SolveCaptchaAction) -> Dict[str, Any]:
        """Manual fallback strategy when automated solving fails."""
        return {
            'success': False,
            'error': 'Manual fallback not implemented - requires human intervention',
            'data': {},
            'confidence': 0.0
        }


def register_captcha_actions(controller):
    """Register CAPTCHA detection and handling actions with the controller."""
    
    handler = AdvancedCaptchaHandler()
    
    @controller.registry.action(
        'Detect CAPTCHA challenges on the current page',
        param_model=DetectCaptchaAction
    )
    async def detect_captcha(params: DetectCaptchaAction, page):
        """Detect CAPTCHAs with comprehensive analysis and type identification."""
        try:
            return await handler.detect_captcha(params, page)
        except Exception as e:
            logger.error(f"CAPTCHA detection action failed: {e}")
            return CaptchaDetectionResponse(
                captcha_detected=False,
                captcha_count=0,
                captcha_info=[],
                detection_time=0,
                page_url=page.url,
                error=str(e)
            )
    
    @controller.registry.action(
        'Handle detected CAPTCHA challenges with multiple strategies',
        param_model=HandleCaptchaAction
    )
    async def handle_captcha(params: HandleCaptchaAction, page):
        """Handle CAPTCHAs using various strategies including human intervention coordination."""
        try:
            return await handler.handle_captcha(params, page)
        except Exception as e:
            logger.error(f"CAPTCHA handling action failed: {e}")
            return CaptchaHandlingResponse(
                success=False,
                strategy_used=params.strategy,
                resolution_time=0,
                attempts_made=0,
                human_intervention_required=True,
                page_state_preserved=False,
                error=str(e)
            )
    
    @controller.registry.action(
        'Attempt to automatically solve CAPTCHA using AI analysis',
        param_model=SolveCaptchaAction
    )
    async def solve_captcha(params: SolveCaptchaAction, page):
        """Solve CAPTCHAs using automated AI vision analysis and fallback strategies."""
        try:
            return await handler.solve_captcha(params, page)
        except Exception as e:
            logger.error(f"CAPTCHA solving action failed: {e}")
            return CaptchaSolvingResponse(
                solved=False,
                solution_method='error',
                confidence_score=0.0,
                solving_time=0,
                verification_status='error',
                fallback_used=False,
                error=str(e)
            )
    
    @controller.registry.action(
        'Monitor CAPTCHA resolution status with network tracking',
        param_model=MonitorCaptchaAction
    )
    async def monitor_captcha_resolution(params: MonitorCaptchaAction, page):
        """Monitor CAPTCHA resolution progress with detailed status tracking."""
        try:
            return await handler.monitor_captcha(params, page)
        except Exception as e:
            logger.error(f"CAPTCHA monitoring action failed: {e}")
            return CaptchaMonitoringResponse(
                monitoring_completed=False,
                final_status='error',
                status_changes=[],
                total_monitoring_time=0,
                error=str(e)
            )
    
    logger.info("🛡️ CAPTCHA detection and handling actions registered successfully") 