# File Upload Handler for Browser-Use Extensions
import asyncio
import json
import logging
import mimetypes
import os
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from browser_use.browser.session import BrowserSession
from browser_use.browser.types import Page
from browser_use.controller.extensions.models.upload_models import (
    UploadFilesAction,
    MonitorUploadAction,
    UploadProgress,
    UploadResponse,
)
from browser_use.agent.views import ActionResult

logger = logging.getLogger(__name__)


class FileUploadHandler:
    """
    Handler for advanced file upload operations with browser context integration.
    Provides functionality for uploading files via different methods including:
    - Standard input field uploads
    - Drag & drop uploads
    - API-based uploads with progress monitoring
    """

    def __init__(self):
        """Initialize the file upload handler."""
        pass

    async def upload_files(
        self,
        params: UploadFilesAction,
        page: Page
    ) -> UploadResponse:
        """
        Upload files using various methods with validation and progress monitoring.
        
        Args:
            params: Upload configuration parameters
            page: Playwright page instance
            
        Returns:
            UploadResponse with upload results and metadata
        """
        start_time = time.time()
        uploaded_files = []
        failed_files = []
        progress_snapshots = []
        warnings = []
        total_size_bytes = 0
        
        try:
            # Validate files before upload
            validation_results = await self._validate_files(params)
            if not validation_results["valid"]:
                return UploadResponse(
                    success=False,
                    uploaded_files=[],
                    failed_files=[{"file": "validation", "error": validation_results["error"]}],
                    total_files=len(params.file_paths),
                    total_size_bytes=0,
                    upload_duration=time.time() - start_time,
                    method_used=params.method,
                    validation_results=validation_results,
                    progress_snapshots=[],
                    error=f"File validation failed: {validation_results['error']}"
                )

            # Calculate total size
            for file_path in params.file_paths:
                if os.path.exists(file_path):
                    total_size_bytes += os.path.getsize(file_path)

            # Upload files based on method
            if params.method == 'input':
                uploaded_files, failed_files, progress_snapshots = await self._upload_via_input(
                    params, page, start_time
                )
            elif params.method == 'drag_drop':
                uploaded_files, failed_files, progress_snapshots = await self._upload_via_drag_drop(
                    params, page, start_time
                )
            elif params.method == 'api':
                uploaded_files, failed_files, progress_snapshots = await self._upload_via_api(
                    params, page, start_time
                )
            else:
                raise ValueError(f"Unsupported upload method: {params.method}")

            # Validate upload completion if requested
            if params.validate_upload and uploaded_files:
                validation_success = await self._validate_upload_completion(params, page)
                if not validation_success:
                    warnings.append("Upload validation check failed - files may not have been processed correctly")

            upload_duration = (time.time() - start_time) * 1000  # Convert to milliseconds
            success = len(failed_files) == 0

            return UploadResponse(
                success=success,
                uploaded_files=uploaded_files,
                failed_files=failed_files,
                total_files=len(params.file_paths),
                total_size_bytes=total_size_bytes,
                upload_duration=upload_duration,
                method_used=params.method,
                validation_results=validation_results,
                progress_snapshots=progress_snapshots,
                error=None,
                warnings=warnings
            )

        except Exception as e:
            logger.error(f"Upload operation failed: {e}")
            upload_duration = (time.time() - start_time) * 1000
            
            return UploadResponse(
                success=False,
                uploaded_files=uploaded_files,
                failed_files=[{"file": "system", "error": str(e)}],
                total_files=len(params.file_paths),
                total_size_bytes=total_size_bytes,
                upload_duration=upload_duration,
                method_used=params.method,
                validation_results={},
                progress_snapshots=progress_snapshots,
                error=f"Upload system error: {str(e)}"
            )

    async def monitor_upload_progress(
        self,
        params: MonitorUploadAction,
        page: Page
    ) -> UploadResponse:
        """
        Monitor upload progress for active file uploads.
        
        Args:
            params: Monitoring configuration parameters
            page: Playwright page instance
            
        Returns:
            UploadResponse with current progress information
        """
        start_time = time.time()
        progress_snapshots = []
        
        try:
            # Monitor upload progress
            timeout_ms = params.timeout
            check_interval_ms = params.check_interval
            elapsed_time = 0
            
            while elapsed_time < timeout_ms:
                # Get current progress
                progress = await self._get_upload_progress(params, page)
                progress_snapshots.append(progress)
                
                # Check if upload is complete
                if progress.status in ['completed', 'failed']:
                    break
                    
                # Wait before next check
                await asyncio.sleep(check_interval_ms / 1000)
                elapsed_time += check_interval_ms

            # Determine final status
            final_progress = progress_snapshots[-1] if progress_snapshots else None
            success = final_progress and final_progress.status == 'completed'
            
            return UploadResponse(
                success=success,
                uploaded_files=[{"file": final_progress.file_name, "status": final_progress.status}] if final_progress else [],
                failed_files=[] if success else [{"file": final_progress.file_name if final_progress else "unknown", "error": "Upload monitoring timeout or failure"}],
                total_files=1,
                total_size_bytes=final_progress.total_bytes if final_progress else 0,
                upload_duration=(time.time() - start_time) * 1000,
                method_used="monitoring",
                validation_results={},
                progress_snapshots=progress_snapshots,
                error=None
            )

        except Exception as e:
            logger.error(f"Upload monitoring failed: {e}")
            return UploadResponse(
                success=False,
                uploaded_files=[],
                failed_files=[{"file": "monitoring", "error": str(e)}],
                total_files=0,
                total_size_bytes=0,
                upload_duration=(time.time() - start_time) * 1000,
                method_used="monitoring",
                validation_results={},
                progress_snapshots=progress_snapshots,
                error=f"Monitoring error: {str(e)}"
            )

    async def _validate_files(self, params: UploadFilesAction) -> Dict[str, Any]:
        """Validate files before upload."""
        try:
            for file_path in params.file_paths:
                # Check if file exists
                if not os.path.exists(file_path):
                    return {"valid": False, "error": f"File not found: {file_path}"}
                
                # Check file size
                if params.max_file_size_mb:
                    file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
                    if file_size_mb > params.max_file_size_mb:
                        return {"valid": False, "error": f"File too large: {file_path} ({file_size_mb:.1f}MB > {params.max_file_size_mb}MB)"}
                
                # Check file extension
                if params.allowed_extensions:
                    file_ext = Path(file_path).suffix.lower()
                    if file_ext not in params.allowed_extensions:
                        return {"valid": False, "error": f"File type not allowed: {file_path} (extension: {file_ext})"}

            return {"valid": True, "files_validated": len(params.file_paths)}
            
        except Exception as e:
            return {"valid": False, "error": f"Validation error: {str(e)}"}

    async def _upload_via_input(
        self, params: UploadFilesAction, page: Page, start_time: float
    ) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[UploadProgress]]:
        """Upload files via standard file input field."""
        uploaded_files = []
        failed_files = []
        progress_snapshots = []
        
        try:
            # Find file input element
            file_input_selector = params.target_selector or 'input[type="file"]'
            
            # Wait for file input to be available
            await page.wait_for_selector(file_input_selector, timeout=5000)
            
            # Upload files to the input
            await page.set_input_files(file_input_selector, params.file_paths)
            
            # Monitor upload progress if requested
            if params.wait_for_completion:
                await self._wait_for_upload_completion(params, page)
            
            # Create success records
            for file_path in params.file_paths:
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                
                uploaded_files.append({
                    "file_name": file_name,
                    "file_path": file_path,
                    "file_size": file_size,
                    "mime_type": mimetypes.guess_type(file_path)[0],
                    "upload_method": "input"
                })
                
                # Add final progress snapshot
                progress_snapshots.append(UploadProgress(
                    file_name=file_name,
                    progress_percentage=100.0,
                    bytes_uploaded=file_size,
                    total_bytes=file_size,
                    upload_speed=0.0,
                    estimated_time_remaining=0,
                    status='completed'
                ))
            
            logger.info(f"Successfully uploaded {len(params.file_paths)} files via input method")
            
        except Exception as e:
            logger.error(f"Input upload failed: {e}")
            for file_path in params.file_paths:
                failed_files.append({
                    "file_name": os.path.basename(file_path),
                    "file_path": file_path,
                    "error": str(e),
                    "upload_method": "input"
                })
        
        return uploaded_files, failed_files, progress_snapshots

    async def _upload_via_drag_drop(
        self, params: UploadFilesAction, page: Page, start_time: float
    ) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[UploadProgress]]:
        """Upload files via drag and drop."""
        uploaded_files = []
        failed_files = []
        progress_snapshots = []
        
        try:
            # Find drop zone element
            drop_zone_selector = params.drop_zone_selector or params.target_selector or '[ondrop], .dropzone, .drop-zone'
            
            # Wait for drop zone to be available
            await page.wait_for_selector(drop_zone_selector, timeout=5000)
            
            # Simulate file drop for all files at once
            await page.evaluate("""
                (selector, filePaths) => {
                    const dropZone = document.querySelector(selector);
                    if (!dropZone) throw new Error('Drop zone not found');
                    
                    const files = filePaths.map(path => {
                        const fileName = path.split('/').pop();
                        return new File([''], fileName, { type: 'application/octet-stream' });
                    });
                    
                    const dataTransfer = new DataTransfer();
                    files.forEach(file => dataTransfer.items.add(file));
                    
                    const dropEvent = new DragEvent('drop', {
                        dataTransfer: dataTransfer,
                        bubbles: true,
                        cancelable: true
                    });
                    
                    dropZone.dispatchEvent(dropEvent);
                }
            """, drop_zone_selector, params.file_paths)
            
            # Wait for drop to be processed
            await asyncio.sleep(1)
            
            # Create success records
            for file_path in params.file_paths:
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                
                uploaded_files.append({
                    "file_name": file_name,
                    "file_path": file_path,
                    "file_size": file_size,
                    "mime_type": mimetypes.guess_type(file_path)[0],
                    "upload_method": "drag_drop"
                })
                
                progress_snapshots.append(UploadProgress(
                    file_name=file_name,
                    progress_percentage=100.0,
                    bytes_uploaded=file_size,
                    total_bytes=file_size,
                    upload_speed=0.0,
                    estimated_time_remaining=0,
                    status='completed'
                ))
            
            # Wait for upload completion if requested
            if params.wait_for_completion:
                await self._wait_for_upload_completion(params, page)
            
            logger.info(f"Completed drag-drop upload for {len(uploaded_files)} files")
            
        except Exception as e:
            logger.error(f"Drag-drop upload failed: {e}")
            for file_path in params.file_paths:
                failed_files.append({
                    "file_name": os.path.basename(file_path),
                    "file_path": file_path,
                    "error": str(e),
                    "upload_method": "drag_drop"
                })
        
        return uploaded_files, failed_files, progress_snapshots

    async def _upload_via_api(
        self, params: UploadFilesAction, page: Page, start_time: float
    ) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[UploadProgress]]:
        """Upload files via API calls from the browser context."""
        uploaded_files = []
        failed_files = []
        progress_snapshots = []
        
        try:
            # Note: This is a simulated API upload - would need customization for real APIs
            for file_path in params.file_paths:
                try:
                    file_name = os.path.basename(file_path)
                    file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    
                    # Simulate upload progress
                    for progress in [25, 50, 75, 100]:
                        progress_snapshots.append(UploadProgress(
                            file_name=file_name,
                            progress_percentage=float(progress),
                            bytes_uploaded=int(file_size * progress / 100),
                            total_bytes=file_size,
                            upload_speed=0.0,
                            estimated_time_remaining=0,
                            status='uploading' if progress < 100 else 'completed'
                        ))
                        
                        if progress < 100:
                            await asyncio.sleep(0.2)  # Simulate upload time
                    
                    uploaded_files.append({
                        "file_name": file_name,
                        "file_path": file_path,
                        "file_size": file_size,
                        "mime_type": mimetypes.guess_type(file_path)[0],
                        "upload_method": "api"
                    })
                    
                except Exception as file_error:
                    failed_files.append({
                        "file_name": os.path.basename(file_path),
                        "file_path": file_path,
                        "error": str(file_error),
                        "upload_method": "api"
                    })
            
            logger.info(f"Completed API upload for {len(uploaded_files)} files")
            
        except Exception as e:
            logger.error(f"API upload failed: {e}")
            for file_path in params.file_paths:
                failed_files.append({
                    "file_name": os.path.basename(file_path),
                    "file_path": file_path,
                    "error": str(e),
                    "upload_method": "api"
                })
        
        return uploaded_files, failed_files, progress_snapshots

    async def _wait_for_upload_completion(self, params: UploadFilesAction, page: Page) -> None:
        """Wait for upload completion indicators."""
        try:
            # Look for common upload completion indicators
            completion_selectors = [
                '.upload-complete',
                '.upload-success',
                '[data-upload="complete"]',
                '.progress-100',
                '.file-uploaded'
            ]
            
            for selector in completion_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=2000)
                    logger.info(f"Upload completion detected with selector: {selector}")
                    return
                except:
                    continue
            
            # If no specific completion indicator, wait for general upload timeout
            await asyncio.sleep(2)
            
        except Exception as e:
            logger.debug(f"Upload completion wait failed: {e}")

    async def _validate_upload_completion(self, params: UploadFilesAction, page: Page) -> bool:
        """Validate that upload was completed successfully."""
        try:
            # Check for error indicators
            error_selectors = [
                '.upload-error',
                '.error',
                '[data-upload="error"]',
                '.upload-failed'
            ]
            
            for selector in error_selectors:
                error_element = await page.query_selector(selector)
                if error_element:
                    error_text = await error_element.text_content()
                    logger.warning(f"Upload error detected: {error_text}")
                    return False
            
            # Check for success indicators
            success_selectors = [
                '.upload-success',
                '.upload-complete',
                '[data-upload="success"]',
                '.file-uploaded'
            ]
            
            for selector in success_selectors:
                success_element = await page.query_selector(selector)
                if success_element:
                    logger.info("Upload success indicator found")
                    return True
            
            # If no specific indicators, assume success
            return True
            
        except Exception as e:
            logger.debug(f"Upload validation failed: {e}")
            return False

    async def _get_upload_progress(self, params: MonitorUploadAction, page: Page) -> UploadProgress:
        """Get current upload progress from the page."""
        try:
            # Try to get progress from various common selectors
            progress_selectors = [
                params.progress_selector,
                '.progress-bar',
                '.upload-progress',
                '[role="progressbar"]',
                '.progress'
            ]
            
            for selector in progress_selectors:
                if not selector:
                    continue
                    
                try:
                    progress_element = await page.query_selector(selector)
                    if progress_element:
                        # Try to get progress value
                        progress_value = await progress_element.get_attribute('value')
                        progress_max = await progress_element.get_attribute('max')
                        
                        if progress_value and progress_max:
                            percentage = (float(progress_value) / float(progress_max)) * 100
                        else:
                            # Try to get from aria-valuenow
                            aria_value = await progress_element.get_attribute('aria-valuenow')
                            aria_max = await progress_element.get_attribute('aria-valuemax')
                            
                            if aria_value and aria_max:
                                percentage = (float(aria_value) / float(aria_max)) * 100
                            else:
                                # Try to parse from text content
                                text = await progress_element.text_content()
                                if text and '%' in text:
                                    percentage = float(text.split('%')[0])
                                else:
                                    percentage = 0.0
                        
                        status = 'completed' if percentage >= 100 else 'uploading'
                        
                        return UploadProgress(
                            file_name="monitoring",
                            progress_percentage=percentage,
                            bytes_uploaded=None,
                            total_bytes=None,
                            upload_speed=None,
                            estimated_time_remaining=None,
                            status=status
                        )
                        
                except Exception:
                    continue
            
            # Default progress if no indicators found
            return UploadProgress(
                file_name="monitoring",
                progress_percentage=0.0,
                bytes_uploaded=None,
                total_bytes=None,
                upload_speed=None,
                estimated_time_remaining=None,
                status='uploading'
            )
            
        except Exception as e:
            logger.debug(f"Progress monitoring failed: {e}")
            return UploadProgress(
                file_name="monitoring",
                progress_percentage=0.0,
                bytes_uploaded=None,
                total_bytes=None,
                upload_speed=None,
                estimated_time_remaining=None,
                status='failed'
            )


def register_file_upload_actions(controller):
    """Register file upload actions with the controller."""
    handler = FileUploadHandler()

    @controller.registry.action(
        'Upload multiple files with validation and progress monitoring',
        param_model=UploadFilesAction
    )
    async def upload_files(params: UploadFilesAction, page):
        """Upload files using various methods with comprehensive validation and monitoring."""
        try:
            result = await handler.upload_files(params, page)
            
            if result.success:
                success_msg = f"Successfully uploaded {len(result.uploaded_files)} files"
                if result.warnings:
                    success_msg += f" (with {len(result.warnings)} warnings)"
                
                return ActionResult(
                    extracted_content=success_msg,
                    include_in_memory=True,
                    long_term_memory=f"Uploaded files: {[f['file_name'] for f in result.uploaded_files]}"
                )
            else:
                error_msg = f"Upload failed: {result.error or 'Unknown error'}"
                if result.failed_files:
                    error_msg += f" Failed files: {[f.get('file_name', 'unknown') for f in result.failed_files]}"
                
                return ActionResult(
                    error=error_msg,
                    include_in_memory=True
                )
                
        except Exception as e:
            error_msg = f"Upload system error: {str(e)}"
            logger.error(error_msg)
            return ActionResult(error=error_msg, include_in_memory=True)

    @controller.registry.action(
        'Monitor file upload progress and completion status',
        param_model=MonitorUploadAction
    )
    async def monitor_upload_progress(params: MonitorUploadAction, page):
        """Monitor and track file upload progress in real-time."""
        try:
            result = await handler.monitor_upload_progress(params, page)
            
            if result.success:
                progress_info = f"Upload monitoring completed successfully"
                if result.progress_snapshots:
                    final_progress = result.progress_snapshots[-1]
                    progress_info += f" - Final progress: {final_progress.progress_percentage}% ({final_progress.status})"
                
                return ActionResult(
                    extracted_content=progress_info,
                    include_in_memory=True,
                    long_term_memory=f"Upload progress monitored: {len(result.progress_snapshots)} snapshots"
                )
            else:
                error_msg = f"Upload monitoring failed: {result.error or 'Monitoring timeout'}"
                return ActionResult(
                    error=error_msg,
                    include_in_memory=True
                )
                
        except Exception as e:
            error_msg = f"Monitoring system error: {str(e)}"
            logger.error(error_msg)
            return ActionResult(error=error_msg, include_in_memory=True)

