"""
Advanced iframe manager for Browser-Use extension system.
Handles iframe detection, context switching, and nested iframe operations.
"""

import time
import logging
import asyncio
import fnmatch
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlparse

from playwright.async_api import Page, <PERSON><PERSON>, <PERSON>ement<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as Play<PERSON><PERSON><PERSON><PERSON>
from pydantic import BaseModel

from .models.iframe_models import (
    DetectIframeAction, SwitchIframeContextAction, ExecuteInIframeAction,
    MonitorIframeAction, ExtractIframeContentAction,
    IframeInfo, IframeDetectionResponse, IframeContextResponse,
    IframeExecutionResponse, IframeMonitoringResponse, IframeContentResponse
)

logger = logging.getLogger(__name__)


class AdvancedIframeManager:
    """
    Advanced iframe manager with comprehensive iframe handling capabilities.
    
    Features:
    - Automatic iframe detection with cross-origin support
    - Smart context switching with load validation
    - Nested iframe navigation and operations
    - Content extraction with formatting
    - Event monitoring and change detection
    - Ad iframe filtering and optimization
    """
    
    def __init__(self):
        self.logger = logger
        self._iframe_cache: Dict[str, IframeInfo] = {}
        self._context_stack: List[str] = []  # Track context switches
        self._monitoring_tasks: List[asyncio.Task] = []
        
        # Initialize ad domain patterns
        self._ad_domains = {
            'doubleclick.net', 'googleadservices.com', 'googlesyndication.com',
            'facebook.com/tr', 'google-analytics.com', 'googletagmanager.com',
            'adsystem.amazon.com', 'adroll.com', 'outbrain.com', 'taboola.com'
        }
    
    def _init_detection_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for iframe detection and classification."""
        return {
            'content_iframes': [
                'iframe[src*="embed"]',
                'iframe[src*="widget"]', 
                'iframe[src*="content"]',
                'iframe[name*="content"]'
            ],
            'payment_iframes': [
                'iframe[src*="payment"]',
                'iframe[src*="checkout"]',
                'iframe[src*="stripe"]',
                'iframe[src*="paypal"]'
            ],
            'media_iframes': [
                'iframe[src*="youtube"]',
                'iframe[src*="vimeo"]',
                'iframe[src*="video"]',
                'iframe[src*="player"]'
            ],
            'form_iframes': [
                'iframe[src*="form"]',
                'iframe[src*="survey"]',
                'iframe[src*="feedback"]'
            ]
        }

    async def detect_iframes(self, params: DetectIframeAction, page: Page) -> IframeDetectionResponse:
        """Detect and analyze all iframes on the page with advanced filtering."""
        start_time = time.time()
        detected_iframes = []
        warnings = []
        
        try:
            self.logger.info(f"🔍 Starting iframe detection with cross-origin: {params.include_cross_origin}")
            
            # Get all iframe elements from the page
            iframe_elements = await page.query_selector_all('iframe')
            
            cross_origin_count = 0
            nested_count = 0
            accessible_count = 0
            
            for i, iframe_element in enumerate(iframe_elements):
                try:
                    iframe_info = await self._analyze_iframe_element(
                        page, iframe_element, i, params
                    )
                    
                    if iframe_info:
                        # Apply filtering
                        if self._should_include_iframe(iframe_info, params):
                            detected_iframes.append(iframe_info)
                            
                            if iframe_info.is_cross_origin:
                                cross_origin_count += 1
                            if iframe_info.is_nested:
                                nested_count += 1
                            if iframe_info.access_allowed:
                                accessible_count += 1
                                
                        else:
                            warnings.append(f"Iframe {iframe_info.selector} filtered out")
                            
                except Exception as e:
                    warnings.append(f"Error analyzing iframe {i}: {str(e)}")
                    continue
            
            # Detect nested iframes if requested
            if params.detect_nested:
                nested_iframes = await self._detect_nested_iframes(page, params)
                for nested in nested_iframes:
                    if nested not in detected_iframes:
                        detected_iframes.append(nested)
                        nested_count += 1
            
            detection_time = (time.time() - start_time) * 1000
            
            self.logger.info(f"✅ Iframe detection completed: {len(detected_iframes)} iframes found in {detection_time:.1f}ms")
            
            return IframeDetectionResponse(
                iframes_detected=len(detected_iframes) > 0,
                iframe_count=len(detected_iframes),
                iframe_info=detected_iframes,
                cross_origin_count=cross_origin_count,
                nested_count=nested_count,
                accessible_count=accessible_count,
                detection_time=detection_time,
                page_url=page.url,
                warnings=warnings
            )
            
        except Exception as e:
            detection_time = (time.time() - start_time) * 1000
            self.logger.error(f"❌ Iframe detection failed: {e}")
            
            return IframeDetectionResponse(
                iframes_detected=False,
                iframe_count=0,
                iframe_info=[],
                cross_origin_count=0,
                nested_count=0,
                accessible_count=0,
                detection_time=detection_time,
                page_url=page.url,
                error=str(e),
                warnings=warnings
            )

    async def switch_iframe_context(self, params: SwitchIframeContextAction, page: Page) -> IframeContextResponse:
        """Switch to iframe context with comprehensive validation."""
        start_time = time.time()
        warnings = []
        previous_context = None
        
        try:
            self.logger.info(f"🔄 Switching to iframe context: {params.iframe_selector}")
            
            # Store current context
            if self._context_stack:
                previous_context = self._context_stack[-1]
            
            # Find and validate iframe
            iframe_element = await page.query_selector(params.iframe_selector)
            if not iframe_element:
                raise ValueError(f"Iframe not found with selector: {params.iframe_selector}")
            
            # Get iframe properties
            iframe_src = await iframe_element.get_attribute('src')
            iframe_url = iframe_src if iframe_src else "about:blank"
            
            # Wait for iframe to load if requested
            content_accessible = True
            if params.wait_for_load:
                try:
                    # Wait for iframe to be attached and loaded
                    await page.wait_for_selector(params.iframe_selector, timeout=params.load_timeout)
                    
                    # Try to access iframe content
                    frame = await iframe_element.content_frame()
                    if frame:
                        await frame.wait_for_load_state('domcontentloaded', timeout=params.load_timeout)
                        
                        if params.verify_content:
                            # Try to access frame content to verify accessibility
                            try:
                                await frame.content()
                                content_accessible = True
                            except Exception:
                                content_accessible = False
                                warnings.append("Iframe content not accessible (likely cross-origin)")
                    
                except Exception as e:
                    warnings.append(f"Iframe loading timeout or error: {e}")
                    content_accessible = False
            
            # Add to context stack
            self._context_stack.append(params.iframe_selector)
            
            load_time = (time.time() - start_time) * 1000
            
            self.logger.info(f"✅ Successfully switched to iframe context in {load_time:.1f}ms")
            
            return IframeContextResponse(
                success=True,
                iframe_selector=params.iframe_selector,
                iframe_url=iframe_url,
                content_accessible=content_accessible,
                load_time=load_time,
                context_switched=True,
                previous_context=previous_context,
                warnings=warnings
            )
            
        except Exception as e:
            load_time = (time.time() - start_time) * 1000
            self.logger.error(f"❌ Iframe context switch failed: {e}")
            
            return IframeContextResponse(
                success=False,
                iframe_selector=params.iframe_selector,
                iframe_url=None,
                content_accessible=False,
                load_time=load_time,
                context_switched=False,
                previous_context=previous_context,
                error=str(e),
                warnings=warnings
            )

    async def execute_in_iframe(self, params: ExecuteInIframeAction, page: Page) -> IframeExecutionResponse:
        """Execute actions inside iframe with automatic context management."""
        start_time = time.time()
        warnings = []
        result_data = None
        context_restored = False
        
        try:
            self.logger.info(f"⚡ Executing {params.action_type} in iframe: {params.iframe_selector}")
            
            # Find iframe and get frame reference
            iframe_element = await page.query_selector(params.iframe_selector)
            if not iframe_element:
                raise ValueError(f"Iframe not found: {params.iframe_selector}")
            
            frame = await iframe_element.content_frame()
            if not frame:
                raise ValueError(f"Cannot access frame content: {params.iframe_selector}")
            
            # Wait for iframe if requested
            if params.wait_for_iframe:
                try:
                    await frame.wait_for_load_state('domcontentloaded', timeout=5000)
                except Exception as e:
                    warnings.append(f"Iframe load wait timeout: {e}")
            
            # Execute the specific action
            if params.action_type == 'click':
                if not params.target_selector:
                    raise ValueError("target_selector required for click action")
                element = await frame.query_selector(params.target_selector)
                if element:
                    await element.click()
                    result_data = {"clicked": True, "selector": params.target_selector}
                else:
                    raise ValueError(f"Element not found: {params.target_selector}")
                    
            elif params.action_type == 'type_text':
                if not params.target_selector or 'text' not in params.action_params:
                    raise ValueError("target_selector and text required for type_text action")
                element = await frame.query_selector(params.target_selector)
                if element:
                    await element.fill(params.action_params['text'])
                    result_data = {"text_typed": params.action_params['text']}
                else:
                    raise ValueError(f"Element not found: {params.target_selector}")
                    
            elif params.action_type == 'extract_text':
                if params.target_selector:
                    element = await frame.query_selector(params.target_selector)
                    if element:
                        text = await element.text_content()
                        result_data = {"extracted_text": text}
                    else:
                        raise ValueError(f"Element not found: {params.target_selector}")
                else:
                    # Extract all text from iframe
                    text = await frame.text_content('body')
                    result_data = {"extracted_text": text}
                    
            elif params.action_type == 'get_elements':
                selector = params.target_selector or '*'
                elements = await frame.query_selector_all(selector)
                element_info = []
                for el in elements:
                    tag_name = await el.evaluate('el => el.tagName.toLowerCase()')
                    element_info.append({"tag": tag_name})
                result_data = {"elements": element_info, "count": len(elements)}
                
            elif params.action_type == 'wait_for_element':
                if not params.target_selector:
                    raise ValueError("target_selector required for wait_for_element action")
                timeout = params.action_params.get('timeout', 10000)
                await frame.wait_for_selector(params.target_selector, timeout=timeout)
                result_data = {"element_appeared": True}
                
            elif params.action_type == 'take_screenshot':
                # Take screenshot of the iframe
                screenshot_path = f"iframe_screenshot_{int(time.time())}.png"
                await iframe_element.screenshot(path=screenshot_path)
                result_data = {"screenshot_path": screenshot_path}
                
            elif params.action_type == 'execute_script':
                if 'script' not in params.action_params:
                    raise ValueError("script required for execute_script action")
                script_result = await frame.evaluate(params.action_params['script'])
                result_data = {"script_result": script_result}
                
            else:
                raise ValueError(f"Unsupported action type: {params.action_type}")
            
            # Restore context if requested
            if params.switch_back and self._context_stack:
                self._context_stack.pop()
                context_restored = True
            
            execution_time = (time.time() - start_time) * 1000
            
            self.logger.info(f"✅ Iframe action {params.action_type} completed in {execution_time:.1f}ms")
            
            return IframeExecutionResponse(
                success=True,
                action_type=params.action_type,
                iframe_selector=params.iframe_selector,
                execution_time=execution_time,
                result_data=result_data,
                context_restored=context_restored,
                warnings=warnings
            )
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self.logger.error(f"❌ Iframe action execution failed: {e}")
            
            return IframeExecutionResponse(
                success=False,
                action_type=params.action_type,
                iframe_selector=params.iframe_selector,
                execution_time=execution_time,
                result_data=None,
                context_restored=context_restored,
                error=str(e),
                warnings=warnings
            )

    async def monitor_iframes(self, params: MonitorIframeAction, page: Page) -> IframeMonitoringResponse:
        """Monitor iframe events and content changes."""
        start_time = time.time()
        events_detected = []
        content_changes = []
        warnings = []
        
        try:
            self.logger.info(f"👁️ Starting iframe monitoring for {params.monitor_duration}ms")
            
            # Setup event listeners
            if params.iframe_selector:
                # Monitor specific iframe
                iframe_element = await page.query_selector(params.iframe_selector)
                if iframe_element:
                    iframes_to_monitor = [iframe_element]
                else:
                    raise ValueError(f"Iframe not found: {params.iframe_selector}")
            else:
                # Monitor all iframes
                iframes_to_monitor = await page.query_selector_all('iframe')
            
            monitoring_start = time.time()
            end_time = monitoring_start + (params.monitor_duration / 1000)
            
            # Store initial content for change detection
            initial_content = {}
            if params.content_change_detection:
                for i, iframe in enumerate(iframes_to_monitor):
                    try:
                        frame = await iframe.content_frame()
                        if frame:
                            content = await frame.content()
                            initial_content[i] = content
                    except Exception:
                        pass  # Skip inaccessible iframes
            
            # Monitoring loop
            while time.time() < end_time:
                # Check for content changes
                if params.content_change_detection:
                    for i, iframe in enumerate(iframes_to_monitor):
                        try:
                            frame = await iframe.content_frame()
                            if frame:
                                current_content = await frame.content()
                                if i in initial_content and initial_content[i] != current_content:
                                    content_changes.append({
                                        "iframe_index": i,
                                        "timestamp": time.time(),
                                        "change_type": "content_modified"
                                    })
                                    initial_content[i] = current_content
                        except Exception:
                            pass
                
                # Simulate event detection (would be replaced with actual event listeners)
                # In a real implementation, you'd set up proper event handlers
                
                await asyncio.sleep(params.check_interval / 1000)
            
            total_duration = (time.time() - start_time) * 1000
            
            self.logger.info(f"✅ Iframe monitoring completed: {len(events_detected)} events, {len(content_changes)} changes")
            
            return IframeMonitoringResponse(
                monitoring_completed=True,
                events_detected=events_detected,
                content_changes=content_changes,
                total_events=len(events_detected),
                monitoring_duration=total_duration,
                iframes_monitored=len(iframes_to_monitor),
                warnings=warnings
            )
            
        except Exception as e:
            total_duration = (time.time() - start_time) * 1000
            self.logger.error(f"❌ Iframe monitoring failed: {e}")
            
            return IframeMonitoringResponse(
                monitoring_completed=False,
                events_detected=[],
                content_changes=[],
                total_events=0,
                monitoring_duration=total_duration,
                iframes_monitored=0,
                error=str(e),
                warnings=warnings
            )

    async def extract_iframe_content(self, params: ExtractIframeContentAction, page: Page) -> IframeContentResponse:
        """Extract and format content from iframes with nested support."""
        start_time = time.time()
        content_data = {}
        warnings = []
        cross_origin_count = 0
        
        try:
            self.logger.info(f"📄 Extracting iframe content with type: {params.content_type}")
            
            # Get target iframes
            if params.iframe_selector:
                iframe_elements = await page.query_selector_all(params.iframe_selector)
            else:
                iframe_elements = await page.query_selector_all('iframe')
            
            iframes_processed = 0
            total_content_size = 0
            nested_levels_processed = 0
            
            for i, iframe_element in enumerate(iframe_elements):
                try:
                    # Check if iframe should be excluded
                    is_visible = await iframe_element.is_visible()
                    if params.exclude_hidden and not is_visible:
                        continue
                    
                    # Extract content based on type
                    iframe_content = await self._extract_iframe_content_by_type(
                        iframe_element, params, 0
                    )
                    
                    if iframe_content:
                        iframe_key = f"iframe_{i}"
                        content_data[iframe_key] = iframe_content
                        
                        # Calculate content size
                        if isinstance(iframe_content, str):
                            total_content_size += len(iframe_content)
                        elif isinstance(iframe_content, dict) and 'text' in iframe_content:
                            total_content_size += len(iframe_content['text'])
                        
                        iframes_processed += 1
                        
                        # Track nesting level
                        if iframe_content.get('nesting_level', 0) > nested_levels_processed:
                            nested_levels_processed = iframe_content.get('nesting_level', 0)
                    
                except Exception as e:
                    warnings.append(f"Error extracting from iframe {i}: {str(e)}")
                    cross_origin_count += 1
                    continue
            
            extraction_time = (time.time() - start_time) * 1000
            
            # Generate content summary
            content_summary = f"Extracted content from {iframes_processed} iframes, {total_content_size} characters total"
            
            self.logger.info(f"✅ Content extraction completed in {extraction_time:.1f}ms")
            
            return IframeContentResponse(
                extraction_successful=iframes_processed > 0,
                content_data=content_data,
                iframes_processed=iframes_processed,
                total_content_size=total_content_size,
                nested_levels_processed=nested_levels_processed,
                cross_origin_iframes=cross_origin_count,
                extraction_time=extraction_time,
                content_summary=content_summary,
                warnings=warnings
            )
            
        except Exception as e:
            extraction_time = (time.time() - start_time) * 1000
            self.logger.error(f"❌ Content extraction failed: {e}")
            
            return IframeContentResponse(
                extraction_successful=False,
                content_data={},
                iframes_processed=0,
                total_content_size=0,
                nested_levels_processed=0,
                cross_origin_iframes=0,
                extraction_time=extraction_time,
                error=str(e),
                warnings=warnings
            )

    # Helper methods
    
    async def _analyze_iframe_element(
        self, page: Page, iframe_element: ElementHandle, index: int, params: DetectIframeAction
    ) -> Optional[IframeInfo]:
        """Analyze individual iframe element and extract metadata."""
        try:
            # Basic attributes
            src = await iframe_element.get_attribute('src') or ""
            iframe_id = await iframe_element.get_attribute('id')
            name = await iframe_element.get_attribute('name')
            title = await iframe_element.get_attribute('title')
            width = await iframe_element.get_attribute('width')
            height = await iframe_element.get_attribute('height')
            
            # Generate selector
            if iframe_id:
                selector = f"iframe#{iframe_id}"
            elif name:
                selector = f"iframe[name='{name}']"
            else:
                selector = f"iframe:nth-of-type({index + 1})"
            
            # Check visibility
            is_visible = await iframe_element.is_visible()
            
            # Determine if cross-origin
            is_cross_origin = self._is_cross_origin(src, page.url)
            
            # Check content accessibility
            access_allowed = True
            load_state = "unknown"
            content_type = None
            
            try:
                frame = await iframe_element.content_frame()
                if frame:
                    await frame.wait_for_load_state('domcontentloaded', timeout=2000)
                    load_state = "loaded"
                    
                    # Try to access content to verify accessibility
                    try:
                        content = await frame.content()
                        access_allowed = True
                        
                        # Detect content type
                        if 'payment' in src.lower() or 'checkout' in src.lower():
                            content_type = "payment"
                        elif 'video' in src.lower() or 'youtube' in src.lower():
                            content_type = "media"
                        elif 'form' in src.lower():
                            content_type = "form"
                        else:
                            content_type = "content"
                            
                    except Exception:
                        access_allowed = False
                        
            except Exception:
                access_allowed = False
                load_state = "failed"
            
            return IframeInfo(
                selector=selector,
                src=src,
                id=iframe_id,
                name=name,
                title=title,
                width=width,
                height=height,
                is_visible=is_visible,
                is_cross_origin=is_cross_origin,
                is_nested=False,  # Will be updated if nested
                nesting_level=0,
                access_allowed=access_allowed,
                load_state=load_state,
                content_type=content_type,
                metadata={"index": index}
            )
            
        except Exception as e:
            self.logger.debug(f"Error analyzing iframe {index}: {e}")
            return None

    async def _detect_nested_iframes(self, page: Page, params: DetectIframeAction) -> List[IframeInfo]:
        """Detect iframes nested within other iframes."""
        nested_iframes = []
        
        try:
            # Get all frames
            frames = page.frames
            
            for frame in frames:
                if frame.url != page.url:  # Skip main frame
                    try:
                        # Look for iframes within this frame
                        iframe_elements = await frame.query_selector_all('iframe')
                        
                        for i, iframe_element in enumerate(iframe_elements):
                            iframe_info = await self._analyze_iframe_element(
                                page, iframe_element, i, params
                            )
                            if iframe_info:
                                iframe_info.is_nested = True
                                iframe_info.nesting_level = 1  # Could be improved to detect deeper nesting
                                iframe_info.parent_iframe = frame.url
                                nested_iframes.append(iframe_info)
                                
                    except Exception as e:
                        self.logger.debug(f"Error checking nested iframes in frame {frame.url}: {e}")
                        continue
                        
        except Exception as e:
            self.logger.debug(f"Error detecting nested iframes: {e}")
        
        return nested_iframes

    def _should_include_iframe(self, iframe_info: IframeInfo, params: DetectIframeAction) -> bool:
        """Determine if iframe should be included based on filtering parameters."""
        
        # Filter by visibility
        if not params.include_hidden and not iframe_info.is_visible:
            return False
        
        # Filter by cross-origin
        if not params.include_cross_origin and iframe_info.is_cross_origin:
            return False
        
        # Filter by domain
        if params.filter_by_domain and iframe_info.src:
            domain_match = False
            iframe_domain = urlparse(iframe_info.src).netloc
            
            for pattern in params.filter_by_domain:
                if fnmatch.fnmatch(iframe_domain, pattern):
                    domain_match = True
                    break
            
            if not domain_match:
                return False
        
        # Filter ads
        if params.exclude_ads and iframe_info.src:
            iframe_domain = urlparse(iframe_info.src).netloc
            if any(ad_domain in iframe_domain for ad_domain in self._ad_domains):
                return False
        
        return True

    def _is_cross_origin(self, iframe_src: str, page_url: str) -> bool:
        """Check if iframe is cross-origin."""
        if not iframe_src or iframe_src.startswith('data:') or iframe_src.startswith('about:'):
            return False
        
        try:
            iframe_domain = urlparse(iframe_src).netloc
            page_domain = urlparse(page_url).netloc
            return iframe_domain != page_domain
        except Exception:
            return False

    async def _extract_iframe_content_by_type(
        self, iframe_element: ElementHandle, params: ExtractIframeContentAction, depth: int
    ) -> Optional[Dict[str, Any]]:
        """Extract content from iframe based on specified content type."""
        if depth > params.max_depth:
            return None
        
        try:
            frame = await iframe_element.content_frame()
            if not frame:
                return None
            
            content_result = {
                "nesting_level": depth,
                "url": frame.url
            }
            
            if params.content_type == 'text':
                text = await frame.text_content('body')
                content_result["text"] = text
                
            elif params.content_type == 'html':
                html = await frame.content()
                content_result["html"] = html
                
            elif params.content_type == 'structured':
                # Extract structured information
                title = await frame.title()
                text = await frame.text_content('body')
                
                # Get form elements
                forms = await frame.query_selector_all('form')
                form_count = len(forms)
                
                # Get links
                links = await frame.query_selector_all('a[href]')
                link_count = len(links)
                
                content_result.update({
                    "title": title,
                    "text": text[:1000] if text else "",  # Limit text length
                    "forms_count": form_count,
                    "links_count": link_count,
                    "content_length": len(text) if text else 0
                })
            
            # Process nested iframes if requested
            if params.include_nested and depth < params.max_depth:
                nested_iframes = await frame.query_selector_all('iframe')
                if nested_iframes:
                    content_result["nested_iframes"] = []
                    for nested_iframe in nested_iframes:
                        nested_content = await self._extract_iframe_content_by_type(
                            nested_iframe, params, depth + 1
                        )
                        if nested_content:
                            content_result["nested_iframes"].append(nested_content)
            
            return content_result
            
        except Exception as e:
            return {"error": str(e), "nesting_level": depth}


def register_iframe_actions(controller):
    """Register iframe management actions with the controller."""
    
    handler = AdvancedIframeManager()
    
    @controller.registry.action(
        'Detect and analyze iframes on the current page',
        param_model=DetectIframeAction
    )
    async def detect_iframes(params: DetectIframeAction, page):
        """Detect and analyze all iframes with advanced filtering options."""
        try:
            return await handler.detect_iframes(params, page)
        except Exception as e:
            logger.error(f"Iframe detection action failed: {e}")
            return IframeDetectionResponse(
                iframes_detected=False,
                iframe_count=0,
                iframe_info=[],
                cross_origin_count=0,
                nested_count=0,
                accessible_count=0,
                detection_time=0,
                page_url=page.url,
                error=str(e)
            )
    
    @controller.registry.action(
        'Switch to iframe context for subsequent operations',
        param_model=SwitchIframeContextAction
    )
    async def switch_iframe_context(params: SwitchIframeContextAction, page):
        """Switch the current context to a specific iframe."""
        try:
            return await handler.switch_iframe_context(params, page)
        except Exception as e:
            logger.error(f"Iframe context switch action failed: {e}")
            return IframeContextResponse(
                success=False,
                iframe_selector=params.iframe_selector,
                iframe_url=None,
                content_accessible=False,
                load_time=0,
                context_switched=False,
                error=str(e)
            )
    
    @controller.registry.action(
        'Execute actions inside iframe with automatic context management',
        param_model=ExecuteInIframeAction
    )
    async def execute_in_iframe(params: ExecuteInIframeAction, page):
        """Execute specific actions inside an iframe."""
        try:
            return await handler.execute_in_iframe(params, page)
        except Exception as e:
            logger.error(f"Iframe execution action failed: {e}")
            return IframeExecutionResponse(
                success=False,
                action_type=params.action_type,
                iframe_selector=params.iframe_selector,
                execution_time=0,
                result_data=None,
                context_restored=False,
                error=str(e)
            )
    
    @controller.registry.action(
        'Monitor iframe events and content changes',
        param_model=MonitorIframeAction
    )
    async def monitor_iframes(params: MonitorIframeAction, page):
        """Monitor iframe events and detect content changes."""
        try:
            return await handler.monitor_iframes(params, page)
        except Exception as e:
            logger.error(f"Iframe monitoring action failed: {e}")
            return IframeMonitoringResponse(
                monitoring_completed=False,
                events_detected=[],
                content_changes=[],
                total_events=0,
                monitoring_duration=0,
                iframes_monitored=0,
                error=str(e)
            )
    
    @controller.registry.action(
        'Extract and format content from iframes',
        param_model=ExtractIframeContentAction
    )
    async def extract_iframe_content(params: ExtractIframeContentAction, page):
        """Extract content from iframes with nested support."""
        try:
            return await handler.extract_iframe_content(params, page)
        except Exception as e:
            logger.error(f"Iframe content extraction action failed: {e}")
            return IframeContentResponse(
                extraction_successful=False,
                content_data={},
                iframes_processed=0,
                total_content_size=0,
                nested_levels_processed=0,
                cross_origin_iframes=0,
                extraction_time=0,
                error=str(e)
            )
    
    logger.info("🖼️ Advanced iframe management actions registered successfully") 