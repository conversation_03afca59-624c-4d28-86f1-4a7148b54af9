from dataclasses import dataclass, field
from typing import Any, List, Dict

from pydantic import BaseModel

from browser_use.dom.history_tree_processor.service import DOMHistoryElement
from browser_use.dom.views import DOMState, DOMElementNode, DOMTextNode

# Utility for cached properties
from functools import cached_property


# Pydantic
class TabInfo(BaseModel):
	"""Represents information about a browser tab"""

	page_id: int
	url: str
	title: str
	parent_page_id: int | None = None  # parent page that contains this popup or cross-origin iframe


@dataclass
class BrowserStateSummary(DOMState):
	"""The summary of the browser's current state designed for an LLM to process"""

	# provided by DOMState:
	# element_tree: DOMElementNode
	# selector_map: SelectorMap

	url: str
	title: str
	tabs: list[TabInfo]
	screenshot: str | None = field(default=None, repr=False)
	pixels_above: int = 0
	pixels_below: int = 0
	browser_errors: list[str] = field(default_factory=list)

	# ------------------------------------------------------------------
	# Dynamically derived convenience properties
	# ------------------------------------------------------------------

	@cached_property
	def all_input_elements(self) -> List[Dict[str, str]]:  # pragma: no cover
		"""Return a flat list with basic details of all <input>, <textarea>, and <select> elements in the DOM.
		
		The structure is intentionally lightweight (plain dictionaries) so that it can
		be serialised easily for LLM consumption without introducing heavy class
		instances. Each dictionary contains the element's ``tag_name`` and a copy of
		its HTML attributes (e.g. ``type``, ``placeholder``).
		"""

		inputs: List[Dict[str, str]] = []

		def _traverse(node: DOMElementNode) -> None:  # type: ignore[valid-type]
			# Only DOMElementNode instances can have children; DOMTextNode will be skipped
			if isinstance(node, DOMElementNode):
				if node.tag_name.lower() in {"input", "textarea", "select"}:
					element_data: Dict[str, str] = {"tag_name": node.tag_name}
					element_data.update(node.attributes)
					inputs.append(element_data)

				for child in node.children:
					if isinstance(child, DOMElementNode):
						_traverse(child)

		# Kick-off traversal from root element
		_traverse(self.element_tree)
		return inputs

	@cached_property
	def all_clickable_elements(self) -> List[Dict[str, str]]:  # pragma: no cover
		"""Return a flat list of elements considered *clickable* based on ``highlight_index`` or ``is_interactive`` flag."""

		clickables: List[Dict[str, str]] = []

		def _traverse(node: DOMElementNode) -> None:  # type: ignore[valid-type]
			if isinstance(node, DOMElementNode):
				is_clickable = bool(node.highlight_index is not None or node.is_interactive)
				if is_clickable:
					element_data: Dict[str, str] = {"tag_name": node.tag_name}
					element_data.update(node.attributes)
					# Try to capture some visible text for heuristic checks
					try:
						from browser_use.utils import safe_call  # hypothetical util, ignore if missing
					except Exception:
						pass
					text_content = ""
					try:
						text_content = node.get_all_text_till_next_clickable_element(max_depth=1)
					except Exception:
						text_content = ""
					element_data["text"] = text_content
					clickables.append(element_data)

				for child in node.children:
					if isinstance(child, DOMElementNode):
						_traverse(child)

		_traverse(self.element_tree)
		return clickables

	@cached_property
	def page_text(self) -> str:  # pragma: no cover
		"""Return the concatenated text content of all visible text nodes in the DOM."""

		texts: List[str] = []

		def _traverse(node: DOMElementNode | DOMTextNode) -> None:  # type: ignore[valid-type]
			if isinstance(node, DOMTextNode):
				texts.append(node.text)
			elif isinstance(node, DOMElementNode):
				for child in node.children:
					_traverse(child)  # recurse

		_traverse(self.element_tree)
		return " ".join(texts).strip()


@dataclass
class BrowserStateHistory:
	"""The summary of the browser's state at a past point in time to usse in LLM message history"""

	url: str
	title: str
	tabs: list[TabInfo]
	interacted_element: list[DOMHistoryElement | None] | list[None]
	screenshot: str | None = None

	def to_dict(self) -> dict[str, Any]:
		data = {}
		data['tabs'] = [tab.model_dump() for tab in self.tabs]
		data['screenshot'] = self.screenshot
		data['interacted_element'] = [el.to_dict() if el else None for el in self.interacted_element]
		data['url'] = self.url
		data['title'] = self.title
		return data


class BrowserError(Exception):
	"""Base class for all browser errors"""


class URLNotAllowedError(BrowserError):
	"""Error raised when a URL is not allowed"""
