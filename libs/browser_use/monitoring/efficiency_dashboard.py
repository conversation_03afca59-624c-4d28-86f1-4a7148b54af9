from __future__ import annotations

import json
import statistics
from collections import defaultdict
from typing import Dict, Any, List


class EfficiencyDashboard:
    """Dashboard para monitorear y mejorar la eficiencia del agente."""

    def __init__(self, metrics_file_path: str):
        self.metrics_file_path = metrics_file_path

    def _load_metrics(self) -> List[Dict[str, Any]]:
        """Carga las métricas desde el archivo JSON."""
        try:
            with open(self.metrics_file_path, "r") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []

    def generate_efficiency_report(self) -> Dict[str, Any]:
        """Genera un reporte de eficiencia a partir de las métricas guardadas."""
        metrics = self._load_metrics()
        if not metrics:
            return {"error": "No metrics found or file is invalid."}

        tasks = defaultdict(list)
        for m in metrics:
            tasks[m["task_type"]].append(m)

        report = {
            "total_tasks_tracked": len(tasks),
            "total_steps_recorded": len(metrics),
            "overall_success_rate": self._calculate_success_rate(metrics),
            "tasks": {},
        }

        for task_name, task_metrics in tasks.items():
            successful_metrics = [m for m in task_metrics if m.get("success")]
            
            if not successful_metrics:
                 avg_steps = "N/A"
                 avg_time = "N/A"
            else:
                avg_steps = statistics.mean([m["steps_taken"] for m in successful_metrics])
                avg_time = statistics.mean([m["time_elapsed"] for m in successful_metrics])


            report["tasks"][task_name] = {
                "number_of_runs": len(task_metrics),
                "success_rate": self._calculate_success_rate(task_metrics),
                "average_steps_on_success": avg_steps,
                "average_time_on_success_seconds": avg_time,
            }

        return report

    def _calculate_success_rate(self, metrics: List[Dict[str, Any]]) -> float:
        """Calcula la tasa de éxito para una lista de métricas."""
        if not metrics:
            return 0.0
        successful_runs = sum(1 for m in metrics if m.get("success"))
        return (successful_runs / len(metrics)) * 100 