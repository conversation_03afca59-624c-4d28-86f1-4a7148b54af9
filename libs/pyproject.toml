[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "browser-use"
description = "Make websites accessible for AI agents"
version = "1.0.0"
readme = "README.md"
requires-python = ">=3.11,<4.0"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "aiofiles>=24.1.0",
    "anyio>=4.9.0",
    "bubus>=1.1.2",
    "google-api-core>=2.25.0",
    "httpx>=0.28.1",
    "langchain-anthropic==0.3.15",
    "langchain-core==0.3.64",
    "langchain-deepseek>=0.1.3",
    "langchain-google-genai==2.1.5",
    "langchain-ollama==0.3.3",
    "langchain-openai==0.3.21",
    "langchain>=0.3.25",
    "logfire>=0.6.0",
    "markdownify==1.1.0",
    "mem0ai>=0.1.106",
    "patchright>=1.52.5",
    "playwright>=1.52.0",
    "psutil>=7.0.0",
    "pydantic>=2.11.5",
    "pyobjc>=11.0; platform_system == 'darwin'",
    "pyperclip>=1.9.0",
    "python-dotenv>=1.0.1",
    "requests>=2.32.3",
    "screeninfo>=0.8.1; platform_system != 'darwin'",
    "typing-extensions>=4.12.2",
    "uuid7>=0.1.0",
    "authlib>=1.6.0",
    "opencv-python>=*********",
    "numpy>=1.26.4",
]
# google-api-core: only used for Google LLM APIs
# pyperclip: only used for examples that use copy/paste
# pyobjc: only used to get screen resolution on macOS
# screeninfo: only used to get screen resolution on Linux/Windows
# markdownify: used for page text content extraction for passing to LLM
# openai: datalib,voice-helpers are actually NOT NEEDED but openai produces noisy errors on exit without them TODO: fix
# rich: used for terminal formatting and styling in CLI
# click: used for command-line argument parsing
# textual: used for terminal UI

[project.optional-dependencies]
memory = [
    # sentence-transformers: depends on pytorch, which does not support python 3.13 yet
    "faiss-cpu>=1.11.0",
    "sentence-transformers>=4.0.2",
]
storage = [
    "redis>=5.0.0",
    "requests>=2.25.0",
]
cli = [
    "rich>=14.0.0",
    "click>=8.1.8",
    "textual>=3.2.0",
]
examples = [
    # botocore: only needed for Bedrock Claude boto3 examples/models/bedrock_claude.py
    "botocore>=1.37.23",
    # "langchain-aws>=0.2.24",  # depends on version of numpy that doesnt have python 3.12 wheels yet, breaks CI
    "imgcat>=0.6.0",
    "stagehand-py>=0.3.6",
    "browserbase>=0.4.0",
]
eval = [
    "lmnr[all]>=0.6.11",
]
all = [
    "browser-use[memory,storage,cli,examples]",
]

# will prefer to use local source code checked out in ../../browser-use (if present) instead of pypi browser-use package
# [tool.uv.sources]
# bubus = { path = "../bubus", editable = true }


[project.urls]
Repository = "https://github.com/browser-use/browser-use"

[project.scripts]
browseruse = "browser_use.cli:main"
browser-use = "browser_use.cli:main"

[tool.codespell]
ignore-words-list = "bu,wit,dont,cant,wont,re-use,re-used,re-using,re-usable,thats,doesnt,doubleclick"
skip = "*.json"

[tool.ruff]
line-length = 130
fix = true

[tool.ruff.lint]
select = ["ASYNC", "E", "F", "FAST", "I", "PLE"]
ignore = ["ASYNC109", "E101", "E402", "E501", "F841", "E731"]  # TODO: determine if adding timeouts to all the unbounded async functions is needed / worth-it so we can un-ignore ASYNC109
unfixable = ["E101", "E402", "E501", "F841", "E731"]

[tool.ruff.format]
quote-style = "single"
indent-style = "tab"
line-ending = "lf"
docstring-code-format = true
docstring-code-line-length = 140
skip-magic-trailing-comma = false

[tool.pyright]
typeCheckingMode = "basic"
exclude = ["tests/old/", ".venv/", ".git/", "__pycache__/"]


[tool.hatch.build]
include = [
    "browser_use/**/*.py",
    "!browser_use/**/tests/*.py",
    "!browser_use/**/tests.py",
    "browser_use/agent/system_prompt.md",
    "browser_use/dom/buildDomTree.js",
    "!tests/**/*.py",
]

[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "session"
asyncio_default_test_loop_scope = "session"
markers = [
    "slow: marks tests as slow (deselect with `-m 'not slow'`)",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "asyncio: mark tests as async tests",
]
testpaths = [
    "tests"
]
python_files = ["test_*.py", "*_test.py"]
addopts = "-v --strict-markers --tb=short"
log_cli = true
log_cli_format = "%(levelname)-8s [%(name)s] %(message)s"
filterwarnings = [
    "ignore::pytest.PytestDeprecationWarning",
    "ignore::DeprecationWarning",
]
log_level = "DEBUG"


[tool.hatch.metadata]
allow-direct-references = true

[tool.uv]
required-environments = [
    "sys_platform == 'darwin' and platform_machine == 'arm64'",
    "sys_platform == 'darwin' and platform_machine == 'x86_64'",
    "sys_platform == 'linux' and platform_machine == 'x86_64'",
    "sys_platform == 'linux' and platform_machine == 'aarch64'",
    # "sys_platform == 'linux' and platform_machine == 'arm64'",  # no pytorch wheels available yet
    "sys_platform == 'win32' and platform_machine == 'x86_64'",
    # "sys_platform == 'win32' and platform_machine == 'arm64'",  # no pytorch wheels available yet
]
dev-dependencies = [
    "ruff>=0.11.2",
    "tokencost>=0.1.16",
    "build>=1.2.2",
    "pytest>=8.3.5",
    "pytest-asyncio>=1.0.0",
    "pytest-httpserver>=1.0.8",
    "fastapi>=0.115.8",
    "inngest>=0.4.19",
    "uvicorn>=0.34.0",
    "langchain-fireworks>=0.2.6",
    "ipdb>=0.13.13",
    "pre-commit>=4.2.0",
    "codespell>=2.4.1",
    "pyright>=1.1.399",
    "ty>=0.0.1a1",
    "pytest-xdist>=3.7.0",
    # "pytest-playwright-asyncio>=0.7.0",  # not actually needed I think
]
