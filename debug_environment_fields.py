#!/usr/bin/env python3

"""
Debug script to analyze environment fields in execution documents
"""

import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path

# Add the parent directory to sys.path to import src modules
sys.path.insert(0, str(Path(__file__).parent))

from src.database.config import DatabaseConfig
from src.database.models.execution import Execution
from beanie import init_beanie
from motor.motor_asyncio import AsyncIOMotorClient

async def debug_environment_fields():
    """Debug environment fields in recent executions."""
    print("=== DEBUGGING ENVIRONMENT FIELDS IN EXECUTIONS ===")
    
    # Initialize database connection
    config = DatabaseConfig()
    client = AsyncIOMotorClient(config.connection_string)
    
    await init_beanie(
        database=getattr(client, config.database_name),
        document_models=[Execution]
    )
    
    print(f"✅ Connected to database: {config.database_name}")
    
    # Find recent executions
    recent_executions = await Execution.find().sort([("started_at", -1)]).limit(10).to_list()
    
    print(f"\n📊 Found {len(recent_executions)} recent executions:")
    
    for i, execution in enumerate(recent_executions, 1):
        print(f"\n--- Execution {i} ---")
        print(f"Execution ID: {execution.execution_id}")
        print(f"Test Type: {execution.test_type}")
        print(f"Started At: {execution.started_at}")
        print(f"Project ID: {execution.project_id}")
        print(f"Suite ID: {execution.suite_id}")
        print(f"Test ID: {execution.test_id}")
        
        # Environment fields
        print(f"\n🌍 Environment Information:")
        print(f"  environment_id: {execution.environment_id}")
        print(f"  environment_name: {execution.environment_name}")
        print(f"  full_url: {execution.full_url}")
        print(f"  environment_config: {execution.environment_config}")
        print(f"  application_version: {execution.application_version}")
        
        # Check metadata
        print(f"\n📋 Metadata Environment Info:")
        if execution.metadata:
            print(f"  metadata.environment_id: {execution.metadata.get('environment_id', 'NOT_FOUND')}")
            print(f"  metadata.environment_name: {execution.metadata.get('environment_name', 'NOT_FOUND')}")
            print(f"  metadata.full_url: {execution.metadata.get('full_url', 'NOT_FOUND')}")
            print(f"  metadata.environment_config: {execution.metadata.get('environment_config', 'NOT_FOUND')}")
            print(f"  metadata.application_version: {execution.metadata.get('application_version', 'NOT_FOUND')}")
            print(f"  metadata keys: {list(execution.metadata.keys())}")
        else:
            print("  No metadata found")
            
        print("=" * 60)
    
    await client.close()

if __name__ == "__main__":
    asyncio.run(debug_environment_fields())
