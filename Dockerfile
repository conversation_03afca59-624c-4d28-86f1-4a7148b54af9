# Multi-stage Dockerfile for QAK Platform
# Stage 1: Build Next.js frontend
# Use Node 20 (LTS) and include minimal build tools required for native deps
FROM node:20-alpine AS frontend-builder

WORKDIR /app/web

# Accept a build argument for the API URL
ARG NEXT_PUBLIC_API_BASE_URL=/api

# Copy package files
COPY web/package*.json ./
RUN apk add --no-cache --virtual .gyp python3 make g++ \
    && npm ci --no-audit --unsafe-perm \
    && apk del .gyp

# Copy source code
COPY web/ ./

# Build Next.js app
RUN NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL} npm run build

# Stage 2: Setup Python backend
FROM python:3.11-slim AS backend-builder

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    wget \
    gnupg \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    build-essential \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir --prefer-binary -r requirements.txt

# Install Playwright and browsers
RUN playwright install --with-deps chromium

# Stage 3: Final production image
FROM python:3.11-slim AS production

WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    gnupg \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    nginx \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 20.x runtime for Next.js
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs

# Copy Python dependencies from builder stage
COPY --from=backend-builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=backend-builder /usr/local/bin /usr/local/bin
COPY --from=backend-builder /root/.cache/ms-playwright /root/.cache/ms-playwright

# Copy built Next.js app
COPY --from=frontend-builder /app/web/.next/standalone ./web/
COPY --from=frontend-builder /app/web/.next/static ./web/.next/static
# Note: No public directory exists in this project

# Copy backend source code
COPY . ./
RUN rm -rf web/node_modules web/.next/cache

# Create necessary directories
RUN mkdir -p /app/logs /app/monitoring_data /app/conversations /app/codegen_sessions /app/projects

# Set default environment variables
ENV LLM_MODEL=gemini-2.5-flash \
    PROMPT_LANGUAGE=es \
    QAK_API_CALL_DELAY=10 \
    BROWSER_USE_GEMINI_RPM=12 \
    BROWSER_USE_GEMINI_TPM=800000 \
    BROWSER_USE_PLANNER_INTERVAL=3 \
    BROWSER_USE_PLANNER_REASONING=true \
    BROWSER_USE_MAX_CONTEXT_TOKENS=120000 \
    BROWSER_USE_VISION_QUALITY=medium \
    BROWSER_USE_SEMANTIC_MEMORY=false \
    EMBEDDING_PROVIDER=huggingface \
    EMBEDDING_MODEL=all-MiniLM-L6-v2 \
    EMBEDDING_DIMS=384 \
    GEMINI_MAIN_MODEL=gemini-2.5-flash \
    GEMINI_PLANNER_MODEL=gemini-2.5-flash \
    GEMINI_EMBEDDING_MODEL=models/text-embedding-004 \
    BROWSER_USE_LOGFIRE=false \
    SAVE_SMOKE_TEST_CONVERSATIONS=true \
    SMOKE_TEST_CONVERSATIONS_PATH="./conversations/smoke_tests"

# Copy service configurations
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Create startup script
COPY docker/start.sh /start.sh
RUN chmod +x /start.sh

# Expose ports
EXPOSE 80 8000 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Start supervisor
CMD ["/start.sh"] 