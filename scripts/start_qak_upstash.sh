#!/bin/bash

# Start QAK System with Upstash Redis (No Local Redis needed!)
# This script starts only Celery worker and QAK API since Redis is in the cloud

set -e

echo "🚀 Starting QAK System with Upstash Redis..."

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "❌ Virtual environment not found. Please run 'python -m venv .venv' first."
    exit 1
fi

# Activate virtual environment
echo "📦 Activating virtual environment..."
source .venv/bin/activate

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create it with your Upstash configuration."
    exit 1
fi

# Load environment variables
export $(grep -v '^#' .env | xargs)

# Verify Upstash configuration
if [ -z "$UPSTASH_REDIS_REST_URL" ] || [ -z "$UPSTASH_REDIS_REST_TOKEN" ]; then
    echo "❌ Upstash Redis configuration missing in .env file."
    echo "   Required variables: UPSTASH_REDIS_REST_URL, UPSTASH_REDIS_REST_TOKEN"
    exit 1
fi

echo "✅ Upstash configuration found: $(echo $UPSTASH_REDIS_REST_URL | sed 's|https://||')"

# Test Upstash connection
echo "🧪 Testing Upstash connection..."
python3 -c "
import os
from redis import Redis
import urllib.parse

upstash_url = os.getenv('UPSTASH_REDIS_REST_URL')
upstash_token = os.getenv('UPSTASH_REDIS_REST_TOKEN')
host = urllib.parse.urlparse(upstash_url).netloc
redis_url = f'rediss://default:{upstash_token}@{host}:6380'

try:
    redis_client = Redis.from_url(redis_url, decode_responses=True)
    redis_client.ping()
    print('✅ Upstash connection successful')
except Exception as e:
    print(f'❌ Upstash connection failed: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ Cannot connect to Upstash. Please check your configuration."
    exit 1
fi

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Start Celery worker in background
echo "🔧 Starting Celery worker with Upstash..."
python celery_worker.py > celery_worker.log 2>&1 &
CELERY_PID=$!
echo "✅ Celery worker started with PID: $CELERY_PID"

# Give worker time to start
sleep 3

# Test worker
echo "🧪 Testing Celery worker..."
if python3 -c "
from src.core.background_jobs.celery_app import celery_app
try:
    inspect = celery_app.control.inspect()
    ping = inspect.ping()
    if ping:
        print('✅ Celery worker is responding')
    else:
        print('⚠️ No workers found but system is ready')
except Exception as e:
    print(f'⚠️ Worker check failed: {e}')
"; then
    echo "✅ Worker system initialized"
else
    echo "⚠️ Worker may need more time to start"
fi

# Start QAK API in background
echo "🌐 Starting QAK API server..."
python app.py > qak_api.log 2>&1 &
API_PID=$!
echo "✅ QAK API started with PID: $API_PID"

# Give API time to start
sleep 5

# Test API
if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    echo "✅ QAK API is responding"
else
    echo "⚠️ QAK API may not be fully ready yet"
fi

# Test background jobs with Upstash
echo "🧪 Testing background jobs system with Upstash..."
HEALTH_RESPONSE=$(curl -s http://localhost:8000/api/v2/background-jobs/health)
if echo "$HEALTH_RESPONSE" | grep -q "available"; then
    echo "✅ Background jobs system is ready with Upstash"
    echo "📊 Redis Status: $(echo "$HEALTH_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data['components']['redis'])" 2>/dev/null || echo 'Status check completed')"
else
    echo "⚠️ Background jobs system may not be ready"
fi

echo ""
echo "🎉 QAK System Started Successfully with Upstash!"
echo ""
echo "☁️ Using Upstash Redis - No local Redis needed!"
echo ""
echo "📊 Service Status:"
echo "   Upstash Redis: ✅ Cloud hosted ($(echo $UPSTASH_REDIS_REST_URL | sed 's|https://||'))"
echo "   Celery Worker: PID $CELERY_PID (logs: celery_worker.log)"
echo "   QAK API:      http://localhost:8000 (PID $API_PID, logs: qak_api.log)"
echo ""
echo "🔗 Quick Links:"
echo "   API Health:     curl http://localhost:8000/health"
echo "   BG Jobs Health: curl http://localhost:8000/api/v2/background-jobs/health"
echo "   API Docs:       http://localhost:8000/docs"
echo ""
echo "📝 Log Files:"
echo "   Celery Worker: tail -f celery_worker.log"
echo "   QAK API:       tail -f qak_api.log"
echo ""
echo "🛑 To stop services:"
echo "   kill $CELERY_PID $API_PID"
echo "   (No need to stop Redis - it's in the cloud!)"
echo ""

# Save PIDs for easy stopping
cat > .qak_pids << EOF
CELERY_PID=$CELERY_PID
API_PID=$API_PID
REDIS_TYPE=upstash
EOF

echo "💾 Process IDs saved to .qak_pids"
echo ""
echo "🔄 System is ready for background job processing with Upstash!"
echo "💡 Tip: Your Redis is in the cloud, so it's persistent across restarts!"