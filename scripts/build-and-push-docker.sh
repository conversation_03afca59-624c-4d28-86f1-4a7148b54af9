#!/bin/bash

# Script para construir y subir la imagen QAK Platform a Docker Hub

set -e

# Configuración
DOCKER_USERNAME="${DOCKER_USERNAME:-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}"
IMAGE_NAME="qak-platform"
VERSION="${VERSION:-latest}"
FULL_IMAGE_NAME="${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🐳 QAK Platform Docker Build & Push Script${NC}"
echo -e "======================================"

# Verificar que Docker está ejecutándose
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker no está ejecutándose. Por favor, inicia Docker Desktop.${NC}"
    exit 1
fi

echo -e "${YELLOW}📋 Información de build:${NC}"
echo -e "   Imagen: ${FULL_IMAGE_NAME}"
echo -e "   Directorio: $(pwd)"
echo -e ""

# Construir la imagen
echo -e "${BLUE}🔨 Construyendo imagen Docker...${NC}"
docker build -t "${FULL_IMAGE_NAME}" . || {
    echo -e "${RED}❌ Error al construir la imagen${NC}"
    exit 1
}

echo -e "${GREEN}✅ Imagen construida exitosamente${NC}"

# Verificar que la imagen se construyó correctamente
echo -e "${BLUE}🔍 Verificando imagen...${NC}"
docker images | grep "${IMAGE_NAME}" || {
    echo -e "${RED}❌ La imagen no se encontró después del build${NC}"
    exit 1
}

# Probar la imagen localmente (opcional)
echo -e "${YELLOW}🧪 ¿Quieres probar la imagen localmente antes de subirla? (y/N)${NC}"
read -r test_locally

if [[ $test_locally =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}🚀 Ejecutando imagen localmente en puerto 8080...${NC}"
    echo -e "${YELLOW}Presiona Ctrl+C para detener la prueba y continuar con el push${NC}"
    
    # Crear archivo .env temporal si no existe
    if [ ! -f .env ]; then
        echo "GOOGLE_API_KEY=your_api_key_here" > .env.temp
        ENV_FILE=".env.temp"
    else
        ENV_FILE=".env"
    fi
    
    docker run --rm -p 8080:80 --env-file "${ENV_FILE}" "${FULL_IMAGE_NAME}" || true
    
    # Limpiar archivo temporal
    if [ -f .env.temp ]; then
        rm .env.temp
    fi
fi

# Login a Docker Hub
echo -e "${BLUE}🔐 Iniciando sesión en Docker Hub...${NC}"
if [ -z "${DOCKER_PASSWORD}" ]; then
    echo -e "${YELLOW}Por favor, inicia sesión en Docker Hub:${NC}"
    docker login || {
        echo -e "${RED}❌ Error al iniciar sesión en Docker Hub${NC}"
        exit 1
    }
else
    echo "${DOCKER_PASSWORD}" | docker login -u "${DOCKER_USERNAME}" --password-stdin || {
        echo -e "${RED}❌ Error al iniciar sesión en Docker Hub${NC}"
        exit 1
    }
fi

# Subir la imagen
echo -e "${BLUE}☁️  Subiendo imagen a Docker Hub...${NC}"
docker push "${FULL_IMAGE_NAME}" || {
    echo -e "${RED}❌ Error al subir la imagen${NC}"
    exit 1
}

echo -e "${GREEN}✅ Imagen subida exitosamente a Docker Hub${NC}"
echo -e ""
echo -e "${GREEN}🎉 ¡Proceso completado!${NC}"
echo -e ""
echo -e "${BLUE}📖 Para usar la imagen:${NC}"
echo -e "   docker pull ${FULL_IMAGE_NAME}"
echo -e "   docker run -d -p 80:80 --env-file .env ${FULL_IMAGE_NAME}"
echo -e ""
echo -e "${BLUE}🔗 URL de Docker Hub:${NC}"
echo -e "   https://hub.docker.com/r/${DOCKER_USERNAME}/${IMAGE_NAME}"
echo -e ""

# Limpiar imágenes temporales (opcional)
echo -e "${YELLOW}¿Quieres limpiar las imágenes temporales de build? (y/N)${NC}"
read -r cleanup

if [[ $cleanup =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}🧹 Limpiando imágenes temporales...${NC}"
    docker image prune -f
    echo -e "${GREEN}✅ Limpieza completada${NC}"
fi

echo -e "${GREEN}🚀 ¡La imagen está lista para ser usada por cualquier persona!${NC}" 