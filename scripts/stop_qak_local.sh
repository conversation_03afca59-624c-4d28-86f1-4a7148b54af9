#!/bin/bash

# Stop QAK System (Local processes)

echo "🛑 Stopping QAK System..."

# Read PIDs if available
if [ -f ".qak_pids" ]; then
    source .qak_pids
    
    if [ ! -z "$CELERY_PID" ]; then
        echo "🔧 Stopping Celery worker (PID: $CELERY_PID)..."
        kill $CELERY_PID 2>/dev/null || echo "   Worker already stopped"
    fi
    
    if [ ! -z "$API_PID" ]; then
        echo "🌐 Stopping QAK API (PID: $API_PID)..."
        kill $API_PID 2>/dev/null || echo "   API already stopped"
    fi
    
    rm .qak_pids
else
    echo "⚠️ No PID file found, trying to find processes..."
    
    # Try to find and kill processes
    echo "🔧 Looking for Celery worker..."
    pkill -f "celery_worker.py" || echo "   No Celery worker found"
    
    echo "🌐 Looking for QAK API..."
    pkill -f "app.py" || echo "   No QAK API found"
fi

# Stop Redis
echo "🔴 Stopping Redis..."
redis-cli shutdown 2>/dev/null || echo "   Redis already stopped"

echo ""
echo "✅ QAK System stopped"
echo ""
echo "📊 Verification:"
echo "   Redis: $(if pgrep redis-server >/dev/null; then echo 'Still running'; else echo 'Stopped'; fi)"
echo "   Celery: $(if pgrep -f celery_worker.py >/dev/null; then echo 'Still running'; else echo 'Stopped'; fi)"
echo "   API: $(if pgrep -f app.py >/dev/null; then echo 'Still running'; else echo 'Stopped'; fi)"