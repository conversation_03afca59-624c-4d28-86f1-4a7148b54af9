#!/bin/bash

# Start Background Jobs Infrastructure for QAK
# This script starts <PERSON><PERSON> and <PERSON><PERSON><PERSON> worker for background analysis processing

set -e

echo "🚀 Starting QAK Background Jobs Infrastructure..."

# Check if virtual environment exists and activate it
if [ -d ".venv" ]; then
    echo "📦 Activating virtual environment..."
    source .venv/bin/activate
else
    echo "⚠️  Virtual environment not found. Please run 'python -m venv .venv' first."
    exit 1
fi

# Check if Red<PERSON> is running
if ! redis-cli ping > /dev/null 2>&1; then
    echo "🔴 Redis is not running. Starting Redis with Docker..."
    docker-compose -f docker-compose.celery.yml up -d redis
    
    # Wait for Redis to be ready
    echo "⏳ Waiting for Redis to start..."
    for i in {1..30}; do
        if redis-cli ping > /dev/null 2>&1; then
            echo "✅ Redis is ready!"
            break
        fi
        sleep 1
    done
    
    if ! redis-cli ping > /dev/null 2>&1; then
        echo "❌ Redis failed to start after 30 seconds"
        exit 1
    fi
else
    echo "✅ Redis is already running"
fi

# Install background job dependencies if not present
echo "📦 Checking background job dependencies..."
if ! python -c "import celery, redis" > /dev/null 2>&1; then
    echo "📦 Installing background job dependencies..."
    pip install -r requirements_background_jobs.txt
fi

echo "🔧 Starting Celery worker..."

# Start Celery worker in background
python celery_worker.py &
CELERY_PID=$!

echo "✅ Celery worker started with PID: $CELERY_PID"
echo "📊 You can monitor tasks at http://localhost:5555 (if Flower is running)"
echo ""
echo "🔄 Background jobs are now ready to process analysis tasks!"
echo ""
echo "To stop the worker, run: kill $CELERY_PID"
echo "To stop Redis, run: docker-compose -f docker-compose.celery.yml down"

# Keep script running and handle Ctrl+C
cleanup() {
    echo ""
    echo "🛑 Stopping background jobs..."
    kill $CELERY_PID 2>/dev/null || true
    echo "✅ Background jobs stopped"
    exit 0
}

trap cleanup SIGINT SIGTERM

# Wait for Celery worker
wait $CELERY_PID