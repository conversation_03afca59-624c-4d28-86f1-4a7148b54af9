#!/usr/bin/env python3
"""
Test script to verify AI script generation functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

from src.services.script_generator_service import ScriptGeneratorService, ScriptGenerationOptions, ScriptFramework
from src.models.standard_result import TestStep, ExecutionStatus, TestType
from datetime import datetime
import asyncio

# Simple mock class that mimics Execution without MongoDB dependencies
class MockExecution:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

async def test_ai_script_generation():
    """Test the AI script generation with mock data"""
    
    print("🚀 Testing AI Script Generation System")
    print("=" * 50)
    
    # Create a mock execution
    execution = MockExecution(
        test_type=TestType.CODEGEN.value,
        status=ExecutionStatus.SUCCESS.value,
        execution_id="test-execution-123",
        target_url="https://web-agent-playground.lovable.app/",
        started_at=datetime.now(),
        duration_ms=15000,
        metadata={
            "test_description": "Login to web application",
            "test_instructions": "Navigate to login page, enter credentials, and verify dashboard access"
        }
    )
    
    # Create some mock steps
    steps = [
        TestStep(
            step_number=1,
            action_type="navigate",
            description="Navigate to login page",
            success=True,
            duration_ms=2000,
            url="https://web-agent-playground.lovable.app/",
            error_message=None,
            metadata={"action": "goto", "target": "https://web-agent-playground.lovable.app/"}
        ),
        TestStep(
            step_number=2,
            action_type="type",
            description="Enter email address",
            success=True,
            duration_ms=1500,
            url="https://web-agent-playground.lovable.app/",
            error_message=None,
            metadata={"action": "fill", "target": "#email", "value": "<EMAIL>"}
        ),
        TestStep(
            step_number=3,
            action_type="type", 
            description="Enter password",
            success=True,
            duration_ms=1000,
            url="https://web-agent-playground.lovable.app/",
            error_message=None,
            metadata={"action": "fill", "target": "#password", "value": "admin123"}
        ),
        TestStep(
            step_number=4,
            action_type="click",
            description="Click login button",
            success=True,
            duration_ms=3000,
            url="https://web-agent-playground.lovable.app/dashboard",
            error_message=None,
            metadata={"action": "click", "target": "button[type='submit']"}
        )
    ]
    
    # Add steps to execution
    execution.steps = steps
    
    # Test script generation options
    options = ScriptGenerationOptions(
        framework=ScriptFramework.PLAYWRIGHT_PYTHON,
        include_screenshots=True,
        include_waits=True,
        include_assertions=True,
        add_error_handling=True,
        use_best_locators=True,
        add_comments=True,
        page_object_pattern=False
    )
    
    # Initialize script generator
    generator = ScriptGeneratorService()
    
    print(f"📝 Generating {options.framework.value} script...")
    print(f"🔧 Options: screenshots={options.include_screenshots}, waits={options.include_waits}")
    
    try:
        # Generate script
        result = await generator.generate_script_from_execution(
            execution=execution,
            options=options,
            save_to_execution=False  # Don't save to DB for testing
        )
        
        print("\n✅ Script Generation Successful!")
        print("=" * 50)
        print(f"📊 Framework: {result['framework']}")
        print(f"📈 Steps Count: {result['steps_count']}")
        print(f"📅 Generated At: {result['generated_at']}")
        print(f"🎯 Target URL: {result['target_url']}")
        
        print("\n📜 Generated Script:")
        print("-" * 30)
        print(result['script_content'])
        print("-" * 30)
        
        print("\n🔍 Metadata:")
        for key, value in result['metadata'].items():
            print(f"  {key}: {value}")
            
        print("\n🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Script generation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ai_script_generation())
