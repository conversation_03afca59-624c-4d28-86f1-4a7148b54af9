"""
Migrate Semantic Memory from JSON to MongoDB

This script reads the consolidated semantic memory JSON file and migrates
the patterns and interactions to their respective MongoDB collections.
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
import uuid

# Add the project root to the python path more explicitly
import sys
import os

# Get the absolute path to the project root (parent of scripts/)
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

from src.database.connection import initialize_database, shutdown_database
from src.database.models import SemanticPattern, PatternContext, SemanticInteraction, TargetElement
from src.database.repositories.semantic_pattern_repository import SemanticPatternRepository
from src.database.repositories.semantic_interaction_repository import SemanticInteractionRepository

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def migrate():
    """Main migration function"""
    logger.info("Starting migration...")
    json_path = project_root / "semantic_memories" / "consolidated_semantic_memory.json"
    if not json_path.exists():
        logger.error(f"File not found: {json_path}")
        return

    logger.info("Initializing database...")
    await initialize_database()
    
    logger.info("Creating repositories...")
    pattern_repo = SemanticPatternRepository()
    interaction_repo = SemanticInteractionRepository()

    logger.info(f"Loading data from {json_path}...")
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # Migrate patterns
    logger.info("Starting pattern migration...")
    patterns = data.get("patterns", [])
    migrated_patterns = 0
    for i, p_data in enumerate(patterns):
        logger.debug(f"Processing pattern {i+1}/{len(patterns)}")
        try:
            last_used = None
            if p_data.get("last_used"):
                try:
                    # Attempt to parse different possible ISO formats
                    last_used_str = p_data["last_used"].replace("Z", "+00:00")
                    last_used = datetime.fromisoformat(last_used_str)
                except ValueError:
                    # Fallback for formats like 'YYYY-MM-DD HH:MM:SS.micros'
                    last_used = datetime.strptime(p_data["last_used"], "%Y-%m-%d %H:%M:%S.%f")

            pattern_doc = SemanticPattern(
                pattern_id=p_data.get("pattern_id", str(uuid.uuid4())),
                pattern_type=p_data.get("pattern_type"),
                description=p_data.get("description"),
                context=PatternContext(**p_data.get("context", {})),
                success_indicators=p_data.get("success_indicators", []),
                failure_indicators=p_data.get("failure_indicators", []),
                confidence_score=p_data.get("confidence_score", 0.0),
                usage_count=p_data.get("usage_count", 0),
                last_used=last_used,
                merged_from=p_data.get("merged_from", 0)
            )
            await pattern_repo.upsert_pattern(pattern_doc)
            migrated_patterns += 1
        except Exception as e:
            logger.error(f"Failed to migrate pattern {p_data.get('pattern_id')}: {e}", exc_info=True)
    
    logger.info(f"Migrated {migrated_patterns} patterns.")

    # Migrate interactions
    logger.info("Starting interaction migration...")
    interactions = data.get("recent_interactions", [])
    migrated_interactions = 0
    for i, i_data in enumerate(interactions):
        logger.debug(f"Processing interaction {i+1}/{len(interactions)}")
        try:
            timestamp = datetime.now()
            if i_data.get("timestamp"):
                try:
                    timestamp_str = i_data["timestamp"].replace("Z", "+00:00")
                    timestamp = datetime.fromisoformat(timestamp_str)
                except ValueError:
                    timestamp = datetime.strptime(i_data["timestamp"], "%Y-%m-%d %H:%M:%S.%f")

            interaction_doc = SemanticInteraction(
                interaction_id=str(uuid.uuid4()),
                agent_id="migrated_agent", # Placeholder agent_id
                timestamp=timestamp,
                url=i_data.get("url"),
                action_type=i_data.get("action_type"),
                target_element=TargetElement(element_data=i_data.get("target_element", {})),
                context=i_data.get("context"),
                success=i_data.get("success"),
                result=i_data.get("result"),
                lesson_learned=i_data.get("lesson_learned")
            )
            await interaction_repo.add_interaction(interaction_doc)
            migrated_interactions += 1
        except Exception as e:
            logger.error(f"Failed to migrate interaction: {e}", exc_info=True)

    logger.info(f"Migrated {migrated_interactions} interactions.")

    logger.info("Shutting down database...")
    await shutdown_database()
    logger.info("Migration finished.")

if __name__ == "__main__":
    try:
        asyncio.run(migrate())
    except Exception as e:
        # Log to file as well to ensure we capture the full traceback
        with open("migration_error.log", "w") as f:
            import traceback
            traceback.print_exc(file=f)
        logger.error("Migration script failed. See migration_error.log for details.", exc_info=True) 