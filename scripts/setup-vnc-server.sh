#!/bin/bash
# Script de configuración automática de QAK con VNC para servidores Ubuntu/Debian

set -e

echo "🔧 Configurando QAK con soporte VNC completo"
echo "Este script instalará todas las dependencias necesarias automáticamente"

# Detectar sistema operativo
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
else
    echo "❌ No se pudo detectar el sistema operativo"
    exit 1
fi

echo "📋 Sistema detectado: $OS"

# Actualizar sistema
echo "📦 Actualizando paquetes del sistema..."
if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
    sudo apt-get update
    
    # Instalar dependencias VNC
    echo "🖥️ Instalando dependencias VNC..."
    sudo apt-get install -y \
        xvfb \
        x11vnc \
        fluxbox \
        nodejs \
        npm \
        python3 \
        python3-pip \
        wget \
        curl
        
elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
    sudo yum update -y
    
    # Instalar dependencias VNC
    echo "🖥️ Instalando dependencias VNC..."
    sudo yum install -y \
        xorg-x11-server-Xvfb \
        x11vnc \
        fluxbox \
        nodejs \
        npm \
        python3 \
        python3-pip \
        wget \
        curl
else
    echo "❌ Sistema operativo no soportado: $OS"
    echo "Sistemas soportados: Ubuntu, Debian, CentOS, Red Hat"
    exit 1
fi

# Instalar websockify
echo "🌐 Instalando websockify..."
sudo npm install -g websockify

# Descargar e instalar noVNC
echo "📺 Instalando noVNC..."
NOVNC_VERSION="1.4.0"
sudo mkdir -p /usr/share/novnc
wget -O /tmp/novnc.tar.gz "https://github.com/novnc/noVNC/archive/refs/tags/v${NOVNC_VERSION}.tar.gz"
sudo tar -xzf /tmp/novnc.tar.gz -C /usr/share/
sudo mv "/usr/share/noVNC-${NOVNC_VERSION}"/* /usr/share/novnc/
sudo rm -rf "/usr/share/noVNC-${NOVNC_VERSION}" /tmp/novnc.tar.gz

# Instalar Playwright
echo "🎭 Instalando Playwright..."
sudo npm install -g playwright
sudo playwright install

# Crear servicio systemd para QAK con VNC
echo "⚙️ Configurando servicio systemd..."
cat << EOF | sudo tee /etc/systemd/system/qak-vnc.service
[Unit]
Description=QAK with VNC Support
After=network.target

[Service]
Type=forking
User=root
Environment=DISPLAY=:99
Environment=QAK_VNC_ENABLED=true
ExecStartPre=/usr/bin/Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX
ExecStartPre=/bin/sleep 2
ExecStartPre=/usr/bin/fluxbox
ExecStartPre=/usr/bin/x11vnc -display :99 -rfbport 5900 -nopw -forever -shared -bg
ExecStart=/usr/bin/websockify --web /usr/share/novnc 6080 localhost:5900
ExecStop=/bin/pkill -f "Xvfb|fluxbox|x11vnc|websockify"
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Habilitar servicio
sudo systemctl daemon-reload
sudo systemctl enable qak-vnc.service

echo "✅ Configuración completada!"
echo ""
echo "🎉 QAK está listo para usar con soporte VNC completo:"
echo ""
echo "📋 Para iniciar los servicios VNC:"
echo "   sudo systemctl start qak-vnc"
echo ""
echo "📋 Para verificar el estado:"
echo "   sudo systemctl status qak-vnc"
echo ""
echo "🌐 Acceso web VNC:"
echo "   http://tu-servidor:6080/vnc.html"
echo ""
echo "🔗 Acceso VNC directo:"
echo "   vnc://tu-servidor:5900"
echo ""
echo "📝 Nota: Los servicios VNC se iniciarán automáticamente en el próximo reinicio"
echo "📝 Display virtual configurado en :99"
