#!/usr/bin/env python3
"""
Distribute Consolidated Memory Script for browser-use

This script distributes a consolidated semantic memory from MongoDB to multiple
agent memory directories, useful for sharing learned knowledge across agents.

Features:
- Distributes consolidated memory to agent-specific files from MongoDB
- Creates base memory templates for new agents
- Manages memory versioning and updates
- Handles incremental distribution

Usage:
    python scripts/distribute_consolidated_memory.py [options]
"""

import asyncio
import json
import logging
import argparse
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from uuid import uuid4

# Add project root to path
import sys
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

from src.database.connection import initialize_database, shutdown_database
from src.database.repositories.semantic_pattern_repository import SemanticPatternRepository
from src.database.repositories.semantic_interaction_repository import SemanticInteractionRepository

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MemoryDistributor:
    """Distributes consolidated semantic memory from MongoDB to multiple agents"""
    
    def __init__(self, 
                 target_dir: str = "semantic_memories",
                 base_memory_dir: str = "memory_templates"):
        self.target_dir = Path(target_dir)
        self.base_memory_dir = Path(base_memory_dir)
        
        # Distribution stats
        self.stats = {
            'agents_updated': 0,
            'new_agents_created': 0,
            'patterns_distributed': 0,
            'errors': 0,
            'processing_time': 0.0
        }
        
    async def distribute(self, 
                        agent_ids: Optional[List[str]] = None,
                        create_new_templates: bool = True,
                        merge_mode: str = 'smart',
                        backup_existing: bool = True) -> Dict[str, Any]:
        """
        Distribute consolidated memory from DB to agents
        
        Args:
            agent_ids: Specific agent IDs to update (None = all existing)
            create_new_templates: Whether to create template memories for new agents
            merge_mode: 'replace', 'merge', or 'smart' merging strategy
            backup_existing: Whether to backup existing memories before updating
        """
        start_time = datetime.now()
        logger.info("🔄 Starting memory distribution from MongoDB...")
        
        # Initialize DB
        await initialize_database()
        
        # Load consolidated memory from DB
        consolidated_memory = await self._load_memory_from_db()
        if not consolidated_memory:
            logger.error("❌ Failed to load consolidated memory from MongoDB")
            await shutdown_database()
            return self.stats
        
        logger.info(f"📚 Loaded memory from DB with {len(consolidated_memory.get('patterns', []))} patterns")
        
        # Get target agents
        target_agents = await self._get_target_agents(agent_ids)
        logger.info(f"🎯 Found {len(target_agents)} agents to update")
        
        # Distribute to each agent
        for agent_id in target_agents:
            try:
                await self._distribute_to_agent(
                    agent_id, consolidated_memory, merge_mode, backup_existing
                )
                self.stats['agents_updated'] += 1
            except Exception as e:
                logger.error(f"❌ Error distributing to agent {agent_id}: {e}")
                self.stats['errors'] += 1
        
        # Create templates if requested
        if create_new_templates:
            await self._create_memory_templates(consolidated_memory)
        
        # Update stats
        self.stats['patterns_distributed'] = len(consolidated_memory.get('patterns', []))
        self.stats['processing_time'] = (datetime.now() - start_time).total_seconds()
        
        await self._generate_distribution_report()
        
        # Shutdown DB
        await shutdown_database()
        
        logger.info(f"✅ Distribution completed in {self.stats['processing_time']:.2f}s")
        return self.stats
    
    async def _load_memory_from_db(self) -> Optional[Dict[str, Any]]:
        """Load the consolidated memory from MongoDB"""
        try:
            pattern_repo = SemanticPatternRepository()
            interaction_repo = SemanticInteractionRepository()

            logger.info("Fetching patterns from DB...")
            patterns_cursor = pattern_repo.collection.find()
            patterns = [p async for p in patterns_cursor]
            logger.info(f"Found {len(patterns)} patterns.")

            logger.info("Fetching recent interactions from DB...")
            # Limit to a reasonable number, e.g., 100
            interactions_cursor = interaction_repo.collection.find().sort("timestamp", -1).limit(100)
            interactions = [i async for i in interactions_cursor]
            logger.info(f"Found {len(interactions)} recent interactions.")

            # Serialize to JSON-compatible format
            for p in patterns:
                p['_id'] = str(p['_id'])
                if 'last_used' in p and p['last_used']:
                    p['last_used'] = p['last_used'].isoformat()
            
            for i in interactions:
                i['_id'] = str(i['_id'])
                if 'timestamp' in i and i['timestamp']:
                    i['timestamp'] = i['timestamp'].isoformat()
            
            return {
                "consolidation_info": {
                    "created_at": datetime.now().isoformat(),
                    "consolidator_version": "2.0.0-mongodb",
                    "source": "MongoDB"
                },
                "patterns": patterns,
                "recent_interactions": interactions
            }
        
        except Exception as e:
            logger.error(f"Error loading memory from MongoDB: {e}", exc_info=True)
            return None
    
    async def _get_target_agents(self, agent_ids: Optional[List[str]]) -> List[str]:
        """Get list of target agent IDs"""
        if agent_ids:
            return agent_ids
        
        # Find existing agent memory files
        if not self.target_dir.exists():
            self.target_dir.mkdir(parents=True, exist_ok=True)
            return []
        
        agents = []
        for file_path in self.target_dir.glob("semantic_memory_*.json"):
            # Extract agent ID from filename
            filename = file_path.stem
            if filename.startswith("semantic_memory_"):
                agent_id = filename[16:]  # Remove "semantic_memory_" prefix
                agents.append(agent_id)
        
        return agents
    
    async def _distribute_to_agent(self, 
                                  agent_id: str, 
                                  consolidated_memory: Dict[str, Any],
                                  merge_mode: str,
                                  backup_existing: bool) -> None:
        """Distribute memory to a specific agent"""
        logger.debug(f"📤 Distributing memory to agent {agent_id}")
        
        agent_file = self.target_dir / f"semantic_memory_{agent_id}.json"
        
        # Backup existing memory if requested
        if backup_existing and agent_file.exists():
            await self._backup_agent_memory(agent_id)
        
        # Load existing agent memory
        existing_memory = None
        if agent_file.exists():
            try:
                with open(agent_file, 'r', encoding='utf-8') as f:
                    existing_memory = json.load(f)
            except Exception as e:
                logger.warning(f"Could not load existing memory for {agent_id}: {e}")
        
        # Create new memory based on merge mode
        if merge_mode == 'replace' or not existing_memory:
            new_memory = self._create_agent_memory_from_consolidated(agent_id, consolidated_memory)
        elif merge_mode == 'merge':
            new_memory = self._merge_memories(existing_memory, consolidated_memory, agent_id)
        elif merge_mode == 'smart':
            new_memory = self._smart_merge_memories(existing_memory, consolidated_memory, agent_id)
        else:
            raise ValueError(f"Unknown merge mode: {merge_mode}")
        
        # Save updated memory
        with open(agent_file, 'w', encoding='utf-8') as f:
            json.dump(new_memory, f, indent=2, default=str, ensure_ascii=False)
        
        logger.debug(f"✅ Updated memory for agent {agent_id}")
    
    def _create_agent_memory_from_consolidated(self, 
                                             agent_id: str, 
                                             consolidated_memory: Dict[str, Any]) -> Dict[str, Any]:
        """Create new agent memory from consolidated memory"""
        return {
            "agent_id": agent_id,
            "timestamp": datetime.now().isoformat(),
            "patterns": consolidated_memory.get('patterns', []),
            "recent_interactions": [],  # Start with empty interactions
            "stats": {
                "distribution_info": {
                    "distributed_from": "consolidated_memory",
                    "distributed_at": datetime.now().isoformat(),
                    "patterns_received": len(consolidated_memory.get('patterns', []))
                }
            }
        }
    
    def _merge_memories(self, 
                       existing: Dict[str, Any], 
                       consolidated: Dict[str, Any], 
                       agent_id: str) -> Dict[str, Any]:
        """Simple merge: combine all patterns and interactions"""
        # Combine patterns (avoiding duplicates by pattern_id)
        existing_patterns = {p.get('pattern_id'): p for p in existing.get('patterns', [])}
        consolidated_patterns = {p.get('pattern_id'): p for p in consolidated.get('patterns', [])}
        
        # Consolidated patterns take precedence
        all_patterns = {**existing_patterns, **consolidated_patterns}
        
        # Combine interactions (keep existing + recent from consolidated)
        existing_interactions = existing.get('recent_interactions', [])
        consolidated_interactions = consolidated.get('recent_interactions', [])[:100]  # Limit
        
        return {
            "agent_id": agent_id,
            "timestamp": datetime.now().isoformat(),
            "patterns": list(all_patterns.values()),
            "recent_interactions": existing_interactions + consolidated_interactions,
            "stats": {
                "merge_info": {
                    "merged_at": datetime.now().isoformat(),
                    "existing_patterns": len(existing_patterns),
                    "consolidated_patterns": len(consolidated_patterns),
                    "final_patterns": len(all_patterns)
                }
            }
        }
    
    def _smart_merge_memories(self, 
                            existing: Dict[str, Any], 
                            consolidated: Dict[str, Any], 
                            agent_id: str) -> Dict[str, Any]:
        """Smart merge: prioritize patterns based on confidence and usage"""
        # Get patterns from both sources
        existing_patterns = {p.get('pattern_id'): p for p in existing.get('patterns', [])}
        consolidated_patterns = {p.get('pattern_id'): p for p in consolidated.get('patterns', [])}
        
        # Smart merging logic
        final_patterns = {}
        
        # Start with existing patterns
        for pattern_id, pattern in existing_patterns.items():
            final_patterns[pattern_id] = pattern
        
        # Add or update with consolidated patterns
        for pattern_id, consolidated_pattern in consolidated_patterns.items():
            if pattern_id not in final_patterns:
                # New pattern from consolidated memory
                final_patterns[pattern_id] = consolidated_pattern
            else:
                # Choose the better pattern
                existing_pattern = final_patterns[pattern_id]
                better_pattern = self._choose_better_pattern(existing_pattern, consolidated_pattern)
                final_patterns[pattern_id] = better_pattern
        
        # Limit interactions to most recent
        existing_interactions = existing.get('recent_interactions', [])
        recent_consolidated = consolidated.get('recent_interactions', [])[:50]
        
        # Combine and sort by timestamp
        all_interactions = existing_interactions + recent_consolidated
        all_interactions.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        final_interactions = all_interactions[:500]  # Keep top 500
        
        return {
            "agent_id": agent_id,
            "timestamp": datetime.now().isoformat(),
            "patterns": list(final_patterns.values()),
            "recent_interactions": final_interactions,
            "stats": {
                "smart_merge_info": {
                    "merged_at": datetime.now().isoformat(),
                    "existing_patterns": len(existing_patterns),
                    "consolidated_patterns": len(consolidated_patterns),
                    "final_patterns": len(final_patterns),
                    "patterns_from_existing": sum(1 for p in final_patterns.values() 
                                                if p.get('pattern_id') in existing_patterns),
                    "patterns_from_consolidated": sum(1 for p in final_patterns.values() 
                                                    if p.get('pattern_id') not in existing_patterns)
                }
            }
        }
    
    def _choose_better_pattern(self, 
                             existing: Dict[str, Any], 
                             consolidated: Dict[str, Any]) -> Dict[str, Any]:
        """Choose the better pattern between existing and consolidated"""
        # Calculate quality scores
        existing_score = (
            existing.get('confidence_score', 0) * 0.4 +
            min(existing.get('usage_count', 0), 100) * 0.01 * 0.6  # Cap usage at 100 for scoring
        )
        
        consolidated_score = (
            consolidated.get('confidence_score', 0) * 0.4 +
            min(consolidated.get('usage_count', 0), 100) * 0.01 * 0.6
        )
        
        # Return the better one (or merged if very close)
        if abs(existing_score - consolidated_score) < 0.1:
            # Very close scores - merge them
            return self._merge_similar_patterns([existing, consolidated])
        elif consolidated_score > existing_score:
            return consolidated
        else:
            return existing
    
    def _merge_similar_patterns(self, patterns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Merge two similar patterns (simplified version)"""
        if len(patterns) == 1:
            return patterns[0]
        
        # Use the pattern with higher confidence as base
        base_pattern = max(patterns, key=lambda p: p.get('confidence_score', 0))
        merged = base_pattern.copy()
        
        # Combine success/failure indicators
        all_success = set()
        all_failure = set()
        total_usage = 0
        
        for pattern in patterns:
            all_success.update(pattern.get('success_indicators', []))
            all_failure.update(pattern.get('failure_indicators', []))
            total_usage += pattern.get('usage_count', 0)
        
        merged['success_indicators'] = list(all_success)[:10]
        merged['failure_indicators'] = list(all_failure)[:10]
        merged['usage_count'] = total_usage
        merged['last_used'] = max(p.get('last_used', '') for p in patterns)
        
        return merged
    
    async def _backup_agent_memory(self, agent_id: str) -> None:
        """Create backup of existing agent memory"""
        try:
            source_file = self.target_dir / f"semantic_memory_{agent_id}.json"
            backup_dir = self.target_dir / "backups"
            backup_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_dir / f"semantic_memory_{agent_id}_{timestamp}.json"
            
            shutil.copy2(source_file, backup_file)
            logger.debug(f"📦 Backed up memory for {agent_id} to {backup_file}")
            
        except Exception as e:
            logger.warning(f"Could not backup memory for {agent_id}: {e}")
    
    async def _create_memory_templates(self, consolidated_memory: Dict[str, Any]) -> None:
        """Create memory templates for new agents"""
        try:
            self.base_memory_dir.mkdir(parents=True, exist_ok=True)
            
            # Create different template types
            templates = {
                "basic_template.json": self._create_basic_template(consolidated_memory),
                "advanced_template.json": self._create_advanced_template(consolidated_memory),
                "minimal_template.json": self._create_minimal_template(consolidated_memory)
            }
            
            for template_name, template_data in templates.items():
                template_file = self.base_memory_dir / template_name
                
                with open(template_file, 'w', encoding='utf-8') as f:
                    json.dump(template_data, f, indent=2, default=str, ensure_ascii=False)
                
                logger.info(f"📋 Created template: {template_file}")
                
        except Exception as e:
            logger.error(f"Error creating templates: {e}")
    
    def _create_basic_template(self, consolidated_memory: Dict[str, Any]) -> Dict[str, Any]:
        """Create basic memory template with most useful patterns"""
        patterns = consolidated_memory.get('patterns', [])
        
        # Select patterns with high confidence and usage
        selected_patterns = [
            p for p in patterns 
            if p.get('confidence_score', 0) > 0.7 and p.get('usage_count', 0) > 5
        ][:50]  # Top 50 patterns
        
        return {
            "agent_id": "NEW_AGENT_ID",
            "timestamp": datetime.now().isoformat(),
            "patterns": selected_patterns,
            "recent_interactions": [],
            "template_info": {
                "template_type": "basic",
                "created_at": datetime.now().isoformat(),
                "source_patterns": len(patterns),
                "selected_patterns": len(selected_patterns)
            }
        }
    
    def _create_advanced_template(self, consolidated_memory: Dict[str, Any]) -> Dict[str, Any]:
        """Create advanced template with comprehensive patterns"""
        return {
            "agent_id": "NEW_AGENT_ID",
            "timestamp": datetime.now().isoformat(),
            "patterns": consolidated_memory.get('patterns', []),
            "recent_interactions": [],
            "template_info": {
                "template_type": "advanced",
                "created_at": datetime.now().isoformat(),
                "total_patterns": len(consolidated_memory.get('patterns', []))
            }
        }
    
    def _create_minimal_template(self, consolidated_memory: Dict[str, Any]) -> Dict[str, Any]:
        """Create minimal template with only essential patterns"""
        patterns = consolidated_memory.get('patterns', [])
        
        # Select only the most essential patterns
        essential_patterns = [
            p for p in patterns 
            if p.get('confidence_score', 0) > 0.8 and p.get('usage_count', 0) > 20
        ][:20]  # Top 20 most essential
        
        return {
            "agent_id": "NEW_AGENT_ID",
            "timestamp": datetime.now().isoformat(),
            "patterns": essential_patterns,
            "recent_interactions": [],
            "template_info": {
                "template_type": "minimal",
                "created_at": datetime.now().isoformat(),
                "essential_patterns": len(essential_patterns)
            }
        }
    
    async def _generate_distribution_report(self) -> None:
        """Generate distribution report"""
        report = f"""
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                            MEMORY DISTRIBUTION REPORT                                ║
╠══════════════════════════════════════════════════════════════════════════════════════╣
║ Processing Time: {self.stats['processing_time']:.2f} seconds                                                        ║
║                                                                                      ║
║ DISTRIBUTION RESULTS:                                                                ║
║ • Agents updated: {self.stats['agents_updated']:<10}                                                             ║
║ • New templates created: {self.stats['new_agents_created']:<10}                                                       ║
║ • Patterns distributed: {self.stats['patterns_distributed']:<10}                                                      ║
║ • Errors encountered: {self.stats['errors']:<10}                                                           ║
║                                                                                      ║
║ FILES:                                                                               ║
║ • Source: {'MongoDB':<30}                                                       ║
║ • Target directory: {str(self.target_dir):<30}                                               ║
║ • Templates directory: {str(self.base_memory_dir):<30}                                           ║
╚══════════════════════════════════════════════════════════════════════════════════════╝
"""
        
        logger.info(report)
        
        # Save report
        report_path = self.target_dir / f"distribution_report_{datetime.now():%Y%m%d_%H%M%S}.txt"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        logger.info(f"📄 Distribution report saved to {report_path}")


async def main():
    """Main function to run the distributor from command line"""
    parser = argparse.ArgumentParser(
        description="Distribute consolidated semantic memory from MongoDB to agent memories."
    )
    
    parser.add_argument('--target-dir', default='semantic_memories',
                        help='Directory for agent memory files')
    parser.add_argument('--templates-dir', default='memory_templates',
                        help='Directory for memory templates')
    parser.add_argument('--agent-ids', nargs='*',
                        help='Specific agent IDs to update (e.g., agent1 agent2)')
    parser.add_argument('--no-create-templates', action='store_false',
                        dest='create_templates',
                        help='Disable creation of new memory templates')
    parser.add_argument('--merge-mode', choices=['replace', 'merge', 'smart'],
                        default='smart', help='Strategy for merging memories')
    parser.add_argument('--no-backup', action='store_false',
                        dest='backup', help='Disable backup of existing memories')

    args = parser.parse_args()

    distributor = MemoryDistributor(
        target_dir=args.target_dir,
        base_memory_dir=args.templates_dir
    )
    
    try:
        asyncio.run(distributor.distribute(
            agent_ids=args.agent_ids,
            create_new_templates=args.create_templates,
            merge_mode=args.merge_mode,
            backup_existing=args.backup
        ))
    except Exception as e:
        logger.error(f"An error occurred during distribution: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main()) 