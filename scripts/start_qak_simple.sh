#!/bin/bash

# Start QAK with Simple Local Background Jobs
# Uses local Redis, Celery worker, and QAK API

set -e

echo "🚀 Starting QAK with Simple Background Jobs..."

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "❌ Virtual environment not found. Please run './scripts/setup_simple_background_jobs.sh' first."
    exit 1
fi

# Activate virtual environment
source .venv/bin/activate

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Start Redis if not running
if ! check_port 6379; then
    echo "🔴 Starting local Redis..."
    
    # Create redis config if it doesn't exist
    if [ ! -f "redis-local.conf" ]; then
        cat > redis-local.conf << EOF
port 6379
bind 127.0.0.1
save 900 1
save 300 10
save 60 10000
dir ./redis-data
EOF
        mkdir -p redis-data
    fi
    
    redis-server redis-local.conf --daemonize yes
    sleep 2
    
    if check_port 6379; then
        echo "✅ Redis started on port 6379"
    else
        echo "❌ Failed to start Redis"
        exit 1
    fi
else
    echo "✅ Redis already running on port 6379"
fi

# Test Redis connection
if redis-cli ping >/dev/null 2>&1; then
    echo "✅ Redis connection test successful"
else
    echo "❌ Redis connection failed"
    exit 1
fi

# Set local Redis URL for this session (override Upstash)
export REDIS_URL="redis://localhost:6379/0"
echo "✅ Using local Redis for background jobs"

# Start Celery worker in background
echo "🔧 Starting Celery worker..."
python celery_worker.py > celery_worker.log 2>&1 &
CELERY_PID=$!
echo "✅ Celery worker started with PID: $CELERY_PID"

# Give worker time to start
sleep 3

# Test worker
echo "🧪 Testing Celery worker..."
if python3 -c "
from src.core.background_jobs.celery_app import celery_app
try:
    inspect = celery_app.control.inspect()
    ping = inspect.ping()
    if ping:
        print('✅ Celery worker is responding')
    else:
        print('⚠️ No active workers found yet')
except Exception as e:
    print(f'⚠️ Worker check: {e}')
"; then
    echo "✅ Worker system ready"
fi

# Start QAK API in background
echo "🌐 Starting QAK API server..."
python app.py > qak_api.log 2>&1 &
API_PID=$!
echo "✅ QAK API started with PID: $API_PID"

# Give API time to start
sleep 5

# Test API
if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    echo "✅ QAK API is responding"
else
    echo "⚠️ QAK API may not be fully ready yet"
fi

# Test background jobs
echo "🧪 Testing background jobs system..."
HEALTH_RESPONSE=$(curl -s http://localhost:8000/api/v2/background-jobs/health 2>/dev/null || echo '{}')
if echo "$HEALTH_RESPONSE" | grep -q "available"; then
    echo "✅ Background jobs system is ready"
else
    echo "⚠️ Background jobs system may not be ready"
fi

echo ""
echo "🎉 QAK System Started Successfully!"
echo ""
echo "📊 Service Status:"
echo "   Local Redis:  ✅ localhost:6379 (data: ./redis-data/)"
echo "   Celery Worker: PID $CELERY_PID (logs: celery_worker.log)"
echo "   QAK API:      http://localhost:8000 (PID $API_PID, logs: qak_api.log)"
echo ""
echo "🔗 Quick Tests:"
echo "   curl http://localhost:8000/health"
echo "   curl http://localhost:8000/api/v2/background-jobs/health"
echo ""
echo "📝 Monitor Logs:"
echo "   tail -f celery_worker.log"
echo "   tail -f qak_api.log"
echo ""
echo "🛑 Stop Services:"
echo "   kill $CELERY_PID $API_PID"
echo "   redis-cli shutdown"
echo ""

# Save PIDs for easy stopping
cat > .qak_pids << EOF
CELERY_PID=$CELERY_PID
API_PID=$API_PID
REDIS_TYPE=local
EOF

echo "💾 Process IDs saved to .qak_pids"
echo "🔄 Ready for background job processing!"

# Optional: Show a sample job start command
echo ""
echo "🧪 Test Background Job (optional):"
echo 'curl -X POST http://localhost:8000/api/v2/background-jobs/analysis/start \'
echo '  -H "Content-Type: application/json" \'
echo '  -d "{\"execution_id\":\"test_123\",\"result_data\":{\"test\":true}}"'