#!/bin/bash

# Stop All QAK Processes - Emergency Stop Script
# Kills all QAK-related processes regardless of how they were started

echo "🛑 Stopping ALL QAK Processes..."

# Function to kill processes by pattern and show what was killed
kill_processes() {
    local pattern="$1"
    local description="$2"
    
    echo "🔍 Looking for $description..."
    
    # Find PIDs matching the pattern
    PIDS=$(pgrep -f "$pattern" 2>/dev/null || true)
    
    if [ -n "$PIDS" ]; then
        echo "   Found PIDs: $PIDS"
        for pid in $PIDS; do
            # Get process info before killing
            process_info=$(ps -p $pid -o pid,ppid,command 2>/dev/null | tail -n +2 || echo "Process $pid")
            echo "   Killing: $process_info"
            kill $pid 2>/dev/null || true
        done
        
        # Wait a moment and force kill if still running
        sleep 2
        remaining=$(pgrep -f "$pattern" 2>/dev/null || true)
        if [ -n "$remaining" ]; then
            echo "   Force killing remaining: $remaining"
            kill -9 $remaining 2>/dev/null || true
        fi
        echo "   ✅ $description stopped"
    else
        echo "   ✅ No $description processes found"
    fi
}

# Kill QAK API processes
kill_processes "python.*app\.py" "QAK API"

# Kill Celery worker processes
kill_processes "celery.*worker" "Celery workers"
kill_processes "python.*celery_worker\.py" "Celery worker scripts"

# Kill any other Celery processes
kill_processes "celery" "other Celery processes"

# Kill Redis if it was started by our script (local only)
echo "🔍 Looking for local Redis..."
REDIS_PIDS=$(pgrep redis-server 2>/dev/null || true)
if [ -n "$REDIS_PIDS" ]; then
    echo "   Found Redis PIDs: $REDIS_PIDS"
    
    # Check if Redis is using our local config
    for pid in $REDIS_PIDS; do
        redis_cmd=$(ps -p $pid -o command --no-headers 2>/dev/null || echo "")
        if echo "$redis_cmd" | grep -q "redis-local.conf"; then
            echo "   Killing local Redis: $pid"
            kill $pid 2>/dev/null || true
        else
            echo "   Skipping system Redis: $pid (not our local instance)"
        fi
    done
    echo "   ✅ Local Redis stopped"
else
    echo "   ✅ No Redis processes found"
fi

# Alternative: Stop Redis gracefully if redis-cli is available
if command -v redis-cli &> /dev/null; then
    echo "🔧 Attempting graceful Redis shutdown..."
    redis-cli -p 6379 shutdown 2>/dev/null && echo "   ✅ Redis shutdown successful" || echo "   ⚠️ Redis already stopped or not local"
fi

# Kill any Python processes that might be hanging around
echo "🔍 Looking for other Python processes in QAK directory..."
QAK_PYTHON_PIDS=$(pgrep -f "python.*$(pwd)" 2>/dev/null | grep -v $$ || true)
if [ -n "$QAK_PYTHON_PIDS" ]; then
    echo "   Found QAK Python PIDs: $QAK_PYTHON_PIDS"
    kill $QAK_PYTHON_PIDS 2>/dev/null || true
    echo "   ✅ QAK Python processes stopped"
fi

# Clean up PID file
if [ -f ".qak_pids" ]; then
    echo "🗑️ Cleaning up PID file..."
    rm .qak_pids
    echo "   ✅ PID file removed"
fi

# Kill any processes using ports 8000 (API) and 6379 (Redis)
echo "🔍 Checking for processes on QAK ports..."

# Port 8000 (API)
PORT_8000_PID=$(lsof -ti:8000 2>/dev/null || true)
if [ -n "$PORT_8000_PID" ]; then
    echo "   Killing process on port 8000: $PORT_8000_PID"
    kill $PORT_8000_PID 2>/dev/null || true
fi

# Port 6379 (Redis) - only if it looks like our local Redis
PORT_6379_PID=$(lsof -ti:6379 2>/dev/null || true)
if [ -n "$PORT_6379_PID" ]; then
    redis_cmd=$(ps -p $PORT_6379_PID -o command --no-headers 2>/dev/null || echo "")
    if echo "$redis_cmd" | grep -q "redis-local.conf\|redis-server.*6379"; then
        echo "   Killing local Redis on port 6379: $PORT_6379_PID"
        kill $PORT_6379_PID 2>/dev/null || true
    fi
fi

# Final verification
echo ""
echo "🧪 Final Verification:"

# Check if any QAK processes are still running
remaining_qak=$(pgrep -f "python.*app\.py\|celery.*worker\|python.*celery_worker\.py" 2>/dev/null || true)
if [ -n "$remaining_qak" ]; then
    echo "⚠️ Some QAK processes still running: $remaining_qak"
else
    echo "✅ All QAK processes stopped"
fi

# Check ports
if lsof -i:8000 >/dev/null 2>&1; then
    echo "⚠️ Port 8000 still in use"
else
    echo "✅ Port 8000 free"
fi

if lsof -i:6379 >/dev/null 2>&1; then
    local_redis=$(lsof -i:6379 2>/dev/null | grep -v COMMAND | head -1)
    echo "ℹ️ Port 6379 in use: $local_redis"
else
    echo "✅ Port 6379 free"
fi

echo ""
echo "🎉 Stop All Complete!"
echo ""
echo "📋 What was stopped:"
echo "   - QAK API (app.py)"
echo "   - Celery workers"
echo "   - Local Redis (if started by QAK)"
echo "   - Any hanging Python processes"
echo ""
echo "🚀 To restart:"
echo "   ./scripts/start_qak_simple.sh"