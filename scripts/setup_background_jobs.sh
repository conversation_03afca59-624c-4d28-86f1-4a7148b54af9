#!/bin/bash

# Setup Background Jobs for QAK
# This script installs and configures the background jobs system

set -e

echo "🚀 Setting up QAK Background Jobs System..."

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "❌ Virtual environment not found. Please run 'python -m venv .venv' first."
    exit 1
fi

echo "📦 Activating virtual environment..."
source .venv/bin/activate

echo "📥 Installing background job dependencies..."
pip install 'celery[redis]' redis flower kombu

echo "🔧 Testing background jobs system..."
python3 -c "
from src.core.background_jobs import BACKGROUND_JOBS_AVAILABLE
if BACKGROUND_JOBS_AVAILABLE:
    print('✅ Background jobs system is ready!')
    from src.core.background_jobs.job_manager import get_job_manager
    print('✅ Job manager initialized successfully')
else:
    print('❌ Background jobs system not available')
    exit(1)
"

echo ""
echo "🎉 Background Jobs Setup Complete!"
echo ""
echo "Next steps:"
echo "1. Start Redis: docker run -d -p 6379:6379 redis:7-alpine"
echo "2. Start Celery worker: python celery_worker.py"
echo "3. Start QAK API: python app.py"
echo "4. Check health: curl http://localhost:8000/api/v2/background-jobs/health"
echo ""
echo "Optional: Start Flower monitoring: celery -A src.core.background_jobs.celery_app flower --port=5555"