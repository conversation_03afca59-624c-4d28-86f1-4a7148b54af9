#!/usr/bin/env python3
"""
Migration Script: Legacy JSON ➜ MongoDB

Runs batch migration for projects, executions, codegen sessions and artifacts.
Requires env var `MONGODB_URI` with credentials. Use in one-off mode.

Usage:
    python scripts/migrate_legacy_to_mongo.py --dry-run   # preview counts only
    python scripts/migrate_legacy_to_mongo.py             # execute migration
"""
from __future__ import annotations

import asyncio
import os
import sys
import logging
from pathlib import Path
from typing import Dict, Any

import click
from dotenv import load_dotenv
import functools
import json
from datetime import datetime
import uuid

# Load environment variables from .env file
load_dotenv()

# Ensure project root in path
PROJECT_ROOT = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s | %(message)s")
logger = logging.getLogger("migration")

# Import services lazily after path adjustment
from src.services import ProjectService, ExecutionService, CodegenService, ArtifactService
from src.database.connection import get_database_manager, DatabaseManager


async def migrate_projects(service: ProjectService, dry_run: bool, db=None) -> Dict[str, Any]:
    """Migrate legacy project files using Motor directly."""
    legacy_dir = service.legacy_base_dir / "projects"
    if not legacy_dir.exists():
        return {"legacy_files": 0, "status": "directory not found"}

    project_files = list(legacy_dir.glob("*.json"))
    total_files = len(project_files)

    if dry_run:
        return {"legacy_files": total_files, "dry_run": True}

    migrated_count = 0
    skipped_count = 0
    for file in project_files:
        try:
            proj_data = json.loads(file.read_text())
            # Convert legacy data to MongoDB document format
            proj_doc = {
                "project_id": proj_data.get("project_id", str(uuid.uuid4())),
                "name": proj_data.get("name", "Unnamed Project"),
                "description": proj_data.get("description", ""),
                "tags": proj_data.get("tags", []),
                "test_suites": proj_data.get("test_suites", {}),
                "github_config": proj_data.get("github_config", {"enabled": False, "repo": None, "token": None}),
                "created_at": datetime.fromisoformat(proj_data["created_at"].replace("Z", "+00:00")) if "created_at" in proj_data and isinstance(proj_data["created_at"], str) else datetime.now(),
                "updated_at": datetime.fromisoformat(proj_data["updated_at"].replace("Z", "+00:00")) if "updated_at" in proj_data and isinstance(proj_data["updated_at"], str) else datetime.now()
            }
            if not dry_run and db is not None:
                # Check if document already exists
                existing = await db["projects"].find_one({"project_id": proj_doc["project_id"]})
                if existing:
                    logger.info(f"Skipped existing project file {file.name}")
                    skipped_count += 1
                    continue
                await db["projects"].insert_one(proj_doc)
                migrated_count += 1
                logger.info(f"Migrated project file {file.name}")
        except Exception as e:
            logger.error(f"Failed to migrate project file {file.name}: {e}")

    return {"total_found": total_files, "migrated": migrated_count, "skipped": skipped_count}


async def migrate_executions(service: ExecutionService, dry_run: bool) -> Dict[str, Any]:
    if dry_run:
        legacy_files = len(list((service.legacy_exec_dir).glob("execution_*.json")))
        return {"legacy_files": legacy_files, "dry_run": True}
    return (await service.migrate_legacy_executions()).data


async def migrate_sessions(service: CodegenService, dry_run: bool, db=None) -> Dict[str, Any]:
    """Migrate legacy codegen session files using Motor directly."""
    if not service.legacy_dir.exists():
        return {"legacy_files": 0, "status": "directory not found"}
        
    # Find all .json files, excluding history files
    session_files = [f for f in service.legacy_dir.glob("*.json") if "history" not in f.name]
    total_files = len(session_files)

    if dry_run:
        return {"legacy_files": total_files, "dry_run": True}

    migrated_count = 0
    skipped_count = 0
    for file in session_files:
        try:
            sess_data = json.loads(file.read_text())
            # Convert legacy data to MongoDB document format
            sess_doc = {
                "session_id": sess_data.get("session_id", str(uuid.uuid4())),
                "status": sess_data.get("status", "pending"),
                "target_language": sess_data.get("target_language", "javascript"),
                "url": sess_data.get("url") or sess_data.get("target_url"),
                "created_at": datetime.fromisoformat(sess_data["created_at"].replace("Z", "+00:00")) if "created_at" in sess_data and isinstance(sess_data["created_at"], str) else datetime.now(),
                "updated_at": datetime.fromisoformat(sess_data["updated_at"].replace("Z", "+00:00")) if "updated_at" in sess_data and isinstance(sess_data["updated_at"], str) else datetime.now(),
                "completed_at": datetime.fromisoformat(sess_data["completed_at"].replace("Z", "+00:00")) if "completed_at" in sess_data and sess_data["completed_at"] and isinstance(sess_data["completed_at"], str) else None,
                "command_used": sess_data.get("command_used"),
                "error_message": sess_data.get("error_message"),
                "generated_code": sess_data.get("generated_code"),
                "project_integration": sess_data.get("project_integration"),
                "artifacts_path": sess_data.get("artifacts_path"),
                "browser_config": sess_data.get("browser_config"),
                "execution_count": sess_data.get("execution_count", 0),
                "metadata": sess_data
            }
            if not dry_run and db is not None:
                # Check if document already exists
                existing = await db["codegen_sessions"].find_one({"session_id": sess_doc["session_id"]})
                if existing:
                    logger.info(f"Skipped existing session file {file.name}")
                    skipped_count += 1
                    continue
                await db["codegen_sessions"].insert_one(sess_doc)
                migrated_count += 1
                logger.info(f"Migrated session file {file.name}")
        except Exception as e:
            logger.error(f"Failed to migrate session file {file.name}: {e}")
            
    return {"total_found": total_files, "migrated": migrated_count, "skipped": skipped_count}


async def migrate_artifacts(service: ArtifactService, dry_run: bool) -> Dict[str, Any]:
    if dry_run:
        legacy_files = len(list(service.legacy_dir.rglob("*.*")))
        return {"legacy_files": legacy_files, "dry_run": True}
    # Artifact migration could be huge, implement later
    return {"skipped": True}


def async_command(f):
    """Decorator to run an async function with click."""
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper


@click.command()
@click.option("--dry-run", is_flag=True, help="Only show what would be migrated, do not insert in DB.")
@async_command
async def main(dry_run: bool):
    logger.info("Starting migration (dry_run=%s)", dry_run)

    # Ensure DB connection
    db_manager: DatabaseManager = get_database_manager()
    connected = await db_manager.connect()
    if not connected:
        logger.error("Cannot connect to MongoDB. Check MONGODB_URI env variable.")
        sys.exit(1)

    # Use Motor directly instead of Beanie
    db = db_manager.database
    logger.info("Using Motor directly for database operations")

    # Services
    project_service = ProjectService()
    execution_service = ExecutionService()
    codegen_service = CodegenService()
    artifact_service = ArtifactService()

    results = {
        "projects": await migrate_projects(project_service, dry_run, db),
        "executions": await migrate_executions(execution_service, dry_run),
        "sessions": await migrate_sessions(codegen_service, dry_run, db),
        "artifacts": await migrate_artifacts(artifact_service, dry_run),
    }

    logger.info("Migration results:\n%s", results)
    await db_manager.disconnect()


if __name__ == "__main__":
    main() 