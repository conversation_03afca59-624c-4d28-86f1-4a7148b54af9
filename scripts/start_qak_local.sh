#!/bin/bash

# Start QAK System Locally (No Docker)
# This script starts <PERSON><PERSON>, Celery worker, and QAK API in background

set -e

echo "🚀 Starting QAK System Locally..."

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "❌ Virtual environment not found. Please run 'python -m venv .venv' first."
    exit 1
fi

# Activate virtual environment
echo "📦 Activating virtual environment..."
source .venv/bin/activate

# Check if Redis is installed
if ! command -v redis-server &> /dev/null; then
    echo "❌ Redis not installed. Please install with:"
    echo "   macOS: brew install redis"
    echo "   Ubuntu: sudo apt-get install redis-server"
    exit 1
fi

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Start Redis if not running
if ! check_port 6379; then
    echo "🔴 Starting Redis server..."
    redis-server --daemonize yes --port 6379
    sleep 2
    
    if check_port 6379; then
        echo "✅ Redis started on port 6379"
    else
        echo "❌ Failed to start Redis"
        exit 1
    fi
else
    echo "✅ Redis already running on port 6379"
fi

# Test Redis connection
if redis-cli ping >/dev/null 2>&1; then
    echo "✅ Redis connection test successful"
else
    echo "❌ Redis connection failed"
    exit 1
fi

# Start Celery worker in background
echo "🔧 Starting Celery worker..."
python celery_worker.py > celery_worker.log 2>&1 &
CELERY_PID=$!
echo "✅ Celery worker started with PID: $CELERY_PID"

# Give worker time to start
sleep 3

# Test worker
echo "🧪 Testing Celery worker..."
if celery -A src.core.background_jobs.celery_app inspect ping >/dev/null 2>&1; then
    echo "✅ Celery worker is responding"
else
    echo "⚠️ Celery worker may not be fully ready yet"
fi

# Start QAK API in background
echo "🌐 Starting QAK API server..."
python app.py > qak_api.log 2>&1 &
API_PID=$!
echo "✅ QAK API started with PID: $API_PID"

# Give API time to start
sleep 5

# Test API
if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    echo "✅ QAK API is responding"
else
    echo "⚠️ QAK API may not be fully ready yet"
fi

# Test background jobs
echo "🧪 Testing background jobs system..."
if curl -s http://localhost:8000/api/v2/background-jobs/health >/dev/null 2>&1; then
    echo "✅ Background jobs system is ready"
else
    echo "⚠️ Background jobs system may not be ready"
fi

echo ""
echo "🎉 QAK System Started Successfully!"
echo ""
echo "📊 Service Status:"
echo "   Redis:        http://localhost:6379 (PID: $(pgrep redis-server || echo 'N/A'))"
echo "   Celery Worker: PID $CELERY_PID (logs: celery_worker.log)"
echo "   QAK API:      http://localhost:8000 (PID $API_PID, logs: qak_api.log)"
echo ""
echo "🔗 Quick Links:"
echo "   API Health:    curl http://localhost:8000/health"
echo "   BG Jobs Health: curl http://localhost:8000/api/v2/background-jobs/health"
echo "   API Docs:      http://localhost:8000/docs"
echo ""
echo "📝 Log Files:"
echo "   Celery Worker: tail -f celery_worker.log"
echo "   QAK API:       tail -f qak_api.log"
echo ""
echo "🛑 To stop services:"
echo "   kill $CELERY_PID $API_PID"
echo "   redis-cli shutdown"
echo ""

# Save PIDs for easy stopping
cat > .qak_pids << EOF
CELERY_PID=$CELERY_PID
API_PID=$API_PID
EOF

echo "💾 Process IDs saved to .qak_pids"
echo ""
echo "🔄 System is ready for background job processing!"