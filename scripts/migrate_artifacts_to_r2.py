#!/usr/bin/env python3
"""
Migration Script: Move QAK Artifacts to Cloudflare R2

This script migrates existing local artifacts to Cloudflare R2 storage.
It preserves all metadata and provides detailed progress reporting.

Usage:
    python scripts/migrate_artifacts_to_r2.py [OPTIONS]

Environment Variables Required:
    R2_BUCKET_NAME, R2_ACCOUNT_ID, R2_ACCESS_KEY_ID, R2_SECRET_ACCESS_KEY
"""

import asyncio
import argparse
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.enhanced_artifact_collector import EnhancedArtifactCollector
from src.services.artifact_service import ArtifactService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration_to_r2.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def check_r2_credentials():
    """Check if R2 credentials are properly configured."""
    required_vars = ['R2_BUCKET_NAME', 'R2_ACCOUNT_ID', 'R2_ACCESS_KEY_ID', 'R2_SECRET_ACCESS_KEY']
    missing = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing.append(var)
    
    if missing:
        logger.error(f"Missing required environment variables: {missing}")
        logger.error("Please set these variables or use a .env file")
        return False
    
    logger.info("✅ R2 credentials configured")
    return True


async def scan_local_artifacts(artifacts_dir: Path) -> List[Dict]:
    """Scan local artifacts directory and return file information."""
    artifacts = []
    
    if not artifacts_dir.exists():
        logger.warning(f"Artifacts directory not found: {artifacts_dir}")
        return artifacts
    
    logger.info(f"Scanning artifacts in: {artifacts_dir}")
    
    # Scan for artifact files
    for file_path in artifacts_dir.rglob("*"):
        if file_path.is_file() and not file_path.name.startswith('.'):
            try:
                # Extract metadata from path structure
                # Expected: artifacts/YYYY/MM/DD/execution_id/ArtifactType/filename
                path_parts = file_path.relative_to(artifacts_dir).parts
                
                if len(path_parts) >= 5:
                    year, month, day, execution_id, artifact_type = path_parts[:5]
                    filename = path_parts[-1]
                    
                    artifact_info = {
                        'file_path': str(file_path),
                        'relative_path': str(file_path.relative_to(artifacts_dir)),
                        'filename': filename,
                        'size_bytes': file_path.stat().st_size,
                        'execution_id': execution_id,
                        'artifact_type': artifact_type,
                        'date_path': f"{year}/{month}/{day}",
                        'created_time': datetime.fromtimestamp(file_path.stat().st_ctime)
                    }
                    artifacts.append(artifact_info)
                
            except Exception as e:
                logger.warning(f"Failed to process file {file_path}: {e}")
    
    logger.info(f"Found {len(artifacts)} artifact files")
    return artifacts


async def migrate_artifacts(
    artifacts_dir: Path,
    dry_run: bool = True,
    max_concurrent: int = 5,
    size_limit_mb: Optional[int] = None
):
    """Migrate artifacts to Cloudflare R2."""
    
    logger.info("🚀 Starting artifact migration to Cloudflare R2")
    logger.info(f"Dry run: {dry_run}")
    logger.info(f"Max concurrent uploads: {max_concurrent}")
    if size_limit_mb:
        logger.info(f"Size limit: {size_limit_mb} MB")
    
    # Scan local artifacts
    local_artifacts = await scan_local_artifacts(artifacts_dir)
    
    if not local_artifacts:
        logger.info("No artifacts found to migrate")
        return
    
    # Filter by size if specified
    if size_limit_mb:
        size_limit_bytes = size_limit_mb * 1024 * 1024
        filtered_artifacts = [a for a in local_artifacts if a['size_bytes'] <= size_limit_bytes]
        skipped = len(local_artifacts) - len(filtered_artifacts)
        if skipped > 0:
            logger.info(f"Skipping {skipped} artifacts larger than {size_limit_mb} MB")
        local_artifacts = filtered_artifacts
    
    # Calculate total size
    total_size_bytes = sum(a['size_bytes'] for a in local_artifacts)
    total_size_mb = total_size_bytes / (1024 * 1024)
    logger.info(f"Total size to migrate: {total_size_mb:.2f} MB ({len(local_artifacts)} files)")
    
    if dry_run:
        logger.info("🔍 DRY RUN - No files will be uploaded")
        
        # Group by artifact type
        by_type = {}
        for artifact in local_artifacts:
            artifact_type = artifact['artifact_type']
            if artifact_type not in by_type:
                by_type[artifact_type] = {'count': 0, 'size_bytes': 0}
            by_type[artifact_type]['count'] += 1
            by_type[artifact_type]['size_bytes'] += artifact['size_bytes']
        
        logger.info("📊 Migration summary by artifact type:")
        for artifact_type, stats in by_type.items():
            size_mb = stats['size_bytes'] / (1024 * 1024)
            logger.info(f"  {artifact_type}: {stats['count']} files, {size_mb:.2f} MB")
        
        return
    
    # Initialize enhanced collector with R2
    collector = EnhancedArtifactCollector(
        base_storage_path=str(artifacts_dir),
        blob_storage_backend="r2",
        auto_migrate_to_cloud=False  # We'll migrate manually
    )
    
    try:
        await collector.initialize()
        
        # Test R2 connection
        if not collector.blob_backend:
            logger.error("Failed to initialize R2 backend")
            return
        
        logger.info("✅ Connected to Cloudflare R2")
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(max_concurrent)
        
        # Migration statistics
        stats = {
            'total': len(local_artifacts),
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'size_uploaded': 0
        }
        
        async def migrate_single_artifact(artifact_info: Dict) -> bool:
            """Migrate a single artifact to R2."""
            async with semaphore:
                try:
                    file_path = Path(artifact_info['file_path'])
                    
                    # Read file content
                    with open(file_path, 'rb') as f:
                        content = f.read()
                    
                    # Generate blob key
                    blob_key = f"artifacts/{artifact_info['date_path']}/{artifact_info['execution_id'][:8]}/{artifact_info['artifact_type']}/{artifact_info['filename']}"
                    
                    # Prepare metadata
                    metadata = {
                        'execution_id': artifact_info['execution_id'],
                        'artifact_type': artifact_info['artifact_type'],
                        'original_name': artifact_info['filename'],
                        'local_path': artifact_info['relative_path'],
                        'migrated_at': datetime.now().isoformat(),
                        'migration_source': 'local_filesystem'
                    }
                    
                    # Upload to R2
                    blob_url, blob_key_returned = await collector.blob_backend.upload_artifact(
                        file_path=blob_key,
                        content=content,
                        content_type="application/octet-stream",
                        metadata=metadata
                    )
                    
                    stats['successful'] += 1
                    stats['size_uploaded'] += len(content)
                    
                    logger.info(f"✅ Migrated: {artifact_info['relative_path']} -> R2")
                    return True
                    
                except Exception as e:
                    logger.error(f"❌ Failed to migrate {artifact_info['relative_path']}: {e}")
                    stats['failed'] += 1
                    return False
        
        # Execute migrations in batches
        logger.info(f"🔄 Starting migration of {len(local_artifacts)} artifacts...")
        
        # Create tasks for all migrations
        tasks = [migrate_single_artifact(artifact) for artifact in local_artifacts]
        
        # Execute with progress reporting
        completed = 0
        for i in range(0, len(tasks), max_concurrent):
            batch = tasks[i:i + max_concurrent]
            results = await asyncio.gather(*batch, return_exceptions=True)
            completed += len(batch)
            
            progress = (completed / len(tasks)) * 100
            logger.info(f"Progress: {progress:.1f}% ({completed}/{len(tasks)})")
        
        # Final statistics
        logger.info("🎯 Migration completed!")
        logger.info(f"Total artifacts: {stats['total']}")
        logger.info(f"Successful: {stats['successful']}")
        logger.info(f"Failed: {stats['failed']}")
        logger.info(f"Size uploaded: {stats['size_uploaded'] / (1024 * 1024):.2f} MB")
        
        if stats['failed'] > 0:
            logger.warning(f"⚠️  {stats['failed']} artifacts failed to migrate. Check the logs above.")
        
    finally:
        await collector.shutdown()


async def verify_migration(artifacts_dir: Path, sample_size: int = 10):
    """Verify that migrated artifacts are accessible in R2."""
    logger.info(f"🔍 Verifying migration (sampling {sample_size} artifacts)")
    
    # Initialize collector
    collector = EnhancedArtifactCollector(
        base_storage_path=str(artifacts_dir),
        blob_storage_backend="r2"
    )
    
    try:
        await collector.initialize()
        
        if not collector.blob_backend:
            logger.error("Failed to initialize R2 backend for verification")
            return
        
        # List artifacts in R2
        r2_artifacts = await collector.blob_backend.list_artifacts()
        logger.info(f"Found {len(r2_artifacts)} artifacts in R2")
        
        # Sample verification
        sample_artifacts = r2_artifacts[:sample_size]
        verified = 0
        
        for artifact in sample_artifacts:
            try:
                # Try to get metadata
                metadata = await collector.blob_backend.get_artifact_metadata(artifact['key'])
                if metadata:
                    verified += 1
                    logger.info(f"✅ Verified: {artifact['key']}")
                else:
                    logger.warning(f"❌ Failed to get metadata for: {artifact['key']}")
            except Exception as e:
                logger.error(f"❌ Error verifying {artifact['key']}: {e}")
        
        logger.info(f"Verification: {verified}/{len(sample_artifacts)} artifacts verified")
        
    finally:
        await collector.shutdown()


def main():
    parser = argparse.ArgumentParser(description="Migrate QAK artifacts to Cloudflare R2")
    parser.add_argument(
        "--artifacts-dir",
        type=Path,
        default=Path("artifacts"),
        help="Path to artifacts directory (default: artifacts)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be migrated without actually uploading"
    )
    parser.add_argument(
        "--max-concurrent",
        type=int,
        default=5,
        help="Maximum concurrent uploads (default: 5)"
    )
    parser.add_argument(
        "--size-limit-mb",
        type=int,
        help="Skip files larger than this size in MB"
    )
    parser.add_argument(
        "--verify",
        action="store_true",
        help="Verify migration by sampling artifacts in R2"
    )
    
    args = parser.parse_args()
    
    # Check credentials
    if not check_r2_credentials():
        sys.exit(1)
    
    # Run migration or verification
    try:
        if args.verify:
            asyncio.run(verify_migration(args.artifacts_dir))
        else:
            asyncio.run(migrate_artifacts(
                artifacts_dir=args.artifacts_dir,
                dry_run=args.dry_run,
                max_concurrent=args.max_concurrent,
                size_limit_mb=args.size_limit_mb
            ))
    except KeyboardInterrupt:
        logger.info("Migration interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 