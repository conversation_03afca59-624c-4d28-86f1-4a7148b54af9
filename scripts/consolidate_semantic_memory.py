#!/usr/bin/env python3
"""
MongoDB-based Semantic Memory Consolidator for QAK

This script runs the semantic memory consolidation process directly against
the MongoDB database. It fetches all recent interactions from all agents,
learns new patterns, and saves them back to the database, acting as the
centralized learning process for the entire system.

Usage:
    python scripts/consolidate_semantic_memory.py [options]
"""

import asyncio
import logging
import argparse
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import Counter
import uuid

# Setup project path
from pathlib import Path
import sys
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

from src.database.connection import initialize_database, shutdown_database
from src.database.repositories import SemanticPatternRepository, SemanticInteractionRepository
from src.database.models import SemanticPattern as SemanticPatternDoc, PatternContext
from libs.browser_use.agent.semantic_memory import SemanticMemory, InteractionMemory # Re-use logic

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseConsolidator:
    """Consolidates semantic memory by learning patterns from all agent interactions in the DB."""
    
    def __init__(self, pattern_repo: SemanticPatternRepository, interaction_repo: SemanticInteractionRepository):
        self.pattern_repo = pattern_repo
        self.interaction_repo = interaction_repo
        # We can reuse the pattern extraction logic from SemanticMemory by creating a temporary instance
        self.logic_provider = SemanticMemory("consolidator", pattern_repo, interaction_repo)
        
        self.stats = {
            'total_interactions_processed': 0,
            'new_patterns_learned': 0,
            'processing_time_seconds': 0.0,
        }

    async def consolidate(self, max_age_days: Optional[int] = 30) -> None:
        """Main consolidation method using MongoDB as the source and destination."""
        start_time = time.time()
        logger.info("🧠 Starting semantic memory consolidation process from MongoDB...")

        # 1. Fetch all recent interactions from the database
        cutoff_date = datetime.now() - timedelta(days=max_age_days) if max_age_days else None
        interactions_cursor = self.interaction_repo.get_all_interactions(since=cutoff_date)
        all_interactions_docs = await interactions_cursor.to_list(length=None) # Load all
        
        if not all_interactions_docs:
            logger.info("No recent interactions found to process. Consolidation finished.")
            return

        self.stats['total_interactions_processed'] = len(all_interactions_docs)
        logger.info(f"Loaded {self.stats['total_interactions_processed']} interactions for analysis.")

        # 2. Convert to in-memory representation for processing
        interactions = [
            InteractionMemory(
                timestamp=i.timestamp,
                url=i.url,
                action_type=i.action_type,
                target_element=i.target_element.element_data if i.target_element else {},
                context=i.context,
                success=i.success,
                result=i.result,
            ) for i in all_interactions_docs
        ]

        # 3. Group interactions to find potential patterns
        grouped_interactions = self.logic_provider._group_similar_interactions(interactions)
        
        # 4. Learn and save new patterns
        for group in grouped_interactions:
            if len(group) >= self.logic_provider.min_interactions_for_pattern:
                pattern = self.logic_provider._extract_pattern_from_group(group)
                
                if pattern and pattern.confidence_score >= self.logic_provider.min_success_rate_for_pattern:
                    pattern_doc = SemanticPatternDoc(
                        pattern_id=pattern.pattern_id,
                        pattern_type=pattern.pattern_type,
                        description=pattern.description,
                        context=PatternContext(**pattern.context),
                        success_indicators=pattern.success_indicators,
                        failure_indicators=pattern.failure_indicators,
                        confidence_score=pattern.confidence_score,
                        usage_count=pattern.usage_count,
                        last_used=pattern.last_used,
                    )
                    await self.pattern_repo.upsert_pattern(pattern_doc)
                    self.stats['new_patterns_learned'] += 1
        
        self.stats['processing_time_seconds'] = time.time() - start_time
        self._generate_report()
        logger.info(f"✅ Consolidation finished in {self.stats['processing_time_seconds']:.2f}s")

    def _generate_report(self):
        report = f"""
        ╔═══════════════════════════════════════════════════════════╗
        ║    MongoDB SEMANTIC MEMORY CONSOLIDATION REPORT           ║
        ╠═══════════════════════════════════════════════════════════╣
        ║ Processing Time: {self.stats['processing_time_seconds']:.2f} seconds                             ║
        ║ Interactions Processed: {self.stats['total_interactions_processed']}                             ║
        ║ New Patterns Learned: {self.stats['new_patterns_learned']}                               ║
        ╚═══════════════════════════════════════════════════════════╝
        """
        logger.info(report)

async def main():
    parser = argparse.ArgumentParser(
        description="Run semantic memory consolidation from MongoDB."
    )
    parser.add_argument(
        '--max-age-days', 
        type=int, 
        default=30,
        help='Maximum age of interactions to consider for learning.'
    )
    args = parser.parse_args()

    await initialize_database()
    
    pattern_repo = SemanticPatternRepository()
    interaction_repo = SemanticInteractionRepository()
    
    consolidator = DatabaseConsolidator(pattern_repo, interaction_repo)
    
    try:
        await consolidator.consolidate(max_age_days=args.max_age_days)
    finally:
        await shutdown_database()

if __name__ == "__main__":
    asyncio.run(main()) 