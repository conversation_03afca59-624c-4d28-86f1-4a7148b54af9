import argparse
import json
import os
import sys
from pathlib import Path

# Add project root to sys.path to allow imports from libs
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from libs.browser_use.monitoring.efficiency_dashboard import EfficiencyDashboard

DEFAULT_METRICS_FILE = "efficiency_metrics.json"


def generate_recommendations(report: dict) -> list[str]:
    """Genera recomendaciones basadas en el reporte de eficiencia."""
    recommendations = []
    if not report or "tasks" not in report:
        return ["No data to generate recommendations."]

    for task_name, task_data in report["tasks"].items():
        if task_data.get("success_rate", 100) < 75:
            recommendations.append(
                f"Task '{task_name}' has a low success rate ({task_data['success_rate']:.2f}%). "
                f"Review its execution logs to identify common failure points."
            )

        avg_time = task_data.get("average_time_on_success_seconds", 0)
        if isinstance(avg_time, (int, float)) and avg_time > 60:
            recommendations.append(
                f"Task '{task_name}' is taking a long time to complete on average ({avg_time:.2f}s). "
                f"Consider optimizing by grouping actions or using more efficient selectors."
            )
            
        avg_steps = task_data.get("average_steps_on_success", 0)
        if isinstance(avg_steps, (int, float)) and avg_steps > 15:
            recommendations.append(
                f"Task '{task_name}' requires many steps on average ({avg_steps:.2f}). "
                f"Look for opportunities to use higher-level actions or patterns."
            )

    if not recommendations:
        recommendations.append("Overall efficiency looks good. No immediate recommendations.")

    return recommendations


def main():
    parser = argparse.ArgumentParser(description="Generate an efficiency report from agent metrics.")
    parser.add_argument(
        "--metrics-file",
        type=str,
        default=DEFAULT_METRICS_FILE,
        help=f"Path to the metrics JSON file (default: {DEFAULT_METRICS_FILE})",
    )
    args = parser.parse_args()

    if not os.path.exists(args.metrics_file):
        print(f"Error: Metrics file not found at '{args.metrics_file}'")
        sys.exit(1)

    dashboard = EfficiencyDashboard(metrics_file_path=args.metrics_file)
    report = dashboard.generate_efficiency_report()

    print("--- Efficiency Report ---")
    print(json.dumps(report, indent=2))
    
    print("\n--- Recommendations ---")
    recommendations = generate_recommendations(report)
    for rec in recommendations:
        print(f"- {rec}")


if __name__ == "__main__":
    main() 