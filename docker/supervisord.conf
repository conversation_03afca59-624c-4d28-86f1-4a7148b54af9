[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
stdout_logfile=/var/log/supervisor/nginx.log
stderr_logfile=/var/log/supervisor/nginx.log
autorestart=true

[program:backend]
command=uvicorn src.api.api:app --host 0.0.0.0 --port 8000 --log-level info
directory=/app
stdout_logfile=/var/log/supervisor/backend.log
stderr_logfile=/var/log/supervisor/backend.log
autorestart=true
environment=PYTHONPATH="/app:/app/src"

[program:frontend]
command=node server.js
directory=/app/web/.next/standalone
stdout_logfile=/var/log/supervisor/frontend.log
stderr_logfile=/var/log/supervisor/frontend.log
autorestart=true
environment=PORT=3000,HOSTNAME=0.0.0.0 