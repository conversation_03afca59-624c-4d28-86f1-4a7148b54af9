#!/bin/bash

# Script de inicio para contenedor Docker con VNC completo
# Configura automáticamente todos los servicios VNC necesarios

set -e

echo "🚀 Iniciando QAK con soporte VNC completo..."

# Configurar variables de entorno
export DISPLAY=:0
export QAK_VNC_ENABLED=true
export QAK_FORCE_VNC=true

# Función para limpiar procesos al salir
cleanup() {
    echo "🧹 Limpiando procesos VNC..."
    pkill -f x11vnc || true
    pkill -f Xvfb || true
    pkill -f websockify || true
    pkill -f fluxbox || true
    exit 0
}

# Configurar trap para limpieza
trap cleanup SIGTERM SIGINT

# Verificar dependencias críticas
echo "� Verificando dependencias VNC..."

if ! command -v Xvfb &> /dev/null; then
    echo "❌ Error: Xvfb no encontrado"
    exit 1
fi

if ! command -v x11vnc &> /dev/null; then
    echo "❌ Error: x11vnc no encontrado"
    exit 1
fi

if ! command -v websockify &> /dev/null; then
    echo "❌ Error: websockify no encontrado"
    exit 1
fi

if ! command -v fluxbox &> /dev/null; then
    echo "❌ Error: fluxbox no encontrado"
    exit 1
fi

echo "✅ Todas las dependencias VNC disponibles"

# Configurar directorio noVNC
if [ ! -d "/usr/share/novnc" ]; then
    echo "❌ Error: noVNC no encontrado en /usr/share/novnc"
    exit 1
fi

echo "✅ noVNC disponible en /usr/share/novnc"

# Iniciar Xvfb (X Virtual Framebuffer)
echo "🖥️ Iniciando Xvfb (display virtual)..."
Xvfb :0 -screen 0 1920x1080x24 -ac +extension GLX &
XVFB_PID=$!

# Esperar a que Xvfb se inicie
sleep 2

# Verificar que Xvfb esté corriendo
if ! kill -0 $XVFB_PID 2>/dev/null; then
    echo "❌ Error: Xvfb falló al iniciar"
    exit 1
fi

echo "✅ Xvfb iniciado exitosamente (PID: $XVFB_PID)"

# Iniciar window manager (fluxbox)
echo "🪟 Iniciando Fluxbox (window manager)..."
fluxbox &
FLUXBOX_PID=$!

# Esperar a que el window manager se inicie
sleep 1

# Iniciar servidor VNC (x11vnc)
echo "🔗 Iniciando servidor VNC..."
x11vnc -display :0 -rfbport 5900 -nopw -forever -shared -bg
VNC_PID=$!

# Esperar a que VNC se inicie
sleep 2

# Verificar que VNC esté corriendo
if ! netstat -tuln | grep :5900 &> /dev/null; then
    echo "❌ Error: Servidor VNC no está escuchando en puerto 5900"
    exit 1
fi

echo "✅ Servidor VNC iniciado en puerto 5900"

# Iniciar websockify (gateway web para VNC)
echo "🌐 Iniciando websockify (VNC web gateway)..."
websockify --web /usr/share/novnc 6080 localhost:5900 &
WEBSOCKIFY_PID=$!

# Esperar a que websockify se inicie
sleep 2

# Verificar que websockify esté corriendo
if ! netstat -tuln | grep :6080 &> /dev/null; then
    echo "❌ Error: websockify no está escuchando en puerto 6080"
    exit 1
fi

echo "✅ websockify iniciado en puerto 6080"

# Mostrar información de acceso
echo ""
echo "🎉 Servicios VNC iniciados exitosamente!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📡 Acceso VNC Directo:  vnc://localhost:5900"
echo "🌐 Acceso VNC Web:      http://localhost:6080"
echo "🖥️ Display Virtual:     :0 (1920x1080)"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Verificar si Playwright está disponible
echo "🎭 Verificando Playwright..."
if command -v playwright &> /dev/null; then
    playwright_version=$(playwright --version 2>/dev/null || echo "unknown")
    echo "✅ Playwright disponible: $playwright_version"
else
    echo "⚠️ Playwright no encontrado - se instalará automáticamente cuando sea necesario"
fi

# Iniciar servidor QAK
echo "🚀 Iniciando servidor QAK..."
echo "📍 Directorio de trabajo: $(pwd)"
echo "🐍 Python version: $(python3 --version)"

# Verificar archivo principal
if [ ! -f "app.py" ]; then
    echo "❌ Error: app.py no encontrado en $(pwd)"
    echo "📁 Contenido del directorio:"
    ls -la
    exit 1
fi

# Verificar requirements
if [ -f "requirements.txt" ]; then
    echo "📦 Verificando dependencias Python..."
    pip3 install -r requirements.txt
else
    echo "⚠️ requirements.txt no encontrado"
fi

echo ""
echo "🎯 Iniciando QAK Backend..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🌐 QAK Interface:       http://localhost:8000"
echo "🔧 API Health:          http://localhost:8000/api/health"
echo "📋 VNC Dependencies:    http://localhost:8000/api/codegen/vnc-dependencies"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Ejecutar aplicación QAK
exec python3 app.py
