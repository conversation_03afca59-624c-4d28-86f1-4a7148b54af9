# System Patterns: AgentQA - Automatización de Pruebas con IA

## System Architecture

AgentQA implementa una arquitectura modular multicapa que separa responsabilidades y facilita el mantenimiento y escalabilidad.

```mermaid
graph TD
    subgraph "Frontend Layer"
        WEB[Next.js Web Interface]
        CLI[Python CLI Interface]
        API_CLIENT[API Client Libraries]
    end

    subgraph "API Layer"
        FASTAPI[FastAPI Application]
        PROJECT_ROUTES[Project Routes]
        SUITE_ROUTES[Suite Routes]
        TESTCASE_ROUTES[TestCase Routes]
        PROMPT_ROUTES[Prompt Routes]
        CONFIG_ROUTES[Config Routes]
        TRANSLATION_ROUTES[Translation Routes]
    end

    subgraph "Service Layer"
        TEST_SERVICE[TestService Core]
        PROMPT_SERVICE[PromptService]
        STORY_AGENT[StoryAgent]
        BROWSER_AGENT[BrowserAutomationAgent]
        PROJECT_MANAGER[ProjectManagerService]
    end

    subgraph "Integration Layer"
        BROWSER_USE[browser-use Library]
        LANGCHAIN[LangChain Framework]
        AI_PROVIDERS[AI Providers: Gemini/OpenAI/Claude/Groq]
    end

    subgraph "Storage Layer"
        PROJECT_FILES[JSON Project Files]
        PROMPT_TEMPLATES[Markdown Prompt Templates]
        TEST_HISTORY[Test Execution History]
        SCREENSHOTS[Screenshot Storage]
    end

    WEB --> FASTAPI
    CLI --> TEST_SERVICE
    API_CLIENT --> FASTAPI
    
    FASTAPI --> PROJECT_ROUTES
    FASTAPI --> SUITE_ROUTES
    FASTAPI --> TESTCASE_ROUTES
    FASTAPI --> PROMPT_ROUTES
    FASTAPI --> CONFIG_ROUTES
    FASTAPI --> TRANSLATION_ROUTES
    
    PROJECT_ROUTES --> TEST_SERVICE
    SUITE_ROUTES --> TEST_SERVICE
    TESTCASE_ROUTES --> TEST_SERVICE
    PROMPT_ROUTES --> PROMPT_SERVICE
    
    TEST_SERVICE --> STORY_AGENT
    TEST_SERVICE --> BROWSER_AGENT
    TEST_SERVICE --> PROJECT_MANAGER
    
    STORY_AGENT --> LANGCHAIN
    BROWSER_AGENT --> BROWSER_USE
    BROWSER_USE --> AI_PROVIDERS
    LANGCHAIN --> AI_PROVIDERS
    
    PROJECT_MANAGER --> PROJECT_FILES
    BROWSER_AGENT --> TEST_HISTORY
    BROWSER_AGENT --> SCREENSHOTS
    PROMPT_SERVICE --> PROMPT_TEMPLATES

    style FASTAPI fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style TEST_SERVICE fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style BROWSER_USE fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style AI_PROVIDERS fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
```

## Key Technical Decisions

### **1. Arquitectura Modular por Capas**
- **API Layer**: Endpoints REST organizados por dominio (projects, suites, testcases)
- **Service Layer**: Lógica de negocio centralizada en servicios especializados
- **Integration Layer**: Abstracción de integraciones externas (IA, browser automation)
- **Storage Layer**: Persistencia basada en archivos JSON para simplicidad

### **2. Patrón Agent-Based Architecture**
- **StoryAgent**: Especializado en procesamiento de historias de usuario y generación de casos de prueba
- **BrowserAutomationAgent**: Dedicado a la ejecución de pruebas en navegadores reales
- **Separación de responsabilidades**: Cada agente tiene un dominio específico

### **3. Pipeline-Driven Workflow**
- **Full Pipeline**: User Story → Enhanced Story → Manual Tests → Gherkin → Code → Execution
- **Smoke Test Pipeline**: Direct description → Immediate execution
- **Transformación progresiva**: Cada etapa agrega valor y estructura

### **4. Multi-Provider AI Strategy**
- **Primary**: Google Gemini (GOOGLE_API_KEY)
- **Fallbacks**: OpenAI, Anthropic Claude, Groq
- **Configuración dinámica**: Permite cambiar proveedores sin modificar código

### **5. Framework-Agnostic Code Generation**
- **Soporta múltiples frameworks**: Selenium, Playwright, Cypress, Robot Framework
- **Templates dinámicos**: Generación basada en prompts estructurados
- **Extensibilidad**: Fácil agregar nuevos frameworks

## 2. Frontend Architecture (Next.js)

### **Component Structure**
```
web/src/
├── app/                     # App Router pages
│   ├── (dashboard)/         # Dashboard layout group
│   │   ├── page.tsx         # Main dashboard
│   │   ├── projects/        # Projects management
│   │   ├── qa-assistant/    # AI-powered QA tools
│   │   ├── smoke-test-playground/  # Test execution
│   │   ├── ai-tools/        # AI utilities
│   │   ├── prompts/         # Prompt management
│   │   └── settings/        # Configuration & browser setup
│   ├── globals.css          # Global styles
│   ├── layout.tsx           # Root layout
│   └── page.tsx             # Home redirect
├── components/              # Reusable UI components
│   ├── ui/                  # shadcn/ui base components
│   ├── sidebar.tsx          # Navigation sidebar
│   ├── project-form.tsx     # Project management
│   ├── browser-config.tsx   # Browser configuration
│   ├── prompt-editor.tsx    # Prompt editing interface
│   └── test-execution.tsx   # Test running interface
├── lib/                     # Utilities and services
│   ├── api.ts               # API client (fetch wrapper)
│   ├── utils.ts             # Helper functions
│   └── types.ts             # TypeScript definitions
└── hooks/                   # Custom React hooks
    └── use-api.ts           # TanStack Query hooks
```

### **Frontend Features Implemented**
*   **Complete Dashboard:** Main interface with navigation sidebar
*   **Project Management:** Full CRUD for test projects
*   **QA Assistant:** AI-powered tools for test generation
*   **Smoke Test Playground:** Interactive test execution interface
*   **Browser Configuration Manager:** Advanced browser settings
*   **Prompts Management:** Editor with ES↔EN translation
*   **AI Tools:** Collection of AI-powered utilities
*   **Settings:** Configuration panels and preferences

### **Design Patterns Used**
*   **App Router:** Next.js 15 App Router with layout groups
*   **Component Composition:** Reusable UI components with shadcn/ui
*   **Server Components:** Where appropriate for performance
*   **Client Components:** For interactive functionality
*   **Form Validation:** React Hook Form + Zod schemas
*   **State Management:** TanStack Query for server state
*   **Responsive Design:** Tailwind CSS with mobile-first approach
*   **API Integration:** Custom hooks with React Query for caching

## Design Patterns

### **1. Repository Pattern**
```python
# ProjectManagerService actúa como repository para proyectos
class ProjectManagerService:
    def get_project(self, project_id: str) -> Dict
    def create_project(self, project_data: Dict) -> Dict
    def update_project(self, project_id: str, updates: Dict) -> Dict
```

### **2. Service Layer Pattern**
```python
# TestService centraliza lógica de negocio
class TestService:
    def execute_test_case(self, project_id: str, suite_id: str, test_id: str) -> Dict
    def generate_code(self, framework: str, gherkin: str) -> str
```

### **3. Strategy Pattern**
```python
# Diferentes estrategias de configuración de browser
class BrowserConfigurations:
    @staticmethod
    def get_fast_config() -> BrowserHelperConfig
    def get_robust_config() -> BrowserHelperConfig
    def get_secure_config() -> BrowserHelperConfig
```

### **4. Template Method Pattern**
```python
# Pipeline de ejecución con pasos definidos
async def execute_full_pipeline(user_story: str):
    enhanced = await enhance_story(user_story)
    manual_tests = await generate_manual_tests(enhanced)
    gherkin = await generate_gherkin(manual_tests)
    code = await generate_code(gherkin)
    result = await execute_test(code)
    return result
```

### **5. Factory Pattern**
```python
# Factory para crear agentes según configuración
def create_agent(agent_type: str, config: Dict) -> Agent:
    if agent_type == "story":
        return StoryAgent(config)
    elif agent_type == "browser":
        return BrowserAutomationAgent(config)
```

### **6. Observer Pattern**
```python
# Notificaciones de estado de ejecución
class TestExecutionObserver:
    async def on_test_started(self, test_id: str)
    async def on_test_completed(self, test_id: str, result: Dict)
    async def on_test_failed(self, test_id: str, error: str)
```

## Component Relationships

### **Core Dependencies**
- **FastAPI** ← Routes ← Services ← Agents ← AI Providers
- **TestService** ← ProjectManager + StoryAgent + BrowserAgent
- **BrowserAgent** ← browser-use ← LangChain ← AI Models

### **Data Flow Patterns**
1. **Request Flow**: Frontend → API Routes → Services → Agents → AI/Browser
2. **Response Flow**: AI/Browser → Agents → Services → API Routes → Frontend
3. **Storage Flow**: Services → ProjectManager → JSON Files
4. **History Flow**: BrowserAgent → Test History → Screenshots

### **Configuration Hierarchy**
```
Environment Variables (.env)
    ↓
Default Configurations (BrowserConfigurations)
    ↓
Custom Configurations (JSON files)
    ↓
Runtime Configuration (API requests)
```

### **Error Handling Chain**
```
Browser Execution Errors → BrowserAgent
    ↓
Service Layer Validation → TestService
    ↓
API Error Responses → FastAPI Routes
    ↓
Frontend Error Display → React Components
```

### **Prompt Management**
```
Markdown Templates (prompts/) → PromptService
    ↓
Dynamic Prompt Generation → AI Agents
    ↓
Context-Aware Prompts → LangChain
    ↓
AI Model Responses → Structured Output
```

## Security & Performance Patterns

### **Security Measures**
- **API Key Management**: Secure storage in environment variables
- **CORS Configuration**: Controlled cross-origin access
- **Input Validation**: Pydantic models for request validation
- **Browser Security**: Configurable security settings for test environments

### **Performance Optimizations**
- **Async Operations**: FastAPI + async/await for concurrent execution
- **Connection Pooling**: Reuse of browser instances when possible
- **Caching Strategy**: Template caching for prompts
- **Resource Management**: Automatic cleanup of browser sessions

### **Scalability Considerations**
- **Modular Architecture**: Easy to scale individual components
- **Stateless Services**: Services can be replicated without state issues
- **File-based Storage**: Simple to backup and replicate
- **Configuration-driven**: Behavior configurable without code changes
