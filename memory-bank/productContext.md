# Product Context: AgentQA - Automatización de Pruebas con IA

## Why This Project Exists

AgentQA surge de la necesidad de democratizar y automatizar las pruebas de software mediante inteligencia artificial. En el mundo del desarrollo actual, las pruebas siguen siendo uno de los cuellos de botella más significativos, requiriendo expertise técnico específico y tiempo considerable para crear, mantener y ejecutar suites de pruebas efectivas.

El proyecto busca transformar el paradigma tradicional de testing, permitiendo que cualquier persona (incluyendo stakeholders no técnicos) pueda generar pruebas automatizadas utilizando lenguaje natural. Esto reduce la barrera de entrada al testing automatizado y acelera significativamente el ciclo de desarrollo.

## Problems It Solves

### 1. **Barrera Técnica en Automatización de Pruebas**
- **Problema:** Los QA testers y desarrolladores necesitan conocer múltiples frameworks (Se<PERSON>ium, Playwright, Cypress) y lenguajes de programación para crear pruebas automatizadas.
- **Solución:** AgentQA permite generar código de automatización a partir de historias de usuario escritas en lenguaje natural.

### 2. **Desconexión entre Requisitos y Pruebas**
- **Problema:** Las pruebas frecuentemente no reflejan los casos de uso reales porque se escriben después del desarrollo, sin contexto del comportamiento real del usuario.
- **Solución:** El sistema genera pruebas basadas en historias de usuario reales y puede ejecutarlas en navegadores reales para validar comportamientos.

### 3. **Mantenimiento Costoso de Suites de Pruebas**
- **Problema:** Las pruebas automatizadas requieren mantenimiento constante cuando cambia la UI o funcionalidad.
- **Solución:** Al generar pruebas desde descripciones en lenguaje natural, es más fácil regenerar y actualizar pruebas cuando hay cambios.

### 4. **Falta de Trazabilidad entre User Stories y Testing**
- **Problema:** Es difícil asegurar que todas las user stories tengan cobertura de pruebas y que las pruebas reflejen los requisitos reales.
- **Solución:** AgentQA mantiene la trazabilidad desde user story → enhanced story → manual tests → Gherkin → código automatizado → ejecución.

### 5. **Tiempo de Feedback Lento**
- **Problema:** Las pruebas manuales son lentas y las automatizadas requieren setup complejo.
- **Solución:** Ejecución rápida de "Smoke Tests" directos y "Full Tests" con pipeline completo, ambos ejecutados en navegadores reales.

## How It Should Work

### **Flujo Principal: Full Test Pipeline**
```
User Story → Enhanced Story → Manual Tests → Gherkin → Automation Code → Execution → Results
```

1. **Input de User Story:** El usuario proporciona una historia de usuario en lenguaje natural
2. **Enhancement con IA:** El sistema mejora la historia agregando contexto, casos edge y detalles técnicos
3. **Generación de Tests Manuales:** Convierte la historia mejorada en pasos de prueba manuales detallados
4. **Conversión a Gherkin:** Transforma los tests manuales a formato Gherkin (Given/When/Then)
5. **Generación de Código:** Produce código ejecutable en el framework seleccionado (Selenium, Playwright, Cypress, etc.)
6. **Ejecución Automatizada:** Ejecuta las pruebas en navegador real usando browser-use
7. **Captura de Resultados:** Documenta screenshots, errores, y métricas de ejecución

### **Flujo Rápido: Smoke Test**
```
Test Description → Direct Execution → Results
```
- Ejecución directa sin pasos intermedios para validaciones rápidas
- Ideal para verificaciones puntuales y regression testing

### **Características Clave del Funcionamiento:**

#### **Multilenguaje y Traducción Inteligente**
- Soporte para inglés y español
- Las instrucciones de ejecución permanecen en inglés para compatibilidad con browser-use
- Las respuestas se traducen al idioma preferido del usuario

#### **Múltiples Frameworks de Salida**
- Selenium + PyTest (Python)
- Playwright (Python/JavaScript)
- Cypress (JavaScript)
- Robot Framework
- Selenium + Cucumber (Java)

#### **Gestión de Proyectos**
- Organización en proyectos con múltiples suites de pruebas
- Cada suite puede contener múltiples casos de prueba
- Historial completo de ejecuciones con screenshots y métricas

#### **Interfaces Múltiples**
- **Web UI (Next.js):** Interfaz principal para usuarios finales
- **API REST:** Para integraciones con CI/CD y herramientas externas
- **CLI:** Para automatización y uso en scripts

#### **Ejecución en Navegador Real**
- Usa browser-use para ejecutar pruebas en navegadores reales (no headless)
- Captura screenshots automáticamente durante la ejecución
- Permite ver el comportamiento real de la aplicación

### **Value Proposition:**
"Transforma historias de usuario en código de automatización ejecutable, eliminando la barrera técnica entre requisitos de negocio y testing automatizado, mientras mantiene la trazabilidad completa del proceso."

