# Plan de Mejora de Visualización y Procesamiento de Steps

## 1. Contexto y Problemas Actuales
- **Visibilidad limitada**: La captura muestra un listado plano de resultados (`is_done`, `success`, `error`, etc.) difícil de escanear rápidamente.
- **Ausencia de validación semántica**: No existe un mecanismo automático que confirme si cada *step* cumplió realmente lo prometido.
- **Carga cognitiva**: El usuario final debe leer campos técnicos para entender qué ocurrió.

## 2. Objetivos
1. Preprocesar los pasos antes de persistirlos para garantizar estructura y metadatos consistentes.
2. Posprocesar ejecuciones con un LLM para:
   - Resumir pasos.
   - Verificar cumplimiento de la intención declarada.
   - Generar explicaciones amigables.
3. Rediseñar la interfaz para que el usuario final comprenda fácilmente el flujo y el estado de cada paso.

## 3. Arquitectura Propuesta
```mermaid
graph LR
A[Captura de Step en tiempo real] --> B[Preprocesador]\n(Normaliza + valida)
B --> C[Persistencia RAW]
C --> D[Pipeline LLM]\n(Validación + Enriquecimiento)
D --> E[Persistencia Enriquecida]
E --> F[API de Consultas]
F --> G[Frontend Mejorado]
```

### 3.1 Preprocesamiento
- Normalizar campos (`timestamp`, `actor`, `action`, `target`, `result`).
- Detectar y etiquetar *steps* duplicados o redundantes.
- Agregar `trace_id` para correlación.

### 3.2 Pipeline LLM
- **Prompt**: "Analiza el siguiente paso, evalúa si se cumplió la intención y produce un resumen de una línea y un veredicto (success|warning|error)".
- **Modelo**: OpenAI GPT-4o o local Llama-3 70B con función de *function calling* para devolver JSON.
- **Salidas**:
  - `summary`: string
  - `compliance`: bool
  - `explanation`: string (max 150 char)
  - `confidence`: 0-1
- Almacenar en `steps_enriched` junto a la versión RAW.

## 4. Flujo de Datos (Backend)
1. **Collector** publica evento `step_captured`.
2. **Preprocessor Service** suscribe y transforma.
3. Guarda en `executions_raw` (Mongo/PostgreSQL JSONB).
4. Job asíncrono envía lotes a LLM.
5. Recibe respuesta y guarda en `executions_enriched`.
6. WebSocket / SSE notifica al Frontend para actualización en vivo.

## 5. Diseño de Experiencia de Usuario (Frontend)
### 5.1 Componentes Visuales
- **Timeline vertical** con iconos de estado:
  - 🟢 Éxito
  - 🟡 Advertencia
  - 🔴 Error
- **Tarjeta expandible** por step:
  - Header: resumen LLM + tiempo + estado.
  - Body colapsable: detalles RAW + explicación LLM.
- **Filtro/Buscador** por estado, actor, texto.
- **Indicadores de progreso** (barra horizontal).

### 5.2 Accesibilidad y Claridad
- Colores con contraste AA.
- Tooltips descriptivos.
- Mensajes en lenguaje no técnico.

## 6. Roadmap
| Fase | Hito | Duración |
|------|------|----------|
| 0 | Definir esquema de datos normalizado | 0.5 semana |
| 1 | Implementar Preprocessor Service | 1 semana |
| 2 | Integrar Pipeline LLM (MVP) | 1 semana |
| 3 | Diseñar y codificar componentes Timeline | 1.5 semanas |
| 4 | QA + Feedback con usuarios | 1 semana |
| 5 | Iteración y optimización de prompts | continuo |

## 7. Métricas de Éxito
- 🔹 **Time-to-Insight**: tiempo medio hasta que un usuario entiende la causa de un fallo (<30 s).
- 🔹 **Error Detection Rate**: % de errores captados automáticamente por LLM (>90 %).
- 🔹 **User Satisfaction (CSAT)**: >4/5.

## 8. Riesgos y Mitigaciones
| Riesgo | Impacto | Mitigación |
|--------|---------|------------|
| Costos de LLM | $$$ | Batch + cache resultados |
| Alucinaciones | Medio | `confidence` + fallback a reglas |
| Latencia de posprocesado | Alto | Procesar en paralelo y mostrar placeholder |

## 9. Detalle Técnico: ¿Dónde y qué cambiar?

### 9.1 Backend (Python)
| Área | Acción | Archivo / Carpeta | Detalle |
|------|--------|-------------------|---------|
| Eventos | Publicar evento `step_captured` | `src/core/artifact_collector.py` | Emitir `StepCapturedEvent` al capturar cada paso. |
| Modelos | Crear modelo Pydantic `StepRaw` y `StepEnriched` | `src/models/step.py` *(nuevo)* | Campos: `id`, `trace_id`, `timestamp`, `actor`, `action`, `target`, `raw_result`, `summary`, `compliance`, `explanation`, `confidence`. |
| Pre-procesador | Nuevo servicio `ExecutionPreprocessor` | `src/services/execution_preprocessor.py` *(nuevo)* | Suscribirse a `StepCapturedEvent`, normalizar y validar, guardar en DB. |
| LLM Job | Nuevo worker `StepEnricherJob` | `src/services/llm/step_enricher.py` *(nuevo)* | Obtiene lotes de pasos RAW, llama LLM, guarda enriquecidos. Compatible con Celery/Redis. |
| Persistencia | Tablas/colecciones `executions_raw` y `executions_enriched` | `src/database/models/execution_raw.py`, `execution_enriched.py` *(nuevo)* | SQLAlchemy + Alembic migration. |
| Repositorios | CRUD de ejecuciones | `src/database/repositories/execution_repository.py` *(nuevo)* | Métodos: `save_raw`, `save_enriched`, `get_by_trace_id`. |
| API REST | Endpoint `/executions/{id}` + SSE `/executions/{id}/stream` | `src/api/execution_routes.py` *(nuevo)* | GET raw+enriched, stream via `fastapi.responses.EventSourceResponse`. |
| WebSocket Broker | Broadcast a Frontend | `src/core/queue_manager.py` / `src/api/v2/execution_routes.py` | Emitir evento cuando un paso se enriquece.

### 9.2 Tests (Python)
| Test | Archivo | Cobertura |
|------|---------|-----------|
| Preprocesador | `tests/preprocessor/test_normalization.py` | Valida esquema y duplicados. |
| LLM Job | `tests/llm/test_enrichment_job.py` | Mock de respuesta LLM. |
| API | `tests/api/test_execution_routes.py` | GET y stream.

---

### 9.3 Frontend (Next.js + TS/React)
| Área | Acción | Archivo / Carpeta | Detalle |
|------|--------|-------------------|---------|
| Hook de datos | `use-execution.ts` *(nuevo)* | Llama API + escucha SSE; mantiene state de pasos. |
| Librería UI | Crear componente `Timeline.tsx` | `web/src/components/execution/Timeline.tsx` | Renderiza lista vertical con iconos de estado. |
| Tarjeta Step | `StepCard.tsx` | `web/src/components/execution/StepCard.tsx` | Header con resumen, body colapsable con detalles. |
| Página | Integrar timeline | `web/src/app/analytics/page.tsx` | Mostrar timeline y filtros. |
| Estilos | Colores y accesibilidad | `web/src/app/globals.css` / Radix UI tokens | Usar variables de tema AA.

### 9.4 Dev-Ops
| Acción | Archivo | Detalle |
|--------|---------|---------|
| Variable API_KEY_LLM | `.env`, `docs/environment_variables.md` | Clave para proveedor LLM. |
| Cola de trabajos | `docker/docker-compose.yml` | Agregar servicio Redis + worker Celery. |
| Métricas | `src/monitoring/efficiency_dashboard.py` | Registrar latencia y costos de LLM.

### 9.5 Timeline de Refactor (Task-List)
- [ ] Crear modelos y migraciones.
- [ ] Implementar `ExecutionPreprocessor`.
- [ ] Jobs y cola Celery.
- [ ] API + SSE.
- [ ] Hooks y componentes Frontend.
- [ ] QA + pruebas automatizadas.
- [ ] Actualizar documentación y variables de entorno.

> Con esta tabla puedes asignar responsabilidades y estimar esfuerzo con granularidad de archivo. 

## 10. Reutilización y Mejora del Validador LLM Existente

Para acelerar la entrega y minimizar retrabajo, aprovecharemos la infraestructura ya disponible de análisis con LLM.

| Recurso Existente | Acción de Mejora | Resultado Esperado |
|-------------------|------------------|--------------------|
| `src/utilities/llm_result_validator.py` | Refactorizar a `StepEnricher` y mover a `src/services/llm/step_enricher.py`. | Clase reutilizable que devuelve JSON enriquecido. |
| `_analyze_step_success` en `project_manager_service.py` | Delegar en `StepEnricher.enrich` y eliminar lógica duplicada. | Única fuente de verdad para evaluación de steps. |
| Endpoint de prueba `api/llm_validation_test_routes.py` | Convertir en suite de tests de integración para el nuevo pipeline. | Validaciones automáticas CI/CD. |
| Helper `create_llm_instance` | Añadir soporte a `function_call` y caching de prompts (Redis). | Menor latencia y coste. |
| `response_cleaner.py` | Ampliar para soportar nuevas claves (`summary`, `confidence`). | Consistencia de parsing.

Beneficios:
- Menos dependencias nuevas que mantener.
- Mantiene cobertura de tests ya existentes.
- Facilita rollback si algo falla.

---

## 11. Plan de Trabajo – Mini-Tareas

> Cada tarea está pensada para ~0.5-2 h de esfuerzo; puede asignarse a distintos miembros del equipo.

### 11.1 Preparación
1. [ ] Crear rama `feat/step-enrichment`.
2. [ ] Definir variable `LLM_PROVIDER` y actualizar `docs/environment_variables.md`.

### 11.2 Back-End
**Modelos & Persistencia**
3. [ ] Añadir archivo `src/models/step.py` con `StepRaw` y `StepEnriched`.
4. [ ] Generar migración Alembic `add_executions_tables`.

**Pre-procesador**
5. [ ] Crear `src/services/execution_preprocessor.py` con listener `StepCapturedEvent`.
6. [ ] Unit test de normalización (`tests/preprocessor/test_normalization.py`).

**LLM Enricher**
7. [ ] Mover `llm_result_validator.py` → `services/llm/step_enricher.py`.
8. [ ] Implementar método `enrich_batch(steps: List[StepRaw]) -> List[StepEnriched]`.
9. [ ] Añadir caching Redis utilizando `hash(step_raw)`.
10. [ ] Crear worker Celery `StepEnricherJob`.
11. [ ] Tests de mock LLM (`tests/llm/test_enrichment_job.py`).

**API & SSE**
12. [ ] Crear router `src/api/execution_routes.py` con:
    • GET `/executions/{trace_id}`.
    • GET `/executions/{trace_id}/stream` (SSE).
13. [ ] Actualizar `src/core/queue_manager.py` para broadcast.
14. [ ] Tests de API (`tests/api/test_execution_routes.py`).

### 11.3 Front-End (Next.js)
15. [ ] Crear hook `use-execution.ts` (SWR + EventSource).
16. [ ] Implementar componente `Timeline.tsx`.
17. [ ] Implementar `StepCard.tsx` con Radix UI + accesibilidad AA.
18. [ ] Añadir filtros quick-search y select de estado.
19. [ ] Integrar en página `app/analytics/page.tsx`.
20. [ ] Test e2e Playwright: ver que timeline se actualiza en vivo.

### 11.4 Dev-Ops / Observabilidad
21. [ ] Agregar Redis y celery-worker a `docker-compose.yml`.
22. [ ] Configurar métricas en `monitoring/efficiency_dashboard.py` (latencia, coste tokens).
23. [ ] Actualizar alertas Prometheus (umbral de latencia >2 s).

### 11.5 QA & Documentación
24. [ ] Actualizar README y Changelog.
25. [ ] Revisar cobertura (>80 %).
26. [ ] Demo interna y feedback.

---
**Entrega mínima viable**: tareas 3-14 + 15-18 + 21.

**Entrega completa**: todas las tareas 1-26. 