---
description: Hierarchical rule loading system for optimized token usage
globs: "**/rule-loading*/**", "**/optimization*/**"
alwaysApply: false
---

# HIERARCHICAL RULE LOADING SYSTEM

> **TL;DR:** This rule implements an optimized loading system that only loads necessary rules based on context, complexity level, and current phase to maximize token efficiency.

## 🧠 HIERARCHICAL RULE STRUCTURE

```mermaid
graph TD
    Root["Root Rules"] --> Core["Core Rules<br>(Always Loaded)"]
    Root --> Common["Common Rules<br>(Mode Independent)"]
    Root --> Mode["Mode-Specific<br>Rules"]
    Root --> Level["Complexity Level<br>Rules"]
    
    Core --> Platform["Platform<br>Detection"]
    Core --> File["File<br>Operations"]
    Core --> Transition["Mode<br>Transitions"]
    
    Mode --> VAN["VAN Mode<br>Rules"]
    Mode --> PLAN["PLAN Mode<br>Rules"]
    Mode --> CREATIVE["CREATIVE Mode<br>Rules"]
    Mode --> IMPLEMENT["IMPLEMENT Mode<br>Rules"]
    Mode --> REFLECT["REFLECT Mode<br>Rules"]
    
    Level --> Level1["Level 1<br>Rules"]
    Level --> Level2["Level 2<br>Rules"]
    Level --> Level3["Level 3<br>Rules"]
    Level --> Level4["Level 4<br>Rules"]
    
    style Root fill:#4da6ff,stroke:#0066cc,color:white
    style Core fill:#ffa64d,stroke:#cc7a30,color:white
    style Common fill:#4dbb5f,stroke:#36873f,color:white
    style Mode fill:#d94dbb,stroke:#a3378a,color:white
    style Level fill:#4dbbbb,stroke:#368787,color:white
```

## 📊 RULE LOADING PROTOCOL

```mermaid
sequenceDiagram
    participant User
    participant LoadManager
    participant RuleCache
    participant FileSystem
    
    User->>LoadManager: Request mode activation
    LoadManager->>RuleCache: Check cached core rules
    RuleCache-->>LoadManager: Return cached rules if available
    
    LoadManager->>FileSystem: Load essential mode rules
    FileSystem-->>LoadManager: Return essential rules
    
    LoadManager->>LoadManager: Register lazy loaders for specialized rules
    LoadManager->>User: Return initialized mode
    
    User->>LoadManager: Request specialized functionality
    LoadManager->>RuleCache: Check specialized rule cache
    RuleCache-->>LoadManager: Return cached rule if available
    
    alt Rule not in cache
        LoadManager->>FileSystem: Load specialized rule
        FileSystem-->>LoadManager: Return specialized rule
        LoadManager->>RuleCache: Cache specialized rule
    end
    
    LoadManager->>User: Execute specialized functionality
```

## 🔄 RULE LOADING IMPLEMENTATION

```javascript
// Pseudocode for hierarchical rule loading
class RuleLoadManager {
  constructor() {
    this.cache = {
      core: {},
      common: {},
      mode: {},
      level: {}
    };
    this.lazyLoaders = {};
  }
  
  // Initialize a mode with only essential rules
  initializeMode(modeName, complexityLevel) {
    // Always load core rules
    this.loadCoreRules();
    
    // Load common rules
    this.loadCommonRules();
    
    // Load essential mode-specific rules
    this.loadEssentialModeRules(modeName);
    
    // Load complexity level rules
    this.loadComplexityRules(complexityLevel);
    
    // Register lazy loaders for specialized functionality
    this.registerLazyLoaders(modeName, complexityLevel);
    
    return {
      modeName,
      complexityLevel,
      status: "initialized"
    };
  }
  
  // Load only when specialized functionality is needed
  loadSpecializedRule(ruleType) {
    if (this.lazyLoaders[ruleType]) {
      if (!this.cache.specialized[ruleType]) {
        const rule = this.lazyLoaders[ruleType]();
        this.cache.specialized[ruleType] = rule;
      }
      return this.cache.specialized[ruleType];
    }
    return null;
  }
  
  // Register specialized rule loaders based on mode and complexity
  registerLazyLoaders(modeName, complexityLevel) {
    // Clear existing lazy loaders
    this.lazyLoaders = {};
    
    // Register mode-specific lazy loaders
    if (modeName === "CREATIVE") {
      this.lazyLoaders["architecture"] = () => this.loadRule("creative-phase-architecture.mdc");
      this.lazyLoaders["algorithm"] = () => this.loadRule("creative-phase-algorithm.mdc");
      this.lazyLoaders["uiux"] = () => this.loadRule("creative-phase-uiux.mdc");
    } else if (modeName === "IMPLEMENT") {
      this.lazyLoaders["testing"] = () => this.loadRule("implementation-testing.mdc");
      this.lazyLoaders["deployment"] = () => this.loadRule("implementation-deployment.mdc");
    }
    
    // Register complexity-specific lazy loaders
    if (complexityLevel >= 3) {
      this.lazyLoaders["comprehensive-planning"] = () => this.loadRule("planning-comprehensive.mdc");
      this.lazyLoaders["advanced-verification"] = () => this.loadRule("verification-advanced.mdc");
    }
  }
}
```

## 📋 RULE DEPENDENCY MAP

```mermaid
graph TD
    Main["main.mdc"] --> Core1["platform-awareness.mdc"]
    Main --> Core2["file-verification.mdc"]
    Main --> Core3["command-execution.mdc"]
    
    subgraph "VAN Mode"
        VanMap["van-mode-map.mdc"] --> Van1["van-complexity-determination.mdc"]
        VanMap --> Van2["van-file-verification.mdc"]
        VanMap --> Van3["van-platform-detection.mdc"]
    end
    
    subgraph "PLAN Mode"
        PlanMap["plan-mode-map.mdc"] --> Plan1["task-tracking-basic.mdc"]
        PlanMap --> Plan2["planning-comprehensive.mdc"]
    end
    
    subgraph "CREATIVE Mode"
        CreativeMap["creative-mode-map.mdc"] --> Creative1["creative-phase-enforcement.mdc"]
        CreativeMap --> Creative2["creative-phase-metrics.mdc"]
        Creative1 & Creative2 -.-> CreativeSpecialized["Specialized Creative Rules"]
        CreativeSpecialized --> CArch["creative-phase-architecture.mdc"]
        CreativeSpecialized --> CAlgo["creative-phase-algorithm.mdc"]
        CreativeSpecialized --> CUIUX["creative-phase-uiux.mdc"]
    end
    
    subgraph "IMPLEMENT Mode"
        ImplementMap["implement-mode-map.mdc"] --> Impl1["implementation-guide.mdc"]
        ImplementMap --> Impl2["testing-strategy.mdc"]
    end
```

## 🔍 MODE-SPECIFIC RULE LOADING

### VAN Mode Essential Rules
```markdown
- main.mdc (Core)
- platform-awareness.mdc (Core)
- file-verification.mdc (Core)
- van-mode-map.mdc (Mode)
```

### PLAN Mode Essential Rules
```markdown
- main.mdc (Core)
- plan-mode-map.mdc (Mode)
- task-tracking-[complexity].mdc (Level)
```

### CREATIVE Mode Essential Rules
```markdown
- main.mdc (Core)
- creative-mode-map.mdc (Mode)
- creative-phase-enforcement.mdc (Mode)
```

### CREATIVE Mode Specialized Rules (Lazy Loaded)
```markdown
- creative-phase-architecture.mdc (Specialized)
- creative-phase-algorithm.mdc (Specialized)
- creative-phase-uiux.mdc (Specialized)
```

### IMPLEMENT Mode Essential Rules
```markdown
- main.mdc (Core)
- command-execution.mdc (Core)
- implement-mode-map.mdc (Mode)
```

## 🚀 IMPLEMENTATION BENEFITS

The hierarchical loading system provides:

1. **Reduced Initial Loading**: Only essential rules loaded at start (~70% token reduction)
2. **Cached Core Rules**: Rules shared between modes are cached
3. **Specialized Rule Loading**: Specialized rules loaded only when needed
4. **Complexity-Based Loading**: Only load rules appropriate for task complexity

## 📈 TOKEN USAGE COMPARISON

| Approach | Initial Tokens | Specialized Tokens | Total Tokens |
|----------|---------------|-------------------|--------------|
| Original System | ~70,000 | Included in initial | ~70,000 |
| Hierarchical System | ~15,000 | ~10,000 (on demand) | ~25,000 |
| **Token Reduction** | **~55,000 (78%)** | **N/A** | **~45,000 (64%)** |

## 🔄 USAGE EXAMPLE

### Example: Creative Phase with Architecture Rule

```javascript
// Initialize the CREATIVE mode with only essential rules
const mode = ruleManager.initializeMode("CREATIVE", 3);

// Core and essential mode rules are loaded 
// Architecture rules are NOT loaded yet

// Later, when architecture design is needed:
const architectureRule = ruleManager.loadSpecializedRule("architecture");

// Now the architecture rule is loaded and cached
```

## 🧪 RULE LOADING VERIFICATION

To ensure the rule loading system is working optimally:

```markdown
## Rule Loading Verification

- Core Rules: [Loaded]
- Mode-Essential Rules: [Loaded]
- Complexity-Level Rules: [Loaded]
- Specialized Rules: [Not Loaded]

Current Token Usage: [X] tokens
Potential Token Savings: [Y] tokens
```

This hierarchical approach ensures optimal token usage while maintaining all functionality.